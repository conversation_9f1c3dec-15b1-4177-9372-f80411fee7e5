/**
 * Copyright(C),2015-2018,北京清能互联科技有限公司 Author:   jxm Date:    2018/11/3010:43 History:
 * <author><time><version><desc>
 */
package com.tsintergy.lf.core.constants;

/**
 * 系统设置 常量
 *
 * <AUTHOR>
 */
public class SystemConstant {


    /**
     * 预测天数 key
     */
    public static final String FORECAST_DAY = "forecast_day";

    /**
     * 正常日推荐算法 value 全网算法id,地市算法id
     */
    public static final String NORMAL_ALGORITHM = "normal_algorithm";

    /**
     * 节假日推荐算法 value 全网算法id,地市算法id
     */
    public static final String HOLIDAY_ALGORITHM = "holiday_algorithm";

    /**
     * 自动上报 value   全网,地市'
     */
    public static final String AUTO_REPORT = "auto_report";

    /**
     * 子网上报截止时间   value   第一个开关，第二个时间
     */
    public static final String END_REPORT_TIME = "end_report_time";

    /**
     * 全网5分钟超短期预测 value 第一个开关，第二个预测时长
     */
    public static final String PROVINCE_SHORT_5 = "province_short_5";

    /**
     * '全网15分钟超短期预测 value 第一个开关，第二个预测时长
     */
    public static final String PROVINCE_SHORT_15 = "province_short_15";

    /**
     * 地市15分钟超短期预测 value 第一个开关，第二个预测时长
     */
    public static final String CITY_SHORT_5 = "city_short_5";

    /**
     * 地市15分钟超短期预测 value 第一个开关，第二个预测时长
     */
    public static final String CITY_SHORT_15 = "city_short_15";

    /**
     * 系统通用分隔符
     */
    public static final String SEPARATOR_PUNCTUATION = ",";

    /**
     * 节假日提前启动天数
     */
    public static final String HOLIDAY_DAY = "early_start_holiday_days";

    /**
     * 目标准确率
     */
    public static final String TARGET_ACCURACY = "target_accuracy";

    /**
     * 亮度设置
     */
    public static final String colorStr = "color_str";

    /**
     * 系统时间
     */
    public static final String SYSTEM_TIME = "system_time";

    /**
     * 网状设置
     */
    public static final String isOpenReseau = "open_reseau";

    /**
     * 是否开启页面网格
     */
    public static final String OpenReseau = "1";

    /**
     * 综合模型算法比例key值
     */
    public static final String SCALE_COMPREHENSIVE_MODEL = "scale_comprehensive_model";

    /**
     * 中长期算法月度气象设置key值
     */
    public static final String LONG_FORECAST_WEATHER_MONTH = "long_forecast_weather_month";

    /**
     * 中长期算法年度气象设置key值
     */
    public static final String LONG_FORECAST_WEATHER_YEAR = "long_forecast_weather_year";

    /**
     * 中长期算法年度&历年平均key值
     */
    public static final String LONG_FORECAST_WEATHER_YEAR_AVG_Y = "long_forecast_weather_year_avg_y";

    /**
     * 中长期算法月度&历年平均key值
     */
    public static final String LONG_FORECAST_WEATHER_YEAR_AVG_M = "long_forecast_weather_year_avg_m";


    /**
     * 省调默认登录页面设置
     */
    public static  final  String PROVINCE_DEFAULT_INDEX = "province_default_index";

    /**
     * 地调默认登录页面设置
     */
    public static  final  String CITY_DEFAULT_INDEX = "city_default_index";





    public static final  String PEAK_TIME="peak_time";

    public static final  String TROUGH_TIME="trough_time";

    public static final  String PEAK_SECTION_TIME = "peak_section_time";

    public static final String PROVINCE_END_REPORT_TIME="province_end_report_time";

    public static final String LOGIN_INFO="login_info";


    //101人工调整
    public static final String WEATHER_CODE_MANUAL = "101";

    public static final String WEATHER_CODE_NOT_MANUAL = "102";
    //0 使用历史气象
    public static final int FCST_DAY_WEATHER_HIS = 0;

    //1 使用预测气象
    public static final int FCST_DAY_WEATHER_FC = 1;


    //空调负荷发送d5000开关
    public static final String ACLOAD_SENDD5000_SWITCH = "acLoadSendD5000";


    //基础负荷增长率设置
    public static final String BASICLOAD_INCREASERATE = "ac_load_incress_rate";


    //空调负荷导入数据展示并发送d5000
    public static final String IMPORTDATASHOW_SENDD5000 = "improt_data_show_send_d5000";


    //省调运行算法列表
    public static final String PROVINCE_RUN_ALGORITHM = "province_run_algorithm";

    //地调运行算法列表
    public static final String CITY_RUN_ALGORITHM = "city_run_algorithm";

    //中长期-气象设置；预测内容-月度预测
    public static final String LONG_FORECAST_MONTH = "1";

    //中长期-气象设置；预测内容-年度预测
    public static final String LONG_FORECAST_YEAR = "2";

    //中长期-气象设置；方案选择-历年平均
    public static final String LONG_FORECAST_PLAN_AVG_YEAR = "1";

    //中长期-气象设置；方案选择-月气象特性
    public static final String LONG_FORECAST_PLAN_MONTH = "2";

    //中长期-气象设置；方案选择-日气象特性
    public static final String LONG_FORECAST_PLAN_DAY = "3";




    /**
     * 系统设置-步长设置 参数替换步长
     */
    public static final String PARAMETER_SUBSTITUTION_STEP = "parameter_substitution_step";

    /**
     * 系统设置-步长设置 增减步长
     */
    public static final String INCREASE_AND_DECREASE_STEP = "increase_and_decrease_step";

    /**
     * 系统设置-步长设置 拉伸步长
     */
    public static final String STRETCH_STEP = "stretch_step";

    /**
     * 系统设置-步长设置 极值步长
     */
    public static final String EXTREMUM_STEP = "extremum_step";


    /**
     * 系统设置-负荷较地区准确率统计 分母值
     */
    public static final String ACCURACY_DENOMINATOR = "accuracy_denominator";

    /**
     * 负荷上传国调算法和口径
     */
    public static final String GUODIAO_UPLOADFILE_ALGO_CALI = "guodiao_uploadfile_algo_cali";

    public static final String CM_ACCURACY_ALGORITHM_IDS = "cm_accuracy_algorithm_ids";

    public static final String GUODIAO_UPLOAD_FILE_ALGORITHM_LIST = "guodiao_upload_file_algo_list";
}

