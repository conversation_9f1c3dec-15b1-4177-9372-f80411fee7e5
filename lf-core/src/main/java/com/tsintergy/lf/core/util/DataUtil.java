/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司
 * Author:  wangchen
 * Date:  2018/11/22 11:13
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.core.util;

import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2018/11/22
 * @since 1.0.0
 */
public class DataUtil {

    public static List<BigDecimal> listAdd(List<BigDecimal> list1, List<BigDecimal> list2) {
        if (CollectionUtils.isEmpty(list1) && CollectionUtils.isEmpty(list2)) {
            return null;
        } else if (CollectionUtils.isEmpty(list1) && !CollectionUtils.isEmpty(list2)) {
            return list2;
        } else  if (!CollectionUtils.isEmpty(list1) && CollectionUtils.isEmpty(list2)) {
            return list1;
        } else {
            Assert.isTrue(list1.size() == list2.size());
            List<BigDecimal> result = new ArrayList<>();
            for (int i = 0; i < list1.size(); i++) {
                BigDecimal value1 = list1.get(i);
                BigDecimal value2 = list2.get(i);
                BigDecimal addValue = null;
                if (value1 != null && value2 == null) {
                    addValue = value1;
                } else if (value1 == null && value2 != null) {
                    addValue = value2;
                } else if (value1 != null && value2 != null) {
                    addValue = value1.add(value2);
                }
                result.add(addValue);
            }
            return result;
        }
    }

    public static List<BigDecimal> listDivide(List<BigDecimal> list1, List<BigDecimal> list2) {
        Assert.isTrue(!CollectionUtils.isEmpty(list1));
        Assert.isTrue(!CollectionUtils.isEmpty(list2));
        Assert.isTrue(list1.size() == list2.size());
        List<BigDecimal> result = new ArrayList<>();
        for (int i = 0; i < list1.size(); i++) {
            BigDecimal value1 = list1.get(i);
            BigDecimal value2 = list2.get(i);
            BigDecimal divideValue = null;
            if (value1 == null || value2 == null) {
                divideValue = null;
            } else {
                divideValue = value1.divide(value2,4,BigDecimal.ROUND_HALF_UP);
            }
            result.add(divideValue);
        }
        return result;
    }

    public static List<BigDecimal> multiply(List<BigDecimal> values,BigDecimal value2) {
        if (!CollectionUtils.isEmpty(values)) {
            List<BigDecimal> result = new ArrayList<>();
            values.stream().forEach(value1 -> {
                result.add(value1 == null ? null : value1.multiply(value2).setScale(4, RoundingMode.HALF_UP));
            });
            return result;
        }
        return null;
    }

    public static BigDecimal subtract(BigDecimal value1,BigDecimal value2) {
        if (value1 == null && value2 != null) {
            return BigDecimal.ZERO.subtract(value2);
        } else if (value1 != null && value2 == null) {
            return value1;
        } else if (value1 != null && value2 != null) {
            return value1.subtract(value2);
        } else {
            return null;
        }
    }

    public static List<BigDecimal> getCountListWithTwoList(List<BigDecimal> list, List<BigDecimal> list1) {
        List<BigDecimal> decimals = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            decimals.add(null);
            BigDecimal big = list.get(i);
            BigDecimal big1 = list1.get(i);
            if (big != null) {
                BigDecimal count = big.add(big1 == null ? BigDecimal.ZERO : big1);
                decimals.set(i, count);
            }
        }
        return decimals;
    }

    public static List getNullList(int pointNum) {
        List list = new ArrayList();
        for (int i = 0; i < pointNum; i++) {
            list.add(null);
        }
        return list;
    }

    /**
     * map转list
     *
     * @param map
     * @return
     */
    public static List mapToList(Map map) {
        if (map == null || map.size() < 1) {
            return null;
        }
        List list = new ArrayList();
        Set keySet = map.keySet();
        Iterator it = keySet.iterator();
        while (it.hasNext()) {
            list.add(map.get(it.next()));
        }
        return list;
    }


    /**
     * 功能描述: <br>
     * 通过key 对map排序
     *
     * @param map
     * @return
     * <AUTHOR>
     * @since 1.0.0
     */
    public static Map<String, BigDecimal> sortMapByKey(Map<String, BigDecimal> map) {
        if (map == null || map.isEmpty()) {
            return null;
        }
        Map<String, BigDecimal> sortMap = new TreeMap<String, BigDecimal>(
                new MapKeyComparator());
        sortMap.putAll(map);
        return sortMap;
    }

    static class MapKeyComparator implements Comparator<String> {
        @Override
        public int compare(String str1, String str2) {
            return str1.compareTo(str2);
        }
    }

    public static List<BigDecimal> generate96Zero() {
        List<BigDecimal> result = new ArrayList<>();
        for (int i = 0; i < 96; i++) {
            result.add(BigDecimal.ZERO);
        }
        return result;
    }

    /**
     * 功能描述: 多列list 列列相加<br>
     *
     * @param srcList 源list
     * @return: java.util.List<java.math.BigDecimal>
     * @since: 1.0.0
     * @author: <EMAIL>
     * @date: 2022/8/4 15:09
     */
    public static List<BigDecimal> addList(List<List<BigDecimal>> srcList) {
        List<BigDecimal> result = new ArrayList<>();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(srcList)) {
            for (int i = 0; i < srcList.get(0).size(); i++) {
                BigDecimal bb = BigDecimal.ZERO;
                for (List<BigDecimal> aa : srcList) {
                    if (aa.size() != srcList.get(0).size()) {
                        throw new RuntimeException("两个相加的集合不符合要求，请查看...");
                    }
                    bb = bb.add(aa.get(i));
                }
                result.add(bb);
            }
        }
        return result;
    }

    /**
     * 对96点负荷曲线进行分段调整
     *
     * @param originalCurve    原始96点负荷曲线
     * @param extremePositions 5个极值点的位置索引（0-95之间，按顺序排列）
     * @param targetValues     5个极值点的目标值
     * @return 调整后的96点负荷曲线
     */
    public static List<BigDecimal> adjustLoadCurve(
            List<BigDecimal> originalCurve,
            List<Integer> extremePositions,
            List<BigDecimal> targetValues) {

        // 1. 参数校验
        validateInput(originalCurve, extremePositions, targetValues);

        // 2. 创建调整后的曲线副本（初始化为原始值）
        List<BigDecimal> adjustedCurve = new ArrayList<>(originalCurve);

        // 3. 极值点去重并排序
        List<Point> sortedPoints = prepareControlPoints(extremePositions, targetValues, originalCurve);

        // 4. 分段处理（确保每段只处理一次）
        processSegments(adjustedCurve, originalCurve, sortedPoints);

        return adjustedCurve;
    }

    // 参数校验方法
    private static void validateInput(List<BigDecimal> curve, List<Integer> positions, List<BigDecimal> values) {
        if (curve.size() != 96 || positions.size() != 5 || values.size() != 5) {
            throw new IllegalArgumentException("输入参数长度不匹配");
        }
    }

    // 极值点预处理（排序+去重）
    private static List<Point> prepareControlPoints(List<Integer> positions,
                                                    List<BigDecimal> targets,
                                                    List<BigDecimal> original) {
        List<Point> points = new ArrayList<>();
        for (int i = 0; i < positions.size(); i++) {
            points.add(new Point(positions.get(i), targets.get(i), original.get(positions.get(i))));
        }

        // 按位置排序并去重
        points.sort((a, b) -> Integer.compare(a.position, b.position));
        List<Point> uniquePoints = new ArrayList<>();
        int lastPos = -1;
        for (Point p : points) {
            if (p.position != lastPos) {
                uniquePoints.add(p);
                lastPos = p.position;
            }
        }
        return uniquePoints;
    }

    // 分段处理核心逻辑
    private static void processSegments(List<BigDecimal> adjusted,
                                        List<BigDecimal> original,
                                        List<Point> controlPoints) {
        // 添加边界点（起始和结束）
        List<Integer> boundaries = new ArrayList<>();
        boundaries.add(0);
        for (int i = 1; i < controlPoints.size(); i++) {
            int mid = (controlPoints.get(i - 1).position + controlPoints.get(i).position) / 2;
            boundaries.add(mid);
        }
        boundaries.add(95);

        // 逐段处理
        for (int i = 0; i < controlPoints.size(); i++) {
            Point point = controlPoints.get(i);
            int start = boundaries.get(i);
            int end = boundaries.get(i + 1);

            // 设置极值点（确保只设置一次）
            adjusted.set(point.position, point.targetValue);

            // 曲线保持调整（优化后的方法）
            adjustWithShapePreservation(adjusted, original,
                    start, end,
                    point.position,
                    point.originalValue,
                    point.targetValue);
        }

        // 边界平滑处理
        for (int i = 1; i < boundaries.size() - 1; i++) {
            smoothTransition(adjusted,
                    boundaries.get(i - 1),
                    boundaries.get(i),
                    boundaries.get(i + 1));
        }
    }

    // 优化后的形状保持调整算法
    private static void adjustWithShapePreservation(List<BigDecimal> adjusted,
                                                    List<BigDecimal> original,
                                                    int start, int end,
                                                    int peakPos,
                                                    BigDecimal origPeak,
                                                    BigDecimal targetPeak) {
        BigDecimal delta = targetPeak.subtract(origPeak);
        BigDecimal ratio = origPeak.compareTo(BigDecimal.ZERO) != 0
                ? targetPeak.divide(origPeak, 10, RoundingMode.HALF_UP)
                : BigDecimal.ONE;

        // 计算影响范围
        int influenceRange = Math.min(10, Math.max(peakPos - start, end - peakPos));

        for (int i = start; i <= end; i++) {
            if (i == peakPos) continue;

            // 1. 计算距离权重（高斯分布）
            double distance = Math.abs(i - peakPos);
            double gaussianWeight = Math.exp(-Math.pow(distance, 2) / (2 * Math.pow(influenceRange / 2.0, 2)));

            // 2. 计算比例调整量
            BigDecimal ratioAdjustment = original.get(i).multiply(ratio.subtract(BigDecimal.ONE));

            // 3. 计算绝对值调整量
            BigDecimal absAdjustment = delta.multiply(BigDecimal.valueOf(1.0 - gaussianWeight));

            // 4. 综合调整（加权平均）
            BigDecimal adjustment = ratioAdjustment.multiply(BigDecimal.valueOf(gaussianWeight))
                    .add(absAdjustment.multiply(BigDecimal.valueOf(1.0 - gaussianWeight)));

            adjusted.set(i, original.get(i).add(adjustment));

            // 确保调整后的值不会出现负值
            if (adjusted.get(i).compareTo(BigDecimal.ZERO) < 0) {
                adjusted.set(i, BigDecimal.ZERO);
            }
        }
    }

    // 边界平滑处理
    private static void smoothTransition(List<BigDecimal> curve,
                                         int leftBound,
                                         int boundary,
                                         int rightBound) {
        int smoothWindow = 3; // 平滑窗口大小
        for (int i = boundary - smoothWindow; i <= boundary + smoothWindow; i++) {
            if (i <= leftBound || i >= rightBound) continue;

            // 使用加权平均平滑
            BigDecimal sum = BigDecimal.ZERO;
            int count = 0;
            for (int j = i - 1; j <= i + 1; j++) {
                if (j >= leftBound && j <= rightBound) {
                    sum = sum.add(curve.get(j));
                    count++;
                }
            }
            curve.set(i, sum.divide(BigDecimal.valueOf(count), 4, RoundingMode.HALF_UP));
        }
    }

    public static List<BigDecimal> segmentedLoadAdjustmentByExtrema(
            List<BigDecimal> yOrig,
            List<BigDecimal> extremaValues,
            List<BigDecimal> targetValues,
            boolean[] isMaximumFlags) {

        // 参数校验
        if (extremaValues.size() != 5 || targetValues.size() != 5 || isMaximumFlags.length != 5) {
            throw new IllegalArgumentException("极值点相关参数必须包含5个元素");
        }
        if (yOrig.size() != 96) {
            throw new IllegalArgumentException("原始负荷数据必须为96点");
        }

        // 初始化调整后的曲线（复制原始数据）
        List<BigDecimal> yNew = new ArrayList<>(yOrig); // 基于原始列表创建新列表
        int n = yOrig.size();

        // 第一步：定位极值点在原始数据中的索引（找到最接近的索引）
        List<Integer> extremaIndices = new ArrayList<>();
        for (BigDecimal extremaVal : extremaValues) {
            int closestIdx = 0;
            BigDecimal minDiff = yOrig.get(0).subtract(extremaVal).abs();

            // 遍历原始数据找到最接近值的索引
            for (int j = 1; j < n; j++) {
                BigDecimal diff = yOrig.get(j).subtract(extremaVal).abs();
                if (diff.compareTo(minDiff) < 0) {
                    minDiff = diff;
                    closestIdx = j;
                }
            }
            extremaIndices.add(closestIdx);
        }

        // 第二步：将极值点按索引位置排序（保持关联信息）
        List<ExtremaInfo> extremaList = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            extremaList.add(new ExtremaInfo(
                    extremaIndices.get(i),
                    extremaValues.get(i),
                    targetValues.get(i),
                    isMaximumFlags[i]
            ));
        }
        // 按索引升序排序
        extremaList.sort(Comparator.comparingInt(info -> info.index));

        // 提取排序后的信息
        int[] sortedIndices = new int[5];
        BigDecimal[] sortedExtremaValues = new BigDecimal[5];
        BigDecimal[] sortedTargets = new BigDecimal[5];
        for (int i = 0; i < 5; i++) {
            ExtremaInfo info = extremaList.get(i);
            sortedIndices[i] = info.index;
            sortedExtremaValues[i] = info.value;
            sortedTargets[i] = info.target;
        }

        // 第三步：计算分割点（相邻极值点的中点）
        List<Integer> splitPoints = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            splitPoints.add((sortedIndices[i] + sortedIndices[i + 1]) / 2);
        }

        // 第四步：计算5个分段的起止索引
        List<int[]> segmentRanges = new ArrayList<>();
        segmentRanges.add(new int[]{0, splitPoints.get(0)});
        segmentRanges.add(new int[]{splitPoints.get(0) + 1, splitPoints.get(1)});
        segmentRanges.add(new int[]{splitPoints.get(1) + 1, splitPoints.get(2)});
        segmentRanges.add(new int[]{splitPoints.get(2) + 1, splitPoints.get(3)});
        segmentRanges.add(new int[]{splitPoints.get(3) + 1, n - 1});

        // 第五步：调整每段的极值点和段内数据
        for (int i = 0; i < 5; i++) {
            int[] range = segmentRanges.get(i);
            int start = range[0];
            int end = range[1];
            int peakIdx = sortedIndices[i];  // 当前段的极值点索引
            BigDecimal originalPeak = yOrig.get(peakIdx);
            BigDecimal targetPeak = sortedTargets[i];

            // 调整极值点到目标值
            yNew.set(peakIdx, targetPeak);

            // 计算调整比例（避免除0）
            BigDecimal adjustmentRatio;
            if (originalPeak.compareTo(BigDecimal.ZERO) == 0) {
                adjustmentRatio = BigDecimal.ZERO;
            } else {
                adjustmentRatio = targetPeak.subtract(originalPeak)
                        .divide(originalPeak, 10, RoundingMode.HALF_UP);
            }

            // 按比例调整段内其他点：新值 = 原始值 * (1 + 比例)
            BigDecimal onePlusRatio = BigDecimal.ONE.add(adjustmentRatio);
            for (int j = start; j <= end; j++) {
                if (j != peakIdx) {  // 跳过已调整的极值点
                    BigDecimal newValue = yOrig.get(j).multiply(onePlusRatio)
                            .setScale(10, RoundingMode.HALF_UP);
                    yNew.set(j, newValue);
                }
            }
        }

        // 第六步：优化分割点的过渡（处理段间平滑性）
        for (int splitIdx : splitPoints) {
            // 找到分割点左侧和右侧的极值点位置
            Integer leftExtremaPos = null;
            Integer rightExtremaPos = null;

            for (int i = 0; i < 5; i++) {
                if (sortedIndices[i] < splitIdx) {
                    leftExtremaPos = i;
                } else if (sortedIndices[i] > splitIdx) {
                    rightExtremaPos = i;
                    break;
                }
            }

            if (leftExtremaPos == null || rightExtremaPos == null) {
                continue;
            }

            // 获取左右极值点的原始值和目标值
            int leftIdx = sortedIndices[leftExtremaPos];
            BigDecimal leftOrig = yOrig.get(leftIdx);
            BigDecimal leftTarget = sortedTargets[leftExtremaPos];
            BigDecimal leftDiff = leftTarget.subtract(leftOrig);  // 左侧总调整量

            int rightIdx = sortedIndices[rightExtremaPos];
            BigDecimal rightOrig = yOrig.get(rightIdx);
            BigDecimal rightTarget = sortedTargets[rightExtremaPos];
            BigDecimal rightDiff = rightTarget.subtract(rightOrig);  // 右侧总调整量

            // 计算左右调整比例（用于判断调整方向）
            BigDecimal leftRatio = calculateRatio(leftOrig, leftDiff);
            BigDecimal rightRatio = calculateRatio(rightOrig, rightDiff);

            // 情况1：两边同时抬高或同时降低（同方向调整）
            if ((leftRatio.compareTo(BigDecimal.ZERO) > 0 && rightRatio.compareTo(BigDecimal.ZERO) > 0) ||
                    (leftRatio.compareTo(BigDecimal.ZERO) < 0 && rightRatio.compareTo(BigDecimal.ZERO) < 0)) {

                // 收集左右极值点之间的中间点
                List<Integer> middlePoints = new ArrayList<>();
                for (int j = leftIdx + 1; j < rightIdx; j++) {
                    middlePoints.add(j);
                }

                if (!middlePoints.isEmpty()) {
                    BigDecimal totalDiff = rightDiff.subtract(leftDiff);  // 调整量差值
                    int numMiddle = middlePoints.size();

                    for (int i = 0; i < numMiddle; i++) {
                        int pointIdx = middlePoints.get(i);
                        // 计算相对位置（0~1，0靠近左极值，1靠近右极值）
                        BigDecimal relativePos = calculateRelativePos(i, numMiddle);
                        // 线性插值调整量
                        BigDecimal adjustment = leftDiff.add(totalDiff.multiply(relativePos))
                                .setScale(10, RoundingMode.HALF_UP);
                        yNew.set(pointIdx, yOrig.get(pointIdx).add(adjustment));
                    }
                }
            }

            // 情况2：一边升高一边降低（反方向调整）
            else if ((leftRatio.compareTo(BigDecimal.ZERO) > 0 && rightRatio.compareTo(BigDecimal.ZERO) < 0) ||
                    (leftRatio.compareTo(BigDecimal.ZERO) < 0 && rightRatio.compareTo(BigDecimal.ZERO) > 0)) {

                // 分割点保持原始值不变
                yNew.set(splitIdx, yOrig.get(splitIdx));

                // 处理分割点左侧的点（从分割点向左侧极值点）
                List<Integer> leftPoints = new ArrayList<>();
                for (int j = leftIdx + 1; j < splitIdx; j++) {
                    leftPoints.add(j);
                }

                if (!leftPoints.isEmpty()) {
                    int numLeft = leftPoints.size();
                    for (int i = 0; i < numLeft; i++) {
                        int pointIdx = leftPoints.get(i);
                        // 相对位置（0~1，0靠近分割点，1靠近左极值）
                        BigDecimal relativePos = calculateLeftRelativePos(i, numLeft);
                        // 分摊调整量
                        BigDecimal adjustment = leftDiff.multiply(relativePos)
                                .setScale(10, RoundingMode.HALF_UP);
                        yNew.set(pointIdx, yOrig.get(pointIdx).add(adjustment));
                    }
                }

                // 处理分割点右侧的点（从分割点向右侧极值点）
                List<Integer> rightPoints = new ArrayList<>();
                for (int j = splitIdx + 1; j < rightIdx; j++) {
                    rightPoints.add(j);
                }

                if (!rightPoints.isEmpty()) {
                    int numRight = rightPoints.size();
                    for (int i = 0; i < numRight; i++) {
                        int pointIdx = rightPoints.get(i);
                        // 相对位置（0~1，0靠近分割点，1靠近右极值）
                        BigDecimal relativePos = calculateRelativePos(i, numRight);
                        // 分摊调整量
                        BigDecimal adjustment = rightDiff.multiply(relativePos)
                                .setScale(10, RoundingMode.HALF_UP);
                        yNew.set(pointIdx, yOrig.get(pointIdx).add(adjustment));
                    }
                }
            }
        }

        return yNew;
    }

    /**
     * 计算调整比例（辅助方法）
     */
    private static BigDecimal calculateRatio(BigDecimal original, BigDecimal diff) {
        if (original.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return diff.divide(original, 10, RoundingMode.HALF_UP);
    }

    /**
     * 计算从左到右的相对位置（0~1）
     */
    private static BigDecimal calculateRelativePos(int index, int total) {
        if (total <= 1) {
            return new BigDecimal("0.5");
        }
        return new BigDecimal(index)
                .divide(new BigDecimal(total - 1), 10, RoundingMode.HALF_UP);
    }

    /**
     * 计算左侧点相对分割点的位置（0~1）
     */
    private static BigDecimal calculateLeftRelativePos(int index, int total) {
        if (total <= 1) {
            return new BigDecimal("0.5");
        }
        return new BigDecimal(total - 1 - index)
                .divide(new BigDecimal(total - 1), 10, RoundingMode.HALF_UP);
    }

    /**
     * 封装极值点信息（内部辅助类）
     */
    private static class ExtremaInfo {
        int index;
        BigDecimal value;
        BigDecimal target;
        boolean isMaximum;

        public ExtremaInfo(int index, BigDecimal value, BigDecimal target, boolean isMaximum) {
            this.index = index;
            this.value = value;
            this.target = target;
            this.isMaximum = isMaximum;
        }
    }

    // 极值点数据结构
    private static class Point {
        int position;
        BigDecimal targetValue;
        BigDecimal originalValue;

        Point(int pos, BigDecimal target, BigDecimal original) {
            this.position = pos;
            this.targetValue = target;
            this.originalValue = original;
        }
    }
}