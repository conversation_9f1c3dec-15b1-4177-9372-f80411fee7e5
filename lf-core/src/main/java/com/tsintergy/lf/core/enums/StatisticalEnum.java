/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司 Author:  wangchen Date:  2018/10/12 9:03 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.core.enums;

/**
 * 全局统计类型变量枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum StatisticalEnum {

    MAX(1, "MAX"),

    AVG(2, "AVG"),

    MIN(3, "MIN"),

    BaoGongAVG(4, "BaoGongAVG"),

    NoonMIN(5, "NoonMIN"),

    DawnMIN(6, "DawnMIN");

    /**
     * 数值
     */
    private Integer type;

    /**
     * 名称
     */
    private String value;

    StatisticalEnum(Integer type, String value) {
        this.type = type;
        this.value = value;
    }

    public static String getValue(Integer i) {
        String typeName = null;
        StatisticalEnum[] values = StatisticalEnum.values();
        for (StatisticalEnum weatherTypeEnum : values) {
            if (i.equals(weatherTypeEnum.type)) {
                typeName = weatherTypeEnum.value;
            }
        }
        return typeName;

    }


    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}