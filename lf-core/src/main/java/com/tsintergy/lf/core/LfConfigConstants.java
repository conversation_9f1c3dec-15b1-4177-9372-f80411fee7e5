package com.tsintergy.lf.core;

import com.tsieframework.boot.autoconfigure.TsieFrameworkConfigConstants;

/**
 * <p>
 *     常量配置
 * </p>
 *
 * <AUTHOR>
 * @date 2020-02-18 11:00:43
 */
public class LfConfigConstants {

    /**
     * 项目名
     */
    public static final String PROJECT_NAME = "lf";
    /**
     * 属性配置前缀
     */
    public static final String PROPERTIES_PREFIX = TsieFrameworkConfigConstants.TSIE_PREFIX + "." + PROJECT_NAME;
    /**
     * job属性配置前缀
     */
    public static final String PROPERTIES_JOB_PREFIX = TsieFrameworkConfigConstants.TSIE_PREFIX + "." + PROJECT_NAME + ".job";
    /**
     * Controller映射配置前缀
     */
    public static final String MAPPING_PREFIX = "/" + PROJECT_NAME;

}
