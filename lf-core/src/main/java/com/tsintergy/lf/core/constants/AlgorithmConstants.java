/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司 Author: liufeng Date: 2018/6/6 17:47 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.core.constants;

/**
 * 算法常量类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class AlgorithmConstants {


    public static final String USER_ID = "userId";

    /**
     * 人工决策算法ID
     */
    public static final String MD_ALGORITHM_ID = "0";

    /**
     * 台风算法ID
     */
    public static final String TYPHOON_ALGORITHM_ID = "11";

    /**
     * 免考申请的状态是已经审批
     */
    public static final Integer CHECK_STATUS = 1;

    /**
     * 历史负荷 最大值
     */
    public final static String LOAD_STATS = "loadStats";

    /**
     * 特殊算法type
     */
    public static final String SPECIAL_ALGORITHM_TYPE = "3";

    /**
     * 节假日算法type
     */
    public static final String HOLIDAY_ALGORITHM_TYPE = "2";

    /**
     * 正常日算法type
     */
    public static final String NORMAL_ALGORITHM_TYPE = "1";

    /**
     * 通用算法type
     */
    public static final String COMMON_ALGORITHM_TYPE = "0";


    /**
     * 算法服务传回的压缩包后缀
     */
    public static final String ZIP_SUFFIX = ".tar.gz";


    /**
     * 文件编码格式 稳定度算法用到  其他算法用的是GB2312
     */
    public final static String ENCODING = "UTF-8";

    /**
     * 授权文件的路径（所有的都用同一个授权文件）
     */
    public final static String LICENCE_PATH = "LICENCE.PATH";

/**
 * ---------------------------------------稳定度分析---------------------------------------------
 */
    /**
     * 稳定度分析控制参数写入文件
     */
    public final static String PRECISION_IN_FILE_NAME = "precision.ctr";

    /**
     * 存放历史数据的文件
     */
    public final static String PRECISION_IN_DATA_NAME = "load.txt";

    /**
     * 算法输出结果（分量）存放文件
     */
    public final static String PRECISION_OUT_LOAD_DATA_NAME = "loadDivision.out";

    /**
     * 算法输出结果(上下限) 存放文件名称
     */
    public final static String PRECISION_OUT_DATA_NAME = "precision.out";


    public final static String EMPTY = " ";

    /**
     * -------------------------------------------------模板所用参数-----------------------------------------------------------
     * 不同模板参数名不同 故存在如 同一数据表示方式不同 如历史出力  表示为 HISTORY_LOAD  or   HIS_LOAD
     */

    //综合相似模板参数
    public static final String ModelType = "ModelType";

    //相似日天数
    public static final String SIMILAR_DAY_NUM = "SimilarDayNum";

    public static final String HISTORY_LOAD = "HistoryLoad";

    public static final String TEMPERATURE = "Temperature";

    public static final String PRECIPITATION = "Precipitation";

    public static final String HUMIDITY = "Humidity";

    /**
     * 综合相似 近几日气象条件开关
     */
    public static final Integer CompositeOn = 1;

    // 基准日
    public final static String BASEDAY = "BaseDay";

    public final static String CITYNAME = "CityName";

    //预测起始日
    public final static String FORECAST_START_DAY = "ForecastStarDay";

    //预测结束日
    public final static String FORECAST_END_DAY = "ForecasetEndDay";

    /**
     * 历史负荷
     */
    public final static String HIS_LOAD = "LoadHisList";


    /**
     * 灵敏度分析自变量统计类型
     */
    public final static String INDVARI_TYPE = "IndVariType";

    public final static String KEY_LOAD_INFO = "keyLoadInfo";

    /**
     * 灵敏度分析 file out 数据标识 拟合精度
     */
    public final static String FITTING_ACCURACY = "FittingAccuracy";

    /**
     * 灵敏度分析 file out 数据标识  灵敏度
     */
    public final static String INDVARI_AND_SENSITIVITY = "IndVariAndSensitivity";

    /**
     * 灵敏度分析 file out 数据标识   自变量实际值和拟合值
     */
    public final static String IND_VARI_ACT_FIT_VALUE = "IndVariActFitValue";

    /**
     * 预处理起始日
     */
    public static final String PRE_PROPESS_BEGIN_DAY = "PreprocessBeginDay";

    //历史气象数据
    public final static String DATA_WEATHER_KEY = "hisWeather";

    //历史气象特性
    public final static String WEATHER_FEATURE_HIS = "hisWeatherFeature";

    public final static String DATA_ALGORITHM_KEY = "hisAlgorithmLoad";

    //SVM算法的预测结果
    public final static String SVM_FC = "SvmFcList";

    //Xg算法的预测结果
    public final static String XG_FC = "XgFcList";

    //ANN算法的预测结果
    public final static String ANN_FC = "AnnFcList";

    //标典算法的预测结果
    public final static String SVM_LARGE_FC = "BdFcList";

    //LightGBM算法的预测结果
    public final static String LGB_FC = "LightFcList";

    public final static String START_WITH_ZERO = "startWithZero";

    //湿度
    public final static String HUMIDITY_DATA = "HumidityList";

    //温度
    public final static String TEMPERATURE_DATA = "TemperatureList";

    //实感温度 xg补充算法专用
    public final static String EFFECTIVE_TEMPERATURE = "ApparentTemperatureList";

    // 焓值  xg补充算法专用
    public final static String ENTHALPY_VALUE = "EnthalpyList";

    // 综合气象 key
    public final static String SYNTHESIZE_WEATHER_KEY = "synthesizeWeather";

    //降水
    public final static String PRECIPITATION_DATA = "PrecipitationList";

    //风速
    public final static String WIND_DATA = "WindList";

    //调休日期
    public final static String OFFDATES = "OffDates";

    //节假日信息
    public final static String HOLIDAY_INFO = "HolidayInfo";

    //近两年强台风日期
    public final static String TYPHOON_DATAS = "TyphoonDatas";

    //气象特性数据 key
    public final static String MAX_TEMPERATURE = "MaxTemperatureList";

    //算法类型
    public final static String ALGORITHM_TYPE = "AlgorithmType";

    public final static String NEW_POINT = "Point";

    public final static String LOAD_FEATURE_HIS = "hisLoadFeature";

    //算法模块回传参数标识 口径id
    public final static String CALIBER_ID = "caliberId";

    //算法模块回传参数标识 城市id
    public final static String CITY_ID = "cityId";

    //算法模块回传参数标识 算法id
    public final static String ALGORITHM_ID = "algorithmId";

    //1 算法内部滚动预测 预测多天，只调用一次算法  2 程序循环一天一天调用 默认为 1
    public final static String FORECAST_CYCLIC_TYPE = "forecastCyclicType";


    /**
     * 超短期 预测起始日期
     */
    public final static String FORECAST_BEGIN_DAY = "ForecastBeginDay";

    /**
     * 超短期 时间间隔
     */
    public final static String TIME_SPAN = "TimeSpan";

    /**
     * 超短期 预测起始点数
     */
    public final static String FORECAST_BEGIN_POINT = "ForecastBeginPoint";

    /**
     * 超短期 预测点数
     */
    public final static String FORECAST_POINT = "ForecastPoint";

    /**
     * 算法输入文件的名称
     */
    public final static String MULTIMETHOD_IN_FILE_NAME = "FILE_IN.e";

    /**
     * 算法输出文件的名称
     */
    public final static String MULTIMETHOD_OUT_NAME = "FILE_OUT.e";

    /**
     * 数据修复算法输出文件的名称
     */
    public final static String PRE_PROCESS_OUT_NAME = "FILE_OUT_sketchy.e";


    /**
     * e文件名称
     */
    public final static String ForecastEtableName = "ForecastLoad";

    public final static String ModifyLoadEtableName = "ModifiedLoad";

    /**
     * 调试模式
     */
    public final static String IsDebug = "ALGORITHM.DEBUG";

    /**
     * message文件
     */
    public final static String MESSAGE_NAME = "message.e";


    /**
     * ----------------正常日预测算法相关路径-------------------------------------------------------------------------------------------
     */

    public final static String FORECAST_IN_PATH = "FORECAST.IN.PATH";

    public final static String FORECAST_OUT_PATH = "FORECAST.OUT.PATH";

    public final static String FORECAST_RUN_PATH = "FORECAST.RUN.PATH";


    public final static String LONGVEC_IN_PATH = "LONGVEC.IN.PATH";

    public final static String LONGVEC_OUT_PATH = "LONGVEC.OUT.PATH";

    public final static String LONGVEC_RUN_PATH = "LONGVEC.RUN.PATH";

    public final static String LONGVEC_MAX_TEMPLATE_NAME = "longvecmax";

    public final static String LONG_VEC_MAX_NAME = "longvecmax";


    public final static String LONGVEC_MIN_TEMPLATE_NAME = "longvecmin";

    public final static String LONG_VEC_MIN_NAME = "longvecmin";


    public final static String LONGVEC_ENERGY_TEMPLATE_NAME = "longvecenergy";

    public final static String LONG_VEC_ENERGY_NAME = "longvecenergy";


    /**
     * 每日算法 标点 exe文件名
     */
    public final static String PRECDIC_EXE_NAME = "Predict";

    /**
     * 每日算法 通用freemarker的模板名称
     */
    public final static String FORECAST_TEMPLATE_NAME = "forecast";

    /**
     * 神经网络
     */
    public final static String NEW_EXE = "NewPointANN";

    /**
     * xg的exe文件
     */
    public final static String XG_EXE_NAME = "Xgboost";

    public final static String STLF_EXE_NAME = "STLF_MultiMethod";

    public final static String MF_TEMPLATE_NAME = "modelfusion";

    public final static String LONG_COMPOSITE_TEMPLATE_NAME = "longComposite";

    public final static String MF_EXE_NAME = "ModelFusion";

    public final static String LONG_COMPOSITE_EXE_NAME = "LongComposite";


    /**
     * ----------------台风预测算法---------------------------------------------------------------------------------------------------------------
     */
    public final static String TYPHOON_IN = "TYPHOON.IN.PATH";

    public final static String TYPHOON_RUN = "TYPHOON.RUN.PATH";

    public final static String TYPHOON_OUT = "TYPHOON.OUT.PATH";

    public final static String TYPHOON_EXE_NAME = "TyphoonLoadPredict";

    public final static String TYPHOON_TEMPLATE_NAME = "typhoon";

    /**
     * ----------------超短期预测算法相关路径---------------------------------------------------------------------------------------------------------------
     */
    public final static String SHORT_IN_PATH = "SHORT.IN.PATH";

    public final static String SHORT_OUT_PATH = "SHORT.OUT.PATH";

    public final static String SHORT_RUN_PATH = "SHORT.RUN.PATH";

    public final static String SHORT_TEMPLATE_96_NAME = "shortForecast96";

    public final static String SHORT_TEMPLATE_288_NAME = "shortForecast288";

    public final static String SHORT_EXE_NAME = "V_STLF";


    /**
     * ----------------LGB算法相关路径---------------------------------------------------------------------------------------------------------------
     */
    public final static String LGB_EXE_NAME = "StlfLgb";

    public final static String LGB_TEMPLATE_NAME = "replenish";

    /**
     * ----------------节假日预测算法相关路径---------------------------------------------------------------------------------------------------------------
     */
    public final static String HOLIDAY_IN = "HOLIDAY.IN.PATH";

    public final static String HOLIDAY_RUN = "HOLIDAY.RUN.PATH";

    public final static String HOLIDAY_OUT = "HOLIDAY.OUT.PATH";

    public final static String HOLIDAY_EXE_NAME = "STLF_FEST_SVM";

    public static final String HOLIDAY_TEMPLATE_NAME = "holiday";


    /**
     * ----------------数据清洗---------------------------------------------------------------------------------------------------------------------------------
     */
    public final static String PREPROCESS_IN_PATH = "PREPROCESS.IN.PATH";

    public final static String PREPROCESS_RUN_PATH = "PREPROCESS.RUN.PATH";

    public final static String PREPROCESS_OUT_PATH = "PREPROCESS.OUT.PATH";

    public final static String PREPROCESS_EXE_NAME = "DataPreprocess";

    public final static String PREPROCESS_TEMPLATE_NAME = "preProcess";


    /**
     * ----------------稳定度算法---------------------------------------------------------------------------------------------------------------------------------
     */
    public final static String PRECISION_IN = "PRECISION.IN.PATH";

    public final static String PRECISION_RUN = "PRECISION.RUN.PATH";

    public final static String PRECISION_OUT = "PRECISION.OUT.PATH";

    /**
     * 稳定度算法名称
     */
    public final static String PRECISION_NAME = "precision";

    /**
     * ----------------相似性日算法---------------------------------------------------------------------------------------------------------------------------------
     */
    public final static String SIMILARITY_IN_PATH = "SIMSILARITY.IN.PATH";

    public final static String SIMILARITY_OUT_PATH = "SIMSILARITY.OUT.PATH";

    public final static String SIMILARITY_RUN_PATH = "SIMSILARITY.RUN.PATH";

    public static final String SIMILARITY_EXE_NAME = "xiangsiri";

    public final static String SIMILARITY_TEMPLATE_NAME = "similar";

    /**
     * ----------------灵敏度算法---------------------------------------------------------------------------------------------------------------
     */
    public final static String SENSITIVITY_IN_PATH = "SELF_SENSITIVITY.IN.PATH";

    public final static String SENSITIVITY_RUN_PATH = "SELF_SENSITIVITY.RUN.PATH";

    public final static String SENSITIVITY_OUT_PATH = "SELF_SENSITIVITY.OUT.PATH";

    public static final String SENSITIVITY_EXE_NAME = "ANALYSIS_SENSITIVITY";

    public final static String SENSITIVITY_TEMPLATE_NAME = "sensitivity";


    public static final String isAlgo109 = "isAlgo109";

    public static final String isAlgo106 = "isAlgo106";

    public static final String isAlgo105 = "isAlgo105";

    public static final String isAlgo103 = "isAlgo103";

    public static final String isAlgo104 = "isAlgo104";

    public static final String threshold = "threshold";

    /**
     * GT算法仅训练模式
     */
    public static final String ONLY_TRAIN = "OnlyTrain";

}