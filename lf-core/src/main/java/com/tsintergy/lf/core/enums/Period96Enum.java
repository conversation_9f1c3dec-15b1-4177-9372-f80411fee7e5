package com.tsintergy.lf.core.enums;

import com.tsieframework.core.base.enums.TsieEnum;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description: 固定96点枚举值
 * @dateTime 2022/10/10 18:41
 */
public enum Period96Enum implements TsieEnum {
    T0(0, "00:00"),
    T1(1, "00:15"),
    T2(2, "00:30"),
    T3(3, "00:45"),
    T4(4, "01:00"),
    T5(5, "01:15"),
    T6(6, "01:30"),
    T7(7, "01:45"),
    T8(8, "02:00"),
    T9(9, "02:15"),
    T10(10, "02:30"),
    T11(11, "02:45"),
    T12(12, "03:00"),
    T13(13, "03:15"),
    T14(14, "03:30"),
    T15(15, "03:45"),
    T16(16, "04:00"),
    T17(17, "04:15"),
    T18(18, "04:30"),
    T19(19, "04:45"),
    T20(20, "05:00"),
    T21(21, "05:15"),
    T22(22, "05:30"),
    T23(23, "05:45"),
    T24(24, "06:00"),
    T25(25, "06:15"),
    T26(26, "06:30"),
    T27(27, "06:45"),
    T28(28, "07:00"),
    T29(29, "07:15"),
    T30(30, "07:30"),
    T31(31, "07:45"),
    T32(32, "08:00"),
    T33(33, "08:15"),
    T34(34, "08:30"),
    T35(35, "08:45"),
    T36(36, "09:00"),
    T37(37, "09:15"),
    T38(38, "09:30"),
    T39(39, "09:45"),
    T40(40, "10:00"),
    T41(41, "10:15"),
    T42(42, "10:30"),
    T43(43, "10:45"),
    T44(44, "11:00"),
    T45(45, "11:15"),
    T46(46, "11:30"),
    T47(47, "11:45"),
    T48(48, "12:00"),
    T49(49, "12:15"),
    T50(50, "12:30"),
    T51(51, "12:45"),
    T52(52, "13:00"),
    T53(53, "13:15"),
    T54(54, "13:30"),
    T55(55, "13:45"),
    T56(56, "14:00"),
    T57(57, "14:15"),
    T58(58, "14:30"),
    T59(59, "14:45"),
    T60(60, "15:00"),
    T61(61, "15:15"),
    T62(62, "15:30"),
    T63(63, "15:45"),
    T64(64, "16:00"),
    T65(65, "16:15"),
    T66(66, "16:30"),
    T67(67, "16:45"),
    T68(68, "17:00"),
    T69(69, "17:15"),
    T70(70, "17:30"),
    T71(71, "17:45"),
    T72(72, "18:00"),
    T73(73, "18:15"),
    T74(74, "18:30"),
    T75(75, "18:45"),
    T76(76, "19:00"),
    T77(77, "19:15"),
    T78(78, "19:30"),
    T79(79, "19:45"),
    T80(80, "20:00"),
    T81(81, "20:15"),
    T82(82, "20:30"),
    T83(83, "20:45"),
    T84(84, "21:00"),
    T85(85, "21:15"),
    T86(86, "21:30"),
    T87(87, "21:45"),
    T88(88, "22:00"),
    T89(89, "22:15"),
    T90(90, "22:30"),
    T91(91, "22:45"),
    T92(92, "23:00"),
    T93(93, "23:15"),
    T94(94, "23:30"),
    T95(95, "23:45"),
    T96(96, "24:00");

    private Integer id;
    private String text;

    private static Map<String,Period96Enum> cacheMap = new HashMap<>();
    Period96Enum(Integer id, String text) {
        this.text = text;
        this.id = id;
    }

    @Override
    public Integer getId() {
        return id;
    }

    @Override
    public String getText() {
        return text;
    }

    public static Period96Enum getByText(String text) {
        Period96Enum period96Enum = cacheMap.get(text);
        if(null == period96Enum) {
            Optional<Period96Enum> first = Arrays.stream(Period96Enum.values()).filter(p -> p.getText().equals(text)).findFirst();
            if(first.isPresent()) {
                period96Enum = first.get();
                cacheMap.put(text,first.get());
            }
        }
        return period96Enum;
    }
}
