/**
 * Copyright(C),2015‐2019,北京清能互联科技有限公司
 * Author:yangjin
 * Date:2019/6/2619:27
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.core.enums;

/**
 *Description: <br>
 *
 *<AUTHOR>
 *@create 2019/6/26
 *@since 1.0.0
 */
public enum WeatherColumnEnum {

    HIGHESTTEMPERATURE(0,"highestTemperature","最高温度"),
    AVETEMPERATURE(1,"aveTemperature","平均温度"),
    LOWESTTEMPERATURE(2,"lowestTemperature","最低温度"),
    AVEHUMIDITY(3,"aveHumidity","平均相对湿度"),
    MINWINDS(4,"minWinds","最大风速"),
    AVEWINDSPEED(5,"aveWinds","平均风速"),
    AVEWINDS(6,"minWinds","最低风速"),
    RAINFALL(7,"rainfall","日降水量"),



    ;

    private Integer id;
    private String name;
    private String value;


    WeatherColumnEnum(Integer id, String value, String name) {
        this.id = id;
        this.name = name;
        this.value = value;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }



    public static String getValueById(Integer id){
        for (WeatherColumnEnum w :WeatherColumnEnum.values()){
            if(w.getId().equals(id)){
                return w.getValue();
            }
        }
        return null;
    }
}