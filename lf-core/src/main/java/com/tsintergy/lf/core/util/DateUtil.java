/**
 * Copyright(C),2015-2018,北京清能互联科技有限公司 Author:   jxm Date:    2018/11/2710:40 History:
 * <author><time><version><desc>
 */
package com.tsintergy.lf.core.util;

import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.lf.core.constants.Constants;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.jsoup.helper.StringUtil;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * Description:<br>
 *
 * <AUTHOR>
 * @create2018/11/27
 * @since1.0.0
 */
public class DateUtil {

    /**
     * yyyy-MM-dd HH:mm:ss
     */
    public final static String DATE_FORMAT1 = "yyyy-MM-dd HH:mm:ss";

    /**
     * yyyy-MM-dd
     */
    public final static String DATE_FORMAT2 = "yyyy-MM-dd";

    /**
     * YYYYMMDD
     */
    public final static String DATE_FORMAT4 = "yyyyMMdd";

    /**
     * YYYYMMddHHmmss
     */
    public final static String DATE_FORMAT5 = "yyyyMMddHHmmss";

    /**
     * YYYYMMddHHmmssSSS
     */
    public final static String DATE_FORMAT6 = "yyyyMMddHHmmssSSS";

    /**
     * yyyy-MM
     */
    public final static String DATE_FORMAT7 = "yyyy-MM";

    /**
     * YYYYMMdd HH:mm:ss
     */
    public final static String DATE_FORMAT8 = "yyyyMMdd HH:mm:ss";

    /**
     * yyyyMM
     */
    public final static String DATE_FORMAT9 = "yyyyMM";

    /**
     * yyyyMM
     */
    public final static String DATE_FORMAT10 = "MMdd";

    /**
     * YYYYMMdd HH:mm:ss
     */
    public final static String DATE_FORMAT11 = "yyyy-MM-dd HH:mm:ss";

    public final static String DATE_FORMAT12 = "yyyy";


    /**
     * 获取当前日期的前一天（字符串类型），如果传入的参数为空，则返回空
     *
     * @param formatstr 格式化参数
     * @return 前一天
     */
    public static String getPreDay(String formatstr) {
        String preday = "";
        SimpleDateFormat format = new SimpleDateFormat(formatstr);
        try {
            Date ddate = DateUtil.getCureDate();
            Calendar cal = Calendar.getInstance();
            cal.setTime(ddate);
            cal.add(Calendar.DAY_OF_MONTH, -1);
            Date preddate = cal.getTime();
            preday = format.format(preddate);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return preday;
    }

    /**
     * 获取当前日期月份的最后一天
     */
    public static Date getLastDayOfMonth(Date date) {
        Calendar ca = Calendar.getInstance();
        ca.setTime(date);
        ca.set(Calendar.DAY_OF_MONTH, ca.getActualMaximum(Calendar.DAY_OF_MONTH));
        return ca.getTime();
    }

    public static Date getFirstDayDateOfYear(final Date date) {
        final Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        final int last = cal.getActualMinimum(Calendar.DAY_OF_YEAR);
        cal.set(Calendar.DAY_OF_YEAR, last);
        return cal.getTime();

    }

    public static Date getLastDayOfYear(Date date) {
        final Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        final int last = cal.getActualMaximum(Calendar.DAY_OF_YEAR);
        cal.set(Calendar.DAY_OF_YEAR, last);
        return cal.getTime();

    }

    /**
     * 获取指定年月的最后一天
     */
    public static String getLastDayOfMonth(String year, String month) {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR, Integer.valueOf(year));
        //设置月份
        cal.set(Calendar.MONTH, Integer.valueOf(month) - 1);
        //获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DATE);
        //设置日历中月份的最大天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(cal.getTime());
    }

    /**
     * 获取当前日期月份的第一天
     */
    public static Date getFirstDayOfMonth(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.MONTH, 0);
        c.set(Calendar.DAY_OF_MONTH, 1);
        return c.getTime();
    }

    /**
     * 通过时间秒毫秒数判断两个时间的间隔
     */
    public static int differentDaysByMillisecond(Date date1, Date date2) {
        int days = (int) ((date2.getTime() - date1.getTime()) / (1000 * 3600 * 24));
        return days;
    }

    /**
     * 获取系统的明天时间（Date类型）
     *
     * @return 明天
     */
    public static Date getNexDay() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, 1);

        return cal.getTime();
    }


    /**
     * 功能描述: <br> 获取某年中的第一天
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    public static Date getYearFirst(Integer year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        Date currYearFirst = calendar.getTime();
        return currYearFirst;
    }

    /**
     * 功能描述: <br> 获取某年最后一天日期
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    public static Date getYearLast(Integer year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        calendar.roll(Calendar.DAY_OF_YEAR, -1);
        Date currYearLast = calendar.getTime();
        return currYearLast;
    }

    /**
     *      功能描述: <br>     获取日期所在的周数   
     *
     * @return  
     * <AUTHOR> 
     * @since 1.0.0     
     */
    public static String getWeekOfYear(Date date) {
        Calendar cal = Calendar.getInstance();
        //设置周一为一周的第一天
        cal.setFirstDayOfWeek(Calendar.MONDAY);
        cal.setTime(date);
        int num = cal.get(Calendar.WEEK_OF_YEAR);
        String weekNum = String.valueOf(num);
        if (weekNum.length() == 1) {
            weekNum = "0" + weekNum;
        }
        return weekNum;
    }

    /**
     * 获取指定年月的第一天
     */
    public static String getFirstDayOfMonth(String year, String month) {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR, Integer.valueOf(year));
        //设置月份
        cal.set(Calendar.MONTH, Integer.valueOf(month) - 1);
        //获取某月最小天数
        int firstDay = cal.getMinimum(Calendar.DATE);
        //设置日历中月份的最小天数
        cal.set(Calendar.DAY_OF_MONTH, firstDay);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(cal.getTime());
    }

    /**
     * 格式化周日期
     *
     * @param date 每周周一日期
     */
    public static String formatWeekDate(Date date) {
        if (date == null) {
            return null;
        }
        String dateStr = DateUtil.formateDate(date);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_YEAR, 6);
        if (calendar.getTime().after(new Date())) {
            calendar.setTime(new Date());
        }
        dateStr = dateStr + "~" + DateUtil.formateDate(calendar.getTime());
        return dateStr;
    }

    /**
     * 获取指定日期所在周的周一日期
     */
    public static Date getMondayByDate(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        // 判断要计算的日期是否是周日，如果是则减一天计算周六的，否则会出问题，计算到下一周去了
        if (1 == cal.get(Calendar.DAY_OF_WEEK)) {
            cal.add(Calendar.DAY_OF_MONTH, -1);
        }
        cal.setFirstDayOfWeek(Calendar.MONDAY);
        int day = cal.get(Calendar.DAY_OF_WEEK);
        cal.add(Calendar.DATE, cal.getFirstDayOfWeek() - day);
        return cal.getTime();
    }


    /**
     * 将日期类型 转换为字符串类型
     *
     * @param date 日期
     * @param formatstr 格式化参数类型。为空系统默认采用yyyy-MM-dd
     * @return 日期
     */
    public static String getStrDate(Date date, String formatstr) {
        String fstr = "yyyy-MM-dd";

        if (formatstr != null) {
            fstr = formatstr;
        }

        SimpleDateFormat format = new SimpleDateFormat(fstr);
        String strdate = "";

        if (date == null) {
            return strdate;
        }

        try {
            strdate = format.format(date);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return strdate;
    }

    /**
     * 获取日期的星期
     */
    public static String getWeek(Date date) {
        return getWeekFormater().format(date);
    }


    /**
     * 取得两个日期之间的所有日期集合，包含起始日期和结束日期
     *
     * @param startdate 起始日期
     * @param enddate 结束日期
     * @return 日期集合
     */
    public static List<Date> getListBetweenDay(Date startdate, Date enddate) {
        List<Date> list = new ArrayList<Date>();
        Calendar startcal = Calendar.getInstance();
        startcal.setTime(startdate);
        for (Date date = startdate;
            date.before(enddate) || date.equals(enddate); ) {
            list.add(date);
            startcal.add(Calendar.DAY_OF_MONTH, 1);
            date = startcal.getTime();
        }
        return list;
    }

    /**
     * date2比date1多的天数
     */
    public static int differentDaysByCalendar(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        int day1 = cal1.get(Calendar.DAY_OF_YEAR);
        int day2 = cal2.get(Calendar.DAY_OF_YEAR);

        int year1 = cal1.get(Calendar.YEAR);
        int year2 = cal2.get(Calendar.YEAR);
        //同一年
        if (year1 != year2) {
            int timeDistance = 0;
            for (int i = year1; i < year2; i++) {
                //闰年
                if (i % 4 == 0 && i % 100 != 0 || i % 400 == 0) {
                    timeDistance += 366;
                }
                //不是闰年
                else {
                    timeDistance += 365;
                }
            }
            return timeDistance + (day2 - day1);
        }
        //不同年
        else {
            return day2 - day1;
        }
    }


    /**
     * 获取系统的当天时间（Date类型）
     *
     * @return 当天
     */
    public static Date getCureDate() {
        Calendar cal = Calendar.getInstance();

        return cal.getTime();
    }

    /**
     * 计算两个时段的分钟数
     */
    public static int getMinutes(String startColumn, String endColumn) {
        if (startColumn != null && endColumn != null) {
            try {
                Calendar cal1 = Calendar.getInstance();
                int hour1 = Integer.parseInt(startColumn.replace(":", "").substring(0, 2));
                int minutes1 = Integer.parseInt(startColumn.replace(":", "").substring(2, 4));
                cal1.set(Calendar.HOUR_OF_DAY, hour1);
                cal1.set(Calendar.MINUTE, minutes1);

                Calendar cal2 = Calendar.getInstance();
                int hour2 = Integer.parseInt(endColumn.replace(":", "").substring(0, 2));
                int minutes2 = Integer.parseInt(endColumn.replace(":", "").substring(2, 4));
                cal2.set(Calendar.HOUR_OF_DAY, hour2);
                cal2.set(Calendar.MINUTE, minutes2);

                long minutes = (cal1.getTimeInMillis() - cal2.getTimeInMillis()) / 1000 / 60;

                return (int) minutes;

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return 0;
    }


    /**
     * 获取年初
     *
     * @param year 传入年 如：2018
     * @return 2018-01-01 yyyy-MM-dd
     */
    public static Date getYearStat(String year) {
        if (!StringUtils.isEmpty(year)) {
            String yearStat = year + "-" + "01" + "-" + "01";
            return getDate(yearStat, null);
        } else {
            return null;
        }
    }

    /**
     * 获取年末
     *
     * @param year 传入年 如：2019
     * @return 2018-12-31 yyyy-MM-dd
     */
    public static Date getYearEnd(String year) {
        if (!StringUtils.isEmpty(year)) {
            String yearStat = year + "-" + "12" + "-" + "31";
            return getDate(yearStat, null);
        } else {
            return null;
        }
    }

    /**
     * 获取指定日期的前或后推N天（字符串类型），如果传入的参数为空，则返回空
     *
     * @param date 日期
     * @param n 跳转天数 负数就是往前推，正数即往后推
     * @param formatstr 格式化参数
     * @return 返回字符型日期
     */
    public static String getMoveDay(String date, int n, String formatstr) {
        String preday = "";
        SimpleDateFormat format = new SimpleDateFormat(formatstr);
        try {
            Date ddate = format.parse(date);
            Calendar cal = Calendar.getInstance();
            cal.setTime(ddate);
            cal.add(Calendar.DAY_OF_MONTH, n);
            Date preddate = cal.getTime();
            preday = format.format(preddate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return preday;
    }

    /**
     * 获取指定日期的前或后推N天（Date类型），如果传入的参数为空，则返回空
     *
     * @param date 日期
     * @param n 跳转天数 负数就是往前推，正数即往后推
     * @return 返回日期型
     */
    public static Date getMoveDay(Date date, int n) {
        Date preddate = null;
        try {
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            cal.add(Calendar.DAY_OF_MONTH, n);
            preddate = cal.getTime();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return preddate;
    }

    /**
     * 取得两个日期之间的所有日期集合，包含起始日期和结束日期
     *
     * @param startdate 起始日期
     * @param enddate 结束日期
     * @param formatstr 格式化字符串
     * @return 日期集合
     */
    public static List<String> getListBetweenDay(String startdate, String enddate,
        String formatstr) {
        List<String> list = new ArrayList<String>();

        if ((startdate == null) || (enddate == null)) {
            return null;
        }

        String fstr = formatstr;

        if (fstr == null) {
            fstr = "yyyy-MM-dd";
        }

        SimpleDateFormat format = new SimpleDateFormat(fstr);

        try {
            Date dstartdate = format.parse(startdate);
            Date denddate = format.parse(enddate);
            Calendar startcal = Calendar.getInstance();
            startcal.setTime(dstartdate);

            for (Date date = dstartdate;
                date.before(denddate) || date.equals(denddate); ) {
                list.add(format.format(date));
                startcal.add(Calendar.DAY_OF_MONTH, 1);
                date = startcal.getTime();
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return list;
    }

    /**
     * 取得两个日期之间的所有日期集合，包含起始日期和结束日期
     *
     * @param startdate 起始日期
     * @param enddate 结束日期
     * @return 日期集合
     */
    public static List<String> getListBetweenDay(String startdate, String enddate) {
        List<String> list = new ArrayList<String>();

        if ((startdate == null) || (enddate == null)) {
            return null;
        }

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

        try {
            Date dstartdate = format.parse(startdate);

            Date denddate = format.parse(enddate);
            Calendar startcal = Calendar.getInstance();
            startcal.setTime(dstartdate);

            for (Date date = dstartdate;
                date.before(denddate) || date.equals(denddate); ) {
                list.add(format.format(date));
                startcal.add(Calendar.DAY_OF_MONTH, 1);
                date = startcal.getTime();
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return list;
    }


    /**
     * 取得两个日期之间的相隔天数
     *
     * @param startdate 起始日期
     * @param enddate 结束日期
     * @param formatstr 格式化字符串
     * @return 相隔天数
     */
    public static Integer getDayCountBetweenDay(String startdate, String enddate,
        String formatstr) {
        int dayCount = 0;
        List list = DateUtil.getListBetweenDay(startdate, enddate, formatstr);
        if (list != null && list.size() > 0) {
            dayCount = list.size() - 1;
        }
        return dayCount;
    }


    /**
     * 将字符串类型 转换为日期类型
     *
     * @param strdate 字符串类型日期
     * @param formatstr 格式化参数类型  。为空系统默认采用yyyy-MM-dd
     * @return 日期
     */
    public static Date getDate(String strdate, String formatstr) {
        String fstr = "yyyy-MM-dd";

        if (formatstr != null) {
            fstr = formatstr;
        }

        SimpleDateFormat format = new SimpleDateFormat(fstr);
        Date date = null;

        if (strdate == null) {
            return date;
        }

        try {
            date = format.parse(strdate);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return date;
    }

    /**
     * 根据yyyyy-MM-dd字符串解析成相应的日期时间
     */
    public static String formateDate(Date date) {
        return getDateFormate().format(date);
    }

    private static DateFormat getDateFormate() {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return dateFormat;
    }

    private static DateFormat getDateTimeFormater() {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return dateFormat;
    }

    private static DateFormat getWeekFormater() {
        DateFormat dateFormat = new SimpleDateFormat("EEE", Locale.CHINA);
        return dateFormat;
    }


    /**
     * 获取指定日期所在月
     *
     * @return yyyy-MM
     */
    public static String getMonthByDate(Date date) {
        return DateUtil.formateDate(date).substring(0, 7);
    }

    /**
     * 获取指定日期所在月第一天
     *
     * @return yyyy-MM
     */
    public static Date getMonthFirstByDate(Date date) {
        String substring = DateUtil.formateDate(date).substring(0, 7);
        return DateUtils.string2Date(substring + "-01", DateFormatType.SIMPLE_DATE_FORMAT_STR);
    }

    /**
     * 获取指定日期所在月
     *
     * @return MM
     */
    public static String getMonthDate(Date date) {
        return DateUtil.formateDate(date).substring(5, 7);
    }

    /**
     * 获取指定日期所在季度
     */
    public static String getQuarterByDate(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int m = cal.get(Calendar.MONTH) + 1;
        int q = 0;
        if (m <= 3) {
            q = 1;
        } else if (m <= 6) {
            q = 2;
        } else if (m <= 9) {
            q = 3;
        } else if (m <= 12) {
            q = 4;
        }
        return cal.get(Calendar.YEAR) + "-" + q;
    }

    /**
     * 获取指定日期所在年份
     */
    public static String getYearByDate(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        return String.valueOf(cal.get(Calendar.YEAR));
    }


    public static String getDateToStrFORMAT(Date date, String format) {
        if (date != null) {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            return sdf.format(date);
        }
        return null;
    }


    public static Date getDateFromString(String dateStr, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        Date date = null;
        try {
            date = sdf.parse(dateStr);
        } catch (Exception e) {
            return null;
        }

        return date;
    }


    public static String getUid() {
        String uid = DateTime.now().toString("yyyyMMddHHmmssSSS");
        return uid;
    }


    public static boolean isWeekend(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        if (calendar.get(Calendar.DAY_OF_WEEK) != 1 && calendar.get(Calendar.DAY_OF_WEEK) != 7) {
            return false;
        }
        return true;
    }

    public static boolean isWeekendOrSaturday(Date date, Integer type) {
        // type 1 只判断周六 2 只判断周末
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int i = calendar.get(Calendar.DAY_OF_WEEK);
        if (type == 1 && i == 7) {
            return true;
        } else if (type == 2 && i == 1) {
            return true;
        }
        return false;
    }


    /**
     * 1 第一季度 2 第二季度 3 第三季度 4 第四季度
     */
    public static int getSeason(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        int month = c.get(Calendar.MONTH);
        int season = getSeason(month + 1);
        return season;
    }

    public static int getNewSeason(int month) {
        int season = 0;
        switch (month) {
            case 4:
            case 5:
            case 6:
            case 7:
            case 8:
            case 9:
                season = 2;
                break;
            case 10:
            case 11:
            case 12:
            case 1:
            case 2:
            case 3:
                season = 4;
                break;
            default:
                break;
        }
        return season;
    }

    /**
     * 根据月份区分季节，默认3、4、5为春季，6、7、8为夏季，9、10、11为秋季，12、1、2为冬季
     * @param month
     * @return
     */
    public static int getSeason(int month) {
        int season = 0;
        switch (month) {
            case 3:
            case 4:
            case 5:
                season = 1;
                break;
            case 6:
            case 7:
            case 8:
                season = 2;
                break;
            case 9:
            case 10:
            case 11:
                season = 3;
                break;
            case 12:
            case 1:
            case 2:
                season = 4;
                break;
            default:
                break;
        }
        return season;
    }

    public static Date getFormatDate(Date date) {
        return getDateFromString(getDateToStrFORMAT(date, DATE_FORMAT2), DATE_FORMAT2);
    }


    /**
     * 获取上个月最后一天
     */
    public static Date lastDayOfLastMonth(Date date) {
        Calendar endTime = Calendar.getInstance();
        endTime.setTime(date);
        endTime.add(Calendar.MONTH, -1);
        endTime.set(Calendar.DATE, endTime.getActualMaximum(Calendar.DAY_OF_MONTH));
        return endTime.getTime();
    }


    /**
     * 返回月份list
     *
     * @return 月份集合 示例：01 02 03... ...
     */
    public static List<String> getMonthList() {
        List<String> monthList = new ArrayList<String>();

        for (int j = 1; j <= 12; j++) {
            if (j < 10) {
                monthList.add("0" + j);
            } else {
                monthList.add("" + j);
            }
        }

        return monthList;
    }

    /**
     * 获取系统的当天时间（字符串类型）
     *
     * @param formatstr 格式化字符串参数
     * @return 当天
     */
    public static String getCureDateStr(String formatstr) {
        String fstr = formatstr;

        if (fstr == null) {
            fstr = "yyyy-MM-dd";
        }

        SimpleDateFormat format = new SimpleDateFormat(fstr);
        Date date = DateUtil.getCureDate();
        String dstr = format.format(date);

        return dstr;
    }

    /**
     * 从日期中取出 hhmm
     */
    public static String getStrHourFromDate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("HHmmss");
        String strDateFormat = sdf.format(date);
        return strDateFormat.substring(0, 4);
    }

    /**
     * 计算传入的时间是第几个时段点
     *
     * @param time 时刻 HHmm
     * @return 时刻点数（1~96）
     */
    public static int getTimePoint(String time, Integer pointNum) {
        int point = 0;
        if (time == null || time.length() != 4) {
            return point;
        }
        //当前小时
        String strhour = time.substring(0, 2);
        //当前分钟
        String strminute = time.substring(2, 4);

        int hour = Integer.parseInt(strhour);
        int minute = Integer.parseInt(strminute);
        if (pointNum == 288) {
            if (Constants.LOAD_CURVE_START_WITH_ZERO) {
                point = hour * 12 + minute / 5 + 1;
            } else {
                point = hour * 12 + minute / 5;
            }
        } else {
            if (Constants.LOAD_CURVE_START_WITH_ZERO) {
                point = hour * 4 + minute / 15 + 1;
            } else {
                point = hour * 4 + minute / 15;
            }
        }

        return point;
    }

    public static Date getFirstDayDateOfMonth(final Date date) {

        final Calendar cal = Calendar.getInstance();

        cal.setTime(date);

        final int last = cal.getActualMinimum(Calendar.DAY_OF_MONTH);

        cal.set(Calendar.DAY_OF_MONTH, last);

        return cal.getTime();

    }


    /**
     *      * 根据日期 找到对应日期的 星期     
     */
    public static String getDayOfWeekByDate(Date date) {
        String dayOfweek = "-1";
        try {
            SimpleDateFormat formatter = new SimpleDateFormat("E");
            String str = formatter.format(date);
            dayOfweek = str;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return dayOfweek;
    }


    /**
     * 获取最近的五分钟整点
     */
    public static Date getShortFcTime(Date date) {
        if (date == null) {
            date = new Date();
        }
        Calendar time = Calendar.getInstance();
        time.setTime(date);
        int minute = time.get(Calendar.MINUTE);
        time.set(Calendar.MINUTE, (minute / 5) * 5);
        time.set(Calendar.SECOND, 0);
        time.set(Calendar.MILLISECOND, 0);
        return time.getTime();

    }


    /**
     * 获取最近的十五分钟整点
     */
    public static Date getFifteenFcTime(Date date) {
        if (date == null) {
            date = new Date();
        }
        Calendar time = Calendar.getInstance();
        time.setTime(date);
        int minute = time.get(Calendar.MINUTE);
        time.set(Calendar.MINUTE, (minute / 15) * 15);
        time.set(Calendar.SECOND, 0);
        time.set(Calendar.MILLISECOND, 0);
        return time.getTime();

    }


    /**
     * 根据第多少个点 获取时分 timeType 1 五分钟间隔  2 十五分钟间隔 默认第一个点从 00:00 开始
     */
    public static String getHHmmTime(String startTime, Integer timeType, Integer pointNum) {
        if (StringUtil.isBlank(startTime)) {
            startTime = "0000";
        }
        String nowDate = getStrDate(new Date(), "yyyyMMddHHmm");
        nowDate = nowDate.substring(0, 8) + startTime;
        Long nowTime = getDate(nowDate, "yyyyMMddHHmm").getTime();
        int fiveMinuteTime = 5 * 60 * 1000;
        nowTime += timeType == 1 ? (pointNum - 1) * fiveMinuteTime : (pointNum - 1) * fiveMinuteTime * 3;
        String dateStr = getDateToStrFORMAT(new Date(nowTime), "HH:mm");
        //负荷曲线记录2400时刻，返回时间24:00
        if (!Constants.LOAD_CURVE_START_WITH_ZERO && StringUtils.equals("00:00", dateStr)) {
            return "24:00";
        }
        return dateStr;
    }

    public static Date getFirstDay(int year, int month) {
        // 获取Calendar类的实例
        Calendar c = Calendar.getInstance();
        // 设置年份
        c.set(Calendar.YEAR, year);
        // 设置月份，因为月份从0开始，所以用month - 1
        c.set(Calendar.MONTH, month - 1);
        // 设置日期
        c.set(Calendar.DAY_OF_MONTH, 1);
        return c.getTime();
    }

    public static Date getLastDay(int year, int month) {
        // 获取Calendar类的实例
        Calendar c = Calendar.getInstance();
        // 设置年份
        c.set(Calendar.YEAR, year);
        // 设置月份，因为月份从0开始，所以用month - 1
        c.set(Calendar.MONTH, month - 1);
        // 获取当前时间下，该月的最大日期的数字
        int lastDay = c.getActualMaximum(Calendar.DAY_OF_MONTH);
        // 将获取的最大日期数设置为Calendar实例的日期数
        c.set(Calendar.DAY_OF_MONTH, lastDay);
        return c.getTime();
    }

    public static String getDateToStr(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT2);
        return sdf.format(date);
    }


    public static String getNowDate() {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT1);
        return sdf.format(new Date());
    }


    /**
     * @param date 日期
     * @param day 天数
     * @return 时间相加天数或相减天数
     */
    public static Date getSubDate(Date date, Integer day) {
        if (date != null && day != null) {
            Calendar c = Calendar.getInstance();
            c.setTime(date);
            c.add(Calendar.DATE, day);
            return c.getTime();
        }
        return null;
    }

    public static Date getSubMonth(Date date, Integer day) {
        if (date != null && day != null) {
            Calendar c = Calendar.getInstance();
            c.setTime(date);
            c.add(Calendar.MONTH, day);
            return c.getTime();
        }
        return null;
    }

    /**
     * 清空小时分钟秒
     */
    public static Date clearHHmmss(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 返回d1与d2之间的天数： D1 - D2
     *
     * @param d1 被减时间
     * @param d2 减时间
     * @return 两个时间相差的分钟数(忽略小时分钟秒)
     */
    public static int getDaysDifferMinutes(Date d1, Date d2) {
        double millisPerMinutes = org.apache.commons.lang3.time.DateUtils.MILLIS_PER_MINUTE;
        double differ = (d1.getTime() - d2.getTime()) / millisPerMinutes;
        //logger.info("the diff of double is:" + differ);
        return (int) (differ);
    }

    /**
     * 返回d1与d2之间的天数： D1 - D2
     *
     * @return 如果按时间拍列的话，d1在d2的前面，返回负数；否则返回两者之间相差的天数
     */
    public static int getDaysDiffer2(Date d1, Date d2) {
        Calendar d1Calendar = Calendar.getInstance();
        d1Calendar.setTime(d1);
        d1Calendar.set(Calendar.HOUR_OF_DAY, 0);
        d1Calendar.set(Calendar.MINUTE, 0);
        d1Calendar.set(Calendar.SECOND, 0);
        d1Calendar.set(Calendar.MILLISECOND, 0);

        Calendar d2Calendar = Calendar.getInstance();
        d2Calendar.setTime(d2);
        d2Calendar.set(Calendar.HOUR_OF_DAY, 0);
        d2Calendar.set(Calendar.MINUTE, 0);
        d2Calendar.set(Calendar.SECOND, 0);
        d2Calendar.set(Calendar.MILLISECOND, 0);

        if (d1Calendar.after(d2Calendar)) {//if equal, return false
            int dayNum = 0;
            while (true) {
                d1Calendar.add(Calendar.DAY_OF_YEAR, -1);
                if (d1Calendar.before(d2Calendar))    //if equal, return false
                {
                    break;
                }
                dayNum++;
            }
            return dayNum;
        } else if (d1Calendar.equals(d2Calendar)) {
            return 0;
        } else {
            return (-1 * getDaysDiffer2(d2, d1));
        }
    }

    /**
     * 两日期之间相差天数
     */
    public static int getIntervalDayNum(Date begin_date, Date end_date) {
        int day = 0;
        try {
            Calendar begin = Calendar.getInstance();
            begin.setTime(begin_date);
            begin.set(Calendar.HOUR_OF_DAY, 0);
            begin.set(Calendar.MINUTE, 0);
            begin.set(Calendar.SECOND, 0);
            begin.set(Calendar.MILLISECOND, 0);

            Calendar end = Calendar.getInstance();
            end.setTime(end_date);
            end.set(Calendar.HOUR_OF_DAY, 0);
            end.set(Calendar.MINUTE, 0);
            end.set(Calendar.SECOND, 0);
            end.set(Calendar.MILLISECOND, 0);
            day = (int) ((end.getTimeInMillis() - begin.getTimeInMillis()) / (24 * 60 * 60 * 1000));
        } catch (Exception e) {
            System.out.println("getIntervalDayNum failed! " + e.getMessage());
            return -1;
        }
        return day;
    }


    /**
     * @param minDate 开始日期 yyyy-MM-dd HH:mm:ss
     * @param maxDate 终止日期yyyy-MM-dd HH:mm:ss
     * @return List<String>
     * @fun 获得两个日期之间的所有月份
     */
    public static List<String> getMonthBetween(String minDate, String maxDate) throws Exception {
        // 转换格式yyyy-MM
        minDate = minDate.substring(0, 7);
        maxDate = maxDate.substring(0, 7);

        ArrayList<String> result = new ArrayList<String>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");// 格式化为年月

        Calendar min = Calendar.getInstance();
        Calendar max = Calendar.getInstance();

        min.setTime(sdf.parse(minDate));
        min.set(min.get(Calendar.YEAR), min.get(Calendar.MONTH), 1);

        max.setTime(sdf.parse(maxDate));
        max.set(max.get(Calendar.YEAR), max.get(Calendar.MONTH), 2);

        Calendar curr = min;
        while (curr.before(max)) {
            result.add(sdf.format(curr.getTime()));
            curr.add(Calendar.MONTH, 1);
        }
        return result;
    }


    /**
     * 返回两日期之间：milliseconds
     */
    public static Long getIntervalMillisecondNum(Date begin_date, Date end_date) {
        long minute = 0;
        try {
            minute = end_date.getTime() - begin_date.getTime();
        } catch (Exception e) {
            System.out.println("getDateToMinutes failed! " + e.getMessage());
            return (long) -1;
        }
        //输出结果 相差的milliseconds
        return minute;
    }

    /**
     * 判断日期是否为同一天
     */
    public static boolean isSameDay(Date day1, Date day2) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String ds1 = sdf.format(day1);
        String ds2 = sdf.format(day2);
        if (ds1.equals(ds2)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 将字符串转换成日期
     *
     * @param dateStr:yyyy-MM-dd HH:mm:ss
     */
    public static Date getStrToDatePARSE(String dateStr) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = null;
        try {
            date = format.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }


    /**
     * 根据日期获取 星期 （2019-05-06 ——> 星期一）
     */
    public static String dateToWeek(Date date) {
        String[] weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        //一周的第几天
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0) {
            w = 0;
        }
        return weekDays[w];
    }

    public static String getHour(Date date) {
        String dateStr = DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_TIME_FORMAT_STR);
        return dateStr.substring(8, 10);
    }

    public static String getMinute(Date date) {
        String dateStr = DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_TIME_FORMAT_STR);
        return dateStr.substring(10, 12);
    }

    /**
     * 计算月份天数
     *
     * @param year 年份
     * @param month 月度
     * @return 月总天数
     */
    public static int countDaysOfMonth(String year, String month) {
        int days = 0;

        try {
            if ((year != null) && (month != null)) {
                int yearI = Integer.parseInt(year);
                int monthI = Integer.parseInt(month);
                days = countDaysOfMonth(yearI, monthI);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return days;
    }


    /**
     * 计算月份天数
     *
     * @param year 年份
     * @param month 月度
     * @return 月总天数
     */
    public static int countDaysOfMonth(int year, int month) {
        int days = 0;

        try {
            month = month - 1;

            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.YEAR, year);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.MONTH, month);
            days = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return days;
    }


    /**
     * 判断日期是工作日还是休息日
     *
     * @param time 日期
     */
    public static Boolean isWorkingDay(long time) {
        LocalDateTime dateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(time), ZoneOffset.of("+8"));
        String formatTime = dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        DayOfWeek week = dateTime.getDayOfWeek();
        if (week == DayOfWeek.SATURDAY || week == DayOfWeek.SUNDAY) {
            return false;
        }
        return true;
    }


    /**
     * 获取举例最近的15分钟整点，返回结果格式HH:mm
     */
    public static String getNearestQuarterHourString() {
        LocalDateTime currentTime = LocalDateTime.now();
        LocalDateTime nearestQuarterHour = currentTime.truncatedTo(ChronoUnit.HOURS)
            .plusMinutes((currentTime.getMinute() / 15) * 15);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        String nearestQuarterHourString = nearestQuarterHour.format(formatter);
        if (!Constants.LOAD_CURVE_START_WITH_ZERO && "00:00".equals(nearestQuarterHourString)) {
            return "24:00";
        }
        return nearestQuarterHourString;
    }

    /**
     * 获取指定时间间隔的所有事件列表
     *
     * @param intervalType 1 秒 2 分钟   类型返回统一带秒：00:00:00
     * @param interval 秒间隔 or 分钟间隔
     * @param startWithZero true 从00开始；不从00开始
     */
    public static List<String> getAllTimeListByInterval(Integer intervalType, Integer interval,
        Boolean startWithZero) {
        List<String> totalTimeList = new ArrayList<>();
        Date date;
        LocalDate today = LocalDate.now();
        LocalDateTime todayStart = today.atStartOfDay();
        LocalDateTime todayEnd = today.atTime(23, 59, 59);
        date = Date.from(todayStart.atZone(ZoneId.systemDefault()).toInstant());
        Date dateFlag = Date.from(todayEnd.atZone(ZoneId.systemDefault()).toInstant());
        Calendar calendar = Calendar.getInstance();
        if (startWithZero) {
            if (Constants.SECOND.equals(intervalType)) {
                do {
                    totalTimeList.add(DateUtils.date2String(date, DateFormatType.DATE_FORMAT_STR).substring(11));
                    calendar.setTime(date);
                    calendar.add(Calendar.SECOND, interval);
                    date = calendar.getTime();
                } while (!date.after(dateFlag));

            } else {
                do {
                    totalTimeList.add(DateUtils.date2String(date, DateFormatType.DATE_FORMAT_STR).substring(11));
                    calendar.setTime(date);
                    calendar.add(Calendar.MINUTE, interval);
                    date = calendar.getTime();
                } while (!date.after(dateFlag));
            }
        } else {
            if (Constants.SECOND.equals(intervalType)) {
                Calendar start = Calendar.getInstance();
                start.setTime(date);
                start.add(Calendar.SECOND, interval);
                Date startDate = start.getTime();
                do {
                    totalTimeList
                        .add(DateUtils.date2String(startDate, DateFormatType.DATE_FORMAT_STR).substring(11));
                    calendar.setTime(startDate);
                    calendar.add(Calendar.SECOND, interval);
                    startDate = calendar.getTime();
                } while (!startDate.after(dateFlag));
                totalTimeList.add("24:00:00");
            } else {
                Calendar start = Calendar.getInstance();
                start.setTime(date);
                start.add(Calendar.MINUTE, interval);
                Date startDate = start.getTime();
                do {
                    totalTimeList
                        .add(DateUtils.date2String(startDate, DateFormatType.DATE_FORMAT_STR).substring(11));
                    calendar.setTime(startDate);
                    calendar.add(Calendar.MINUTE, interval);
                    startDate = calendar.getTime();
                } while (!startDate.after(dateFlag));
                totalTimeList.add("24:00:00");
            }

        }

        return totalTimeList;
    }

    /**
     * 判断时间是否在一个时间段内；只校验时间 不校验日期
     * @param timestamp 目标时间日期；
     * @param startTime 格式 HH:mm
     * @param endTime 格式 HH:mm
     * @return
     */
    public static boolean isWithinTimeRange(Timestamp timestamp, String startTime, String endTime) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(timestamp);

        DateFormat format = new SimpleDateFormat("HH:mm");
        try {
            Calendar calendarA = Calendar.getInstance();
            calendarA.setTime(format.parse(startTime));

            Calendar calendarB = Calendar.getInstance();
            calendarB.setTime(format.parse(endTime));

            // 将日期部分设为相同，以便只比较时分秒
            calendar.set(Calendar.YEAR, calendarA.get(Calendar.YEAR));
            calendar.set(Calendar.MONTH, calendarA.get(Calendar.MONTH));
            calendar.set(Calendar.DAY_OF_MONTH, calendarA.get(Calendar.DAY_OF_MONTH));

            // 判断时间是否在指定区间内
            if (calendar.after(calendarA) && calendar.before(calendarB)) {
                return true;
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return false;
    }

    /**
     * 判断两个时间间隔是否小于hour
     * @return
     */
    public static boolean isInTimeFrame(Date dateSrc, Date dateTar, int hour) {
        // 创建两个Calendar对象，并将其时间设置为对应的Date对象的时间
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(dateSrc);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(dateTar);

        // 计算两个时间之间的小时差异
        long hours = Math.abs(cal2.get(Calendar.HOUR_OF_DAY) - cal1.get(Calendar.HOUR_OF_DAY));

        // 判断差异是否大于等于hour个小时
        if (hours >= hour) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 将开始时间和结束时间按固定的天切割成多个开始时间和结束时间
     */
    public static List<Date[]> splitDateRange(Date startDate, Date endDate, int days) {
        List<Date[]> dateRanges = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();

        Date currentStartDate = startDate;
        while (currentStartDate.before(endDate)) {
            calendar.setTime(currentStartDate);
            calendar.add(Calendar.DAY_OF_MONTH, days - 1);
            Date currentEndDate = calendar.getTime();

            if (currentEndDate.after(endDate)) {
                currentEndDate = endDate;
            }

            dateRanges.add(new Date[]{currentStartDate, currentEndDate});

            // 设置下一个开始时间
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            currentStartDate = calendar.getTime();
        }

        if (currentStartDate.equals(endDate)) {
            dateRanges.add(new Date[]{currentStartDate, currentStartDate});
        }
        return dateRanges;
    }

    public static int getDayOfWeek(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        //一周的第几天
        return cal.get(Calendar.DAY_OF_WEEK);
    }

}

