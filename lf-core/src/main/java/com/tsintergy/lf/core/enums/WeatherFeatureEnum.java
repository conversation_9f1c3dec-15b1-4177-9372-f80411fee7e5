/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  wang<PERSON>
 * Date:  2019/8/27 9:24
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.core.enums;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/8/27
 * @since 1.0.0
 */
public enum WeatherFeatureEnum {

    Max(0, "日最高温"),

    Avg(1, "日平均温"),

    <PERSON>(2, "日最低温"),

    <PERSON><PERSON><PERSON><PERSON>(3, "日平均相对湿度"),

    <PERSON><PERSON><PERSON>(4, "日最大风速"),

    AvgWind(5, "日平均风速"),

    lowWind(6, "日最低风速"),

    <PERSON>(7, "日降雨量");

    private Integer type;

    private String name;


    WeatherFeatureEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String findNameByType(Integer type) {
        WeatherFeatureEnum[] values = WeatherFeatureEnum.values();
        for (WeatherFeatureEnum weatherFeatureEnum : values) {
            if (weatherFeatureEnum.getType() == type) {
                return weatherFeatureEnum.getName();
            }
        }
        return null;
    }


}