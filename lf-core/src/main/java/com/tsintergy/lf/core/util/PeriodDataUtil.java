/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司
 * Author:  wangchen
 * Date:  2018/11/20 22:29
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.core.util;


import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.BasePeriod24VO;
import com.tsieframework.core.base.vo.BasePeriod96VO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2018/11/20 
 * @since 1.0.0
 */
public class PeriodDataUtil {

    private static final Logger logger = LoggerFactory.getLogger(PeriodDataUtil.class);
    /**
     * 获取当前时间24点的整点值（例如 2018-11-20 15:00:00）
     * @return
     */
    public static String getCurrentHourOf24() {
        String date = com.tsieframework.core.base.format.datetime.DateUtils.date2String(new Date(), DateFormatType.DATE_FORMAT_STR);
        String tempDate = date.substring(0,13);
        return tempDate + ":00:00";
    }

    public static String getHourOf24(Date date) {
        String time = com.tsieframework.core.base.format.datetime.DateUtils.date2String(date,DateFormatType.DATE_FORMAT_STR);
        String tempDate = time.substring(0,13);
        return tempDate + ":00:00";
    }

    /**
     * 给24点对象赋值
     * @param date
     * @param basePeriod24VO
     * @param value
     * @throws NoSuchMethodException
     * @throws InvocationTargetException
     * @throws IllegalAccessException
     */
    public static void setDataOf24(Date date, BasePeriod24VO basePeriod24VO, BigDecimal value) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        String time = DateUtils.date2String(date,DateFormatType.DATE_FORMAT_STR);
        String hour = time.substring(11,13);
        String methodName = "setT" + hour + "00";
        Method method = BasePeriod24VO.class.getMethod(methodName,BigDecimal.class);
        method.invoke(basePeriod24VO,value);
    }

    /**
     * 将24点的数补全96 (整点需要最少有两个点有值）
     * @param basePeriod96VO
     */
    public static void do24To96VO(BasePeriod96VO basePeriod96VO) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Field[] fields = BasePeriod24VO.class.getDeclaredFields();
        List<String> fieldNameList = new ArrayList<String>();
        for (Field field : fields) {
            String methodName = "get" + field.getName().substring(0,1).toUpperCase() + field.getName().substring(1);
            Method method = BasePeriod96VO.class.getMethod(methodName,null);
            Object value = method.invoke(basePeriod96VO,null);
            if (value != null) {
                fieldNameList.add(field.getName());
            }
        }
        if (fieldNameList.size() < 2) {
            logger.error("24点转96点，对象中的数值小于两个，无法实现递增");
            return;
        }
        //补全第24点值，根据22点和23点的值估算
        complement24point(basePeriod96VO,fieldNameList);
        String startFieldName = fieldNameList.get(0);
        String endFieldName = fieldNameList.get(1);
        for(int i = 1; i < fieldNameList.size(); i++) {
            complementBetweenValue(basePeriod96VO,startFieldName,endFieldName);
            if (i + 1 >= fieldNameList.size()) {
                return;
            }
            startFieldName = endFieldName;
            endFieldName = fieldNameList.get(1 + i);
        }
    }

    private static void complement24point(BasePeriod96VO basePeriod96VO, List<String> fieldNameList) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        Object value24 = getFieldValue(basePeriod96VO, "t2400");
        if (value24 != null) {
            return;
        }

        Object valueOf22 = getFieldValue(basePeriod96VO, "t2200");
        Object valueOf23 = getFieldValue(basePeriod96VO, "t2300");
        if (valueOf22 == null || valueOf23 == null) {
            return;
        }
        BigDecimal valueOf24 = ((BigDecimal) valueOf23).subtract((BigDecimal) valueOf22).add((BigDecimal) valueOf23);
        if (valueOf24.compareTo(new BigDecimal(0)) < 0) {
            valueOf24 = new BigDecimal(0);
        }
        setFieldValue(basePeriod96VO, "t2400", valueOf24);
        fieldNameList.add("t2400");
    }

    private static void complementBetweenValue(BasePeriod96VO basePeriod96VO,String startFieldName,String endFieldName) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Integer count = (Integer.parseInt(endFieldName.substring(1,3)) - Integer.parseInt(startFieldName.substring(1,3))) * 4;
        //获取startField值
        BigDecimal startValue = (BigDecimal) getFieldValue(basePeriod96VO,startFieldName);
        //获取endField值
        BigDecimal endValue = (BigDecimal) getFieldValue(basePeriod96VO,endFieldName);
        //获取平均增长值
        BigDecimal avgIncrease = (endValue.subtract(startValue)).divide(new BigDecimal(count),4,BigDecimal.ROUND_UP);
        //递增赋值
        for(int i = 1; i < count; i ++) {
            String fieldName = getIncreaseFieldName(startFieldName);
            Object value = getFieldValue(basePeriod96VO,fieldName);
            if(value != null) {
                continue;
            }
            setFieldValue(basePeriod96VO,fieldName,startValue.add(avgIncrease.multiply(new BigDecimal(Integer.valueOf(i)))));
            startFieldName = fieldName;
        }
    }





    /**
     * 传入24点的List，每两个点之间插入3个点，单天填充成93点List返回；2天填充96+93   src24DataList中间缺点请补null占位；
     */
    public static List<BigDecimal> complementBetweenValue(List<BigDecimal> src24DataList)
            throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        List<BigDecimal> resultList = new ArrayList<>();
        for (int i = 0; i < src24DataList.size() - 1; i++) {
            int count = 4;
            BigDecimal startValue = src24DataList.get(i);
            if (startValue == null) {
                continue;
            }
            BigDecimal next = src24DataList.get(i + 1);
            if (next == null) {
                int x = 2;
                for (int b = i + 2; b < src24DataList.size(); b++) {
                    BigDecimal bigDecimal = src24DataList.get(b);
                    //多填充的数据
                    if (bigDecimal == null) {
                        x++;
                    }
                    if (bigDecimal != null) {
                        next = bigDecimal;
                        count = count * x;
                        break;
                    }
                }
            }
            assert next != null;
            BigDecimal avgIncrease = (next.subtract(src24DataList.get(i)))
                    .divide(new BigDecimal(count), 4, BigDecimal.ROUND_UP);
            resultList.add(src24DataList.get(i));
            for (int a = 1; a < count; a++) {
                startValue = startValue.add(avgIncrease);
                resultList.add(startValue);
            }
        }
        resultList.add(src24DataList.get(src24DataList.size() - 1));
        return resultList;
    }


    public static void setFieldValue(BasePeriod24VO target, String fieldName, BigDecimal value) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        if (value != null) {
            value = value.setScale(2, RoundingMode.HALF_UP);
            String methodName = "set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
            Method method = target.getClass().getMethod(methodName, BigDecimal.class);
            method.invoke(target, value);
        }
    }

    public static Object getFieldValue(BasePeriod24VO target,String fieldName) throws InvocationTargetException, IllegalAccessException, NoSuchMethodException {
        String methodName = "get" + fieldName.substring(0,1).toUpperCase() + fieldName.substring(1);
        Method method = target.getClass().getMethod(methodName,null);
        return method.invoke(target,null);
    }

    /**
     * 96点  获取下一个属性名
     * @param startFieldName
     * @return
     */
    private static String getIncreaseFieldName(String startFieldName) {
        Integer hour = Integer.parseInt(startFieldName.substring(1,3));
        Integer minute = Integer.parseInt(startFieldName.substring(3));
        Integer nextHour = hour;
        Integer nextMinute = minute + 15;
        if (nextMinute == 60) {
            nextHour = nextHour + 1;
            if (nextHour == 24) {
                nextHour = 0;
            }
            nextMinute = 0;
        }
        String targetHour = "" + nextHour;
        String targetMinute = "" + nextMinute;
        if (nextHour < 10) {
            targetHour = "0" + nextHour;
        }
        if (nextMinute == 0) {
            targetMinute = "00";
        }
        return "t" + targetHour +targetMinute;
    }


    /**
     * 将List中的 数据依次插入对象
     * @param target
     * @param dataList
     * @throws NoSuchMethodException
     * @throws IllegalAccessException
     * @throws InvocationTargetException
     */
    public static void setAllFiled(BasePeriod24VO target, List<BigDecimal> dataList) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        List<Field> fields = Arrays.asList(BasePeriod24VO.class.getDeclaredFields());
        List<Field> allFields = new ArrayList<>();
        allFields.addAll(fields);
        if (target instanceof BasePeriod96VO) {
            allFields.addAll(Arrays.asList(BasePeriod96VO.class.getDeclaredFields()));
        }
        logger.info("datalist大小为=======" + dataList.size());
        for (int i = 0 ; i < dataList.size(); i++ ) {
            logger.info("第" + i + "个数的属性为=====" + "allFields.get(i).getName()" + "========值为" + dataList.get(i));
            setFieldValue(target,allFields.get(i).getName(),dataList.get(i));
        }
    }

    /**
     * 将288点过滤成96个点
     * @param hisDataList
     * @param startZero
     * @return
     */
    public static List<BigDecimal> data288to96(List<BigDecimal> hisDataList, boolean startZero) {
        if (hisDataList == null || hisDataList.size() <1) {
            return null;
        }
        List<BigDecimal> list = new ArrayList<>();
        if (startZero) {
            for(int i = 0; i < hisDataList.size(); i++) {
                if (i % 3 == 0) {
                    list.add(hisDataList.get(i));
                }
            }
        } else {
            for(int i = 0; i < hisDataList.size(); i++) {
                if ((i+1) % 3 == 0) {
                    list.add(hisDataList.get(i));
                }
            }
        }
        return list;
    }

    /**
     * 将96点过滤成288个点
     */
    public static List<BigDecimal> data96to288(List<BigDecimal> bigDecimalList) {
        //转换成288点数据
        List<BigDecimal> resultList = new ArrayList<>();
        for (int i = 0; i < bigDecimalList.size(); i++) {
            resultList.add(bigDecimalList.get(i));
            BigDecimal incremental = null;
            try {
                if (i == bigDecimalList.size() - 1) {
                    incremental = bigDecimalList.get(i).subtract(bigDecimalList.get(i - 1)).divide(new BigDecimal(3), 4, BigDecimal.ROUND_HALF_UP);
                } else {
                    incremental = bigDecimalList.get(i + 1).subtract(bigDecimalList.get(i)).divide(new BigDecimal(3), 4, BigDecimal.ROUND_HALF_UP);
                }
            }
            //极端情况下容错处理；填充0
            catch (Exception e) {
                resultList.add(BigDecimal.ZERO);
                resultList.add(BigDecimal.ZERO);
                continue;
            }
            //补充两个点
            resultList.add(bigDecimalList.get(i).add(incremental));
            resultList.add(bigDecimalList.get(i).add(incremental).add(incremental));
        }
        return resultList;
    }


    public static void replaceValues(BasePeriod24VO target, BasePeriod24VO replace) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        List<Field> allFields = new ArrayList<>();
        List<Field> fields = Arrays.asList(BasePeriod24VO.class.getDeclaredFields());
        allFields.addAll(fields);
        if (target instanceof BasePeriod96VO) {
            allFields.addAll(Arrays.asList(BasePeriod96VO.class.getDeclaredFields()));
        }
        for (Field field : allFields) {
            BigDecimal value = (BigDecimal) getFieldValue(replace,field.getName());
            if (value != null) {
                setFieldValue(target,field.getName(), value);
            }
        }
    }


    public static final String[] column96 = { "t0000" ,
        "t0015" ,
        "t0030" ,
        "t0045" ,
        "t0100" ,
        "t0115" ,
        "t0130" ,
        "t0145" ,
        "t0200" ,
        "t0215" ,
        "t0230" ,
        "t0245" ,
        "t0300" ,
        "t0315" ,
        "t0330" ,
        "t0345" ,
        "t0400" ,
        "t0415" ,
        "t0430" ,
        "t0445" ,
        "t0500" ,
        "t0515" ,
        "t0530" ,
        "t0545" ,
        "t0600" ,
        "t0615" ,
        "t0630" ,
        "t0645" ,
        "t0700" ,
        "t0715" ,
        "t0730" ,
        "t0745" ,
        "t0800" ,
        "t0815" ,
        "t0830" ,
        "t0845" ,
        "t0900" ,
        "t0915" ,
        "t0930" ,
        "t0945" ,
        "t1000" ,
        "t1015" ,
        "t1030" ,
        "t1045" ,
        "t1100" ,
        "t1115" ,
        "t1130" ,
        "t1145" ,
        "t1200" ,
        "t1215" ,
        "t1230" ,
        "t1245" ,
        "t1300" ,
        "t1315" ,
        "t1330" ,
        "t1345" ,
        "t1400" ,
        "t1415" ,
        "t1430" ,
        "t1445" ,
        "t1500" ,
        "t1515" ,
        "t1530" ,
        "t1545" ,
        "t1600" ,
        "t1615" ,
        "t1630" ,
        "t1645" ,
        "t1700" ,
        "t1715" ,
        "t1730" ,
        "t1745" ,
        "t1800" ,
        "t1815" ,
        "t1830" ,
        "t1845" ,
        "t1900" ,
        "t1915" ,
        "t1930" ,
        "t1945" ,
        "t2000" ,
        "t2015" ,
        "t2030" ,
        "t2045" ,
        "t2100" ,
        "t2115" ,
        "t2130" ,
        "t2145" ,
        "t2200" ,
        "t2215" ,
        "t2230" ,
        "t2245" ,
        "t2300" ,
        "t2315" ,
        "t2330" ,
        "t2345" };
    public static final String[] column24 = {
        "t0000" ,
        "t0100" ,
        "t0200" ,
        "t0300" ,
        "t0400" ,
        "t0500" ,
        "t0600" ,
        "t0700" ,
        "t0800" ,
        "t0900" ,
        "t1000" ,
        "t1100" ,
        "t1200" ,
        "t1300" ,
        "t1400" ,
        "t1500" ,
            "t1600",
            "t1700",
            "t1800",
            "t1900",
            "t2000",
            "t2100",
            "t2200",
            "t2300"
    };

    public static <T> List<T> convertList(List<?> entities, Class<T> clazz) {
        List<T> dtos = new ArrayList<>(entities.size());
        for (Object entity : entities) {
            if (entity != null) {
                try {
                    T dto = clazz.getDeclaredConstructor().newInstance();
                    ColumnUtil.copyPropertiesIgnoreNull(entity, dto);
                    dtos.add(dto);
                } catch (Exception e) {
                    throw TsieExceptionUtils.newBusinessException("Failed to create an instance of " + clazz.getName(), e);
                }
            }
        }
        return dtos;
    }

}