package com.tsintergy.lf.core.enums;

/**
 * 气象相似日筛选条件
 *
 * @Auther: wangh
 * @Date: 2019/7/4 16:36
 * @Description:
 */
public enum WeatherScreenEnum {

    // 日最高温度
    HIGHEST_TEMPERATURE(1, "highestTemperature"),
    //日平均温度
    AVE_TEMPERATURE(2, "aveTemperature"),
    //日最低温度
    LOWEST_TEMPERATURE(3, "lowestTemperature"),
    //日降雨量
    RAINFALL(4, "rainfall"),
    //近三日降雨量
    RECENTLY_RAINFALL(5, "recentlyRainfall"),
    //日平均湿度
    AVE_HUMIDITY(6, "aveHumidity"),
    //日最大风速
    MAX_WINDS(7, "maxWinds"),
    //日平均风速
    AVE_WINDS(8, "aveWinds");

    private Integer type;

    private String sqlName;

    WeatherScreenEnum(Integer type, String sqlName) {
        this.type = type;
        this.sqlName = sqlName;
    }

    public static String getSqlName(Integer type) {
        String sqlName;
        WeatherScreenEnum[] values = WeatherScreenEnum.values();
        for (WeatherScreenEnum screenEnum : values) {
            if (screenEnum.type.equals(type)) {
                return screenEnum.sqlName;
            }
        }
        return WeatherScreenEnum.HIGHEST_TEMPERATURE.sqlName;
    }
}
