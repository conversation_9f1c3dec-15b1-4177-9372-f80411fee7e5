/**
 * Copyright(C),2015‐2021,北京清能互联科技有限公司
 */
package com.tsintergy.lf.core.constants;

import com.tsintergy.lf.core.LfConfigConstants;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.ArrayBlockingQueue;

/**
 * Description: 缓存相关常量 <br>
 *
 * @Author: <EMAIL>
 * @Date: 2021/12/8 15:41
 * @Version: 1.0.0
 */
public class CacheConstants {

    /**
     * 误差分析中使用的队列
     */
    public static final Queue<Map<String, Object>> queue = new ArrayBlockingQueue<Map<String, Object>>(1024);


    /*--------------------------------------map中的key-----------------------------------------*/

    /**
     * 预测起始时间
     */
    public static final String FORECAST_DATE = "forecast_date";

    /**
     * 预测天数
     */
    public static final String FORECAST_COUNT = "forecast_count";

    /**
     * 从零点开始
     */
    public final static String START_WITH_ZERO = "startWithZero";



    /*--------------------------------------session中的key-----------------------------------------*/

    /**
     * session中的系统时间,保存的类型是Date
     */
    public static final String SYSTEM_DATE = "SYSTEM_DATE";

    /**
     * session中的用户ID,保存的类型是String
     */
    public static final String USER_ID = "USER_ID";

    /**
     * session中的城市ID,保存的类型是String
     */
    public static final String CITY_ID = "CITY_ID";

    /**
     * session中的自动预测算法ID,保存的类型是String
     */
    public static final String AUTO_FORECAST_ALGORITHM_ID = "AUTO_FORECAST_ALGORITHM_ID";

    /**
     * session中的默认口径id
     */
    public static final String CALIBER_ID = "CALIBER_ID";

    /**
     * token密匙
     */
    public final static String tokenKey = "base64EncodedSecretKey";

    /**
     * token标识
     */
    public final static String TOKEN = "load-token";



    /*--------------------------------------redis中的key前缀-----------------------------------------*/

    /**
     * 城市前缀,具体key的方式为“dict.city.belongId.cityId”
     */
    public static final String CACHE_ID_CITY_PREFIX = "lf.city.";

    /**
     * 算法前缀
     */
    public static final String CACHE_ID_ALGORITHM_PREFIX = "lf.algorithm.";


    /**
     * 系统设置前缀
     */
    public static final String CACHE_ID_SETTING_SYSTEM_PREFIX = "lf.settingSystem.";


    /**
     * 口径前缀
     */
    public static final String CACHE_ID_CALIBER_PREFIX = "lf.caliber.";

    /**
     * 稳定度分析redis缓存key
     */
    public static final String CACHE_ANALYSISRESULT_KEY =
        LfConfigConstants.PROJECT_NAME + Constants.SEPARATOR_BROKEN_LINE + "analysisresult";


    /**
     * 灵敏度分析redis缓存key
     */
    public static final String CACHE_SENSITIVITYRESULT_KEY =
        LfConfigConstants.PROJECT_NAME + Constants.SEPARATOR_BROKEN_LINE + "sensitivityResult";

    /**
     * 综合相似日查找redis缓存key
     */
    public static final String CACHE_SIMILARRESULT_KEY =
        LfConfigConstants.PROJECT_NAME + Constants.SEPARATOR_BROKEN_LINE + "similarResult";

    /**
     * 算法预测redis缓存key
     */
    public static final String CACHE_FORECAST_KEY =
        LfConfigConstants.PROJECT_NAME + Constants.SEPARATOR_BROKEN_LINE + "forecastResult";

}