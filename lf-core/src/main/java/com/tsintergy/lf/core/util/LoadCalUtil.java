package com.tsintergy.lf.core.util;

import com.tsieframework.core.base.vo.BasePeriod24VO;
import com.tsieframework.core.base.vo.BasePeriod288VO;
import com.tsieframework.core.base.vo.BasePeriod96VO;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.constants.Constants;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 负荷计算工具类
 * Created by liufei on 2017/12/16.
 */
public class LoadCalUtil {

    /**
     * 功能描述: <br>
     * 负荷预测时刻点的偏差率 -- 针对于部分负荷较小的地区统计准去率计算时 固定分母值 denominator
     *
     * @param his 历史数据
     * @param fc  预测数据
     * @param denominator 固定分母值
     * @return
     * <AUTHOR>
     * @since 1.0.0
     */
    public static BigDecimal getPointDeviationRatio(BigDecimal his, BigDecimal fc,BigDecimal denominator) throws Exception {
        if (his == null || fc == null || denominator == null) {
            return null;
        }
        if (fc.compareTo(BigDecimal.ZERO) == 0) {
            return new BigDecimal(0);
        }
        if (his.compareTo(BigDecimal.ZERO) == 0) {
            return new BigDecimal(1);
        }
        if (denominator.compareTo(BigDecimal.ZERO) == 0) {
            return new BigDecimal(1);
        }
        return fc.subtract(his).abs().divide(denominator, 9, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 功能描述: <br>
     * 计算日平均负荷预测准确率
     * 计算方式 国网标准:(RMSPE)
     *
     * @param hisVO      历史
     * @param fcVO       预测
     * @param loadNumber 96点or24点
     * @param denominator 负荷较小地区 统计准确率时用分母
     * @return
     * <AUTHOR>
     * @since 1.0.0
     */
    public static BigDecimal getDayAccuracy(BasePeriod24VO hisVO, BasePeriod24VO fcVO, Integer loadNumber,BigDecimal denominator) throws Exception {
        Map<String, BigDecimal> hisDataMap = BasePeriodUtils.toMap(hisVO, loadNumber, Constants.LOAD_CURVE_START_WITH_ZERO);
        Map<String, BigDecimal> fcDataMap = BasePeriodUtils.toMap(fcVO, loadNumber, Constants.LOAD_CURVE_START_WITH_ZERO);
        BigDecimal sumNumber = new BigDecimal(0);
        int count = 0;
        for (String column : hisDataMap.keySet()) {
            BigDecimal pointDeviationNumber = null;
            if (denominator != null){
                pointDeviationNumber = getPointDeviationRatio(hisDataMap.get(column), fcDataMap.get(column),denominator);
            }else {
                pointDeviationNumber = getPointDeviationRatio(hisDataMap.get(column), fcDataMap.get(column),hisDataMap.get(column));
            }
            if (pointDeviationNumber == null) {
                continue;
            }
            //sumNumber: 96点误差平方的和
            sumNumber = pointDeviationNumber.pow(2).add(sumNumber);
            count = count + 1;
        }
        if(count == 0 ){
            return null;
        }
        sumNumber = sumNumber.add(new BigDecimal(0));
        Double sumDeviationNumber = Math.sqrt(sumNumber.divide(BigDecimal.valueOf(count), 9, BigDecimal.ROUND_HALF_UP).doubleValue());
        return new BigDecimal(1).subtract(new BigDecimal(sumDeviationNumber));
    }

    /**
     * 计算准确率
     *
     * @param his
     * @param fc
     * @return
     */
    public static Double getAccuracy(Double his, Double fc) {
        if (his == null || fc == null) {
            return null;
        }
        if (his == 0) {
            return 1.0;
        }
        if (fc == 0) {
            return 0.0;
        }
        return 1 - Math.abs(his - fc) / his;
    }

    /**
     * 计算准确率
     *
     * @param his
     * @param fc
     * @return
     */
    public static BigDecimal getAccuracy(BigDecimal his, BigDecimal fc) {
        if (his == null || fc == null) {
            return null;
        }
        if (his.compareTo(BigDecimal.ZERO) == 0) {
            return new BigDecimal(1);
        }
        if (fc.compareTo(BigDecimal.ZERO) == 0) {
            return new BigDecimal(0);
        }
        return new BigDecimal(1).subtract(BigDecimalUtils.divide(his.subtract(fc).abs(), his, 4));
    }

    /**
     * 计算时刻点的偏差率
     *
     * @param his
     * @param fc
     * @return
     */
    public static BigDecimal getDevication(BigDecimal his, BigDecimal fc) {
        if (his == null || fc == null) {
            return null;
        }
        if (his.compareTo(BigDecimal.ZERO) == 0) {
            return new BigDecimal(1);
        }
        if (fc.compareTo(BigDecimal.ZERO) == 0) {
            return new BigDecimal(0);
        }
        return his.subtract(fc).abs();
    }

    public static BigDecimal getAddUpRate(BigDecimal his, BigDecimal fc) {
        if (his == null || fc == null) {
            return null;
        }
        if (his.compareTo(BigDecimal.ZERO) == 0) {
            return new BigDecimal(1);
        }
        if (fc.compareTo(BigDecimal.ZERO) == 0) {
            return new BigDecimal(0);
        }
        BigDecimal subtract = his.subtract(fc);
        return subtract.divide(fc,6,BigDecimal.ROUND_HALF_UP);
    }

    public static List<BigDecimal> getAddUpRateList(List<BigDecimal> hisList, List<BigDecimal> fcList) {
        List<BigDecimal> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(hisList)||CollectionUtils.isEmpty(fcList)) {
            return new ArrayList<>();
        }
        if (hisList.size()!=fcList.size()){
            return new ArrayList<>();
        }
        for (int i=0;i<hisList.size();i++){
            BigDecimal addUpRate = getAddUpRate(hisList.get(i), fcList.get(i));
            result.add(addUpRate);
        }
        return result;
    }

    /**
     * 计算方差
     *
     * @param accuracy    准确率
     * @param avgAccuracy 平均准确率
     * @return
     */
    public static BigDecimal getVariance(BigDecimal accuracy, BigDecimal avgAccuracy) {
        if (accuracy == null || avgAccuracy == null) {
            return null;
        }
        return accuracy.subtract(avgAccuracy).pow(2);
    }

    /**
     * 计算标准差
     *
     * @param variance 方差
     * @return
     */
    public static BigDecimal getStandardDeviation(BigDecimal variance) {
        if (variance == null) {
            return null;
        }
        return new BigDecimal(Math.sqrt(variance.doubleValue())).setScale(variance.scale(), RoundingMode.HALF_UP);
    }


    /**
     * @param his
     * @param fc
     * @return
     */
    public static Double getSerialPercision(Double[] his, Double[] fc) {
        if (his == null || fc == null) {
            return null;
        }
        Double tmp = 0.0;
        for (int i = 0; i < his.length; i++) {
            if (his[i] == 0) {
                tmp += 0;
            } else {
                tmp += Math.pow((his[i] - fc[i]) / his[i], 2);
            }
        }
        return 1 - Math.sqrt(tmp / 96);
    }

    /**
     * 计算预测曲线和实际曲线的相似度
     *
     * @param loadCityHisVO
     * @param loadCityFcVO
     * @return
     */
    public static BigDecimal calSimilarity(BasePeriod24VO loadCityHisVO, BasePeriod24VO loadCityFcVO) {

        if (loadCityHisVO == null || loadCityFcVO == null) {
            return null;
        }

        // 实际平均负荷
        BigDecimal avgLoadHis = BigDecimalUtils.avgList(BasePeriodUtils.toList(loadCityHisVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO), 4, false);
        // 预测平均负荷
        BigDecimal avgLoadFc = BigDecimalUtils.avgList(BasePeriodUtils.toList(loadCityFcVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO), 4, false);

        Map<String, BigDecimal> loadCityHisMap = BasePeriodUtils.toMap(loadCityHisVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        Map<String, BigDecimal> loadCityFcMap = BasePeriodUtils.toMap(loadCityFcVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);

        BigDecimal divisor = new BigDecimal(0).setScale(4);   // 实际曲线偏差与预测曲线偏差的乘积再求和
        BigDecimal dividend1 = new BigDecimal(0).setScale(4); // 实际曲线方差求和
        BigDecimal dividend2 = new BigDecimal(0).setScale(4); // 预测曲线方差求和
        for (String column : loadCityHisMap.keySet()) {
            divisor = divisor.add(BigDecimalUtils.multiply(BigDecimalUtils.sub(loadCityHisMap.get(column), avgLoadHis), BigDecimalUtils.sub(loadCityFcMap.get(column), avgLoadFc)));
            dividend1 = dividend1.add(BigDecimalUtils.sub(loadCityHisMap.get(column), avgLoadHis).pow(2));
            dividend2 = dividend2.add(BigDecimalUtils.sub(loadCityFcMap.get(column), avgLoadFc).pow(2));
        }

        BigDecimal similarity = BigDecimal.ZERO;
        if (dividend1.compareTo(BigDecimal.ZERO) != 0 && dividend2.compareTo(BigDecimal.ZERO) != 0) {
            similarity = BigDecimalUtils.divide(divisor, LoadCalUtil.getStandardDeviation(dividend1.multiply(dividend2)), 4);
        }

        return similarity;
    }

    /**
     * 获取最大值
     *
     * @param list
     * @return
     */
    public static BigDecimal max(List<BigDecimal> list) {
        BigDecimal max = null;
        if (list != null) {
            for (BigDecimal b : list) {
                if (b != null) {
                    if (max == null || max.compareTo(b) < 1) {
                        max = b;
                    }
                }
            }
        }
        return max;
    }

    /**
     * 获取最小值
     *
     * @param list
     * @return
     */
    public static BigDecimal min(List<BigDecimal> list) {
        BigDecimal min = null;
        if (list != null) {
            for (BigDecimal b : list) {
                if (b != null) {
                    if (min == null || min.compareTo(b) > -1) {
                        min = b;
                    }
                }
            }
        }
        return min;
    }

    /**
     * 计算负荷率，负荷率 = 日平均负荷/日最大负荷
     *
     * @param avg 平均负荷
     * @param max 最大负荷
     * @return
     */
    public static BigDecimal calLoadGradient(BigDecimal avg, BigDecimal max) {
        return BigDecimalUtils.divide(avg, max, 4);
    }

    /**
     * 计算峰谷差，峰谷差 = 日最大负荷 – 日最小负荷
     *
     * @param max 最大负荷
     * @param min 最小负荷
     * @return
     */
    public static BigDecimal calDifferent(BigDecimal max, BigDecimal min) {
        return BigDecimalUtils.sub(max, min);
    }

    /**
     * 计算峰谷差率，峰谷差率 = （日最大负荷 – 日最小负荷）/日最大负荷
     *
     * @param max 最大负荷
     * @param min 最小负荷
     * @return
     */
    public static BigDecimal calGradient(BigDecimal max, BigDecimal min) {
        return BigDecimalUtils.divide(BigDecimalUtils.sub(max, min), max, 4);
    }


    /**
     * 功能描述: <br>
     * 查询的是负数和正数中的最大值，把负数先变为正 然后取最大值
     * 获取list集合中的最大值(把数据先abs()后的)
     *
     * @return
     * <AUTHOR>
     * @since 1.0.0
     */
    public static BigDecimal getMax(List<BigDecimal> lis) {
        BigDecimal result = BigDecimal.ZERO;
        for (BigDecimal b : lis) {
            if (null != b) {
                b = b.abs();
            }
            if (null == b) {
                continue;
            } else if (null == result) {
                result = b;
            } else if (b.compareTo(result) > 0) {
                result = b;
            }
        }
        return result;
    }

    /**
     * 功能描述: <br>
     *
     * @param
     * @return
     * <AUTHOR>
     * @since 1.0.0
     */
    public static Map<String, List<BigDecimal>> toColumnList(List<? extends BasePeriod24VO> list, int type, Boolean index) {
        Map<String, List<BigDecimal>> resultMap = new HashMap<>();
        for (BasePeriod24VO vo : list) {
            //将每一个对象先变成map集合
            Map<String, BigDecimal> oneMap = BasePeriodUtils.toMap(vo, type, index);
            for (Map.Entry<String, BigDecimal> entry : oneMap.entrySet()) {
                if (resultMap.get(entry.getKey()) == null) {
                    List<BigDecimal> tempList = new ArrayList<>();
                    tempList.add(entry.getValue());
                    resultMap.put(entry.getKey(), tempList);
                } else {
                    List<BigDecimal> list1 = resultMap.get(entry.getKey());
                    list1.add(entry.getValue());
                }
            }
        }
        return resultMap;
    }

    public static List<BigDecimal> getNullList(Integer point) throws Exception {
        List<BigDecimal> nullList = new ArrayList<>();
        for (int i = 0; i < point; i++) {
            nullList.add(null);
        }
        return nullList;
    }


    public static List<BigDecimal> getZeroList(Integer point) throws Exception {
        List<BigDecimal> zeroList = new ArrayList<>();
        for (int i = 0; i < point; i++) {
            zeroList.add(new BigDecimal(0));
        }
        return zeroList;
    }


    public static List<BigDecimal> subtract(List<BigDecimal> list1, List<BigDecimal> list2) {
        if (CollectionUtils.isEmpty(list1) || CollectionUtils.isEmpty(list2)) {
            return null;
        }
        List<BigDecimal> number = new ArrayList<>();
        for (int i = 0; i < list1.size(); i++) {
            BigDecimal number1 = list1.get(i);
            BigDecimal number2 = list2.get(i);
            BigDecimal sub = sub(number1,number2);
            number.add(sub);
        }
        return number;
    }


    /**
     *    
     *  功能描述: <br> 
     *  月，周，日
     * (最大/最小)负荷预测准确率
     *
     * @return  
     * <AUTHOR> 
     * @since 1.0.0     
     */
    public static BigDecimal calcMaxMinAccuracy(BigDecimal real, BigDecimal fc) throws Exception {
        BigDecimal deviation = getPointDeviationRatio(real, fc,real);
        if (deviation != null) {
            return sub(BigDecimal.ONE, deviation);
        }
        return deviation;
    }


    /**
     * 减法：处理null值情况，保持正常返回
     *
     * @param meiosis 减数
     * @param minuend 被减数
     * @return
     */
    public static BigDecimal sub(BigDecimal meiosis, BigDecimal minuend) {
        if (null == meiosis && null == minuend) {
            return null;
        }
        if (null == meiosis) {
            return null;
        }
        if (null == minuend) {
            return meiosis;
        }
        return meiosis.subtract(minuend);
    }


    /**
     *    
     *  功能描述: <br> 
     * 计算最大负荷预测月综合准确率(8*日最大负荷月综合平均准确率+周最大负荷月平均准确率+月最大负荷预测准确率)/10
     *
     * @param dayAvgAccuracy  日最大/最小负荷预测月平均准确率
     * @param weekAvgAccuracy 周最大/最小负荷预测月平均准确率
     * @param monthAccuracy   月最大/最小负荷预测准确率
     * @return  
     * <AUTHOR> 
     * @since 1.0.0     
     */
    public static BigDecimal calcMaxSynthesizeAccuracy(BigDecimal dayAvgAccuracy, BigDecimal weekAvgAccuracy, BigDecimal monthAccuracy) throws Exception {
        BigDecimal number = null;
        if (dayAvgAccuracy != null) {
            number = dayAvgAccuracy.multiply(new BigDecimal(8));
        }
        if (weekAvgAccuracy != null) {
            number = add(number, weekAvgAccuracy);
        }
        if (monthAccuracy != null) {
            number = add(number, monthAccuracy);
        }
        if (number == null) {
            return null;
        }
        BigDecimal result = divide(number, new BigDecimal(10), 4);
        return result;
    }


    /**
     * 除法：1、处理null值情况，默认用1替代
     * 2、处理被除数有可能为0的情况，默认用1替代
     *
     * @param divisor
     * @param dividend
     * @param scale
     * @return
     */
    public static BigDecimal divide(BigDecimal divisor, BigDecimal dividend, Integer scale) {
        if (null == divisor && null == dividend) {
            return null;
        }
        if (null == divisor) {
            return dividend;
        }
        if (null == dividend) {
            return divisor;
        }
        if (null != scale && scale > 0) {
            return divisor.divide(dividend, scale, BigDecimal.ROUND_HALF_EVEN);
        } else {
            return divisor.divide(dividend, 2, BigDecimal.ROUND_HALF_EVEN);
        }
    }


    /**
     * 加法：处理null值情况，默认用0替换
     *
     * @param one
     * @param two
     * @return
     */
    public static BigDecimal add(BigDecimal one, BigDecimal two) {
        if (null == one && null == two) {
            return null;
        }
        if (null == one) {
            return two;
        }
        if (null == two) {
            return one;
        }

        return one.add(two);
    }

    /**
     *    
     *  功能描述: <br> 
     *  计算月负荷预测综合准确率(8*96点的月平均准确率+4*最大负荷月平均准确率+2*最小负荷月综合准确率+月电量预测准确率)/15
     *
     * @param pointAvg              96点负荷预测月平均准确率
     * @param maxSynthesizeAccuracy 最大负荷月综合准确率
     * @param minSynthesizeAccuracy 最小负荷月综合准确率
     * @param energyAccuracy        月电量预测准确率   
     * @return  
     * <AUTHOR> 
     * @since 1.0.0     
     */
    public static BigDecimal calcMonthSynthesizeAccuracy(BigDecimal pointAvg, BigDecimal maxSynthesizeAccuracy, BigDecimal minSynthesizeAccuracy, BigDecimal energyAccuracy) throws Exception {
        BigDecimal number = null;
        if (pointAvg != null) {
            number = pointAvg.multiply(new BigDecimal(8));
        }
        if (maxSynthesizeAccuracy != null) {
            BigDecimal max = maxSynthesizeAccuracy.multiply(new BigDecimal(4));
            number = add(number, max);
        }
        if (minSynthesizeAccuracy != null) {
            BigDecimal min = minSynthesizeAccuracy.multiply(new BigDecimal(2
            ));
            number = add(number, min);
        }
        if (energyAccuracy != null) {
            number = add(number, energyAccuracy);
        }
        if (number != null) {
            BigDecimal result = divide(number, new BigDecimal(15), 4);
            return result;
        }
        return null;
    }


    /**
     * 气象预报准确率
     *
     * @param hisDO 历史气象
     * @param fcDO 预测气象
     * @param loadNumber 96点
     * <AUTHOR>
     * @since 1.0.0
     */
    public static BigDecimal getWeatherAccuracy(BasePeriod24VO hisDO, BasePeriod24VO fcDO, Integer loadNumber)
            throws Exception {
        List<BigDecimal> hisDataMap = BasePeriodUtils
                .toList(hisDO, loadNumber, Constants.LOAD_CURVE_START_WITH_ZERO);
        List<BigDecimal> fcDataMap = BasePeriodUtils
                .toList(fcDO, loadNumber, Constants.LOAD_CURVE_START_WITH_ZERO);
        return accuracy(hisDataMap, fcDataMap);
    }


    /**
     * 计算预报准确率
     *
     * <AUTHOR>
     */
    private static BigDecimal accuracy(List<BigDecimal> hisDataList, List<BigDecimal> fcDataList) throws Exception {
        BigDecimal sumNumber = new BigDecimal(0);
        for (int count = 0; count < hisDataList.size(); count++) {
            BigDecimal pointDeviationNumber = getWeatherPoint(hisDataList.get(count), fcDataList.get(count),
                    hisDataList.get(count));
            if (pointDeviationNumber == null) {
                continue;
            }
            //sumNumber:正确预报次数
            sumNumber = sumNumber.add(pointDeviationNumber);
        }
        BigDecimal accuracy = sumNumber.divide(new BigDecimal(hisDataList.size()), 4, BigDecimal.ROUND_DOWN);
        return accuracy;
    }

    /**
     * 功能描述: <br> 气象预测准确率
     *
     * @param his 历史数据
     * @param fc 预测数据
     * @return 1 记该次为正缺预报  0 忽略该次
     * <AUTHOR>
     * @since 1.0.0
     */
    public static BigDecimal getWeatherPoint(BigDecimal his, BigDecimal fc, BigDecimal divisor) throws Exception {
        if (his == null || fc == null) {
            return null;
        }
        //实际的和预测的 如果两个相差小于1则为正确预报
        if (fc.subtract(his).abs().compareTo(new BigDecimal(2)) <= 0) {
            return new BigDecimal(1);
        } else {
            //错误预报
            return new BigDecimal(0);
        }

    }

    /**
     * 多个对象对应时刻点的累加和值
     *
     * @param lis
     * @param index 起始时刻点的选择（1、如果时刻点从00000开始，则index为true,2、如果时刻点从0015开始，则index为false）, null则以VO实体字段为准
     * @return
     */
    private final static Map<Integer, Set<String>> ENTITY_TYPE = new HashMap<Integer, Set<String>>() {{
        put(new Integer(24), new HashSet<String>() {
            {
                Class cla = BasePeriod24VO.class;
                Field[] pkFields = cla.getDeclaredFields();

                for (Field field : pkFields) {
                    add(field.getName());
                }
            }
        });
        put(new Integer(96), new HashSet<String>() {
            {
                Class cla = BasePeriod96VO.class;

                while (cla != null) {
                    Field[] pkFields = cla.getDeclaredFields();

                    for (Field field : pkFields) {
                        add(field.getName());
                    }
                    cla = cla.getSuperclass();
                }
            }
        });
        put(new Integer(288), new HashSet<String>() {
            {
                Class cla = BasePeriod288VO.class;

                while (cla != null) {
                    Field[] pkFields = cla.getDeclaredFields();

                    for (Field field : pkFields) {
                        add(field.getName());
                    }
                    cla = cla.getSuperclass();
                }
            }
        });
    }};

    /**
     * 多个对象对应时刻点的累加和值
     *
     * @param lis
     * @param index 起始时刻点的选择（1、如果时刻点从00000开始，则index为true,2、如果时刻点从0015开始，则index为false）, null则以VO实体字段为准
     * @return
     */
    public static Map<String, BigDecimal> addListObject(List<? extends BasePeriod24VO> lis, int type, Boolean index) {
        if (!LoadCalUtil.ENTITY_TYPE.containsKey(type)) {
            return null;
        }
        if (null == lis || lis.isEmpty()) {
            return null;
        }
        Map<String, BigDecimal> resultMap = new HashMap<String, BigDecimal>();
        for (BasePeriod24VO vo : lis) {
            Map<String, BigDecimal> oneMap = BasePeriodUtils.toMap(vo, type, index);
            for (Map.Entry<String, BigDecimal> entry : oneMap.entrySet()) {
                resultMap.put(entry.getKey(), add(entry.getValue(), resultMap.get(entry.getKey())));
            }
        }
        return resultMap;
    }

    public static BigDecimal calcEnergy(List<BigDecimal> loadList) {
        if (!CollectionUtils.isEmpty(loadList)) {
            return BigDecimalUtils.divide(BigDecimalUtils.addAllValue(loadList), new BigDecimal(4), 4);
        }
        return null;
    }
}
