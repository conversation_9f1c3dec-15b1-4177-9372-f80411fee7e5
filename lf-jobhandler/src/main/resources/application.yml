spring:
  application:
    name: lf-jobhandler
  profiles:
    include: config-lf, config-common, config-datasource, config-orm, config-jobhandler,config-algorithm, config-cache-redis,config-web
dubbo:
  enabled: false
#  registries:
#    load:
#      register: true
#      address: zookeeper://**********:2181
#      timeout: 60000
#      threads: 50
#    weather:
#      register: true
#      address: zookeeper://**********:2181
#      timeout: 60000
#      threads: 50
tsie:
  application-type: service