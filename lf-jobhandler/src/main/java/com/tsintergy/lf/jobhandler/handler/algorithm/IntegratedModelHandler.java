/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  gss Date:  2020/3/5 15:40 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.jobhandler.handler.algorithm;

import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.SystemConstant;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.jobhandler.common.AbstractBaseHandler;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CaliberDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.AutoForecastService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingSystemDO;
import com.tsintergy.lf.serviceimpl.common.impl.CoreConfigInfo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 综合模型算法 早7点55执行  只跑 cityId =1 全网 和13 调度口径 的  0 55 7 * * ?
 *
 * <AUTHOR>
 * @create 2020/7/5
 * @since 1.0.0
 */
@Component
@JobHandler("integratedModelHandler")
@Slf4j
public class IntegratedModelHandler extends AbstractBaseHandler {

    @Autowired
    private AutoForecastService forecastService;

    @Autowired
    private CoreConfigInfo coreConfigInfo;

    @Autowired
    private SettingSystemService settingSystemService;

    @Autowired
    private CaliberService caliberService;


    @Override
    public ReturnT<String> execute(String s) throws Exception {
        //默认预测明天
        Date startDate = DateUtils.addDays(new Date(), 1);
        Integer forecastType = Integer.parseInt(coreConfigInfo.getRuntimeParam("task.forecast.type"));
        List<AlgorithmEnum> enums = new ArrayList<>();
        enums.add(AlgorithmEnum.COMPREHENSIVE_MODEL);
        List<String> cityIds = new ArrayList<>();
        cityIds.add(CityConstants.PROVINCE_ID);
        //默认的是一次预测三天
        Integer days = 3;
        //查询用户自定义的正常日天数
        SettingSystemDO reportVOS = settingSystemService.findByFieldId(SystemConstant.FORECAST_DAY);
        if (reportVOS != null) {
            days = Integer.parseInt(reportVOS.getValue());
            //系统预测设置的上报起始日从明天开始
            startDate = DateUtils.addDays(new Date(), 1);
        }
        Date endDate = DateUtils.addDays(startDate, days - 1);
        if (StringUtils.isNotEmpty(s)) {
            String[] split = s.split(",");
            String startDateStr = split[0];
            String endDateStr = split[1];
            startDate = DateUtils.string2Date(startDateStr, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
            endDate = DateUtils.string2Date(endDateStr, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
        }

        List<CaliberDO> allCalibers = caliberService.findAllCalibers();
        for(CaliberDO caliberDO:allCalibers) {
            for (String cityId : cityIds) {
                try {
                    forecastService
                        .autoForecast(forecastType, DateUtil.getUid(), cityId, caliberDO.getId(), startDate, endDate,
                            enums, null, null,
                            null);
                }catch (Exception e){
                    XxlJobLogger.log(e.getMessage());
                    e.printStackTrace();
                }
            }
        }
        return ReturnT.SUCCESS;
    }
}

