/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  wangchen
 * Date:  2019/11/17 16:13
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.jobhandler.collect.common;

/**
 * Description:  <br>
 * 重庆短期负荷采集枚举
 * <AUTHOR>
 * @create 2019/11/17 
 * @since 1.0.0
 */
public enum D5000LfClctEnum {
    //2021-1-12日 根据重庆现场要求修改
//    QUAN_WANG("重庆全网有功总加","yc_hs_500401","cur_012"),
//    QUAN_WANG("重庆全网有功总加","yc_hs_720002","cur_049"),
    //2021-2-1日 调整为与d5000平台一致
    QUAN_WANG("重庆全网有功总加","yc_hs_720001","cur_041"),
    DI_DIAO("市区地调转发/系统总有功","yc_hs_500569","cur_091"),
    JIANG_BEI("江北公司全网有功总加","yc_hs_500899","cur_014"),
    BEI_BEI("北碚区域地调有功负荷总加","yc_hs_500820","cur_021"),
    NAN_AN("南岸地调转发/南岸局P","yc_hs_500424","cur_003"),
    QI_NAN("綦南区域地调有功负荷总加","yc_hs_500820","cur_019"),
    CHANG_SHOU("长寿区域地调有功负荷总加","yc_hs_500820","cur_023"),
    YONG_CHUAN("永川区域地调有功负荷总加","yc_hs_500820","cur_015"),
    WAN_ZHOU("万州区域地调有功负荷总加","yc_hs_500820","cur_025"),
    BI_SHAN("璧山区域地调有功负荷总加","yc_hs_500820","cur_017"),
    JIANG_JIN("江津区域地调有功负荷总加","yc_hs_500820","cur_027"),
    //调度口径 2021-5-18修改域名和表名
//    DIAO_DU("重庆全网有功调度总加","yc_hs_500857","cur_014");
    DIAO_DU("重庆全网有功调度总加","yc_hs_720001","cur_039");

    /**
     * 负荷名称
     */
    private String loadName;

    /**
     * 表号
     */
    private String tableName;

    /**
     * 域号
     */
    private String column;

    D5000LfClctEnum(String loadName, String tableName, String column) {
        this.loadName = loadName;
        this.tableName = tableName;
        this.column = column;
    }

    public String getLoadName() {
        return loadName;
    }

    public void setLoadName(String loadName) {
        this.loadName = loadName;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getColumn() {
        return column;
    }

    public void setColumn(String column) {
        this.column = column;
    }
}
