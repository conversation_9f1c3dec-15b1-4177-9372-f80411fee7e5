/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.jobhandler.handler.send;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.BasePeriod96VO;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.sensitivity.SensitivityMetaData;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.sensitivity.SensitivityResult;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.SystemConstant;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.jobhandler.collect.api.KongTiaoFuHeService;
import com.tsintergy.lf.jobhandler.collect.pojo.BaseCityClctRelationDO;
import com.tsintergy.lf.jobhandler.collect.pojo.KongTiaoFuHeDO;
import com.tsintergy.lf.jobhandler.common.AbstractBaseHandler;
import com.tsintergy.lf.jobhandler.common.ParamDate;
import com.tsintergy.lf.serviceapi.algorithm.api.AcSensitivityForecastService;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.*;
import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.*;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.Base96DO;
import com.tsintergy.lf.serviceapi.base.common.enumeration.DateType2;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.dto.AcSensitivityAlgorithmDTO;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherNewEnum;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingSystemDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import net.sf.cglib.beans.BeanMap;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: 空调负荷数据发送d5000 <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/6/8 9:56
 * @Version: 1.0.0
 */

@Component
@JobHandler("acLoadSendD5000Handler")
public class AcLoadSendD5000Handler extends AbstractBaseHandler {

    @Autowired
    LoadAcFcBasicService loadAcFcBasicService;

    @Autowired
    FoundationFcLoadHisMonthService foundationFcLoadHisMonthService;

    @Autowired
    FoundationFcLoadHisDayService foundationFcLoadHisDayService;


    @Autowired
    LoadCityFcService loadCityFcService;

    @Autowired
    KongTiaoFuHeService kongTiaoFuHeService;

    @Autowired
    WeatherCityHisService weatherCityHisService;

    @Autowired
    WeatherCityFcService weatherCityFcService;

    @Autowired
    CityService cityService;


    @Autowired
    LoadAcHisBasicService loadAcHisBasicService;

    private static final String CALIBER_ID="5";


    //空调负荷算法id为13的-受开关影响
    private static final String ACLOADNAME = "ktfh";



    //空调负荷实际-系统计算-每日基础负荷计算-受开关影响
    private static final String ACHISLOADNAME = "ktfh_his";

    //空调负荷实际-系统计算-每日基础负荷计算最大
    private static final String MAXACHISLOADNAME = "ktfh_his_max";


    //系统计算每月一个基础负荷-受系统设置页面和导入开关影响
    private static final String BASELOADNAME = "jcfh";

    //系统计算每月一个基础负荷-最大值
    private static final String MAXBASELOADNAME = "jcfh_max";



    // 新的基础负荷数据-每日的空调负荷基础曲线算法提供
    private static final String BASELOADNAME_DAY = "jcfh_day";

    // 通过假的实际负荷和算法提供的每日基础负荷数据做的假的空调负荷实际
    private static final String AIRLOAD_HIS = "airLoad_his";

    // 通过采集d5000上报的预测负荷和算法提供的每日基础负荷数据做的空调负荷预测数据
    private static final String AIRLOAD_FC = "airLoad_fc";

    // 通过采集d5000上报的预测负荷和算法提供的每日基础负荷数据做的空调负荷预测数据-最大负荷
    private static final String MAXACLOADNAME = "ktfh_max";




    private static final String PROVINCELOADNAME = "xtfh";


    private static final String MAXPROVINCELOADNAME = "xtfh_max";


    private static final String WEATHER_HIS = "weather_his";


    private static final String WEATHER_FC = "weather_fc";



    private static final String FEATURE_X = "feature_x";

    private static final String FEATURE_Y = "feature_y";



    private static final String SENSITIVITY_X = "sensitivity_x";

    private static final String SENSITIVITY_Y = "sensitivity_y";


    @Autowired
    AcSensitivityForecastService acSensitivityForecastService;


    @Autowired
    FoundationLoadHisMonthService foundationLoadHisMonthService;


    @Autowired
    SettingSystemService settingSystemService;

    @Autowired
    AcLoadReportD5000Service acLoadReportD5000Service;

    @Autowired
    LoadCityHisService loadCityHisService;


    @Override
    public ReturnT<String> execute(String s) throws Exception {


        SettingSystemDO byFieldId =
            settingSystemService.findByFieldId(SystemConstant.ACLOAD_SENDD5000_SWITCH);

        SettingSystemDO importDataSendSwitch =
            settingSystemService.findByFieldId(SystemConstant.IMPORTDATASHOW_SENDD5000);

        if(byFieldId != null && byFieldId.getValue().equals("0")){
            XxlJobLogger.log("acload_to_d5000 系统设置为0,故不发送d5000");
            return ReturnT.SUCCESS;
        }


        Date startDate = DateUtils.addDays(new Date(), 1);
        ParamDate paramDate = resolveJobParam(s, startDate, 1);
        startDate = paramDate.getStartDate();
        Date endDate = paramDate.getEndDate();
        String weatherCityId = "1";
            //cityService.findWeatherCityId("1");

        //如果参数为空，写入今天的实际气象,实际气象只传一次
        if(StringUtils.isEmpty(s)){

            //调用灵敏度算法
            wrapSensitivity();



            List<WeatherCityHisDO> weatherCityHisDOs = weatherCityHisService
                .findWeatherCityHisDOs(weatherCityId, WeatherNewEnum.TEMPERATURE.getType(), new Date(), new Date());
            if(CollectionUtils.isEmpty(weatherCityHisDOs)){
                XxlJobLogger.log("当前日期:"+DateUtils.date2String(new Date(),DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR)+",实际气象数据为空");
            }else {
                WeatherCityHisDO weatherCityHisDO = weatherCityHisDOs.get(0);
                saveOrUpdateKongTiaoFuHeDO(weatherCityHisDO,new Date(),WEATHER_HIS);
            }


            List<AcLoadReportD5000DO> acLoadReportD5000DOS = acLoadReportD5000Service
                .getAcLoadReportD5000DO("1", CALIBER_ID, null, null, 4, new Date());

            List<LoadAcHisBasicDO> loadAcHisBasicDOS = null;

            if(!CollectionUtils.isEmpty(acLoadReportD5000DOS)){
                loadAcHisBasicDOS = new ArrayList<>();
                for(AcLoadReportD5000DO acLoadReportD5000DO:acLoadReportD5000DOS){
                    LoadAcHisBasicDO loadAcHisBasicDO = new LoadAcHisBasicDO();
                    BeanUtils.copyProperties(acLoadReportD5000DO,loadAcHisBasicDO);
                    loadAcHisBasicDOS.add(loadAcHisBasicDO);
                }

            }else {
                loadAcHisBasicDOS = loadAcHisBasicService
                    .getloadAcHisBasicDOList("1", CALIBER_ID, new Date(), new Date());
            }


            if(CollectionUtils.isEmpty(loadAcHisBasicDOS)){
                XxlJobLogger.log("当前日期:"+DateUtils.date2String(new Date(),DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR)+",实际空调负荷数据为空");
            }else {
                LoadAcHisBasicDO loadAcHisBasicDO = loadAcHisBasicDOS.get(0);
                saveOrUpdateKongTiaoFuHeDO(loadAcHisBasicDO,new Date(),ACHISLOADNAME);
                List<BigDecimal> acHis = BasePeriodUtils
                    .toList(loadAcHisBasicDO, Constants.LOAD_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO);
                acHis = acHis.stream().filter(t -> t != null).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(acHis)){
                    BigDecimal maxAcHis = Collections.max(acHis);
                    Base96DO base96DO = new Base96DO();
                    base96DO.setT0015(maxAcHis);
                    saveOrUpdateKongTiaoFuHeDO(base96DO,new Date(),MAXACHISLOADNAME);
                }
            }


            //每日的空调负荷基础曲线
            List<FoundationFcLoadHisDayDO> foundationFcBasicLoadHisDay = foundationFcLoadHisDayService
                .getFoundationFcBasicLoadHisDay("1", CALIBER_ID, new java.sql.Date(System.currentTimeMillis()));


            //全省假的实际曲线 做空调实际数据
            List<BigDecimal> loadCityHisDO = loadCityHisService.findLoadCityHisDO(new Date(), "2", CALIBER_ID);
            //通过假的实际曲线计算空调负荷实际数据
            List<BigDecimal> airLoadHis = LoadCalUtil.getNullList(96);
            XxlJobLogger.log("loadcityHisDO:"+CollectionUtils.isEmpty(loadCityHisDO)+","+CollectionUtils.isEmpty(foundationFcBasicLoadHisDay));
            if(!CollectionUtils.isEmpty(loadCityHisDO) && !CollectionUtils.isEmpty(foundationFcBasicLoadHisDay)){
                FoundationFcLoadHisDayDO foundationFcLoadHisDayDO = foundationFcBasicLoadHisDay.get(0);
                List<BigDecimal> basicLoads = BasePeriodUtils.toList(foundationFcLoadHisDayDO, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
                for(int i=0;i<loadCityHisDO.size();i++){
                    BigDecimal var1 = loadCityHisDO.get(i);
                    BigDecimal var2 = basicLoads.get(i);
                    if(var1 != null && var2!= null){
                        BigDecimal airLoadHisTime = var1.subtract(var2);
                        airLoadHis.set(i,airLoadHisTime);
                    }
                }
                Map<String, BigDecimal> decimalMap = ColumnUtil.listToMap(airLoadHis, Constants.LOAD_CURVE_START_WITH_ZERO);
                Base96DO airLoadHisByD5000 = new Base96DO();
                BasePeriodUtils.setAllFiled(airLoadHisByD5000,decimalMap);
                saveOrUpdateKongTiaoFuHeDO(airLoadHisByD5000,new Date(),AIRLOAD_HIS);

                airLoadHis = airLoadHis.stream().filter(t -> t != null).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(airLoadHis)){
                    BigDecimal maxAcHis = Collections.max(airLoadHis);
                    Base96DO base96DO = new Base96DO();
                    base96DO.setT0015(maxAcHis);
                    saveOrUpdateKongTiaoFuHeDO(base96DO,new Date(),MAXACHISLOADNAME);
                }
            }
        }



        while (startDate.before(DateUtils.addDays(endDate, 1))) {

            //如果参数为空，写入今天的实际气象
            if(!StringUtils.isEmpty(s)) {

                List<WeatherCityHisDO> weatherCityHisDOs = weatherCityHisService
                    .findWeatherCityHisDOs(weatherCityId, null, startDate, startDate);

                if(CollectionUtils.isEmpty(weatherCityHisDOs)){
                    XxlJobLogger.log("当前日期:"+DateUtils.date2String(startDate,DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR)+",实际气象数据为空");
                }else {
                    WeatherCityHisDO weatherCityHisDO = weatherCityHisDOs.get(0);
                    saveOrUpdateKongTiaoFuHeDO(weatherCityHisDO,startDate,WEATHER_HIS);
                }




                //空调负荷实际发送-系统自动计算
                List<AcLoadReportD5000DO> acLoadReportD5000HisLoadDOS = acLoadReportD5000Service
                    .getAcLoadReportD5000DO("1", CALIBER_ID, null, null, 4, startDate);

                List<LoadAcHisBasicDO> loadAcHisBasicDOS = null;

                if(!CollectionUtils.isEmpty(acLoadReportD5000HisLoadDOS)){
                    loadAcHisBasicDOS = new ArrayList<>();
                    for(AcLoadReportD5000DO acLoadReportD5000DO:acLoadReportD5000HisLoadDOS){
                        LoadAcHisBasicDO loadAcHisBasicDO = new LoadAcHisBasicDO();
                        BeanUtils.copyProperties(acLoadReportD5000DO,loadAcHisBasicDO);
                        loadAcHisBasicDOS.add(loadAcHisBasicDO);
                    }

                }else {
                    loadAcHisBasicDOS = loadAcHisBasicService
                        .getloadAcHisBasicDOList("1", CALIBER_ID, new Date(), startDate);
                }



                if(CollectionUtils.isEmpty(loadAcHisBasicDOS)){
                    XxlJobLogger.log("当前日期:"+DateUtils.date2String(startDate,DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR)+",实际空调负荷数据为空");
                }else {
                    LoadAcHisBasicDO loadAcHisBasicDO = loadAcHisBasicDOS.get(0);
                    saveOrUpdateKongTiaoFuHeDO(loadAcHisBasicDO,startDate,ACHISLOADNAME);
                    List<BigDecimal> acHis = BasePeriodUtils
                        .toList(loadAcHisBasicDO, Constants.LOAD_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO);
                    acHis = acHis.stream().filter(t -> t != null).collect(Collectors.toList());
                    if(!CollectionUtils.isEmpty(acHis)){
                        BigDecimal maxAcHis = Collections.max(acHis);
                        Base96DO base96DO = new Base96DO();
                        base96DO.setT0015(maxAcHis);
                        saveOrUpdateKongTiaoFuHeDO(base96DO,startDate,MAXACHISLOADNAME);
                    }
                }



                //每日的空调负荷基础曲线
                List<FoundationFcLoadHisDayDO> foundationFcBasicLoadHisDay = foundationFcLoadHisDayService
                    .getFoundationFcBasicLoadHisDay("1", CALIBER_ID, new java.sql.Date(startDate.getTime()));


                //全省假的实际曲线 做空调实际数据
                List<BigDecimal> loadCityHisDO = loadCityHisService.findLoadCityHisDO(startDate, "2", CALIBER_ID);
                //通过假的实际曲线计算空调负荷实际数据
                List<BigDecimal> airLoadHis = LoadCalUtil.getNullList(96);
                if(!CollectionUtils.isEmpty(loadCityHisDO) && !CollectionUtils.isEmpty(foundationFcBasicLoadHisDay)){
                    FoundationFcLoadHisDayDO foundationFcLoadHisDayDO = foundationFcBasicLoadHisDay.get(0);
                    List<BigDecimal> basicLoads = BasePeriodUtils.toList(foundationFcLoadHisDayDO, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
                    for(int i=0;i<loadCityHisDO.size();i++){
                        BigDecimal var1 = loadCityHisDO.get(i);
                        BigDecimal var2 = basicLoads.get(i);
                        if(var1 != null && var2!= null){
                            BigDecimal airLoadHisTime = var1.subtract(var2);
                            airLoadHis.set(i,airLoadHisTime);
                        }
                    }
                    Map<String, BigDecimal> decimalMap = ColumnUtil.listToMap(airLoadHis, Constants.LOAD_CURVE_START_WITH_ZERO);
                    Base96DO airLoadHisByD5000 = new Base96DO();
                    BasePeriodUtils.setAllFiled(airLoadHisByD5000,decimalMap);
                    saveOrUpdateKongTiaoFuHeDO(airLoadHisByD5000,startDate,AIRLOAD_HIS);
                }
            }





            //预测气象数据发送
            List<WeatherCityFcDO> weatherCityFcDOs = weatherCityFcService
                .findWeatherCityFcDOs(weatherCityId, WeatherNewEnum.TEMPERATURE.getType(), startDate, startDate);

            if(CollectionUtils.isEmpty(weatherCityFcDOs)){
                XxlJobLogger.log("当前日期:"+DateUtils.date2String(startDate,DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR)+",预测气象数据为空");
            }else {
                WeatherCityFcDO weatherCityFcDO = weatherCityFcDOs.get(0);
                saveOrUpdateKongTiaoFuHeDO(weatherCityFcDO,startDate,WEATHER_FC);
            }







            //空调负荷预测数据查询-系统自动计算
            List<LoadAcFcBasicDO> loadAcFcBasicDOS = null;

            List<AcLoadReportD5000DO> acLoadReportD5000FcLoadDOS = acLoadReportD5000Service
                .getAcLoadReportD5000DO("1", CALIBER_ID, null, null, 3, startDate);

            if(!CollectionUtils.isEmpty(acLoadReportD5000FcLoadDOS)){
                loadAcFcBasicDOS = new ArrayList<>();
                for(AcLoadReportD5000DO acLoadReportD5000DO:acLoadReportD5000FcLoadDOS){
                    LoadAcFcBasicDO loadAcFcBasicDO = new LoadAcFcBasicDO();
                    BeanUtils.copyProperties(acLoadReportD5000DO,loadAcFcBasicDO);
                    loadAcFcBasicDOS.add(loadAcFcBasicDO);
                }

            }else {
                loadAcFcBasicDOS = loadAcFcBasicService
                    .getloadAcFcBasicDOList("1", CALIBER_ID, startDate, startDate, "13");
            }




            //基础负荷曲线查询
            String dateString = DateUtils.date2String(startDate, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
            String yearStr = dateString.substring(0, 4);
            String monthStr = dateString.substring(4, 6);


            List<AcLoadReportD5000DO> acLoadReportD5000DOS = acLoadReportD5000Service
                .getAcLoadReportD5000DO("1", CALIBER_ID, yearStr, monthStr, DateType2.WORKDAY.getId(),null);

            List<FoundationLoadHisMonthDO> foundationLoadHisMonthDOList = null;
            if(CollectionUtils.isEmpty(acLoadReportD5000DOS) || importDataSendSwitch.getValue().equals("0")){
                foundationLoadHisMonthDOList = foundationLoadHisMonthService
                    .getFoundationLoadHisMonth("1", CALIBER_ID, yearStr, monthStr, DateType2.WORKDAY.getId());
            }else if(!CollectionUtils.isEmpty(acLoadReportD5000DOS) && importDataSendSwitch.getValue().equals("1")){
                foundationLoadHisMonthDOList = new ArrayList<>();
                for(AcLoadReportD5000DO acLoadReportD5000DO:acLoadReportD5000DOS){
                    FoundationLoadHisMonthDO foundationLoadHisMonthDO = new FoundationLoadHisMonthDO();
                    BeanUtils.copyProperties(acLoadReportD5000DO,foundationLoadHisMonthDO);
                    foundationLoadHisMonthDOList.add(foundationLoadHisMonthDO);
                }
            }



            //每日的空调负荷基础曲线
            List<FoundationFcLoadHisDayDO> foundationFcBasicLoadHisDay = foundationFcLoadHisDayService
                .getFoundationFcBasicLoadHisDay("1", CALIBER_ID, new java.sql.Date(startDate.getTime()));
            if(!CollectionUtils.isEmpty(foundationFcBasicLoadHisDay)) {
                FoundationFcLoadHisDayDO foundationFcLoadHisDayDO = foundationFcBasicLoadHisDay.get(0);
                saveOrUpdateKongTiaoFuHeDO(foundationFcLoadHisDayDO,startDate,BASELOADNAME_DAY);
            }


            //采集到的全省的预测数据 做空调预测数据
            List<BigDecimal> loadCityFcDOFromHis = loadCityHisService.findLoadCityHisDO(startDate, "3", CALIBER_ID);
            if(!CollectionUtils.isEmpty(loadCityFcDOFromHis) && !CollectionUtils.isEmpty(foundationFcBasicLoadHisDay)){
                FoundationFcLoadHisDayDO foundationFcLoadHisDayDO = foundationFcBasicLoadHisDay.get(0);
                List<BigDecimal> basicLoads = BasePeriodUtils.toList(foundationFcLoadHisDayDO, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
                List<BigDecimal> airLoadFc = LoadCalUtil.getNullList(96);
                for(int i=0;i<loadCityFcDOFromHis.size();i++){
                    BigDecimal var1 = loadCityFcDOFromHis.get(i);
                    BigDecimal var2 = basicLoads.get(i);
                    if(var1 != null && var2!= null){
                        BigDecimal airLoadFcTime = var1.subtract(var2);
                        airLoadFc.set(i,airLoadFcTime);
                    }
                }
                Map<String, BigDecimal> decimalMap = ColumnUtil.listToMap(airLoadFc, Constants.LOAD_CURVE_START_WITH_ZERO);
                Base96DO airLoadFcByD5000 = new Base96DO();
                BasePeriodUtils.setAllFiled(airLoadFcByD5000,decimalMap);
                saveOrUpdateKongTiaoFuHeDO(airLoadFcByD5000,startDate,AIRLOAD_FC);

                airLoadFc = airLoadFc.stream().filter(t -> t != null).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(airLoadFc)){
                    BigDecimal maxAcFc = Collections.max(airLoadFc);
                    Base96DO base96DO = new Base96DO();
                    base96DO.setT0015(maxAcFc);
                    saveOrUpdateKongTiaoFuHeDO(base96DO,startDate,MAXACLOADNAME);
                }
            }


            // List<FoundationFcLoadHisMonthDO> foundationFcBasicLoadHisMonth = foundationFcLoadHisMonthService
           //     .getFoundationFcBasicLoadHisMonth("1", "1", new java.sql.Date(startDate.getTime()));

            //空调负荷预测数据- 系统自动计算
            if (!CollectionUtils.isEmpty(loadAcFcBasicDOS)) {
                LoadAcFcBasicDO loadAcFcBasicDO = loadAcFcBasicDOS.get(0);
                saveOrUpdateKongTiaoFuHeDO(loadAcFcBasicDO,startDate,ACLOADNAME);

//                List<BigDecimal> acFc = BasePeriodUtils
//                    .toList(loadAcFcBasicDO, Constants.LOAD_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO);
//                acFc = acFc.stream().filter(t -> t != null).collect(Collectors.toList());
//                if(!CollectionUtils.isEmpty(acFc)){
//                    BigDecimal maxAcFc = Collections.max(acFc);
//                    Base96DO base96DO = new Base96DO();
//                    base96DO.setT0015(maxAcFc);
//                    saveOrUpdateKongTiaoFuHeDO(base96DO,startDate,MAXACLOADNAME);
//                }
            }
            XxlJobLogger.log("日期"+DateUtils.date2String(startDate, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR)+","+ACLOADNAME+"存入d5000数据库成功");



            List<LoadCityFcDO> reportLoadFc = loadCityFcService.findReportLoadFc("1", CALIBER_ID, startDate, startDate);

            //发送系统负荷预测数据
            if (!CollectionUtils.isEmpty(reportLoadFc)) {
                LoadCityFcDO loadCityFcDO = reportLoadFc.get(0);
                saveOrUpdateKongTiaoFuHeDO(loadCityFcDO,startDate,PROVINCELOADNAME);

                List<BigDecimal> provinceFc = BasePeriodUtils
                    .toList(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO);
                provinceFc = provinceFc.stream().filter(t -> t != null).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(provinceFc)){
                    BigDecimal maxProvinceFc = Collections.max(provinceFc);
                    Base96DO base96DO = new Base96DO();
                    base96DO.setT0015(maxProvinceFc);
                    saveOrUpdateKongTiaoFuHeDO(base96DO,startDate,MAXPROVINCELOADNAME);
                }



            }
            XxlJobLogger.log("日期"+DateUtils.date2String(startDate, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR)+","+PROVINCELOADNAME+"存入d5000数据库成功");


            //空调负荷系统自动计算的基础负荷曲线
            if (!CollectionUtils.isEmpty(foundationLoadHisMonthDOList)) {
                FoundationLoadHisMonthDO  foundationLoadHisMonthDO = foundationLoadHisMonthDOList.get(0);
                saveOrUpdateKongTiaoFuHeDO(foundationLoadHisMonthDO,startDate,BASELOADNAME);

                List<BigDecimal> basicLoadFc = BasePeriodUtils
                    .toList(foundationLoadHisMonthDO, Constants.LOAD_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO);
                basicLoadFc = basicLoadFc.stream().filter(t -> t != null).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(basicLoadFc)){
                    BigDecimal maxBasicFc = Collections.max(basicLoadFc);
                    Base96DO base96DO = new Base96DO();
                    base96DO.setT0015(maxBasicFc);
                    saveOrUpdateKongTiaoFuHeDO(base96DO,startDate,MAXBASELOADNAME);
                }

            }
            XxlJobLogger.log("日期"+DateUtils.date2String(startDate, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR)+","+BASELOADNAME+"存入d5000数据库成功");

            startDate = DateUtils.addDays(startDate,1);
        }
        return ReturnT.SUCCESS;
    }


    public void wrapSensitivity() throws Exception{

        SettingSystemDO importDataSendSwitch =
            settingSystemService.findByFieldId(SystemConstant.IMPORTDATASHOW_SENDD5000);

        Date date = new Date();
        String dateStr = DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
        String yearStr = dateStr.substring(0, 4);
        List<AcLoadReportD5000DO> acLoadReportD5000DO = acLoadReportD5000Service
            .getAcLoadReportD5000DO("1", CALIBER_ID, yearStr, null, null, null);

        List<AcLoadReportD5000DO> collect = acLoadReportD5000DO.stream()
            .filter(t -> t.getType() >= 5 && t.getType() <= 8).collect(Collectors.toList());

        Map<Integer, List<AcLoadReportD5000DO>> listMap = null;
        if(!CollectionUtils.isEmpty(collect)){
             listMap = collect.stream()
                .collect(Collectors.groupingBy(AcLoadReportD5000DO::getType));
        }


        try {
            AcSensitivityAlgorithmDTO algorithmDTO = new AcSensitivityAlgorithmDTO();
            algorithmDTO.setUserId("1");
            algorithmDTO.setCityId("1");
            algorithmDTO.setCaliberId(CALIBER_ID);
            algorithmDTO.setDateNotIncluded(new ArrayList<>());
            algorithmDTO.setEndDate(new Date());
            algorithmDTO.setLoadType("1");
            algorithmDTO.setMax("40");
            algorithmDTO.setMin("20");
            algorithmDTO.setMinRain(new BigDecimal(0));
            algorithmDTO.setMaxRain(new BigDecimal(5));

            Calendar today = Calendar.getInstance();
            today.set(Calendar.MONTH, 2);
            today.set(Calendar.DATE, 1);
            algorithmDTO.setStartDate(today.getTime());
            algorithmDTO.setStep("0.5");
            algorithmDTO.setWeatherType("21");
            algorithmDTO.setNormalDay(true);
            algorithmDTO.setRestDay(false);
            SensitivityResult sensitivityResult = acSensitivityForecastService.doAcSensitivityAlgorithm(algorithmDTO);

            XxlJobLogger.log("调用灵敏度算法成功:"+sensitivityResult.toString());
            List<List<BigDecimal>> featuresLine = sensitivityResult.getFeaturesLine();


            //特性折线图
            Base96DO featureLineX = new Base96DO();
            Base96DO featureLineY = new Base96DO();
            Map<String, BigDecimal> lineXMap = new HashMap<>();
            Map<String, BigDecimal> lineYMap = new HashMap<>();
            for (int i = 0; i < featuresLine.size(); i++) {
                List<BigDecimal> bigDecimals = featuresLine.get(i);

                int hour = (i+1) / 4;
                int minute = (i+1) % 4;
                String field = null;
                if(hour<10){
                   field = "t0"+hour+(minute == 0?"00":""+(minute*15));
                }else {
                    field = "t"+hour+(minute == 0?"00":""+(minute*15));
                }


                BigDecimal xValue = bigDecimals.get(0);
                BigDecimal yValue = bigDecimals.get(1);
                lineXMap.put(field, xValue);
                lineYMap.put(field, yValue);
            }
            BeanMap beanMapX = BeanMap.create(featureLineX);
            beanMapX.putAll(lineXMap);

            BeanMap beanMapY = BeanMap.create(featureLineY);
            beanMapY.putAll(lineYMap);

            // importDataSendSwitch 为0时表示不展示以不补发送

            if(!CollectionUtils.isEmpty(listMap)){
                List<AcLoadReportD5000DO> acLoadReportD5000DOSX = listMap.get(5);
                List<AcLoadReportD5000DO> acLoadReportD5000DOSY= listMap.get(6);
                if(acLoadReportD5000DOSX != null && importDataSendSwitch.getValue().equals("1")){
                    saveOrUpdateKongTiaoFuHeDO(acLoadReportD5000DOSX.get(0), new Date(), FEATURE_X);
                    saveOrUpdateKongTiaoFuHeDO(acLoadReportD5000DOSY.get(0), new Date(), FEATURE_Y);
                }else {
                    wrapSensitivity("1",CALIBER_ID,9,featureLineX,yearStr);
                    wrapSensitivity("1",CALIBER_ID,10,featureLineY,yearStr);
                    saveOrUpdateKongTiaoFuHeDO(featureLineX, new Date(), FEATURE_X);
                    saveOrUpdateKongTiaoFuHeDO(featureLineY, new Date(), FEATURE_Y);
                }
            }else {
                wrapSensitivity("1",CALIBER_ID,9,featureLineX,yearStr);
                wrapSensitivity("1",CALIBER_ID,10,featureLineY,yearStr);
                saveOrUpdateKongTiaoFuHeDO(featureLineX, new Date(), FEATURE_X);
                saveOrUpdateKongTiaoFuHeDO(featureLineY, new Date(), FEATURE_Y);
            }

            //灵敏度折现
            List<SensitivityMetaData> beanList = sensitivityResult.getBeanList();

            Base96DO sensitivityX = new Base96DO();
            Base96DO sensitivityY = new Base96DO();
            Map<String, BigDecimal> sensitivityXMap = new HashMap<>();
            Map<String, BigDecimal> sensitivityYMap = new HashMap<>();

            for (int i = 0; i < beanList.size(); i++) {
                SensitivityMetaData sensitivityMetaData = beanList.get(i);
                BigDecimal xValue = sensitivityMetaData.getWeatherNorm();
                BigDecimal yValue = sensitivityMetaData.getValue();

                int hour = (i+1) / 4;
                int minute = (i+1) % 4;
                String field = null;
                if(hour<10){
                    field = "t0"+hour+(minute == 0?"00":""+(minute*15));
                }else {
                    field = "t"+hour+(minute == 0?"00":""+(minute*15));
                }
                sensitivityXMap.put(field, xValue);
                sensitivityYMap.put(field, yValue);
            }

            BeanMap sensitivityMapX = BeanMap.create(sensitivityX);
            sensitivityMapX.putAll(sensitivityXMap);

            BeanMap sensitivityMapY = BeanMap.create(sensitivityY);
            sensitivityMapY.putAll(sensitivityYMap);


            if(!CollectionUtils.isEmpty(listMap)){
                List<AcLoadReportD5000DO> acLoadReportD5000DOSX = listMap.get(7);
                List<AcLoadReportD5000DO> acLoadReportD5000DOSY= listMap.get(8);
                if(acLoadReportD5000DOSX != null && importDataSendSwitch.getValue().equals("1")){
                    saveOrUpdateKongTiaoFuHeDO(acLoadReportD5000DOSX.get(0), new Date(), SENSITIVITY_X);
                    saveOrUpdateKongTiaoFuHeDO(acLoadReportD5000DOSY.get(0), new Date(), SENSITIVITY_Y);
                }else {
                    wrapSensitivity("1",CALIBER_ID,11,sensitivityX,yearStr);
                    wrapSensitivity("1",CALIBER_ID,12,sensitivityY,yearStr);
                    saveOrUpdateKongTiaoFuHeDO(sensitivityX, new Date(), SENSITIVITY_X);
                    saveOrUpdateKongTiaoFuHeDO(sensitivityY, new Date(), SENSITIVITY_Y);
                }
            }else {
                wrapSensitivity("1",CALIBER_ID,11,sensitivityX,yearStr);
                wrapSensitivity("1",CALIBER_ID,12,sensitivityY,yearStr);
                saveOrUpdateKongTiaoFuHeDO(sensitivityX, new Date(), SENSITIVITY_X);
                saveOrUpdateKongTiaoFuHeDO(sensitivityY, new Date(), SENSITIVITY_Y);

            }

        }catch (Exception e){
            XxlJobLogger.log(e.getMessage());
            e.printStackTrace();
        }





    }



    void wrapSensitivity(String cityId, String caliberId, Integer dateType,Base96DO base96DO,String yearStr)
        throws Exception {
        List<AcLoadReportD5000DO> acLoadReportD5000DOS = acLoadReportD5000Service
            .getAcLoadReportD5000DO(cityId, caliberId, yearStr, null, dateType, null);
        Map<String, BigDecimal> hashMap = BasePeriodUtils
            .toMap(base96DO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(acLoadReportD5000DOS)) {
            //创建
            BasePeriod96VO basePeriod96VO = new BasePeriod96VO();
            AcLoadReportD5000DO acLoadReportD5000DO = new AcLoadReportD5000DO();
            acLoadReportD5000DO.setId(UUID.randomUUID().toString().replace("-", "").toLowerCase());
            acLoadReportD5000DO.setCaliberId(caliberId);
            acLoadReportD5000DO.setCityId(cityId);
            acLoadReportD5000DO.setType(dateType);
            acLoadReportD5000DO.setYear(yearStr);

            BasePeriodUtils.setAllFiled(basePeriod96VO, hashMap);
            List<BigDecimal> bigDecimals = BasePeriodUtils
                .toList(basePeriod96VO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
            StringBuilder sb = new StringBuilder();
            for(int i=0;i<bigDecimals.size();i++){
                BigDecimal bigDecimal = bigDecimals.get(i);
                if(StringUtils.isEmpty(bigDecimal)){continue;}
                String bigDecimalStr = bigDecimal.toString();
                sb.append(bigDecimalStr).append(",");
            }
            String str = null;
            if(sb.toString().endsWith(",")){
                String s = sb.toString();
                str = s.substring(0, s.length() - 1);
            }else {
                str = sb.toString();
            }
            acLoadReportD5000DO.setData(str);
            acLoadReportD5000DO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            acLoadReportD5000DO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            acLoadReportD5000Service.doSave(acLoadReportD5000DO);
        } else {
            //更新
            AcLoadReportD5000DO acLoadReportD5000DO = acLoadReportD5000DOS.get(0);
            acLoadReportD5000DO.setUpdatetime(new Timestamp(System.currentTimeMillis()));

            BasePeriod96VO basePeriod96VO = new BasePeriod96VO();
            BasePeriodUtils.setAllFiled(basePeriod96VO, hashMap);
            List<BigDecimal> bigDecimals = BasePeriodUtils
                .toList(basePeriod96VO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
            StringBuilder sb = new StringBuilder();
            for(int i=0;i<bigDecimals.size();i++){
                BigDecimal bigDecimal = bigDecimals.get(i);
                //如果数据为空，表名算法出的结果中有为空的数据
                if(bigDecimal == null){
                   continue;
                }else {
                    String bigDecimalStr = bigDecimal.toString();
                    sb.append(bigDecimalStr).append(",");
                }
            }
            String str = null;
            if(sb.toString().endsWith(",")){
                String s = sb.toString();
                str = s.substring(0, s.length() - 1);
            }else {
                str = sb.toString();
            }
            acLoadReportD5000DO.setData(str);
            acLoadReportD5000Service.doUpdate(acLoadReportD5000DO);
        }
    }




   public KongTiaoFuHeDO saveOrUpdateKongTiaoFuHeDO(Base96DO base96DO,Date date,String name) {
        Map<String, BigDecimal> decimalMap = BasePeriodUtils
            .toMap(base96DO, Constants.LOAD_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO);

        LambdaQueryWrapper<KongTiaoFuHeDO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(KongTiaoFuHeDO::getMdate, date);
        queryWrapper.eq(KongTiaoFuHeDO::getName, name);
        List<KongTiaoFuHeDO> list = kongTiaoFuHeService.list(queryWrapper);
        KongTiaoFuHeDO kongTiaoFuHeDO = null;
        if (CollectionUtils.isEmpty(list)) {
            kongTiaoFuHeDO = new KongTiaoFuHeDO();
            kongTiaoFuHeDO.setSysDate(new Timestamp(System.currentTimeMillis()));
            kongTiaoFuHeDO.setName(name);
            kongTiaoFuHeDO.setMdate(date);
            kongTiaoFuHeDO.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
            XxlJobLogger.log("查询到对方数据库中不存在数据");
        } else {
            kongTiaoFuHeDO = list.get(0);
            kongTiaoFuHeDO.setSysDate(new Timestamp(System.currentTimeMillis()));
            XxlJobLogger.log("查询到对方数据库中存在数据:"+kongTiaoFuHeDO.toString());
        }


        Map<String, BigDecimal> map = new HashMap<>();
        for (Map.Entry<String, BigDecimal> entry : decimalMap.entrySet()) {
            // t0000->h00
            String field = entry.getKey();
            BigDecimal value = entry.getValue();
            String hour = field.substring(1, 3);
            String minute = field.substring(3, 5);
            int i = (Integer.valueOf(hour) * 4 + Integer.valueOf(minute) / 15)-1;
            if (i < 10) {
                map.put("h0" + i, value);
            } else {
                map.put("h" + i, value);
            }
        }
        BeanMap beanMap = BeanMap.create(kongTiaoFuHeDO);
        beanMap.putAll(map);

       if (CollectionUtils.isEmpty(list)) {
           kongTiaoFuHeService.save(kongTiaoFuHeDO);
       } else {
           kongTiaoFuHeService.updateById(kongTiaoFuHeDO);
       }

       return kongTiaoFuHeDO;
    }

}