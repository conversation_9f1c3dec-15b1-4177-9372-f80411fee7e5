package com.tsintergy.lf.jobhandler;


import com.tsieframework.boot.autoconfigure.TsieBootApplication;
import com.ulisesbocchio.jasyptspringboot.environment.StandardEncryptableEnvironment;
import java.io.IOException;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.builder.SpringApplicationBuilder;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @date 2020-02-18 11:00:43
 */
@TsieBootApplication
//@EnableDubbo
public class JobBaseApplication {

    public static void main(String[] args) throws IOException {
        new SpringApplicationBuilder()
            .environment(new StandardEncryptableEnvironment())
            .sources(JobBaseApplication.class)
            .run(args);
        System.in.read();
    }
}