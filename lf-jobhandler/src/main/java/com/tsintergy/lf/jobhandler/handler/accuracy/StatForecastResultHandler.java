/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  gss Date:  2020/3/5 15:40 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.jobhandler.handler.accuracy;

import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.lf.jobhandler.common.AbstractBaseHandler;
import com.tsintergy.lf.jobhandler.common.ParamDate;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CaliberDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsAccuracyLoadCityMonthHisService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsAccuracyLoadCityYearHisService;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsAccuracyLoadCityMonthHisDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsAccuracyLoadCityYearHisDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.ForecastResultStatService;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAccuracyDayService;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportAccuracyDayDO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 统计算法的预测结果
 * 0 36 1 * * ?
 *
 * <AUTHOR>
 * @create 2020/3/5
 * @since 1.0.0
 */
@Component
@JobHandler("statForecastResultHandler")
@Slf4j
public class StatForecastResultHandler extends AbstractBaseHandler {

    @Autowired
    private ForecastResultStatService forecastResultStatService;

    @Autowired
    CityService cityService;

    @Autowired
    CaliberService caliberService;

    @Autowired
    private ReportAccuracyDayService reportAccuracyDayService;


    @Autowired
    StatisticsAccuracyLoadCityMonthHisService statisticsAccuracyLoadCityMonthHisService;

    @Autowired
    StatisticsAccuracyLoadCityYearHisService statisticsAccuracyLoadCityYearHisService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        Date startDate = DateUtils.addDays(new Date(), -1);
        ParamDate paramDate = resolveJobParam(s, startDate, 0);
        startDate = paramDate.getStartDate();
        Date endDate = paramDate.getEndDate();
        try {
            XxlJobLogger.log("开始统计算法的预测结果，开始时间为:{},结束时间：{}", startDate, endDate);
            forecastResultStatService.statForecastResult(null, null, null, startDate, endDate);
            XxlJobLogger.log("统计算法的预测结果执行成功啦!");
        } catch (Exception e) {
            XxlJobLogger.log("执行异常，异常为{}", e);
            throw new BusinessException(e.getMessage(), e.toString());
        }


        try{
            List<ReportAccuracyDayDO> reportAccuracyDayVOS = new ArrayList<>();
            List<CityDO> cityIds = cityService.findAllCitys();
            List<CityDO> dos = new ArrayList<>();
            for (CityDO cityDO : cityIds) {
                CityDO cityById = cityService.findCityById(cityDO.getId());
                dos.add(cityById);
            }

            List<CaliberDO> allCalibers = caliberService.findAllCalibers();
            for (CaliberDO caliberDO : allCalibers) {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                String date1 = formatter.format(startDate);
                String date2 = formatter.format(endDate);
                for (CityDO cityDO  : cityIds) {
                    reportAccuracyDayVOS
                            .addAll(reportAccuracyDayService.doStatSaveReportAccuracy(cityDO.getId(), startDate, endDate,caliberDO.getId()));
                }
                try {
                    List<StatisticsAccuracyLoadCityMonthHisDO> monthVOList = statisticsAccuracyLoadCityMonthHisService
                            .getReportMonthAccuracy(dos, caliberDO.getId(), date1.substring(0, 7), date2.substring(0, 7));
                    statisticsAccuracyLoadCityMonthHisService.doSaveOrUpdate(monthVOList);
                } catch (Exception e) {
                    XxlJobLogger.log("统计月负荷准确率异常...", e);
                    e.printStackTrace();
                }

                try {
                    List<StatisticsAccuracyLoadCityYearHisDO> yearVOList = statisticsAccuracyLoadCityYearHisService
                            .getReportYearAccuracy(dos, caliberDO.getId(), date1.substring(0, 4), date2.substring(0, 4));
                    statisticsAccuracyLoadCityYearHisService.doSaveOrUpdate(yearVOList);
                } catch (Exception e) {
                    XxlJobLogger.log("统计年负荷准确率异常...", e);
                    e.printStackTrace();
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return ReturnT.SUCCESS;
    }
}
