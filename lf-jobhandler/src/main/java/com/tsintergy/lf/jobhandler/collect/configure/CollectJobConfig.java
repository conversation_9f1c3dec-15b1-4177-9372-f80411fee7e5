package com.tsintergy.lf.jobhandler.collect.configure;

import com.tsintergy.lf.jobhandler.collect.common.D5000CityHelper;
import com.tsintergy.lf.jobhandler.properties.JobHandlerProperties;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @date 2020-02-18 11:00:43
 */
@EnableConfigurationProperties(JobHandlerProperties.class)
@Configuration
public class CollectJobConfig {

    @Bean
    public D5000CityHelper d5000CityHelper() {
        return new D5000CityHelper();
    }

}
