package com.tsintergy.lf.jobhandler.handler.algorithm;


import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.forecast.api.AutoForecastService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceimpl.common.impl.CoreConfigInfo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;

/**
 * 模型融合预测算法 (综合模型算法）
 * 0 0 8 * *
 */
@Component
@JobHandler("modelFusionAlgorithmHandler")
@Slf4j
public class ModelFusionAlgorithmHandler extends IJobHandler {

    @Autowired
    private ExecutorService fixThreadPoolExecutor;

    @Autowired
    private CityService cityService;

    @Autowired
    private CaliberService caliberService;

    @Autowired
    private CoreConfigInfo coreConfigInfo;

    @Autowired
    private AutoForecastService autoForecastService;

    @Autowired
    private SettingSystemService settingSystemService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        Date startDate = DateUtils.addDays(new Date(), 1);
        //默认的是一次预测三天
        Integer days = 9;
        Date endDate = DateUtils.addDays(startDate, days - 1);

        //添加预测批次，本次预测放在同一文件夹下面
        List<AlgorithmEnum> enums = new ArrayList<>();
        enums.add(AlgorithmEnum.COMPREHENSIVE_MODEL);
        if (StringUtils.isNotEmpty(s)) {
            String[] split = s.split(",");
            String startDateStr = split[0];
            String endDateStr = split[1];
            startDate = DateUtils.string2Date(startDateStr, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
            endDate = DateUtils.string2Date(endDateStr, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
        }
        autoForecastService.autoModelFusionforecast(Constants.PROVINCE_ID, Constants.CALIBER_QUAN, startDate, endDate);
        return ReturnT.SUCCESS;
    }
}
