package com.tsintergy.lf.jobhandler.handler.algorithm; /**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  gss Date:  2020/3/5 15:40 History:
 * <author> <time> <version> <desc>
 */


import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.lf.core.constants.SystemConstant;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.jobhandler.common.AbstractBaseHandler;
import com.tsintergy.lf.jobhandler.common.ParamDate;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CaliberDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.AutoForecastService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingSystemDO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 启动节假日预测算法定时任务 定时任务需求： 节假日的定时任务需要放在正常日预测定时任务之前（节假日期间上报节假日算法） 节假日算法在放假前一天自动启动，节假日期间仍启动对剩余节假日进行预测 例如： 9月30日预测 10月1日-7日；
 * 10月1日预测 10月2日-7日； 10月6日预测 10月7日 10月7日停止节假日预测过程。
 * 0 0 6 * * ?
 */
@Component
@JobHandler("holidayAlgorithmHandler")
@Slf4j
public class HolidayAlgorithmHandler extends AbstractBaseHandler {

    @Autowired
    private ExecutorService fixThreadPoolExecutor;

    @Autowired
    private CityService cityService;

    @Autowired
    private CaliberService caliberService;

    @Autowired
    private AutoForecastService autoForecastService;

    @Autowired
    private HolidayService holidayService;

    @Autowired
    private SettingSystemService settingSystemService;


    @Override
    public ReturnT<String> execute(String s) throws Exception {
        //判断是否启动节假日预测
        ParamDate paramDate = judgeIfStartHoliday(s, new Date());
        if (paramDate != null && paramDate.getStartDate() != null) {
            Date startDate = paramDate.getStartDate();
            Date endDate = paramDate.getEndDate();
            List<CityDO> cityVOS = cityService.findAllCitys();
            String uid = getUid();
            List<CaliberDO> caliberVOS = caliberService.findAllCalibers();
            //1 所有城市
            for (CityDO cityVO : cityVOS) {
                for (CaliberDO caliberVO : caliberVOS) {
                        try {
                        XxlJobLogger.log("节假日算法开始预测，城市id为：{},开始时间为：{}，结束时间为：{}", cityVO.getId(), startDate, endDate);
                        autoForecastService.doForecastHoliday(uid, cityVO.getId(), caliberVO.getId(), startDate, endDate);
                    } catch (Exception e) {
                        e.printStackTrace();
                        XxlJobLogger.log(e.getMessage());
                    }
                }
            }
            XxlJobLogger.log("节假日算法预测执行成功！");
        }
        return ReturnT.SUCCESS;
    }


    protected String getUid() {
        String uid = DateTime.now().toString("yyyyMMddHHmmssSSS");
        return uid;
    }


    /**
     * 判断是否启动节假日算法
     *
     * @param srcDate
     * @return
     */
    private ParamDate judgeIfStartHoliday(String param, Date srcDate) throws Exception {
       Date date=  DateUtil.getFormatDate(srcDate);
        ParamDate paramDate = new ParamDate();
        if (StringUtils.isNotEmpty(param)) {
            paramDate = resolveJobParam(param, null, null);
            return paramDate;
        }
        SettingSystemDO settingSystemDO = settingSystemService.findByFieldId(SystemConstant.HOLIDAY_DAY);
        //默认是节假日前一天开始预测
        Integer advanceDay = 1;
        if (settingSystemDO != null) {
            advanceDay = Integer.valueOf(settingSystemDO.getValue());
        }
        Boolean holidayFlag = false;
        List<HolidayDO> holidayVOS = holidayService.getAllHolidayVOS();
        Map<Date, HolidayDO> map = new HashMap<>();
        //把所有放假的日期转map
        for (HolidayDO vo : holidayVOS) {
            List<Date> dates = DateUtil.getListBetweenDay(new Date(vo.getStartDate().getTime()), new Date(vo.getEndDate().getTime()));
            for (Date between : dates) {
                map.put(between, vo);
            }
        }
        //例如前五天需要预测出节假日，2月11日是春节，2月8日预测2月11到2月18日，2月9日预测2月11日到2月18日
        for (int i = advanceDay; i >= 0; i--) {
            Date holidayFcDate = DateUtils.addDays(date, i);
            if (map.containsKey(holidayFcDate)) {
                holidayFlag = true;
                HolidayDO holidayDO = map.get(holidayFcDate);
                paramDate.setStartDate(holidayDO.getStartDate());
                paramDate.setEndDate(DateUtils.addDays(holidayDO.getEndDate(), 1));
                break;
            }
        }
        //需要启动预测
        if (holidayFlag) {
            Date startDate = paramDate.getStartDate();
            if (date.after(startDate) || date.equals(startDate)) {
                paramDate.setStartDate(DateUtils.addDays(date, 1));
            }
        }
        return paramDate;
    }
}
