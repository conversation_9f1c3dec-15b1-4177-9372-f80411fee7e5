/**
 * Copyright (C), 2015‐2021, 北京清能互联科技有限公司 Author:  gss Date:  2021/2/3 13:36 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.jobhandler.handler.algorithm;

import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.SystemConstant;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CaliberDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.AutoForecastService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingSystemDO;
import com.tsintergy.lf.serviceimpl.common.impl.CoreConfigInfo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * lgb算法单独跑  只跑cityId =1 全网   时间 早七点半
 *
 * <AUTHOR>
 * @create 2021/2/3
 * @since 1.0.0
 */
@Component
@JobHandler("lgbForecastEveryDayHandler")
@Slf4j
public class LgbForecastEveryDayHandler extends IJobHandler {

    @Autowired
    private CityService cityService;

    @Autowired
    private CaliberService caliberService;

    @Autowired
    private SettingSystemService settingSystemService;

    @Autowired
    private CoreConfigInfo coreConfigInfo;

    @Autowired
    private AutoForecastService forecastService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        //默认预测明天
        Date startDate = DateUtils.addDays(new Date(), 1);
        //默认的是一次预测三天
        Integer days = 3;
        //查询用户自定义的正常日天数
        SettingSystemDO reportVOS = settingSystemService.findByFieldId(SystemConstant.FORECAST_DAY);
        if (reportVOS != null) {
            days = Integer.parseInt(reportVOS.getValue());
        }
        Date endDate = DateUtils.addDays(startDate, days - 1);
        //添加预测批次，本次预测放在同一文件夹下面
        Integer forecastType = Integer.parseInt(coreConfigInfo.getRuntimeParam("task.forecast.type"));
        List<AlgorithmEnum> enums = new ArrayList<>();
        //补充xg算法--lgb决策树
        enums.add(AlgorithmEnum.REPLENISH_LGB);
        if(StringUtils.isNotEmpty(s)){
            String[] split = s.split(",");
            String startDateStr = split[0];
            String endDateStr = split[1];
            startDate = DateUtils.string2Date(startDateStr, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
            endDate = DateUtils.string2Date(endDateStr, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
        }
        List<CityDO> cityVOS = cityService.findAllCitys();
        List<CaliberDO> allCalibers = caliberService.findAllCalibers();
        for(CaliberDO caliberDO:allCalibers) {
            for (CityDO cityDO : cityVOS) {
                try {
                    forecastService
                        .autoForecast(forecastType, DateUtil.getUid(), cityDO.getId(), caliberDO.getId(),
                            startDate, endDate, enums, null, null, null);
                }catch (Exception e){
                    XxlJobLogger.log(e.getMessage());
                    e.printStackTrace();
                }
            }
        }
        XxlJobLogger.log("LGB预测算法预测执行成功！");
        return ReturnT.SUCCESS;
    }
}
