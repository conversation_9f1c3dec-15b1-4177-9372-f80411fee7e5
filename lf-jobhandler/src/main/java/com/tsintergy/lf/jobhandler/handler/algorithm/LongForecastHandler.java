/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.jobhandler.handler.algorithm;


import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.api.LongForecastAlgorithmService;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CaliberDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * Description: 中长期预测定时任务 <br>
 * 每月15日、22日、30日（当月没有30日时，该月最后一天启动预测）
 * @Author: <EMAIL>
 * @Date: 2022/5/16 15:29
 * @Version: 1.0.0
 */
@Component
@JobHandler("longForecastHandler")
@Slf4j
public class LongForecastHandler extends IJobHandler {


    @Autowired
    private CityService cityService;

    @Autowired
    private CaliberService caliberService;

    @Autowired
    LongForecastAlgorithmService longForecastService;

    public static final String FEBRUARY = "02";

    public static final int  TWENTY_EIGHT = 28;
    @Override
    public ReturnT<String> execute(String s) throws Exception {

        Date startDate;
        Date endDate;
        if(StringUtils.isNotEmpty(s)){
            String[] split = s.split(",");
            String startDateStr = split[0];
            String endDateStr = split[1];
            startDate = DateUtils.string2Date(startDateStr, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
            endDate = DateUtils.string2Date(endDateStr, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
        }else{
            //自动预测时进行判断
            if (!checkDate()){
                return ReturnT.SUCCESS;
            }
            Date today = DateUtil.getCureDate();
            //本月最后一天
            Date monthLastDay = DateUtil.getLastDayOfMonth(today);
            //下个月的第一天
            startDate = DateUtil.getMoveDay(monthLastDay, 1);
            //下个月的最后一天
            endDate = DateUtil.getLastDayOfMonth(startDate);
        }

        List<CityDO> cityVOS = cityService.findAllCitys();
        List<CaliberDO> caliberVOS = caliberService.findAllCalibers();
        //1 所有城市
        for (CityDO cityVO : cityVOS) {
            for (CaliberDO caliberDO : caliberVOS){
                XxlJobLogger
                    .log("中长期预测算法,城市id为:{},口径id为:{}，开始日期：{}，结束日期：{}" , cityVO.getId() ,caliberDO.getId(),startDate,endDate);
                longForecastService.statsLongForecast(caliberDO.getId(),cityVO.getId(),startDate,endDate);
                XxlJobLogger.log("中长期预测算法预测执行成功啦！");
            }
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 检测当天是否为2月28号
     * @return 返回true 则为2月28号 或者不是28号
     */
    private boolean checkDate() {
        Calendar calendar = Calendar.getInstance();
        Date localDate= new Date();
        //localDate可以为默认为当天 如需获取指定日期，只需将指定的日期传入即可
        calendar.setTime(localDate);
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        String monthByDate = DateUtil.getMonthByDate(localDate);
        String month = monthByDate.substring(monthByDate.length()-2,monthByDate.length());
        if (day==TWENTY_EIGHT){
            if (FEBRUARY.equals(month)){
                return true;
            }else{
                return false;
            }
        }
        return true;
    }
}