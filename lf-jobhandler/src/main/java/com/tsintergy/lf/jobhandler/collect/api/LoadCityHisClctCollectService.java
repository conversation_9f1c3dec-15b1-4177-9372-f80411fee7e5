/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangchen Date:  2019/11/12 15:19 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.jobhandler.collect.api;

import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/11/12 
 * @since 1.0.0
 */
public interface LoadCityHisClctCollectService {

    void collectLoadCityHisClct(Date date, boolean realTime, List<String> idList) throws Exception;

    void collectLoadCityHisMinuteClct(Date date, boolean realTime, List<String> idList,int timespan) throws Exception;

}
