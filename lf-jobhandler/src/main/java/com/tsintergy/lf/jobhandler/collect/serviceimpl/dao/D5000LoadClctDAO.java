/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangchen Date:  2019/11/12 15:46 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.jobhandler.collect.serviceimpl.dao;

import com.tsieframework.core.base.dao.mybatis.BaseEntityDAO;
import com.tsieframework.core.component.datasource.dynamic.annotation.DataSource;
import com.tsintergy.lf.jobhandler.collect.pojo.D5000LoadClctDO;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2022/02/25
 * @since 1.0.0
 */
@DataSource("D5000")
@Repository
public interface D5000LoadClctDAO extends BaseEntityDAO<D5000LoadClctDO> {

    /**
     * 通过时间查询负荷，并正序
     * @param tableName
     * @param column
     * @param startTime
     * @param endTime
     * @return
     */
    List<D5000LoadClctDO> findLoadClctDOOrderByTimeAsc(@Param("tableName") String tableName,
        @Param("column") String column, @Param("startTime") String startTime, @Param("endTime") String endTime);

    List<D5000LoadClctDO> findSmallPowerLoadClctDO(@Param("tableName") String tableName, @Param("column") String column,
        @Param("startTime") String startTime, @Param("endTime") String endTime);

}
