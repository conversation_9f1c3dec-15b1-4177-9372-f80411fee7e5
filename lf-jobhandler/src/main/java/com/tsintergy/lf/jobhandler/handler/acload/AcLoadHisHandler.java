package com.tsintergy.lf.jobhandler.handler.acload;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.jobhandler.common.AbstractBaseHandler;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.AirConditionerLoadBaseManageService;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.FoundationLoadHisMonthService;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.LoadAcHisBasicService;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.LoadSolutionManageEntityService;
import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.FoundationLoadHisMonthDO;
import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.LoadAcHisBasicDO;
import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.LoadSolutionManageDO;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CaliberDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.common.enumeration.DateType2;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;


@Component
@JobHandler("acLoadHisHandler")
@Slf4j
public class AcLoadHisHandler extends AbstractBaseHandler {

    @Autowired
    FoundationLoadHisMonthService foundationLoadHisMonthService;

    @Autowired
    LoadAcHisBasicService loadAcHisBasicService;

    @Autowired
    LoadCityHisService loadCityHisService;

    @Autowired
    CaliberService caliberService;

    @Autowired
    CityService cityService;

    @Autowired
    HolidayService holidayService;

    @Autowired
    AirConditionerLoadBaseManageService airConditionerLoadBaseManageService;

    @Autowired
    LoadSolutionManageEntityService loadSolutionManageEntityService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        List<CityDO> allCitys = cityService.findAllCitys();
        List<CaliberDO> allCalibers = caliberService.findAllCalibers();

        if(StringUtils.isEmpty(s)) {
            Date date = DateUtils.addDays(new Date(), -1);
            Calendar now = Calendar.getInstance();
            now.setTime(date);
            int year = now.get(Calendar.YEAR);
            int month = now.get(Calendar.MONTH)+1;
//            for (CityDO cityDO : allCitys) {
//                for (CaliberDO caliberDO : allCalibers) {
//                    wrapperAcLoad(date, year, month, cityDO, caliberDO);
//                }
//            }
            for (CityDO cityDO : allCitys) {
                for (CaliberDO caliberDO : allCalibers) {
                    if (cityDO.getId().equals("1") && caliberDO.getId().equals("1")) {
                        wrapperAcLoad(date, year, month, cityDO, caliberDO);
                        break;
                    }
                }
            }
        }else{
            String[] split = s.split(",");
            String startDateStr = split[0];
            String endDateStr = split[1];
            Date startDate = DateUtils.string2Date(startDateStr, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
            Date endDate = DateUtils.string2Date(endDateStr, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);

            while (startDate.before(endDate)) {
                Calendar instance = Calendar.getInstance();
                instance.setTime(startDate);
                int year = instance.get(Calendar.YEAR);
                int month = instance.get(Calendar.MONTH)+1;
//                for (CityDO cityDO : allCitys) {
//                    for (CaliberDO caliberDO : allCalibers) {
//                        wrapperAcLoad(startDate,year,month,cityDO,caliberDO);
//                    }
//                }
                for (CityDO cityDO : allCitys) {
                    for (CaliberDO caliberDO : allCalibers) {
                        if (cityDO.getId().equals("1") && caliberDO.getId().equals("1")) {
                            wrapperAcLoad(startDate, year, month, cityDO, caliberDO);
                            break;
                        }
                    }
                }
                startDate = DateUtils.addDays(startDate,1);
            }
        }
        return ReturnT.SUCCESS;
    }

    private void wrapperAcLoad(Date date, int year, int month, CityDO cityDO, CaliberDO caliberDO) throws Exception {
        List<LoadCityHisDO> loadCityHisDOS = loadCityHisService.findLoadCityHisDOS(cityDO.getId(), date,
            date, caliberDO.getId());
        if (CollectionUtils.isEmpty(loadCityHisDOS)) {
            XxlJobLogger.log(
                caliberDO.getName() + "," + cityDO.getCity() + "," + DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR) + "实际负荷为空无法生产空调负荷实际数据");
        } else {
            LoadCityHisDO loadCityHisDO = loadCityHisDOS.get(0);
            Calendar instance = Calendar.getInstance();
            instance.setTime(date);
            String yearStr = String.valueOf(year);
            String monthStr = String.valueOf(month < 10 ? "0"+ month : month);

            /*Boolean workingDay = DateUtil.isWorkingDay(date.getTime());

            //判断前一天是工作日还是休息日
            int type = 1;
            if (!workingDay) {
                type = 2;
            }*/

            // 判断四种日期类型
            Integer type = this.getDateType(date);
            List<FoundationLoadHisMonthDO> foundationLoadHisMonth = foundationLoadHisMonthService.getFoundationLoadHisMonth(
                cityDO.getId(), caliberDO.getId(), yearStr, monthStr,type);
            if (CollectionUtils.isEmpty(foundationLoadHisMonth)) {
                XxlJobLogger.log(
                    caliberDO.getName() + "," + cityDO.getCity() + "," + DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR) + "月基础负荷曲线没有生成");
            } else {
                FoundationLoadHisMonthDO foundationLoadHisMonthDO = foundationLoadHisMonth.get(0);
                List<BigDecimal> foundationLoaBigDecimals = BasePeriodUtils.toList(foundationLoadHisMonthDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
                List<BigDecimal> loaBigDecimals = BasePeriodUtils.toList(loadCityHisDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
                List<BigDecimal> result = new ArrayList<>();
                for (int i = 0; i < loaBigDecimals.size(); i++) {
                    BigDecimal load = loaBigDecimals.get(i);
                    BigDecimal found = foundationLoaBigDecimals.get(i);
                    if (load != null && found != null) {
                        BigDecimal difference = load.subtract(found);
                        result.add(difference.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : difference);
                    } else {
                        result.add(null);
                    }
                }
                // 减去基础负荷之后还需要减去（厂用电+网损）
                int season = DateUtil.getSeason(Integer.parseInt(monthStr));
                DateType2 dateType = DateType2.fromId(season);
                LoadSolutionManageDO loadSolutionManageDO = loadSolutionManageEntityService.findOneByQueryWrapper(
                        JpaWrappers.<LoadSolutionManageDO>lambdaQuery()
                                .eq(LoadSolutionManageDO::getCityId, cityDO.getId())
                                .eq(LoadSolutionManageDO::getCaliberId, caliberDO.getId())
                                .eq(LoadSolutionManageDO::getSolutionYear, yearStr)
                                .eq(LoadSolutionManageDO::getDateType, dateType)
                                .eq(LoadSolutionManageDO::getSeason, String.valueOf(season))
                                .eq(LoadSolutionManageDO::getEnableCurve, true)
                );
                if (loadSolutionManageDO != null) {
                    List<BigDecimal> bigDecimals = airConditionerLoadBaseManageService.queryBaseLoadCurveData(loadSolutionManageDO.getId(), date);
                    if (!CollectionUtils.isEmpty(bigDecimals)) {
                        for (int i = 0; i < result.size(); i++) {
                            BigDecimal load = result.get(i);
                            BigDecimal found = bigDecimals.get(i);
                            if (load != null && found != null) {
                                BigDecimal difference = load.subtract(found);
                                result.set(i, difference.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : difference);
                            }
                        }
                    }
                }
                Map<String, BigDecimal> stringBigDecimalMap = ColumnUtil.listToMap(result, Constants.LOAD_CURVE_START_WITH_ZERO);
                List<LoadAcHisBasicDO> loadAcHisBasicDOS = loadAcHisBasicService.getloadAcHisBasicDOList(cityDO.getId(), caliberDO.getId(),
                    date, date);
                if (CollectionUtils.isEmpty(loadAcHisBasicDOS)) {
                    LoadAcHisBasicDO loadAcHisBasicDO = new LoadAcHisBasicDO();
                    loadAcHisBasicDO.setId(UUID.randomUUID().toString().replace("-", "").toLowerCase());
                    loadAcHisBasicDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
                    loadAcHisBasicDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                    loadAcHisBasicDO.setDate(new java.sql.Date(date.getTime()));
                    loadAcHisBasicDO.setCaliberId(caliberDO.getId());
                    loadAcHisBasicDO.setCityId(cityDO.getId());
                    BasePeriodUtils.setAllFiled(loadAcHisBasicDO, stringBigDecimalMap);
                    loadAcHisBasicService.doSave(loadAcHisBasicDO);
                } else {
                    LoadAcHisBasicDO loadAcHisBasicDO = loadAcHisBasicDOS.get(0);
                    loadAcHisBasicDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                    BasePeriodUtils.setAllFiled(loadAcHisBasicDO, stringBigDecimalMap);
                    loadAcHisBasicService.doUpdate(loadAcHisBasicDO);
                }
            }
        }
    }

    private Integer getDateType(Date date) throws Exception {
        String year = DateUtil.getDateToStrFORMAT(date, "yyyy-MM-dd").split("-")[0];
        // 查询节假日
        List<Date> holidays = holidayService.findHolidayByYear(year);
        Set<Date> dateSet = new HashSet<>(holidays.stream().distinct().collect(Collectors.toList()));
        // 查询调休日期
        List<Date> offDates = holidayService.findHolidayByYearAndOffDates(year).stream().distinct().collect(Collectors.toList());
        // 节假日
        if (dateSet.contains(date)) {
            return DateType2.REST.getId();
        }
        // 周六
        boolean weekendOrSaturday = DateUtil.isWeekendOrSaturday(date, 1);
        if (weekendOrSaturday && !offDates.contains(date)) {
            return DateType2.SATURDAY.getId();
        }
        // 周末
        boolean weekendOrSaturday1 = DateUtil.isWeekendOrSaturday(date, 2);
        if (weekendOrSaturday1 && !offDates.contains(date)) {
            return DateType2.WEEKEND.getId();
        }
        // 工作日（除去节假日、周六周末、包含调休日的）
        return DateType2.WORKDAY.getId();
    }
}
