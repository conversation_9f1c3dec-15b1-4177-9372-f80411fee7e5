package com.tsintergy.lf.jobhandler.collect.util;

import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.BasePeriod96VO;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.core.util.PeriodDataUtil;
import com.tsintergy.lf.jobhandler.collect.pojo.D5000LoadClctDO;
import groovy.util.logging.Slf4j;
import java.util.Date;
import java.util.List;

@Slf4j
public class PeriodDataUtils extends PeriodDataUtil{

    public static void assignValue(List<D5000LoadClctDO> d5000LoadClctDOS, BasePeriod96VO basePeriod96VO, java.sql.Date date) {
        PeriodDataUtils.assignValue(d5000LoadClctDOS,basePeriod96VO,LoadEnum.POINT96,date);
    }

    /**
     * 如果采集的时间小于00:30，补采昨天
     * @return
     */
    public static  boolean collectLastDay() {
        String hour = DateUtil.getHour(new Date());
        if ("00".equals(hour)) {
            String minute = DateUtil.getMinute(new Date());
            if (30 > Integer.parseInt(minute)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 负荷赋值
     * @param d5000LoadClctDOS
     * @param basePeriod96VO
     */
    public static void assignValue(List<D5000LoadClctDO> d5000LoadClctDOS, BasePeriod96VO basePeriod96VO, LoadEnum loadEnum, java.sql.Date date) {
        Integer divisor = 15;
        if (loadEnum.equals(LoadEnum.POINT288)) {
            divisor = 5;
        }
        for (D5000LoadClctDO d5000LoadClctDO : d5000LoadClctDOS) {
            String occurTime = DateUtils.date2String(d5000LoadClctDO.getOccurTime(), DateFormatType.SIMPLE_DATE_TIME_FORMAT_STR);
            try {
                int minute = Integer.parseInt(occurTime.substring(10, 12));
                if (minute % divisor == 0) {
                    String fieldName = "t" + occurTime.substring(8, 12);
                    Date occurTemp = DateUtil.clearHHmmss(d5000LoadClctDO.getOccurTime());
                    Date dateTemp = DateUtil.clearHHmmss(date);
                    if (occurTemp.after(dateTemp)) {
                        fieldName = "t2400";
                    }
                    PeriodDataUtil.setFieldValue(basePeriod96VO, fieldName, d5000LoadClctDO.getLoad());
                }
            } catch (Exception e) {
                System.out.println("负荷赋值异常.....");
            }
        }
    }

    public enum LoadEnum {
        POINT96,
        POINT288
    }

}
