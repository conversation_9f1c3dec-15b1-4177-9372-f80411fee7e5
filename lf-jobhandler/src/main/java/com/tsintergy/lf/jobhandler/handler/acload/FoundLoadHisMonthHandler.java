package com.tsintergy.lf.jobhandler.handler.acload;

import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.lf.jobhandler.common.AbstractBaseHandler;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.FoundationLoadHisMonthService;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CaliberDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Calendar;
import java.util.Date;
import java.util.List;


@Component
@JobHandler("foundLoadHisMonthHandler")
@Slf4j
public class FoundLoadHisMonthHandler extends AbstractBaseHandler {
    @Autowired
    FoundationLoadHisMonthService foundationLoadHisMonthService;

    @Autowired
    CaliberService caliberService;

    @Autowired
    CityService cityService;


    @Override
    public ReturnT<String> execute(String s) throws Exception {
        Calendar now = Calendar.getInstance();
        int nowYear = now.get(Calendar.YEAR);
        int nowMonth = now.get(Calendar.MONTH);

        List<CityDO> allCitys = cityService.findAllCitys();
        List<CaliberDO> allCalibers = caliberService.findAllCalibers();
        if (StringUtils.isEmpty(s)) {
            //默认处理当前年的当前日期所在月的下一个月的基础负荷曲线
            int resultMonth = nowMonth+2;
//            for (CityDO cityDO : allCitys) {
//                for (CaliberDO caliberDO : allCalibers) {
//                    for (int i = 1; i <= 2; i++) {
//                        wrapperNowYearBasicLoad(caliberDO.getId(), cityDO.getId(), resultMonth, nowYear, i);
//                    }
//                }
//            }
            for (int type = 1; type <= 4; type++) {
                //补数据不用考虑下一个月，但调用的是计算是下一个月的方法，故这里月加1
                foundationLoadHisMonthService.wrapperNowYearBasicLoad("1", "1", resultMonth, nowYear, type);
            }

        } else {
            String[] split = s.split(",");
            String startDateStr = split[0];
            String endDateStr = split[1];
            Date startDate = DateUtils.string2Date(startDateStr, DateFormatType.YEAR_MONTH_STR);
            Date endDate = DateUtils.string2Date(endDateStr, DateFormatType.YEAR_MONTH_STR);

            while (startDate.before(DateUtils.addMonths(endDate,1))) {
                Calendar instance = Calendar.getInstance();
                instance.setTime(startDate);
                int year = instance.get(Calendar.YEAR);
                int month = instance.get(Calendar.MONTH);
                int resultMonth = month+1;
//                for (CityDO cityDO : allCitys) {
//                    for (CaliberDO caliberDO : allCalibers) {
//                        for (int type = 1; type <= 2; type++) {
//                            //补数据不用考虑下一个月，但调用的是计算是下一个月的方法，故这里月加1
//                            wrapperNowYearBasicLoad(caliberDO.getId(), cityDO.getId(), resultMonth, year, type);
//                        }
//                    }
//                }

                for (int type = 1; type <= 4; type++) {
                    //补数据不用考虑下一个月，但调用的是计算是下一个月的方法，故这里月加1
                    foundationLoadHisMonthService.wrapperNowYearBasicLoad("1", "1", resultMonth, year, type);
                }
                startDate = DateUtils.addMonths(startDate, 1);
            }
        }
        return ReturnT.SUCCESS;
    }
}
