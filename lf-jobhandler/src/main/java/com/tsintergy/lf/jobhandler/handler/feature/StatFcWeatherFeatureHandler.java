/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  gss Date:  2020/3/5 15:40 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.jobhandler.handler.feature;

import com.tsintergy.lf.jobhandler.common.AbstractBaseHandler;
import com.tsintergy.lf.jobhandler.common.ParamDate;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureStatService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 统计预测日气象特性
 * 0 0 3 * * ?
 *
 * <AUTHOR>
 * @create 2020/3/5
 * @since 1.0.0
 */
@Component
@JobHandler("statFcWeatherHandler")
@Slf4j
public class StatFcWeatherFeatureHandler extends AbstractBaseHandler {

    @Autowired
    private WeatherFeatureStatService weatherFeatureStatService;


    @Override
    public ReturnT<String> execute(String s) throws Exception {
        ParamDate paramDate = resolveJobParam(s, new Date(), 3);
        statFcWeatherFeature(paramDate);
        return ReturnT.SUCCESS;
    }


    public void statFcWeatherFeature(ParamDate paramDate) throws Exception {
        XxlJobLogger.log("开始统计日预测气象特性并入库，开始时间为:{},结束日期为：{}", paramDate.getStartDate(), paramDate.getEndDate());
        weatherFeatureStatService.doStatWeatherFeatureCityDayFc(null, paramDate.getStartDate(), paramDate.getEndDate());
        XxlJobLogger.log("统计成功");
    }

}
