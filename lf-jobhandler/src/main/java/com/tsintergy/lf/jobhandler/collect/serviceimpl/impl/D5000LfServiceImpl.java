/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangchen Date:  2019/11/17 16:09 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.jobhandler.collect.serviceimpl.impl;

import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.lf.jobhandler.collect.api.D5000LfService;
import com.tsintergy.lf.jobhandler.collect.common.D5000LfClctEnum;
import com.tsintergy.lf.jobhandler.collect.pojo.D5000LoadClctDO;
import com.tsintergy.lf.jobhandler.collect.serviceimpl.dao.D5000LoadClctDAO;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/11/17
 * @since 1.0.0
 */
@Slf4j
@Service("d5000LfService")
public class D5000LfServiceImpl  implements D5000LfService {

    @Autowired
    private D5000LoadClctDAO d5000LoadClctDAO;

    @Override
    public List<D5000LoadClctDO> findByData(Date date, String loadName) {
        for (D5000LfClctEnum lfClct : D5000LfClctEnum.values()) {
            //如果城市的名称和采集负荷名称相同
            if (lfClct.getLoadName().equals(loadName)) {
                return findD5000LoadClct(date, lfClct.getTableName(), lfClct.getColumn());
            }
        }
        return null;
    }


    private List<D5000LoadClctDO>  findD5000LoadClct(Date date,String tableName,String column) {
        String dateTemp = DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_STR);
        String startStr = "'" + dateTemp + " " + "00:00:000" + "'";
        String endDate = DateUtils.date2String(DateUtils.addDays(date, 1), DateFormatType.SIMPLE_DATE_FORMAT_STR);
        String endStr = "'" + endDate + " " + "00:00:000" + "'";
        tableName = "HISDB.HISDB."+ tableName;
        return d5000LoadClctDAO.findLoadClctDOOrderByTimeAsc(tableName,column,startStr,endStr);
    }

}
