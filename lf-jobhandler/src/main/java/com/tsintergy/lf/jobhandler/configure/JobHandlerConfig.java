package com.tsintergy.lf.jobhandler.configure;

import com.tsieframework.cloud.security.serviceapi.system.api.SecurityService;
import com.tsieframework.cloud.security.serviceimpl.system.impl.SecurityServiceImpl;
import com.tsieframework.core.component.cache.business.CacheService;
import com.tsintergy.lf.jobhandler.properties.JobHandlerProperties;
import org.apache.dubbo.common.threadpool.support.fixed.FixedThreadPool;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import com.tsieframework.cloud.security.core.base.properties.SecurityProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.tsieframework.cloud.security.serviceapi.system.api.SecurityService;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import org.springframework.context.annotation.Lazy;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @date 2020-02-18 11:00:43
 */
@EnableConfigurationProperties(JobHandlerProperties.class)
@Configuration
public class JobHandlerConfig implements ApplicationContextAware , BeanPostProcessor {

    ApplicationContext applicationContext;

    @Bean
    public ScheduledThreadPoolExecutor ScheduledThreadPoolExecutorService(){
        ScheduledThreadPoolExecutor scheduledThreadPoolExecutor = new ScheduledThreadPoolExecutor(5);
        return  scheduledThreadPoolExecutor;
    }


    @Bean("fixThreadPoolExecutor")
    public ExecutorService fixThreadPoolExecutorService(){
        ExecutorService executorService = Executors.newFixedThreadPool(4);
        return  executorService;
    }


    @Bean("singleExecutor")
    public ExecutorService SingleThreadPoolExecutorService(){
        return Executors.newSingleThreadExecutor();
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Bean
    public SecurityService securityService() {
        return new SecurityServiceImpl();
    }

}
