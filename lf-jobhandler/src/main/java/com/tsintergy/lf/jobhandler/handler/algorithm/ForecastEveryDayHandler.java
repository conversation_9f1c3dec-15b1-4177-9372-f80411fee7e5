/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  gss Date:  2020/3/5 15:40 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.jobhandler.handler.algorithm;

import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.lf.core.constants.SystemConstant;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.jobhandler.task.AutoForecastTask;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CaliberDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.AutoForecastService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingSystemDO;
import com.tsintergy.lf.serviceimpl.common.impl.CoreConfigInfo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 普通正常日预测算法定时任务 0 27 5,15 * * ?
 *
 * <AUTHOR>
 * @create 2020/3/5
 * @since 1.0.0
 */
@Component
@JobHandler("forecastEveryDayHandler")
@Slf4j
public class ForecastEveryDayHandler extends IJobHandler {

    @Autowired
    private ExecutorService fixThreadPoolExecutor;

    @Autowired
    private CityService cityService;

    @Autowired
    private CaliberService caliberService;

    @Autowired
    private SettingSystemService settingSystemService;

    @Autowired
    private CoreConfigInfo coreConfigInfo;

    @Autowired
    private AutoForecastService autoForecastService;


    @Override
    public ReturnT<String> execute(String s) throws Exception {
        //默认预测明天
        Date startDate = DateUtils.addDays(new Date(), 1);
        //一次预测几天 都取省会城市的设置
        CityDO provinceCity = cityService.findCityByType(1);
        //默认的是一次预测三天
        Integer days = 3;
        //查询用户自定义的正常日天数
        SettingSystemDO reportVOS = settingSystemService.findByFieldId(SystemConstant.FORECAST_DAY);
        if (reportVOS != null) {
            days = Integer.parseInt(reportVOS.getValue());
            //系统预测设置的上报起始日从明天开始
            startDate = DateUtils.addDays(new Date(), 1);
        }
        Date endDate = DateUtils.addDays(startDate, days - 1);
        List<CityDO> cityVOS = cityService.findAllCitys();
        List<CaliberDO> caliberVOS = caliberService.findAllCalibers();
        Calendar instance = Calendar.getInstance();
        int hour = instance.get(Calendar.HOUR_OF_DAY);
        if (hour == 15) {
            cityVOS.clear();
            CityDO cityById = cityService.findCityById("1");
            cityVOS.add(cityById);
        }
        XxlJobLogger.log("普通正常日预测算法开始预测：城市数量为:{}", cityVOS.size());
        //添加预测批次，本次预测放在同一文件夹下面
        String uid = DateUtil.getUid();
        Integer forecastType = Integer.parseInt(coreConfigInfo.getRuntimeParam("task.forecast.type"));
        List<AlgorithmEnum> enums = new ArrayList<>();
//        enums.add(AlgorithmEnum.FORECAST_SIMILAR);
//        enums.add(AlgorithmEnum.FORECAST_SIMILAR_OFFSET);
        enums.add(AlgorithmEnum.FORECAST_SVM);
        enums.add(AlgorithmEnum.FORECAST_SVM_LARGE);
        enums.add(AlgorithmEnum.FORECAST_XGBOOST);
        if (StringUtils.isNotEmpty(s)) {
            String[] split = s.split(",");
            String startDateStr = split[0];
            String endDateStr = split[1];
            startDate = DateUtils.string2Date(startDateStr, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
            endDate = DateUtils.string2Date(endDateStr, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
        }
        //1 所有城市
        for (CityDO cityVO : cityVOS) {
            //2 所有口径
            for (CaliberDO caliberVO : caliberVOS) {
                XxlJobLogger.log("开始预测:城市id为:{}, 口径id为：{},开始时间为：{}，结束时间为：{}", cityVO.getId(), caliberVO.getId(), startDate, endDate);
                fixThreadPoolExecutor.execute(new AutoForecastTask(forecastType, uid, cityVO.getId(),
                        caliberVO.getId(),
                        autoForecastService, startDate, endDate, enums, null, null, null));
            }
        }
        XxlJobLogger.log("普通正常日预测算法预测执行成功");
        return ReturnT.SUCCESS;
    }


}
