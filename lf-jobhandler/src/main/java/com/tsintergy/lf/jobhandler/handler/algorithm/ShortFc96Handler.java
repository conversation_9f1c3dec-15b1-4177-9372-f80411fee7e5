/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  ThinkPad Date:  2019/5/5 4:10 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.jobhandler.handler.algorithm;

import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.ParamConstants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CaliberDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.ForecastService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemData;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 15分钟间隔的超短期预测 时间：0 1,16,31,46 * * * ?
 *
 * <AUTHOR>
 * @create 2020/5/12
 * @since 1.0.0
 */
@Component
@JobHandler("shortFc96Handler")
@Slf4j
public class ShortFc96Handler extends IJobHandler {

    @Autowired
    private CityService cityService;

    @Autowired
    private CaliberService caliberService;

    @Autowired
    private ForecastService forecastService;

    @Autowired
    private SettingSystemService settingSystemService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        SystemData systemSetting = settingSystemService.getSystemSetting();
        Date date = new Date();
        List<CaliberDO> caliberVOS = caliberService.findAllCalibers();
        String hour = DateUtil.getStrHourFromDate(date);
        Integer startTimePoint = Integer
            .valueOf(String.valueOf(DateUtil
                .getTimePoint(hour, 96)
            ));
        List<CityDO> totalCity = new ArrayList<>();
        //判断超短期是否开启
        if (systemSetting.getProvinceShortFifteenSwitch().equals(ParamConstants.STRING_COMPOSITE_ON)) {
            CityDO province = cityService.findCityById(CityConstants.PROVINCE_ID);
            totalCity.add(province);
        }
        if (systemSetting.getCityShortFifteenSwitch().equals(ParamConstants.STRING_COMPOSITE_ON)) {
            List<CityDO> allCitys = cityService.findAllCitys();
            CityDO province = cityService.findCityById(CityConstants.PROVINCE_ID);
            allCitys.remove(province);
            totalCity.addAll(allCitys);
        }
        if (CollectionUtils.isEmpty(totalCity)) {
            XxlJobLogger.log("全网and地市15min超短期预测均未开启");
            return ReturnT.SUCCESS;
        }
        for (CityDO cityDO : totalCity) {
            for (CaliberDO caliberVO : caliberVOS) {
                //超短期预测
                try {
                    XxlJobLogger.log("开始预测：城市id为：{}, 口径id为:{},日期为：{}", cityDO.getId(), caliberVO.getId(), date);
                    forecastService
                            .doShortForecast(cityDO.getId(), caliberVO.getId(), date, 15, startTimePoint);
                    XxlJobLogger.log("预测成功啦");
                } catch (Exception e) {
                    XxlJobLogger.log("预测失败：{}", e);
                }
            }
        }
        return ReturnT.SUCCESS;
    }

}