/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  wangchen
 * Date:  2019/11/17 17:52
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.jobhandler.collect.common;

import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceimpl.base.dao.CityDAO;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/11/17 
 * @since 1.0.0
 */
public class D5000CityHelper {

    private static final Map<String,String> nameMap = new HashMap<String,String>(){
        {
            put("北碚", D5000LfClctEnum.BEI_BEI.getLoadName());
            put("璧山",D5000LfClctEnum.BI_SHAN.getLoadName());
            put("长寿",D5000LfClctEnum.CHANG_SHOU.getLoadName());
            put("市区",D5000LfClctEnum.DI_DIAO.getLoadName());
            put("江北",D5000LfClctEnum.JIANG_BEI.getLoadName());
            put("江津",D5000LfClctEnum.JIANG_JIN.getLoadName());
            put("南岸",D5000LfClctEnum.NAN_AN.getLoadName());
            put("綦南",D5000LfClctEnum.QI_NAN.getLoadName());
            put("统调口径",D5000LfClctEnum.QUAN_WANG.getLoadName());
            put("永川",D5000LfClctEnum.YONG_CHUAN.getLoadName());
            put("万州",D5000LfClctEnum.WAN_ZHOU.getLoadName());
            put("调度口径",D5000LfClctEnum.DIAO_DU.getLoadName());
        }
    };

    private  Map<String,String> cityMap = new HashMap<>();

    @Autowired
    private CityDAO cityDAO;

    /**
     * 根据短期的城市id获取D5000的负荷名称
     * @param cityId
     * @return
     */
    public  String getLoadName(String cityId) {
        if (cityMap.get(cityId) == null) {
            List<CityDO> cityBaseInitDOS = cityDAO.findVOList();
            cityMap = cityBaseInitDOS.stream().collect(Collectors.toMap(CityDO::getId,CityDO::getCity));
        }
        return nameMap.get(cityMap.get(cityId));
    }

}
