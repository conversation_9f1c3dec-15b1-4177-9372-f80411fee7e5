/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  gss Date:  2020/3/5 15:40 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.jobhandler.handler.feature;

import com.tsieframework.core.base.exception.BusinessException;
import com.tsintergy.lf.jobhandler.common.AbstractBaseHandler;
import com.tsintergy.lf.jobhandler.common.ParamDate;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureStatService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 统计超短期日负荷特性
 * 0 40 1 * * ?
 */
@Component
@JobHandler("shortFcLoadFeatureCityDayHandler")
@Slf4j
public class ShortFcLoadFeatureCityDayHandler extends AbstractBaseHandler {

    @Autowired
    private LoadFeatureStatService loadFeatureStatService;


    @Override
    public ReturnT<String> execute(String s) throws Exception {
        Date startDate = DateUtils.addDays(new Date(), -1);
        ParamDate paramDate = resolveJobParam(s, startDate, 0);
        Date endDate = paramDate.getEndDate();
        try {
            XxlJobLogger.log("开始统计超短期日负荷特性，开始时间为:{},结束日期为：{}", startDate, endDate);
            loadFeatureStatService.doShortFcLoadFeatureCityDay(null, startDate, endDate, null);
            XxlJobLogger.log("统计超短期日负荷特性执行成功啦");
        } catch (Exception e) {
            XxlJobLogger.log("执行异常，异常为{}", e);
            throw new BusinessException(e.getMessage(), e.toString());
        }
        return ReturnT.SUCCESS;
    }
}
