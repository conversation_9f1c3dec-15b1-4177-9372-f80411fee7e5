/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.client;

import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.sensitivity.SensitivityBasicData;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Load;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Weather;
import com.tsintergy.aif.algorithm.serviceimpl.client.facade.impl.AbstractSensitivityInvokeClient;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.enums.StatisticalEnum;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AcSensitivityParam;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.LoadAcHisBasicService;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.LoadFeatureAcHisService;
import com.tsintergy.lf.serviceapi.base.airconditioner.dto.AcFeatureAnalyseDTO;
import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.LoadAcHisBasicDO;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.ForecastDataService;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherStationHisBasicWgService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.*;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description:<br>
 *
 * @Author:<EMAIL>
 * @Date: 2022/5/24 15:17
 * @Version:1.0.0
 */
@Component
public class AcSensitivityClient extends AbstractSensitivityInvokeClient<AcSensitivityParam> {

    @Autowired
    LoadAcHisBasicService loadAcHisBasicService;

    @Autowired
    ForecastDataService forecastDataService;

    @Autowired
    WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    CityService cityService;

    @Autowired
    LoadFeatureAcHisService loadFeatureAcHisService;

    @Autowired
    private WeatherStationHisBasicWgService weatherStationHisBasicWgService;

    @Override
    protected SensitivityBasicData generateBasicData(AcSensitivityParam param) {
        try {
            //查询气象城市
            String cityId = cityService.findWeatherCityId(param.getCityId());
            //自变量类型十位为气象类型
            Integer weatherType = Integer.parseInt(param.getWeatherType()) % 100 / 10;

            //查询降雨量
            List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOList = weatherFeatureCityDayHisService
                    .findWeatherFeatureBySearchData(cityId, param.getBeginDate(), param.getEndDate(), null);

            //过滤日期
            List<Date> dateList = weatherFeatureCityDayHisDOList.stream().filter(weatherFeatureCityDayHisDO ->
                            weatherFeatureCityDayHisDO.getRainfall().compareTo(param.getMinRain()) >= 0
                                    && weatherFeatureCityDayHisDO.getRainfall().compareTo(param.getMaxRain()) <= 0)
                    .map(WeatherFeatureCityDayHisDO::getDate).collect(Collectors.toList());

            List<Date> dates = getDate(param.isNormalDay(), param.isRestDay(), dateList);

            List<AcSensitivityWeather> acSensitivityWeatherList = new ArrayList<>();
            //温度查询历史气象表
            if (WeatherEnum.TEMPERATURE.getType().equals(weatherType)) {
//                List<WeatherCityHisDO> hisWeather = forecastDataService
//                    .findHisWeather(cityId, weatherType, param.getBeginDate(), param.getEndDate());
//                acSensitivityWeatherList = transformWeather(hisWeather, dates);
                //使用标准站点气象
                List<WeatherStationHisBasicWgDO> hisWeatherBasic =
                        weatherStationHisBasicWgService.getStandardStationHisWeatherBasic(cityId, weatherType, param.getBeginDate(), param.getEndDate());
                //如果标准站点数据为空,那么用历史气象代替
                if (CollectionUtils.isEmpty(hisWeatherBasic)) {
                    List<WeatherCityHisDO> hisWeather = forecastDataService
                            .findHisWeather(cityId, weatherType, param.getBeginDate(), param.getEndDate());
                    acSensitivityWeatherList = transformWeather(hisWeather, dates);
                } else {
                    for (WeatherStationHisBasicWgDO baseWeatherDO : hisWeatherBasic) {
                        if (dateList.contains(baseWeatherDO.getDate())) {
                            AcSensitivityWeather weather = new AcSensitivityWeather();
                            weather.setDate(baseWeatherDO.getDate());
                            weather.setWeatherList(BasePeriodUtils
                                    .toList(baseWeatherDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
                            acSensitivityWeatherList.add(weather);
                        }
                    }
                    WeatherStationHisBasicWgDO weatherStationHisBasicWgDO = hisWeatherBasic.stream().min(Comparator.comparing(WeatherStationHisBasicWgDO::getDate)).orElse(null);
                    Date startDate = weatherStationHisBasicWgDO != null ? weatherStationHisBasicWgDO.getDate() : null;
                    //缺少的数据用历史气象填充
                    if (param.getBeginDate().before(startDate)) {
                        List<WeatherCityHisDO> hisWeather = forecastDataService.findHisWeather(cityId, weatherType, param.getBeginDate(), startDate);
                        acSensitivityWeatherList.addAll(transformWeather(hisWeather, dates));
                    }
                    acSensitivityWeatherList.sort(Comparator.comparing(AcSensitivityWeather::getDate));
                }
            } else {
                //实感气象查自综合气象表
                List<StatisticsSynthesizeWeatherCityDayHisDO> synthesizeHisWeather = forecastDataService
                        .findSynthesizeHisWeather(cityId, param.getBeginDate(), param.getEndDate(), weatherType);
                acSensitivityWeatherList = transformWeather(synthesizeHisWeather, dates);
            }


            //查询历史负荷
            List<LoadAcHisBasicDO> hisLoad = loadAcHisBasicService.getloadAcHisBasicDOList(param.getCityId(),
                    param.getCaliberId(), param.getBeginDate(), param.getEndDate());
            List<AcFeatureAnalyseDTO> featureAcHis = loadFeatureAcHisService.getLoadFeatureAcHis(param.getCityId(),
                    param.getCaliberId(), param.getBeginDate(), param.getEndDate(), null, null, null);
            this.removeNegativeValues(hisLoad, featureAcHis, param.getLoadType());
            List<AcSensitivityLoad> acSensitivityLoadList = transformLoad(hisLoad, dates);

            //查询节假日
            List<HolidayDO> holidayDOList = forecastDataService.findAllHolidays();
            holidayDOList
                    .sort(Comparator.comparing(HolidayDO::getDate, Comparator.nullsFirst(Comparator.naturalOrder())));


            param.setWeatherType(StatisticalEnum.getValue(Integer.parseInt(param.getWeatherType()) % 10));
            param.setLoadType(StatisticalEnum.getValue(Integer.valueOf(param.getLoadType())));

            SensitivityBasicData sensitivityBasicData = new SensitivityBasicData(
                    acSensitivityLoadList, acSensitivityWeatherList, holidayDOList, param.getDateNotIncluded());
            return sensitivityBasicData;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private void removeNegativeValues(List<LoadAcHisBasicDO> hisLoad, List<AcFeatureAnalyseDTO> featureAcHis, String loadType) {
        Map<Date, AcFeatureAnalyseDTO> acFeatureAnalyseDTOMap =
                featureAcHis.stream().collect(Collectors.toMap(AcFeatureAnalyseDTO::getDate, Function.identity()));

        Iterator<LoadAcHisBasicDO> iterator = hisLoad.iterator();
        while (iterator.hasNext()) {
            LoadAcHisBasicDO loadAcHisBasicDO = iterator.next();
            AcFeatureAnalyseDTO acFeatureAnalyseDTO = acFeatureAnalyseDTOMap.get(loadAcHisBasicDO.getDate());
            if (acFeatureAnalyseDTO == null) {
                iterator.remove();
                continue;
            }
            //最大负荷小于等于0,则排除
            if ("1".equals(loadType) && acFeatureAnalyseDTO.getMaxLoad().compareTo(BigDecimal.ZERO) <= 0) {
                iterator.remove();
            }
            //平均负荷小于等于0,则排除
            if ("2".equals(loadType) && acFeatureAnalyseDTO.getAvgLoad().compareTo(BigDecimal.ZERO) <= 0) {
                iterator.remove();
            }
            //最小负荷小于等于0,则排除
            if ("3".equals(loadType) && acFeatureAnalyseDTO.getMinLoad().compareTo(BigDecimal.ZERO) <= 0) {
                iterator.remove();
            }
        }
    }

    protected List<Date> getDate(boolean isNormalDay, boolean isRestDay, List<Date> dateList) {
        if (!(isNormalDay ^ isRestDay)) {
            return dateList;
        } else if (isNormalDay) {
            return dateList.stream().filter(date -> !DateUtil.isWeekend(date))
                    .collect(Collectors.toList());
        } else if (isRestDay) {
            return dateList.stream().filter(date -> DateUtil.isWeekend(date))
                    .collect(Collectors.toList());
        }
        return null;
    }


    protected <T extends BaseWeatherDO> List<AcSensitivityWeather> transformWeather(List<T> weatherDOList,
                                                                                    List<Date> dateList) {
        List<AcSensitivityWeather> weatherList = new ArrayList<>();
        for (T baseWeatherDO : weatherDOList) {
            if (dateList.contains(baseWeatherDO.getDate())) {
                AcSensitivityWeather weather = new AcSensitivityWeather();
                weather.setDate(baseWeatherDO.getDate());
                weather.setWeatherList(BasePeriodUtils
                        .toList(baseWeatherDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
                weatherList.add(weather);
            }
        }
        return weatherList;
    }

    protected List<AcSensitivityLoad> transformLoad(List<LoadAcHisBasicDO> hisLoadList, List<Date> dateList) {
        List<AcSensitivityLoad> loadList = new ArrayList<>();
        for (LoadAcHisBasicDO hisLoad : hisLoadList) {
            if (dateList.contains(hisLoad.getDate())) {
                AcSensitivityLoad load = new AcSensitivityLoad();
                load.setDate(hisLoad.getDate());
                load.setLoadList(BasePeriodUtils
                        .toList(hisLoad, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
                loadList.add(load);
            }
        }
        return loadList;
    }

    @Data
    class AcSensitivityWeather implements Weather {

        private Date date;

        private List<BigDecimal> weatherList;
    }


    class AcSensitivityLoad implements Load {

        private Date date;

        private List<BigDecimal> loadList;

        private String deviceId;

        @Override
        public Date getDate() {
            return date;
        }

        public void setDate(Date date) {
            this.date = date;
        }

        @Override
        public List<BigDecimal> getloadList() {
            return loadList;
        }

        public void setLoadList(List<BigDecimal> loadList) {
            this.loadList = loadList;
        }


        public String getDeviceId() {
            return deviceId;
        }

        public void setDeviceId(String deviceId) {
            this.deviceId = deviceId;
        }
    }
}