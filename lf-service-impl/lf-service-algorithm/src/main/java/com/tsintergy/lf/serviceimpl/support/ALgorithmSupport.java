/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司 Author: wangfeng Date: 2018/8/29 10:07 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.support;

import com.google.common.collect.Lists;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.edom.efile.ETable;
import com.tsieframework.util.edom.efile.impl.DefaultEfileParse;
import com.tsintergy.algorithm.alginv.core.console.ConsoleCmdOption;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.ShortConstants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.*;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: 预测类算法抽取执行命令和解析文件 <br> 如果算法调用方式及输出文件的格式遵循统一规范，可以直接调用执行命令和解析算法输出文件
 *
 * <AUTHOR>
 * @create 2018/8/29
 * @since 1.0.0
 */
@Slf4j
public class ALgorithmSupport {

    /**
     * yyyy-MM-dd
     */
    private static final DateFormatType DATE_PATTERN = DateFormatType.SIMPLE_DATE_FORMAT_STR;

    public static void mkdir(String path) {
        File file = new File(path);
        if (!file.exists()) {
            file.mkdirs();
        }
    }

    public static String getUUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 算法输出的e文件
     *
     * @return:
     * @since: 1.0.0
     * @Author:wangh
     * @Date: 2020/8/21 10:13
     */
    public static List<ETable> parseOutEFile(String outPath) throws Exception {
        DefaultEfileParse efileParse = new DefaultEfileParse();
        outPath = FilePathSupport.checkPath(outPath);
        String filePathAndName = outPath + AlgorithmConstants.MULTIMETHOD_OUT_NAME;
        return efileParse.parseFile(filePathAndName);
    }


    /**
     * 功能描述: <br> 解析算法输出的e文件
     *
     * @Author:wangfeng
     * @Date: 2018/8/29 10:13
     */
    public static Result parseOutFcFile(String outPath, String fileOutName) throws BusinessException {
        DefaultEfileParse efileParse = new DefaultEfileParse();
        Result result = new Result();
        outPath = FilePathSupport.checkPath(outPath);
        String filePathAndName =
                outPath + (fileOutName == null ? AlgorithmConstants.MULTIMETHOD_OUT_NAME : fileOutName);
        return parseOutFileOnPathAndName(efileParse, result, filePathAndName);
    }


    public static Result parseShortOutFile(String outPath, String startTimePoint, String timeSpan)
            throws BusinessException {
        DefaultEfileParse efileParse = new DefaultEfileParse();
        List<ETable> list;
        Result result = new Result();
        String filePathAndName;
        outPath = FilePathSupport.checkPath(outPath);
        List<String> columns;
        if (ShortConstants.MINUTE.equals(Integer.valueOf(timeSpan))) {
            columns = ColumnUtil.getColumns(288, Constants.LOAD_CURVE_START_WITH_ZERO, false);
        } else {
            columns = ColumnUtil.getColumns(96, Constants.LOAD_CURVE_START_WITH_ZERO, false);
        }
        filePathAndName =
                outPath + columns.get(Integer.valueOf(startTimePoint)) + "_" + AlgorithmConstants.MULTIMETHOD_OUT_NAME;
        return parseOutFileOnPathAndName(efileParse, result, filePathAndName);
    }


    private static Result parseOutFileOnPathAndName(DefaultEfileParse efileParse, Result result,
                                                    String filePathAndName) {
        List<ETable> list;
        try {
            list = efileParse.parseFile(filePathAndName);
            for (int i = 0; i < list.size(); i++) {
                //获取所有e文件（一个根节点是一个e文件）
                ETable table = list.get(i);
                //获取根元素节点名称<ForecastLoad>
                if (table.getTableName().equals(AlgorithmConstants.ForecastEtableName) || table.getTableName()
                        .equals(AlgorithmConstants.ModifyLoadEtableName)) {
                    parseForecast(result, table);
                }
            }
            return result;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("解析文件异常", e);
        }
    }

    /**
     * 拼接授权文件命令
     *
     * @return 命令参数对象
     */
    public static List<ConsoleCmdOption> addConsoleCmdOption(String licPath) {
        List<ConsoleCmdOption> consoleCmdOptions = new ArrayList<>();
        ConsoleCmdOption consoleCmdOption = new ConsoleCmdOption();
        consoleCmdOption.setName("/c:");
        consoleCmdOption.setValue(licPath);
        consoleCmdOption.setWithSpace(false);
        consoleCmdOptions.add(consoleCmdOption);
        return consoleCmdOptions;
    }

    /**
     * 拼接超短期文件前后缀命令
     *
     * @return 命令参数对象
     */
    public static List<ConsoleCmdOption> addConsoleCmdShortOption(ShortForecastParam shortParam) {
        List<String> columns;
        if (ShortConstants.MINUTE.equals(Integer.valueOf((shortParam.getTimeSpan())))) {
            columns = ColumnUtil.getColumns(288, Constants.LOAD_CURVE_START_WITH_ZERO, false);
        } else {
            columns = ColumnUtil.getColumns(96, Constants.LOAD_CURVE_START_WITH_ZERO, false);
        }
        String startTimePoint = shortParam.getStartTimePoint();
        List<ConsoleCmdOption> consoleCmdOptions = new ArrayList<>();
        ConsoleCmdOption consoleCmdOptionIn = new ConsoleCmdOption();
        consoleCmdOptionIn.setName("/f:");
        consoleCmdOptionIn.setValue(columns.get(Integer.valueOf(startTimePoint)) + "_"
                + "FILE_IN");
        consoleCmdOptionIn.setWithSpace(false);
        consoleCmdOptions.add(consoleCmdOptionIn);

        ConsoleCmdOption consoleCmdOptionOut = new ConsoleCmdOption();
        consoleCmdOptionOut.setName("/t:");
        consoleCmdOptionOut.setValue(columns.get(Integer.valueOf(startTimePoint)) + "_"
                + "FILE_OUT");
        consoleCmdOptionOut.setWithSpace(false);
        consoleCmdOptions.add(consoleCmdOptionOut);
        return consoleCmdOptions;
    }

    public static SensitivityResult parseSensitivityOutFile(String outPath) throws BusinessException {
        DefaultEfileParse efileParse = new DefaultEfileParse();
        List<ETable> list;
        SensitivityResult result = new SensitivityResult();
        String filePathAndName = FilePathSupport.checkPath(outPath) + AlgorithmConstants.MULTIMETHOD_OUT_NAME;
        try {
            list = efileParse.parseFile(filePathAndName);
            Map<String, ETable> collect = list.stream().collect(Collectors.toMap(ETable::getTableName,
                    Function.identity(), (o, n) -> n));
            List<Object[]> fittingAccuracy = collect.get(AlgorithmConstants.FITTING_ACCURACY).getDatas();
            result.setFittingAccuracy(new BigDecimal((String) fittingAccuracy.get(0)[0]));
            parseSensitivity(result, collect.get(AlgorithmConstants.INDVARI_AND_SENSITIVITY));
            parseSensitivityPoint(result, collect.get(AlgorithmConstants.IND_VARI_ACT_FIT_VALUE));
            parseSensitivityLine(result, collect.get(AlgorithmConstants.IND_VARI_ACT_FIT_VALUE));
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return result;
        }
    }

    private static void parseForecast(Result result, ETable table) {
        List<Object[]> forecastDatas = table.getDatas();
        if (forecastDatas.size() == 0) {
            throw TsieExceptionUtils.newBusinessException("输出数据的为空，请检查输出文件");
        }
        List<MetaData<BigDecimal>> metaDataList = Lists.newArrayList();
        // j指的是forecast的第几行
        for (int j = 0; j < forecastDatas.size(); j++) {
            MetaData<BigDecimal> metaData = new MetaData<>();
            Object[] lists = forecastDatas.get(j);
            metaData.setDate(DateUtils.string2Date((String) lists[0], DateFormatType.SIMPLE_DATE_FORMAT_STR));
            List<BigDecimal> dataList = new ArrayList<>();
            for (int k = 1; k < lists.length; k++) {
                try {
                    dataList.add(new BigDecimal((String) lists[k]));
                } catch (Exception e) {
                    dataList.add(null);
                }
            }
            metaData.setValue(dataList);
            metaDataList.add(metaData);
        }
        result.setData(metaDataList);
    }

    /**
     * 装载算法输出的灵敏度数据到结果对象
     *
     * @param result 结果对象
     * @param table  算法输出文件
     */
    private static void parseSensitivity(SensitivityResult result, ETable table) {
        List<Object[]> sensitivityData = table.getDatas();
        List<SensitivityBean> sensitivityBeans = new ArrayList<>();
        for (int j = 0; j < sensitivityData.size(); j++) {
            SensitivityBean sensitivityBean = new SensitivityBean();
            Object[] lists = sensitivityData.get(j);
            sensitivityBean.setWeatherNorm(new BigDecimal((String) lists[0]).setScale(2, BigDecimal.ROUND_HALF_UP));
            sensitivityBean.setValue(new BigDecimal((String) lists[1]).setScale(2, BigDecimal.ROUND_HALF_UP));
            sensitivityBean.setFittingValue(new BigDecimal((String) lists[2]).setScale(2, BigDecimal.ROUND_HALF_UP));
            sensitivityBeans.add(sensitivityBean);
        }
        result.setBeanList(sensitivityBeans);
    }

    /**
     * 装载算法输出的  [散点图]   数据到结果对象
     *
     * @param result 结果对象
     * @param table  算法输出文件
     */
    private static void parseSensitivityPoint(SensitivityResult result, ETable table) {
        List<Object[]> sensitivityData = table.getDatas();
        List<List<BigDecimal>> sensitivityBeans = new ArrayList<>();
        for (int j = 0; j < sensitivityData.size(); j++) {
            List<BigDecimal> metal = new ArrayList<>();
            Object[] lists = sensitivityData.get(j);
            metal.add(new BigDecimal((String) lists[0]).setScale(2, BigDecimal.ROUND_HALF_UP));
            metal.add(new BigDecimal((String) lists[1]).setScale(2, BigDecimal.ROUND_HALF_UP));
            sensitivityBeans.add(metal);
        }
        result.setFeaturesPoint(sensitivityBeans);
    }

    /**
     * 装载算法输出的  []折线图   数据到结果对象
     *
     * @param result 结果对象
     * @param table  算法输出文件
     */
    private static void parseSensitivityLine(SensitivityResult result, ETable table) {
        List<Object[]> sensitivityData = table.getDatas();
        List<List<BigDecimal>> sensitivityBeans = new ArrayList<>();
        for (int j = 0; j < sensitivityData.size(); j++) {
            List<BigDecimal> metal = new ArrayList<>();
            Object[] lists = sensitivityData.get(j);
            metal.add(new BigDecimal((String) lists[0]).setScale(2, BigDecimal.ROUND_HALF_UP));
            metal.add(new BigDecimal((String) lists[2]).setScale(2, BigDecimal.ROUND_HALF_UP));
            sensitivityBeans.add(metal);
        }
        result.setFeaturesLine(sensitivityBeans);
    }


    public static GeneralResult postGeneraResult(String outPath, String fileOutName) throws BusinessException {
        Result result = ALgorithmSupport.parseOutFcFile(outPath, fileOutName);
        GeneralResult generalResult = new GeneralResult();
        List<LoadCityFcDO> resultList = new ArrayList<>();
        List<MetaData<BigDecimal>> resultData = result.getData();
        if (CollectionUtils.isEmpty(resultData)) {
            throw TsieExceptionUtils.newBusinessException("算法执行失败，没有生成FILE_OUT文件");
        }
        Map<Date, List<MetaData>> metaMap = resultData.stream().collect(Collectors.groupingBy(MetaData::getDate));
        for (Map.Entry<Date, List<MetaData>> map : metaMap.entrySet()) {
            Date date = map.getKey();
            List<MetaData> metaDataList = map.getValue();
            for (MetaData metaData : metaDataList) {
                try {
                    LoadCityFcDO loadCityFcDO = new LoadCityFcDO();
                    BasePeriodUtils.setAllFiled(loadCityFcDO,
                            ColumnUtil.listToMap(metaData.getValue(), Constants.LOAD_CURVE_START_WITH_ZERO));
                    if (metaData.getMethodType() != null) {
                        loadCityFcDO.setAlgorithmId(metaData.getMethodType().getId());
                    }
                    loadCityFcDO.setDate(new java.sql.Date(date.getTime()));
                    resultList.add(loadCityFcDO);
                } catch (Exception e) {
                    log.error("预测日期：" + date
                            + "的预测结果为空，核对算法输入文件历史数据和授权文件");
                    throw TsieExceptionUtils.newBusinessException("输出文件为空，解析失败文件失败", e);
                }
            }
            generalResult.setResult(resultList);
        }
        return generalResult;
    }

    public static GeneralResult postGeneraResult(Result result) throws BusinessException {
        GeneralResult generalResult = new GeneralResult();
        List<LoadCityFcDO> resultList = new ArrayList<>();
        List<MetaData<BigDecimal>> resultData = result.getData();
        if (CollectionUtils.isEmpty(resultData)) {
            throw TsieExceptionUtils.newBusinessException("算法执行失败，没有生成FILE_OUT文件");
        }
        Map<Date, List<MetaData>> metaMap = resultData.stream().collect(Collectors.groupingBy(MetaData::getDate));
        for (Map.Entry<Date, List<MetaData>> map : metaMap.entrySet()) {
            Date date = map.getKey();
            List<MetaData> metaDataList = map.getValue();
            for (MetaData metaData : metaDataList) {
                try {
                    LoadCityFcDO loadCityFcDO = new LoadCityFcDO();
                    BasePeriodUtils.setAllFiled(loadCityFcDO, ColumnUtil.listToMap(metaData.getValue(), Constants.LOAD_CURVE_START_WITH_ZERO));
                    if (metaData.getMethodType() != null) {
                        loadCityFcDO.setAlgorithmId(metaData.getMethodType().getId());
                    }
                    loadCityFcDO.setDate(new java.sql.Date(date.getTime()));
                    resultList.add(loadCityFcDO);
                } catch (Exception e) {
                    log.error("预测日期：" + date
                            + "的预测结果为空，核对算法输入文件历史数据和授权文件");
                    throw TsieExceptionUtils.newBusinessException("输出文件为空，解析失败文件失败", e);
                }
            }
            generalResult.setResult(resultList);
        }
        return generalResult;
    }

    /**
     * 功能描述: <br> 解析e文本文件
     *
     * @param filePathAndName 文件路径和名称
     * <AUTHOR>
     * @since 1.0.0
     */
    public static Result parseEFile(String filePathAndName) throws BusinessException {
        DefaultEfileParse efileParse = new DefaultEfileParse();
        List<ETable> list = null;
        Result result = new Result();
        try {
            list = efileParse.parseFile(filePathAndName);
            for (int i = 0; i < list.size(); i++) {
                //获取所有e文件（一个根节点是一个e文件）
                ETable table = list.get(i);
                //获取根元素节点名称<ForecastLoad>
                if (table.getTableName().equals(AlgorithmConstants.ForecastEtableName) || table.getTableName()
                        .equals(AlgorithmConstants.ModifyLoadEtableName) || table.getTableName()
                        .equals(AlgorithmConstants.HISTORY_LOAD)) {
                    List<Object[]> forecastDatas = table.getDatas();
                    if (forecastDatas.size() == 0) {
                        throw TsieExceptionUtils.newBusinessException("输入文件为空，修正失败");
                    }
                    List<MetaData<BigDecimal>> metaDataList = Lists.newArrayList();
                    // j指的是forecast的第几行
                    for (int j = 0; j < forecastDatas.size(); j++) {
                        MetaData<BigDecimal> metaData = new MetaData<BigDecimal>();
                        Object[] lists = forecastDatas.get(j);
                        metaData.setDate(DateUtils.string2Date((String) lists[0],
                                DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR));
                        List<BigDecimal> dataList = new ArrayList<BigDecimal>();
                        for (int k = 1; k < lists.length; k++) {
                            Object temp = lists[k];
                            if (temp == null || "null".equals(temp) || "NULL".equals(temp)) {
                                dataList.add(null);
                                continue;
                            }
                            dataList.add(new BigDecimal(temp.toString()));
                        }
                        metaData.setValue(dataList);
                        metaDataList.add(metaData);
                    }
                    result.setData(metaDataList);
                }
            }
            return result;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }


    /**
     * 功能描述: <br> 构造算法所需的节假日数据的形式
     *
     * @param holidayVOS 节假日
     * <AUTHOR>
     * @since 1.0.0
     */
    public static Map<String, Object> getHolidayInfo(List<HolidayDO> holidayVOS) throws Exception {
        Map<String, Object> mapData = new HashMap<>();
        //节假日信息
        List<HolidayInfo> holidayInfos = new ArrayList<>();
        //调休日期
        List<String> offDates = new ArrayList<>();
        holidayVOS.sort(Comparator.comparing(HolidayDO::getStartDate,
                Comparator.nullsFirst(Comparator.naturalOrder())));
        for (HolidayDO holidayVO : holidayVOS) {
            String offDate = holidayVO.getOffDates();
            if (StringUtils.isNotBlank(offDate)) {
                String[] dateStr = offDate.split(",");
                for (String str : dateStr) {
                    offDates.add(DateUtils.formateString(str, DateFormatType.SIMPLE_DATE_FORMAT_STR,
                            DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR));
                }
            }
            Date isHolidayDate = holidayVO.getDate();
            Date startDate = holidayVO.getStartDate();
            Date endDate = holidayVO.getEndDate();
            List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
            for (Date date : dateList) {
                HolidayInfo info = new HolidayInfo();
                info.setDate(new java.sql.Date(date.getTime()));
                //数据库存储 101 102 103....  算法需传  1 2 3 ....
                info.setHolidayType(holidayVO.getCode() % 100);
                //是否为节假日当天
                if (date.equals(isHolidayDate)) {
                    info.setIsHolidayDay(1);
                } else {
                    info.setIsHolidayDay(0);
                }
                //该日前休假的天数
                Integer dayAhead = DateUtil.getDayCountBetweenDay(DateUtils.date2String(holidayVO.getStartDate(),
                        DATE_PATTERN), DateUtils.date2String(date, DATE_PATTERN), "yyyy-MM-dd");
                //该日后休假的天数
                Integer dayAfter = DateUtil.getDayCountBetweenDay(DateUtils.date2String(date, DATE_PATTERN),
                        DateUtils.date2String(holidayVO.getEndDate(), DATE_PATTERN), "yyyy-MM-dd");
                info.setDaysAhead(dayAhead);
                info.setDaysAfter(dayAfter);
                holidayInfos.add(info);
            }
        }
        mapData.put(AlgorithmConstants.HOLIDAY_INFO, holidayInfos);
        mapData.put(AlgorithmConstants.OFFDATES, offDates);
        return mapData;
    }

    /**
     * 从map中获取相应对象的list 添加了类型检查
     *
     * <AUTHOR>
     */
    public static <T> List<T> getListFromMap(Object obj, Class<T> clazz) {
        List<T> result = new ArrayList<T>();
        if (obj instanceof List<?>) {
            for (Object o : (List<?>) obj) {
                result.add(clazz.cast(o));
            }
            return result;
        }
        return new ArrayList<>();
    }
}