/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司 Author: liufeng Date: 2018/6/6 19:26 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.impl;

import com.tsieframework.cloud.security.serviceapi.system.api.RedisService;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.constants.CacheConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.algorithm.dto.ForecastParam;
import com.tsintergy.lf.serviceapi.algorithm.dto.GeneralResult;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.forecast.api.ForecastDataService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.StatisticsSynthesizeWeatherCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.StatisticsSynthesizeWeatherCityDayHisDO;
import com.tsintergy.lf.serviceimpl.common.impl.CoreConfigInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 每日预测算法服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service(value = "forecastable")
@Slf4j
public class Forecastor extends AbstractForecastService<ForecastParam, GeneralResult> {

    @Autowired
    private ForecastDataService algorithmForecastDataService;

    @Autowired
    private CoreConfigInfo coreConfigInfo;

    @Autowired
    private LoadCityFcService loadCityFcService;

    @Autowired
    private CityService cityService;

    @Autowired
    private RedisService redisService;

    @Override
    public void forecast(ForecastParam param) throws Exception {
        List<Date> dates = param.getDateList();
        if (CollectionUtils.isEmpty(dates)) {
            dates = DateUtil.getListBetweenDay(param.getForecastDate(), param.getForecastEndDate());
        }
        Collections.sort(dates);
        Date startFcstDay = param.getForecastDate() != null ? param.getForecastDate() : dates.get(0);
        Date endFcstDay = param.getForecastEndDate() != null ? param.getForecastEndDate() : dates.get(dates.size() - 1);
        String cityId = param.getCityId();
        String caliberId = param.getCaliberId();
        Map<String, Object> dataMap = new HashMap<>(16);
        //装载数据 实际负荷&气象数据等
        super.mergeData(param, dataMap);
        //如果是xg的补充算法--lgb算法,填充综合气象数据
        if (param.getAlgorithmEnums().contains(AlgorithmEnum.REPLENISH_LGB)) {
            mergeSynthesizeWeatherData(cityId, dataMap, DateUtil.getMoveDay(startFcstDay, -1200), endFcstDay);
        }
        //如果是综合模型算法--补充基准日（包含）前400天的各类算法预测数据
        if (param.getAlgorithmEnums().contains(AlgorithmEnum.COMPREHENSIVE_MODEL)) {
            Date queryStartTime = DateUtils.addYears(startFcstDay, -2);
            mergeAlgorithmFc(dataMap, cityId, queryStartTime, DateUtils.addDays(endFcstDay, 1), caliberId);
        }
        if (param.getForecastType() == null) {
            //如果没有设置，则默认是算法内部做滚动预测
            param.setForecastType(1);
        }
        //算法内部做了滚动预测，预测多天，只调用一次算法
        if (param.getForecastType() == ForecastParam.FORECAST_CYCLIC_TYPE) {
            for (AlgorithmEnum algorithmEnum : param.getAlgorithmEnums()) {
                try {
                    param.setForecastDate(startFcstDay);
                    param.setForecastEndDate(endFcstDay);
                    param.setAlgorithmEnum(algorithmEnum);
                    param.setCityName(algorithmForecastDataService.findCityDOByCityId(cityId).getCity());
                    log.info("开始预测，startDate:" + startFcstDay + ", endDate:" + endFcstDay + ", cityId:" + cityId
                            + ", caliberId:" + caliberId + ", algorithmId:" + algorithmEnum.getType());
                    execute(param, dataMap);
                    log.info("预测完成，startDate:" + startFcstDay + ", endDate:" + endFcstDay + ", cityId:" + cityId
                            + ", caliberId:" + caliberId + ", algorithmId:" + algorithmEnum.getType());
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        } else {//程序循环一天一天调用
            for (AlgorithmEnum algorithmEnum : param.getAlgorithmEnums()) {
                for (Date date : param.getDateList()) {
                    try {
                        param.setForecastDate(date);
                        param.setForecastEndDate(date);
                        param.setAlgorithmEnum(algorithmEnum);
                        param.setCityName(param.getCityName());
                        log.info("开始预测，startDate:" + param.getForecastDate() + ", endDate:" + param.getForecastEndDate()
                                + ", cityId:" + param.getCityId()
                                + ", caliberId:" + param.getCaliberId() + ", algorithmId:" + algorithmEnum.getType());
                        execute(param, dataMap);
                        //获取返回值 需要把上一次预测的数据放入历史数据中
                        GeneralResult result = (GeneralResult) redisService.redisGet(CacheConstants.CACHE_FORECAST_KEY, GeneralResult.class);
                        log.info("预测完成，startDate:" + param.getForecastDate() + ", endDate:" + param.getForecastEndDate()
                                + ", cityId:" + param.getCityId() + ", algorithmId:" + algorithmEnum.getType()
                                + ", caliberId:" + param.getCaliberId());
                        mixFcDataToHisData(param, dataMap, date, result);
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                }
            }
        }
    }

    /**
     * 程序外部一天天预测时 可能存在 基准日数据不存在问题，需要把上一次预测的数据放入历史数据中
     *
     * @param param         预测参数
     * @param srcMap        算法所用数据map集合
     * @param date          预测日期
     * @param executeResult 算法预测结果
     */
    private void mixFcDataToHisData(ForecastParam param, Map<String, Object> srcMap, Date date,
                                    GeneralResult executeResult)
            throws Exception {
        //预测结果累加到历史负荷
        List<LoadCityHisDO> loads = (List<LoadCityHisDO>) srcMap.get(AlgorithmConstants.HIS_LOAD);
        //如果数据够 则不需要将预测结果累加到历史负荷
        Date baseDate = DateUtils.addDays(date, -2);
        if (loads.get(loads.size() - 1).getDate().before(DateUtils.addDays(baseDate, 1))) {
            if (null != executeResult) {
                List<LoadCityFcDO> metaDatas = executeResult.getResult();
                for (LoadCityFcDO result : metaDatas) {
                    LoadCityHisDO vo = new LoadCityHisDO();
                    BeanUtils.copyProperties(result, vo);
                    vo.setCaliberId(param.getCaliberId());
                    vo.setCityId(param.getCityId());
                    loads.add(vo);
                }
                srcMap.put(AlgorithmConstants.HIS_LOAD, loads);
            }
        }
    }

    /**
     * 匹配历史综合气象数据--->针对每日跑批的LGB算法进行数据补充
     */
    private void mergeSynthesizeWeatherData(String cityId, Map<String, Object> datas, Date lastYearStartDate,
                                            Date endFcstDay) throws Exception {
        //如果是预测省 则使用省会城市的气象(人为将省会城市排序为2)
        List<StatisticsSynthesizeWeatherCityDayHisDO> weatherCityHisDOS = null;
        List<StatisticsSynthesizeWeatherCityDayHisDO> enthalpyHisVOS = null;
        cityId = cityService.findWeatherCityId(cityId);
        int weatherType = 0;
        String type = coreConfigInfo.getRuntimeParam("forecast.model.weather");
        if (type != null) {
            weatherType = Integer.parseInt(type);
        }
        //系统设置的 使用历史气象
        if (weatherType == 0) {
            weatherCityHisDOS = algorithmForecastDataService
                    .findSynthesizeHisWeather(cityId, lastYearStartDate, endFcstDay,
                            WeatherEnum.EFFECTIVE_TEMPERATURE.getType());
            enthalpyHisVOS = algorithmForecastDataService
                    .findSynthesizeHisWeather(cityId, lastYearStartDate, endFcstDay,
                            WeatherEnum.ENTHALPY.getType());
            //查看历史数据截止日期
            mergeSynthesizeHis(cityId, datas, endFcstDay, weatherCityHisDOS, AlgorithmConstants.SYNTHESIZE_WEATHER_KEY);
            mergeSynthesizeHis(cityId, datas, endFcstDay, enthalpyHisVOS, AlgorithmConstants.ENTHALPY_VALUE);
        }
        //系统设置的使用 预测气象
        else {
            mergeSynthesizeFc(cityId, datas, lastYearStartDate, endFcstDay, WeatherEnum.EFFECTIVE_TEMPERATURE.getType(),
                    AlgorithmConstants.SYNTHESIZE_WEATHER_KEY);
            mergeSynthesizeFc(cityId, datas, lastYearStartDate, endFcstDay, WeatherEnum.ENTHALPY.getType(),
                    AlgorithmConstants.ENTHALPY_VALUE);
        }
    }

    /**
     * 综合模型专用 算法历史预测负荷数据
     */
    private void mergeAlgorithmFc(Map<String, Object> datas, String cityId, Date queryStartTime,
                                  Date startFcstDay, String caliberId) throws Exception {
        List<LoadCityFcDO> loadCityFcVOS = loadCityFcService
                .findLoadCityFc(Arrays.asList(cityId), queryStartTime, startFcstDay, caliberId);
        if (CollectionUtils.isEmpty(loadCityFcVOS)) {
            log.error("城市id:{}算法执行失败，算法历史预测负荷数据为空", cityId);
            throw TsieExceptionUtils.newBusinessException("算法执行失败，算法历史预测负荷数据为空");
        }
        datas.put(AlgorithmConstants.DATA_ALGORITHM_KEY, loadCityFcVOS);

    }

    private void mergeSynthesizeFc(String cityId, Map<String, Object> datas, Date lastYearStartDate, Date endFcstDay,
                                   Integer type, String mapKey)
            throws Exception {
        List<StatisticsSynthesizeWeatherCityDayFcDO> WeatherCityFcDOS = algorithmForecastDataService
                .findSynthesizeFcWeather(cityId,
                        lastYearStartDate, endFcstDay, type);
        List<StatisticsSynthesizeWeatherCityDayHisDO> hisVOS = new ArrayList<>();
        for (StatisticsSynthesizeWeatherCityDayFcDO fcVO : WeatherCityFcDOS) {
            StatisticsSynthesizeWeatherCityDayHisDO hisVO = new StatisticsSynthesizeWeatherCityDayHisDO();
            BeanUtils.copyProperties(fcVO, hisVO);
            hisVOS.add(hisVO);
        }
        datas.put(mapKey, hisVOS);
    }

    private void mergeSynthesizeHis(String cityId, Map<String, Object> datas, Date endFcstDay,
                                    List<StatisticsSynthesizeWeatherCityDayHisDO> weatherCityHisDOS, String mapKey) throws Exception {
        if (CollectionUtils.isEmpty(weatherCityHisDOS) || weatherCityHisDOS.size() == 0) {
            datas.put(mapKey, new ArrayList<>());
            return;
        }
        //最后一天的气象
        StatisticsSynthesizeWeatherCityDayHisDO weatherCityHisDO = weatherCityHisDOS
                .get(weatherCityHisDOS.size() - 1);
        Date endHisDate = weatherCityHisDO.getDate();
        List<BigDecimal> list = BasePeriodUtils.toList(weatherCityHisDO, Constants.LOAD_CURVE_POINT_NUM,
                Constants.LOAD_CURVE_START_WITH_ZERO);
        //去除null数据
        list.removeAll(Collections.singleton(null));
        Integer weatherType = weatherCityHisDO.getType();
        //若查询历史气象数据的最后的日期在预测结束日之前 说明历史数据缺少  则在预测数据里查找
        if (list.size() < 90 || endHisDate.before(endFcstDay)) {
            List<StatisticsSynthesizeWeatherCityDayFcDO> fcVOS;
            //如果数据不够 则需要把这一天的数据先剔除掉
            if (list.size() < 90) {
                weatherCityHisDOS.remove(weatherCityHisDO);
                fcVOS = algorithmForecastDataService.findSynthesizeFcWeather(cityId, endHisDate, endFcstDay,
                        weatherType);
            } else {
                //如果历史数据的最后一日在预测日之前或者和预测日相等
                fcVOS = algorithmForecastDataService
                        .findSynthesizeFcWeather(cityId, DateUtils.addDays(endHisDate, 1),
                                endFcstDay, weatherType);
            }
            //有预测数据
            for (StatisticsSynthesizeWeatherCityDayFcDO vo : fcVOS) {
                //将预测的数据放入历史数据里
                StatisticsSynthesizeWeatherCityDayHisDO hisVO = new StatisticsSynthesizeWeatherCityDayHisDO();
                hisVO.setDate(vo.getDate());
                hisVO.setType(vo.getType());
                hisVO.setCityId(vo.getCityId());
                List<BigDecimal> period = BasePeriodUtils.toList(vo, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
                BasePeriodUtils.setAllFiled(hisVO, ColumnUtil.listToMap(period, Constants.LOAD_CURVE_START_WITH_ZERO));
                weatherCityHisDOS.add(hisVO);
            }
        }
        datas.put(mapKey, weatherCityHisDOS);
    }


}
