/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.impl;

import com.tsintergy.aif.algorithm.serviceapi.base.dto.sensitivity.SensitivityResult;
import com.tsintergy.lf.serviceapi.algorithm.api.AcSensitivityForecastService;
import com.tsintergy.lf.serviceapi.algorithm.dto.AcSensitivityParam;
import com.tsintergy.lf.serviceapi.base.forecast.dto.AcSensitivityAlgorithmDTO;
import com.tsintergy.lf.serviceimpl.client.AcSensitivityClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:<br>
 *
 * @Author:<EMAIL>
 * @Date: 2022/5/24 21:30
 * @Version:1.0.0
 */
@Service("acSensitivityForecastService")
public class AcSensitivityForecastServiceImpl implements AcSensitivityForecastService {

    @Autowired
    AcSensitivityClient acSensitivityClient;

    @Override
    public SensitivityResult doAcSensitivityAlgorithm(AcSensitivityAlgorithmDTO algorithmDTO) {
        try {
            AcSensitivityParam acSensitivityParam = new AcSensitivityParam(algorithmDTO.getStartDate(),
                algorithmDTO.getEndDate(), null, algorithmDTO.getMin(), algorithmDTO.getMax(), algorithmDTO.getStep(),
                algorithmDTO.getLoadType(), algorithmDTO.getWeatherType());

            acSensitivityParam.setCityId(algorithmDTO.getCityId());
            acSensitivityParam.setCaliberId(algorithmDTO.getCaliberId());
            acSensitivityParam
                .setAlgorithmEnum(com.tsintergy.aif.algorithm.serviceapi.base.dto.AlgorithmEnum.SENSITIVITY);
            acSensitivityParam.setDateNotIncluded(algorithmDTO.getDateNotIncluded());
            acSensitivityParam.setCityId(algorithmDTO.getCityId());
            acSensitivityParam.setUserId(algorithmDTO.getUserId());
            acSensitivityParam.setMaxRain(algorithmDTO.getMaxRain());
            acSensitivityParam.setMinRain(algorithmDTO.getMinRain());
            acSensitivityParam.setNormalDay(algorithmDTO.isNormalDay());
            acSensitivityParam.setRestDay(algorithmDTO.isRestDay());

            //调用灵敏度分析算法
            return acSensitivityClient.invoke(acSensitivityParam);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}