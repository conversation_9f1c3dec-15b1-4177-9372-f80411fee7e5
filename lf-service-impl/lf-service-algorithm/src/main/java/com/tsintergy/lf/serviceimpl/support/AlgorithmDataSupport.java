/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.support;


import com.tsintergy.lf.serviceapi.algorithm.dto.WeatherFeatureDTO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayLongFcService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayLongFcDO;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Description: 算法数据支撑 <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/5/13 18:31
 * @Version: 1.0.0
 */
public class AlgorithmDataSupport {


    @Autowired
    private WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    private WeatherFeatureCityDayLongFcService weatherFeatureCityDayLongFcService;

    public WeatherFeatureDTO getWeatherFeatureDTO(Date start, Date end, String cityId) throws Exception {

        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisStatsDOS = weatherFeatureCityDayHisService
            .listWeatherFeatureCityDayHisDO(cityId, start, end);

        List<WeatherFeatureCityDayLongFcDO> weatherFeatureCityDayFcStatsDOS = weatherFeatureCityDayLongFcService
            .findByParam(cityId, start, end);

        WeatherFeatureDTO weatherFeatureDTO = new WeatherFeatureDTO();
        weatherFeatureDTO.setHisWeatherFeature(weatherFeatureCityDayHisStatsDOS);
        weatherFeatureDTO.setFcWeatherFeature(weatherFeatureCityDayFcStatsDOS);

        return weatherFeatureDTO;
    }

}