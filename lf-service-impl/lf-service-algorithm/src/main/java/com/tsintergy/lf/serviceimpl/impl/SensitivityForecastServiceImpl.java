/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司 Author: liufeng Date: 2018/6/6 19:26 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.MapUtils;
import com.tsieframework.cloud.security.serviceapi.system.api.RedisService;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.constants.CacheConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.enums.AcConfigEnum;
import com.tsintergy.lf.core.enums.StatisticalWeatherEnum;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.SensitivityAcInfo;
import com.tsintergy.lf.serviceapi.algorithm.dto.SensitivityParam;
import com.tsintergy.lf.serviceapi.algorithm.dto.SensitivityResult;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.LoadAcHisBasicService;
import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.LoadAcHisBasicDO;
import com.tsintergy.lf.serviceapi.base.assess.api.SettingAssessService;
import com.tsintergy.lf.serviceapi.base.assess.pojo.SettingAssessDO;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.forecast.api.ForecastDataService;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceimpl.support.ALgorithmSupport;

import java.util.*;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 灵敏度分析算法 逻辑实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service(value = "sensitivityForecastService")
public class SensitivityForecastServiceImpl extends AbstractForecastService<SensitivityParam, SensitivityResult> {

    @Autowired
    private ForecastDataService forecastDataService;

    @Autowired
    private LoadAcHisBasicService loadAcHisBasicService;

    @Autowired
    private CityService cityService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private SettingAssessService settingAssessService;

    private static final String WEATHER_ID = "2";

    private static final List<String> MAX = Arrays.asList("1", "4", "7");
    private static final List<String> AVE = Arrays.asList("2", "5", "8");
    private static final List<String> MIN = Arrays.asList("3", "6", "9");
    private static final List<String> LJ = Arrays.asList("4", "5", "6");
    private static final List<String> TG = Arrays.asList("7", "8", "9");

    @Override
    public void mergeData(SensitivityParam param, Map<String, Object> srcMap) throws Exception {
        //查询历史负荷特性
        List<LoadCityHisDO> hisLoad;
        if (param.getType() == null || param.getType() == 2) {
            // 系统负荷
            hisLoad = forecastDataService.findHisLoad(param.getCityId(),
                            param.getCaliberId(), param.getBeginDate(), param.getEndDate());
        } else {
            hisLoad = new ArrayList<>();
            //空调负荷
            List<LoadAcHisBasicDO> loadAcHisBasicDOS = loadAcHisBasicService.getloadAcHisBasicDOList(param.getCityId(),
                    param.getCaliberId(), param.getBeginDate(), param.getEndDate());
            if (CollectionUtils.isNotEmpty(loadAcHisBasicDOS)) {
                loadAcHisBasicDOS.forEach(loadAcHisBasicDO -> {
                    LoadCityHisDO loadCityHisDO = new LoadCityHisDO();
                    try {
                        BasePeriodUtils.setAllFiled(loadCityHisDO,
                                ColumnUtil.listToMap(loadAcHisBasicDO.getloadList(), Constants.LOAD_CURVE_START_WITH_ZERO));
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                    loadCityHisDO.setDate(new java.sql.Date(loadAcHisBasicDO.getDate().getTime()));
                    hisLoad.add(loadCityHisDO);
                });
            }
        }
        if (CollectionUtils.isEmpty(hisLoad) || hisLoad.size() <= 5) {
            log.warn("历史负荷数据不足，cityId: {}, caliberId: {}, beginDate: {}, endDate: {}",
                    param.getCityId(), param.getCaliberId(), param.getBeginDate(), param.getEndDate());
            return;
        }
        //查询气象城市
        String cityId = cityService.findWeatherCityId(param.getCityId());
        String weatherId = param.getWeatherId();
        String weatherType = param.getWeatherType();
        if (LJ.contains(weatherType)) {
            srcMap.put(AlgorithmConstants.INDVARI_TYPE, StatisticalWeatherEnum.getValue(Integer.parseInt(weatherType)));
            param.setAccumulate("1");
        } else {
            if (MAX.contains(weatherType)) {
                srcMap.put(AlgorithmConstants.INDVARI_TYPE, StatisticalWeatherEnum.MAX.getValue());
            } else if (AVE.contains(weatherType)) {
                srcMap.put(AlgorithmConstants.INDVARI_TYPE, StatisticalWeatherEnum.AVG.getValue());
            } else if (MIN.contains(weatherType)) {
                srcMap.put(AlgorithmConstants.INDVARI_TYPE, StatisticalWeatherEnum.MIN.getValue());
            }
            srcMap.put(AlgorithmConstants.INDVARI_TYPE, StatisticalWeatherEnum.getValue(Integer.parseInt(weatherType)));
            param.setAccumulate("0");
        }
        if (WEATHER_ID.equals(weatherId)) {
            // 福州
            int weatherDataType = TG.contains(weatherType) ? 6 : 2;
            List<WeatherCityHisDO> hisWeather = forecastDataService
                    .findHisWeather(WEATHER_ID, weatherDataType, param.getBeginDate(), param.getEndDate());
            srcMap.put(AlgorithmConstants.DATA_WEATHER_KEY, hisWeather);
        } else {
            // 福建
            Date beginDate = param.getBeginDate();
            Date endDate = param.getEndDate();
            boolean isTgType = TG.contains(weatherType);
            WeatherEnum weatherEnum = isTgType ? WeatherEnum.EFFECTIVE_NEW_TEMPERATURE : WeatherEnum.TEMPERATURE;
            List<WeatherCityHisDO> hisWeatherBasic = isTgType ?
                    forecastDataService.getCityAvgHisEffectiveWeatherData(weatherEnum.getType(), beginDate, endDate) :
                    forecastDataService.getCityAvgHisWeatherData(weatherEnum.getType(), beginDate, endDate);

            srcMap.put(AlgorithmConstants.DATA_WEATHER_KEY, hisWeatherBasic);
        }
        //自变量个位为 1 最大 2 平均 3 最小
        srcMap.put(AlgorithmConstants.HIS_LOAD, hisLoad);
        // 装载考核点设置数据
        if (AcConfigEnum.BG_AVE.getId().equals(param.getLoadType()) ||
                AcConfigEnum.WJ_MIN.getId().equals(param.getLoadType()) ||
                AcConfigEnum.YJ_MIN.getId().equals(param.getLoadType())) {
            List<SensitivityAcInfo> list = new ArrayList<>();
            List<String> ymList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(hisLoad)) {
                hisLoad.forEach(loadCityHisDO -> {
                    ymList.add(DateUtil.getDateToStrFORMAT(loadCityHisDO.getDate(), "yyyyMM"));
                });
            }
            List<SettingAssessDO> allValidAssess = settingAssessService.findAssessByDataList("2024", null, param.getCaliberId(), null);
            Map<String, List<SettingAssessDO>> collectData = new HashMap<>();
            if (CollectionUtils.isNotEmpty(allValidAssess)) {
                collectData = allValidAssess.stream().collect(Collectors.groupingBy(t -> t.getMonth()));
            }
            Map<String, List<SettingAssessDO>> assessSettingByData = settingAssessService.findAssessSettingByData(param.getBeginDate(), param.getEndDate(), param.getCaliberId());
            List<String> collect1 = ymList.stream().distinct().collect(Collectors.toList());
            for (String ym : collect1) {
                List<SettingAssessDO> settingAssessDOS = new ArrayList<>();
                if (!MapUtils.isEmpty(assessSettingByData)) {
                    settingAssessDOS = assessSettingByData.get(ym.substring(0, 4) + "-" + ym.substring(4, 6));
                    if (CollectionUtils.isEmpty(settingAssessDOS)) {
                        settingAssessDOS = collectData.get(ym.substring(4, 6));
                    }
                } else {
                    settingAssessDOS = collectData.get(ym.substring(4, 6));
                }
                if (CollectionUtils.isNotEmpty(settingAssessDOS)) {
                    Map<String, List<SettingAssessDO>> collect = settingAssessDOS.stream().collect(Collectors.groupingBy(t -> t.getAssessName()));
                    for (AcConfigEnum value : AcConfigEnum.values()) {
                        if (param.getLoadType().equals(String.valueOf(value.getId()))) {
                            List<SettingAssessDO> settingAssessDOS1 = collect.get(value.getName());
                            if (CollectionUtils.isNotEmpty(settingAssessDOS1)) {
                                SettingAssessDO settingAssessDO = settingAssessDOS1.get(0);
                                SensitivityAcInfo sensitivityAcInfo = new SensitivityAcInfo();
                                sensitivityAcInfo.setYm(ym+"01");
                                sensitivityAcInfo.setAcConfigName(value.getName());
                                sensitivityAcInfo.setAcConfigId(value.getEnName());
                                sensitivityAcInfo.setStartTime(settingAssessDO.getStartTime());
                                sensitivityAcInfo.setEndTime(settingAssessDO.getEndTime());
                                list.add(sensitivityAcInfo);
                            }
                        }
                    }
                }
            }
            srcMap.put(AlgorithmConstants.KEY_LOAD_INFO, list);
        }
        //节假日数据
        super.mergeHolidayData(srcMap);
    }

    @Override
    public void resolverResult(String tarPath, Map<String, String> supplementData) throws Exception {
        SensitivityResult sensitivityResult = ALgorithmSupport
            .parseSensitivityOutFile(tarPath);
        redisService.redisAdd(supplementData.get(AlgorithmConstants.USER_ID) + Constants.SEPARATOR_BROKEN_LINE
            + CacheConstants.CACHE_SENSITIVITYRESULT_KEY, sensitivityResult);
    }
}
