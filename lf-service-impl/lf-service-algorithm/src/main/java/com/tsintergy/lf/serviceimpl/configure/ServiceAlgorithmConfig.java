package com.tsintergy.lf.serviceimpl.configure;


import com.tsintergy.algorithm.alginv.core.configure.properties.AlginvProperties;
import com.tsintergy.algorithm.alginv.serviceapi.client.api.AlgorithmQueryFacadeService;
import com.tsintergy.algorithm.alginv.serviceimpl.client.invoker.ClientAlgorithmInvoker;
import com.tsintergy.lf.serviceimpl.callback.AlgorithmCallBack;
import com.tsintergy.lf.serviceimpl.client.*;
import com.tsintergy.lf.serviceimpl.executor.analysis.*;
import com.tsintergy.lf.serviceimpl.executor.forecast.BaseMultiMethodExecutor;
import com.tsintergy.lf.serviceimpl.executor.forecast.ModelFusionExecutorBase;
import com.tsintergy.lf.serviceimpl.executor.forecast.ReplenishXGForecastExecutorBase;
import com.tsintergy.lf.serviceimpl.executor.forecast.ShortForecastExecutor;
import com.tsintergy.lf.serviceimpl.executor.holiday.FestSVMExecutor;
import com.tsintergy.lf.serviceimpl.executor.prePropess.LoadPrePropessExecutor;
import com.tsintergy.lf.serviceimpl.executor.typhoon.TyphoonForecastExecutor;
import com.tsintergy.lf.serviceimpl.factory.ForecastMethodFactory;
import com.tsintergy.lf.serviceimpl.support.ALgorithmSupport;
import com.tsintergy.lf.serviceimpl.support.AlgorithmDataSupport;
import com.tsintergy.lf.serviceimpl.support.AnalysisCmdParser;
import com.tsintergy.lf.serviceimpl.support.FilePathSupport;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Service;

/**
 * <p>
 * Service模块配置，通常这里负责注入非DAO、Service的其他类型的Bean定义，方便统一查找
 * </p>
 *
 * <AUTHOR>
 * @date 2020-02-04 14:05:40
 */
@Configuration
@Slf4j
@EnableConfigurationProperties(AlgorithmPathProperties.class)
public class ServiceAlgorithmConfig {


    @Autowired
    private AlgorithmQueryFacadeService algorithmQueryFacadeService;

    private Logger logger = LoggerFactory.getLogger(ServiceAlgorithmConfig.class);

    public ServiceAlgorithmConfig() {
        logger.debug("初始化" + getClass());
    }

    @Bean
    public AnalysisExecutor precisionExecutor() {
        return new AnalysisExecutor();
    }

    @Bean
    public AnalysisAlgorithmClient analysisAlgorithmClient(ClientAlgorithmInvoker clientAlgorithmInvoker,
                                                           AlginvProperties properties) {
        return new AnalysisAlgorithmClient(clientAlgorithmInvoker, properties, algorithmQueryFacadeService);
    }
//
//    @Bean
//    public AlgorithmCallBack algorithmCallBack(AlginvProperties properties) {
//        return new AlgorithmCallBack() {
//            @Override
//            public void onComplete(AlgorithmInvokeId invokeId, AlgorithmInvokeResult result) {
//                log.info("解析案例输出并入库: " + result.getCaseOutput().getTarFile().pathResolver().getTarFilePath(null));
//            }
//        };
//    }

    @Bean
    public AlgorithmCallBack algorithmCallBack() {
        return new AlgorithmCallBack();
    }

    @Bean
    public AnalysisCmdParser analysisCmdParser() {
        return new AnalysisCmdParser();
    }

    @Bean
    public SimilarDayExecutor similarDayExecutor() {
        return new SimilarDayExecutor();
    }

    @Bean
    public ModelFusionExecutorBase modelFusionExecutor() {
        return new ModelFusionExecutorBase();
    }

    @Bean
    public LongCompositeExecutor longSensitivityExecutor() {
        return new LongCompositeExecutor();
    }

    @Bean
    public AnalysisExecutor analysisExecutor() {
        return new AnalysisExecutor();
    }

    @Bean
    public ShortForecastExecutor shortForecastExecutor() {
        return new ShortForecastExecutor();
    }

    @Bean
    public SensitivityExecutor sensitivityExecutor() {
        return new SensitivityExecutor();
    }

    @Bean
    public FilePathSupport customFilePathSupport() {
        return new FilePathSupport();
    }

    @Bean
    public ALgorithmSupport aLgorithmSupport() {
        return new ALgorithmSupport();
    }

    @Bean
    public FestSVMExecutor festSVMExecutor() {
        return new FestSVMExecutor();
    }

    @Bean
    public TyphoonForecastExecutor typhoonForecastExecutor() {
        return new TyphoonForecastExecutor();
    }

    @Bean
    public ForecastMethodFactory forecastMethodFactory() {
        return new ForecastMethodFactory();
    }

    @Bean
    public BaseMultiMethodExecutor baseMultiMethodExecutor() {
        return new BaseMultiMethodExecutor();
    }

    @Bean
    public LoadPrePropessExecutor loadPrePropessExecutor() {
        return new LoadPrePropessExecutor();
    }

    @Bean
    public ReplenishXGForecastExecutorBase replenishXGForecastExecutor() {
        return new ReplenishXGForecastExecutorBase();
    }

    @Bean
    public AlgorithmDataSupport algorithmDataSupport() {
        return new AlgorithmDataSupport();
    }

    @Bean
    public AcSensitivityClient acSensitivityClient() {
        return new AcSensitivityClient();
    }

    @Bean
    public GruFactorsForecastInvokeClient gruFactorsForecastInvokeClient() {
        return new GruFactorsForecastInvokeClient();
    }

    @Bean
    public GTransformerForecastInvokeClient gTransformerForecastInvokeClient() {
        return new GTransformerForecastInvokeClient();
    }

    @Bean
    public TypicalCurveInvokeClient typicalCurveInvokeClient() {
        return new TypicalCurveInvokeClient();
    }

    @Bean
    public GtNetForecastInvokeClient gtNetForecastInvokeClient() {
        return new GtNetForecastInvokeClient();
    }

    @Bean
    public LongVecMaxExecutor longVecMaxExecutor() {
        return new LongVecMaxExecutor();
    }

    @Bean
    public LongVecMinExecutor longVecMinExecutor() {
        return new LongVecMinExecutor();
    }

    @Bean
    public LongVecEnergyExecutor longVecEnergyExecutor() {
        return new LongVecEnergyExecutor();
    }

    @Bean
    public DayMaxLoadClient dayMaxLoadClient() {
        return new DayMaxLoadClient();
    }

    @Bean
    public Day96LoadClient day96LoadClient() {
        return new Day96LoadClient();
    }

    @Bean
    public TransForecastInvokeClient transForecastInvokeClient() {
        return new TransForecastInvokeClient();
    }
}
