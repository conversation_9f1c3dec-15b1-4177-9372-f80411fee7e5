/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司 Author: wangfeng Date: 2018/7/31 13:36 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.executor.forecast;

import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.constants.CacheConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.algorithm.dto.ForecastParam;
import com.tsintergy.lf.serviceapi.algorithm.dto.GeneralResult;
import com.tsintergy.lf.serviceapi.base.base.pojo.BaseLoadCityDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceimpl.executor.AbstractBaseExecutor;
import com.tsintergy.lf.serviceimpl.factory.LoadCityHisFileNameHealper;
import com.tsintergy.lf.serviceimpl.support.ALgorithmSupport;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

/**
 * 每日算法：相似日&偏差补偿&支持向量机&梯度提升&神经网络(原来的新息算法)&标点 通用执行器
 *
 * <AUTHOR>
 * @create 2020/9/3
 * @since 1.0.0
 */
@Slf4j
public class BaseMultiMethodExecutor extends AbstractBaseExecutor<ForecastParam, GeneralResult> {


    /**
     * 每日预测 拼接算法通用基础数据
     *
     * @param param 算法参数
     * @param datas 算法所需基础数据map
     * @param map 要写入 file in的数据map
     */
    @Override
    public void writeData(ForecastParam param, Map<String, Object> datas, Map<String, Object> map)
        throws Exception {
        map.put(AlgorithmConstants.FORECAST_START_DAY,
            DateUtils.date2String(param.getForecastDate(), DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR));
        map.put(AlgorithmConstants.FORECAST_END_DAY,
            DateUtils.date2String(param.getForecastEndDate(), DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR));
        map.put(AlgorithmConstants.ALGORITHM_TYPE, param.getAlgorithmEnum().getType());
        map.put(AlgorithmConstants.NEW_POINT, 0);
        map.put(AlgorithmConstants.CITYNAME, param.getCityName());
        map.put(AlgorithmConstants.START_WITH_ZERO, Constants.LOAD_CURVE_START_WITH_ZERO);
        //从map里获取历史气象数据
        List<WeatherCityHisDO> hisWeather = ALgorithmSupport
            .getListFromMap(datas.get(AlgorithmConstants.DATA_WEATHER_KEY), WeatherCityHisDO.class);
        //从map里获取气象特性数据
        List<WeatherFeatureCityDayHisDO> hisFeature = ALgorithmSupport.getListFromMap(datas
            .get(AlgorithmConstants.WEATHER_FEATURE_HIS), WeatherFeatureCityDayHisDO.class);
        //节假日数据
        List<HolidayDO> holidayDOS = ALgorithmSupport
            .getListFromMap(datas.get(AlgorithmConstants.HOLIDAY_INFO), HolidayDO.class);
        fetchHoliday(map, holidayDOS);
        //从map里获取历史负荷数据
        List<LoadCityHisDO> hisLoadDatas = ALgorithmSupport
            .getListFromMap(datas.get(AlgorithmConstants.HIS_LOAD), LoadCityHisDO.class);
        if (param.getFway() != null) {
            //预测方式 1 T+1 2 T+2  不为null，是实施页面调用
            param
                .setBaseDay(param.getFway() == 1 ? DateUtil.getMoveDay(param.getForecastDate(), -1) :
                    DateUtil.getMoveDay(param.getForecastDate(), -2));
            //如果该方式基准日没有完整数据  走动态获取到基准日的逻辑
            Map<Date, LoadCityHisDO> dateMap = hisLoadDatas.stream()
                .collect(Collectors.toMap(BaseLoadCityDO::getDate, e -> e, (oldV, curV) -> curV));
            LoadCityHisDO nowBaseDayLoad = dateMap.get(param.getBaseDay());
            if (nowBaseDayLoad == null || nowBaseDayLoad.getT2345() == null) {
                //动态获取到基准日的逻辑
                //基准日 不定死 取有完整数据的最近一天数据
                Date forecastStart = param.getForecastDate();
                List<LoadCityHisDO> loadCityHisVOS = hisLoadDatas.stream().filter(
                    loadCityHisVO -> loadCityHisVO.getDate().before(forecastStart))
                    .collect(Collectors.toList());
                Date baseDate;
                for (int i = 1; ; i++) {
                    if (loadCityHisVOS.get(loadCityHisVOS.size() - i).getT2345() != null) {
                        baseDate = hisLoadDatas.get(loadCityHisVOS.size() - i).getDate();
                        break;
                    }
                }
                param.setBaseDay(baseDate);
            }
        } else {
            //动态获取到基准日，取距离预测日最近的有完整的数据
            paramSetBaseDate(param, hisLoadDatas);
        }
        map.put(AlgorithmConstants.BASEDAY,
            DateUtils.date2String(param.getBaseDay(), DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR));
        fetchHisLoad(hisLoadDatas, param, map);
        fetchWeather(map, hisWeather, param, hisFeature);
        map.put(CacheConstants.START_WITH_ZERO, Constants.LOAD_CURVE_START_WITH_ZERO);
    }

    private void fetchHisLoad(List<LoadCityHisDO> hisVOS, ForecastParam param, Map<String, Object> map)
        throws Exception {
        Date baseDate = param.getBaseDay();
        List<LoadCityHisDO> hisLoad;
        if (param.getFway() != null) {
            //实施页面调用 方式选择T+1 hisload是待预测日前一天的整日负荷  方式选择T+2 ，hisload是待预测日的前一天对应的新息点数负荷
            hisLoad = hisVOS.stream().filter(
                loadCityHisVO -> loadCityHisVO.getDate().before(param.getForecastDate()) && loadCityHisVO
                    .getDate().after(DateUtil.getMoveDay(baseDate, -1200))).collect(Collectors.toList());
            if (param.getFway() == 2) {
                map.put(AlgorithmConstants.NEW_POINT, param.getPointNum());
                Map<Date, LoadCityHisDO> hisLoadMap = hisLoad.stream()
                    .collect(Collectors.toMap(e -> e.getDate(), e -> e, (oldv, curV) -> curV));
                LoadCityHisDO loadCityHisVO = hisLoadMap.get(DateUtil.getMoveDay(param.getForecastDate(), -1));
                //筛选出 新息点数 对应的该天负荷
                if (loadCityHisVO != null) {
                    List<BigDecimal> bigDecimals = BasePeriodUtils
                        .toList(loadCityHisVO, Constants.LOAD_CURVE_POINT_NUM,
                            Constants.LOAD_CURVE_START_WITH_ZERO);
                    List<BigDecimal> resultList = new ArrayList<>();
                    int i = 1;
                    for (BigDecimal bigDecimal : bigDecimals) {
                        if (i++ <= (param.getPointNum() == null ? 0 : param.getPointNum())) {
                            resultList.add(bigDecimal);
                        } else {
                            resultList.add(null);
                        }
                    }
                    BasePeriodUtils.setAllFiled(loadCityHisVO,
                        ColumnUtil.listToMap(resultList, Constants.LOAD_CURVE_START_WITH_ZERO));
                }
            }
            map.put(AlgorithmConstants.HIS_LOAD, hisLoad);
            return;
        }
        //综合模型获取400天 其他算法统一传1200天的负荷数据
        if (param.getAlgorithmEnum().getType().equals(AlgorithmEnum.COMPREHENSIVE_MODEL.getType())) {
            //模型融合 基准日（包含）前400天的数据
            hisVOS = hisVOS.stream().filter(
                loadCityHisVO -> loadCityHisVO.getDate().before(DateUtils.addDays(baseDate, 1)) && loadCityHisVO
                    .getDate().after(DateUtil.getMoveDay(baseDate, -400))).collect(Collectors.toList());
        }
        //其他算法统一传1200天负荷
        else {
            Date afterDate = DateUtils.addDays(baseDate, 1);
            putNewPoint(map, hisVOS, afterDate,
                AlgorithmEnum.SHORT_FORECAST.getType().equals(param.getAlgorithmEnum().getType()));
        }
        map.put(AlgorithmConstants.HIS_LOAD, hisVOS);
    }


    /**
     * 功能描述: <br> 计算新息点数
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    public void putNewPoint(Map<String, Object> map, List<LoadCityHisDO> hisLoad, Date afterDate,
        Boolean shortForecast) throws Exception {
        LoadCityHisDO hisVO = hisLoad.get(hisLoad.size() - 1);
        if (hisVO.getDate().equals(afterDate)) {
            List<BigDecimal> list = BasePeriodUtils
                .toList(hisVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
            //新息点数之前直接去除空，如果采集有空数据或者漏数据则会有问题
            // 新的方式是：超短期预测是从00点到现在的时刻点，都算新息点数，如果在这期间有null，则在file_in写NULL
            List<String> columns;
            if (shortForecast) {
                LoadCityHisFileNameHealper fileNameHealper = LoadCityHisFileNameHealper.newInstance();
                //获取临近现在时刻点的时间
                String timeStr = fileNameHealper.getTimeCode(new Date());
                String str = timeStr.substring(0, 3);
                columns = ColumnUtil.getColumnsBetween("0000", str, Constants.LOAD_CURVE_POINT_NUM,
                    Constants.LOAD_CURVE_START_WITH_ZERO, true);
            } else {
                columns = ColumnUtil.getColumnsBetween("0000", "2345", Constants.LOAD_CURVE_POINT_NUM,
                    Constants.LOAD_CURVE_START_WITH_ZERO, true);
            }
            int columnSize = columns.size();
            List<BigDecimal> dataSize = list.subList(0, columnSize);
            int index = 0;
            for (int i = 1; i < dataSize.size(); i++) {
                int data = dataSize.size() - i;
                BigDecimal value = dataSize.get(data);
                if (value != null) {
                    index = data + 1;
                    break;
                }
            }
            //算法新息点数最多为95；手动预测过去日期时存在信息点为96的情况；此处手动处理一下
            if (Constants.LOAD_CURVE_POINT_NUM == index) {
                index = index - 1;
            }
            map.put(AlgorithmConstants.NEW_POINT, index);
        }
    }

    private void fetchWeather(Map<String, Object> map, List<WeatherCityHisDO> hisWeather, ForecastParam param,
        List<WeatherFeatureCityDayHisDO> featureCityDayHisVOS) throws Exception {
        Date baseDate = param.getBaseDay();
        List<WeatherCityHisDO> humiditys = new ArrayList<>();
        List<WeatherCityHisDO> tempreatures = new ArrayList<>();
        List<WeatherCityHisDO> precipitation = new ArrayList<>();
        List<WeatherCityHisDO> wind = new ArrayList<>();
        List<WeatherFeatureCityDayHisDO> hisFeature = new ArrayList<>();
        if (CollectionUtils.isEmpty(hisWeather)) {
            map.put(AlgorithmConstants.TEMPERATURE_DATA, tempreatures);
            map.put(AlgorithmConstants.HUMIDITY_DATA, humiditys);
            map.put(AlgorithmConstants.PRECIPITATION_DATA, precipitation);
            map.put(AlgorithmConstants.WIND_DATA, wind);
            map.put(AlgorithmConstants.MAX_TEMPERATURE, hisFeature);
            return;
        }
        Map<Integer, List<WeatherCityHisDO>> weatherFeatureMap = hisWeather.stream()
            .collect(Collectors.groupingBy(WeatherCityHisDO::getType));
        List<WeatherCityHisDO> humidityList = weatherFeatureMap.get(WeatherEnum.HUMIDITY.getType()) == null ? humiditys
            : weatherFeatureMap.get(WeatherEnum.HUMIDITY.getType());
        List<WeatherCityHisDO> temList = weatherFeatureMap.get(WeatherEnum.TEMPERATURE.getType()) == null ? tempreatures
            : weatherFeatureMap.get(WeatherEnum.TEMPERATURE.getType());
        List<WeatherCityHisDO> preList = weatherFeatureMap.get(WeatherEnum.RAINFALL.getType()) == null ? precipitation
            : weatherFeatureMap.get(WeatherEnum.RAINFALL.getType());
        List<WeatherCityHisDO> windList = weatherFeatureMap.get(WeatherEnum.WINDSPEED.getType()) == null ? wind
            : weatherFeatureMap.get(WeatherEnum.WINDSPEED.getType());
        Date date = getDate(param);
        support(map, hisWeather, param, featureCityDayHisVOS, baseDate, humiditys, tempreatures, precipitation, wind,
            hisFeature, humidityList, temList, preList, windList, date);
    }

    private void support(Map<String, Object> map, List<WeatherCityHisDO> hisWeather, ForecastParam param,
        List<WeatherFeatureCityDayHisDO> featureCityDayHisVOS, Date baseDate,
        List<WeatherCityHisDO> humiditys, List<WeatherCityHisDO> tempreatures,
        List<WeatherCityHisDO> precipitation, List<WeatherCityHisDO> wind,
        List<WeatherFeatureCityDayHisDO> hisFeature, List<WeatherCityHisDO> humidityList,
        List<WeatherCityHisDO> temList, List<WeatherCityHisDO> preList,
        List<WeatherCityHisDO> windList, Date forecastDay) {
        Date starDay = DateUtils.addDays(baseDate, -1200);

        if (!CollectionUtils.isEmpty(hisWeather)) {
            if (!CollectionUtils.isEmpty(humidityList)) {
                humiditys = humidityList.stream()
                    .filter(WeatherCityHisDO -> WeatherCityHisDO.getDate().before(DateUtils.addDays(forecastDay,
                        1)))
                    .collect(Collectors.toList());
            }

            if (!CollectionUtils.isEmpty(temList)) {
                tempreatures = temList.stream()
                    .filter(WeatherCityHisDO -> WeatherCityHisDO.getDate().before(DateUtils.addDays(forecastDay,
                        1)))
                    .collect(Collectors.toList());
            }

            if (!CollectionUtils.isEmpty(preList)) {
                precipitation = preList.stream()
                    .filter(WeatherCityHisDO -> WeatherCityHisDO.getDate().before(DateUtils.addDays(forecastDay,
                        1)))
                    .collect(Collectors.toList());
            }

            if (!CollectionUtils.isEmpty(windList)) {
                wind = windList.stream()
                    .filter(WeatherCityHisDO -> WeatherCityHisDO.getDate().before(DateUtils.addDays(forecastDay,
                        1)))
                    .collect(Collectors.toList());
            }
        }
        //手动预测
        if (ForecastParam.FORECAST_TYPE.equals(param.getType())) {
            for (WeatherFeatureCityDayHisDO vo : featureCityDayHisVOS) {
                if (vo.getDate().after(DateUtil.getMoveDay(starDay, -1)) && vo.getDate()
                    .before(DateUtil.getMoveDay(forecastDay, 1))) {
                    //剔除 用户未选择的训练气象
                    if (!param.getWeatherType().contains(WeatherEnum.HUMIDITY.getType())) {
                        vo.setAveHumidity(null);
                    }
                    if (!param.getWeatherType().contains(WeatherEnum.TEMPERATURE.getType())) {
                        vo.setAveTemperature(null);
                        vo.setHighestTemperature(null);
                        vo.setLowestTemperature(null);
                    }
                    if (!param.getWeatherType().contains(WeatherEnum.RAINFALL.getType())) {
                        vo.setRainfall(null);
                    }
                    if (!param.getWeatherType().contains(WeatherEnum.WINDSPEED.getType())) {
                        vo.setAveWinds(null);
                    }
                    hisFeature.add(vo);
                }
            }
        } else {
            //自动预测
            for (WeatherFeatureCityDayHisDO vo : featureCityDayHisVOS) {
                if (vo.getDate().after(DateUtil.getMoveDay(starDay, -1)) && vo.getDate()
                    .before(DateUtil.getMoveDay(forecastDay, 1))) {
                    hisFeature.add(vo);
                }
            }
        }
        map.put(AlgorithmConstants.TEMPERATURE_DATA, tempreatures);
        map.put(AlgorithmConstants.HUMIDITY_DATA, humiditys);
        map.put(AlgorithmConstants.PRECIPITATION_DATA, precipitation);
        map.put(AlgorithmConstants.WIND_DATA, wind);
        map.put(AlgorithmConstants.MAX_TEMPERATURE, hisFeature);
    }

    private List<WeatherCityHisDO> mergeWeather(List<WeatherCityHisDO> hisWeathers, Date forecastDay,
        Date lastYearStartDate, Date lastYearEndDate, Date startBaseDay) {
        List<WeatherCityHisDO> hisVOS = new ArrayList<>();
        for (WeatherCityHisDO cityHisVO : hisWeathers) {
            Date date = cityHisVO.getDate();
            if (isBetweenDay(date, lastYearStartDate, lastYearEndDate) || isBetweenDay(date, startBaseDay,
                forecastDay)) {
                hisVOS.add(cityHisVO);
            }
        }
        return hisVOS;
    }

    private boolean isBetweenDay(Date date, Date startDate, Date endDate) {
        return date.after(DateUtils.addDays(startDate, -1)) && date.before(DateUtils.addDays(endDate, 1));
    }

    protected Date getDate(ForecastParam param) {
        Date date;
        Date forecastDay = param.getForecastDate();
        Date forecastEndDate = param.getForecastEndDate();
        if (forecastDay.equals(forecastEndDate)) {
            date = param.getForecastDate();
        } else {
            date = param.getForecastEndDate();
        }
        return date;
    }

    private void paramSetBaseDate(ForecastParam forecastParam, List<LoadCityHisDO> hisLoadDatas) {
        Date forecastStart = forecastParam.getForecastDate();
        List<LoadCityHisDO> loadCityHisDOS = hisLoadDatas.stream().filter(
            loadCityHisDO -> loadCityHisDO.getDate().before(forecastStart))
            .collect(Collectors.toList());
        Date baseDate;
        //基准日 手动预测时  是前端传的 其他情况为  取有完整数据的最近一天数据
        if (!ForecastParam.FORECAST_TYPE.equals(forecastParam.getType())) {
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(loadCityHisDOS)) {
                //基准日 不定死 取有完整数据的最近一天数据
                for (int i = 1; ; i++) {
                    if (loadCityHisDOS.get(loadCityHisDOS.size() - i).getT2345() != null) {
                        baseDate = loadCityHisDOS.get(loadCityHisDOS.size() - i).getDate();
                        break;
                    }
                }
                forecastParam.setBaseDay(baseDate);
            }
        }
    }
}