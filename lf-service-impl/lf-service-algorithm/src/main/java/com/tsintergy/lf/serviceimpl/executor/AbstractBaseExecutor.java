/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangchen Date:  2019/7/9 13:36 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.executor;

import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsintergy.algorithm.alginv.core.configure.properties.AlginvProperties;
import com.tsintergy.algorithm.alginv.core.console.ConsoleCmdOption;
import com.tsintergy.algorithm.alginv.serviceimpl.client.AlgorithmClient;
import com.tsintergy.algorithm.alginv.serviceimpl.client.parameter.ClientInvokeParameters;
import com.tsintergy.algorithm.alginv.serviceimpl.client.parameter.SimpleClientInvokeParametersBuilder;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.algorithm.api.Executor;
import com.tsintergy.lf.serviceapi.algorithm.dto.HolidayInfo;
import com.tsintergy.lf.serviceapi.algorithm.dto.Param;
import com.tsintergy.lf.serviceapi.algorithm.dto.Result;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceimpl.support.ALgorithmSupport;
import com.tsintergy.lf.serviceimpl.support.FilePathSupport;
import com.tsintergy.lf.serviceimpl.support.FreemarkUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019/7/9
 * @since 1.0.0
 */
@Slf4j
public abstract class AbstractBaseExecutor<E extends Param, T extends Result> implements Executor<E, T> {

    @Autowired
    private AlginvProperties alginvProperties;

    @Autowired
    private AlgorithmClient algorithmClient;

    @Override
    public void execute(E param) throws BusinessException, IOException, InterruptedException {
        SimpleClientInvokeParametersBuilder simpleClientInvokeParametersBuilder = ClientInvokeParameters
                .compatibleBuilder();
        String outPath = FilePathSupport.getInAbsoluteFile(param, param.getAlgorithmDetail().getOut());
        ClientInvokeParameters parameters =
                simpleClientInvokeParametersBuilder.caseId(ALgorithmSupport.getUUID())
                        .actionId(ALgorithmSupport.getUUID())
                        .algorithmExeName(FilePathSupport.getBasePath(param.getAlgorithmDetail().getRun()) + FilePathSupport
                                .getBasePath(param.getAlgorithmDetail().getExeName()))
                        .caseInputDir(FilePathSupport.getInAbsoluteFile(param, param.getAlgorithmDetail().getIn()))
                        .caseOutputDir(outPath)
                        .algorithmType(param.getAlgorithmEnum().getType())
                        .ignoreConsoleResult(true)
                        //实时返回时设置成同步
                        .async(param.getAlgorithmDetail().getAsync())
                        .build();
        Map<String, String> map = new HashMap<>(16);
        //补充cmd命令：装载算法授权文件所在路径
        fillCmdOptio(param, simpleClientInvokeParametersBuilder);
        //装载算法回调需要的补充参数；
        fillBackParam(param, map);
        parameters.setParameters(map);
        ALgorithmSupport.mkdir(alginvProperties.getCaseBaseDir() + outPath);
        algorithmClient.invoke(parameters);
    }


    /**
     * STEP1： 执行预处理 默认方法
     *
     * @param param 算法所用参数
     * @param datas 算法所用的数据
     */
    @Override
    public void preprocess(E param, Map<String, Object> datas) throws BusinessException {
        Map<String, Object> map = new HashMap<>(16);
        try {
            //1.将ipml中查询的数据与file in模板中的标签一一对应后放入map
            writeData(param, datas, map);
            //2.把处理过的map数据写入file in文件
            writeFile(param, param.getAlgorithmDetail().getIn(), param.getAlgorithmDetail().getTemplateName(), map);
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("算法输入文件写入错误", e);
        }
    }

    /**
     * step1.1 装载file in所需数据
     *
     * @param param 算法所用参数
     * @param datas 算法所用的数据
     * @param map   file in所需数据
     */
    public abstract void writeData(E param, Map<String, Object> datas, Map<String, Object> map)
            throws Exception;


    /**
     * step1.2 生成file in 文件
     *
     * @param param    算法参数
     * @param inPath   in路径
     * @param template 算法template名
     * @param map      数据map
     */
    public void writeFile(E param, String inPath, String template, Map<String, Object> map) throws Exception {
        String filePath;
        filePath = alginvProperties.getCaseBaseDir() + FilePathSupport.getInAbsoluteFile(param, inPath);
        FreemarkUtils utils = new FreemarkUtils();
        utils.outputEfile(map, filePath, template);
    }


    /**
     * 填充节假日接口
     *
     * @param map        file in 数据map
     * @param holidayDOS 节假日列表
     */
    protected void fetchHoliday(Map<String, Object> map, List<HolidayDO> holidayDOS) throws Exception {
        Map<String, Object> mapData = ALgorithmSupport.getHolidayInfo(holidayDOS);
        List<String> offDates = ALgorithmSupport.getListFromMap(mapData.get(AlgorithmConstants.OFFDATES), String.class);
        List<HolidayInfo> holidayInfos = ALgorithmSupport
                .getListFromMap(mapData.get(AlgorithmConstants.HOLIDAY_INFO), HolidayInfo.class);
        map.put(AlgorithmConstants.OFFDATES, offDates);
        map.put(AlgorithmConstants.HOLIDAY_INFO, holidayInfos);
    }

    /**
     * 【通用】算法回调的补充参数，如需其他参数，子类自行补充。例如：ShortForecastExecutor
     *
     * @param param           算法Param对象
     * @param paramInCallBack 算法Client中需要的map对象
     */
    public void fillBackParam(E param, Map<String, String> paramInCallBack) {
        paramInCallBack.put(AlgorithmConstants.CITY_ID, param.getCityId());
        paramInCallBack.put(AlgorithmConstants.CALIBER_ID, param.getCaliberId());
        paramInCallBack.put(AlgorithmConstants.ALGORITHM_ID, param.getAlgorithmEnum().getId());
        paramInCallBack.put(AlgorithmConstants.FORECAST_CYCLIC_TYPE, String.valueOf(param.getForecastType()));
        paramInCallBack.put(AlgorithmConstants.USER_ID, param.getUserId());
        paramInCallBack.put(Constants.IS_RECALL, param.getRecall());
    }

    /**
     * 【通用】算法拼接授权文件所在路径，如需其他命令，子类自行补充。例如：ShortForecastExecutor
     *
     * @param param                               算法Param对象
     * @param simpleClientInvokeParametersBuilder 算法Client参数构建对象
     */
    public void fillCmdOptio(E param, SimpleClientInvokeParametersBuilder simpleClientInvokeParametersBuilder) {
        if (param.getAlgorithmDetail().getAuthorization()) {
            List<ConsoleCmdOption> consoleCmdOptions = ALgorithmSupport
                    .addConsoleCmdOption(FilePathSupport.getBasePath(AlgorithmConstants.LICENCE_PATH));
            for (ConsoleCmdOption consoleCmdOption : consoleCmdOptions) {
                simpleClientInvokeParametersBuilder.addOption(consoleCmdOption);
            }
        }
    }
}