/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.impl;

import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.algorithm.dto.ForecastParam;
import com.tsintergy.lf.serviceapi.algorithm.dto.GeneralRecallResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description: 预测回溯 <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/2 11:06
 * @Version: 1.0.0
 */

@Service("forecastRecallService")
public class ForecastRecallService extends AbstractForecastService<ForecastParam, GeneralRecallResult> {


    @Autowired
    private Forecastor forecastor;

    @Override
    public void forecast(ForecastParam param) throws Exception {
        param.setRecall(Constants.IS_RECALL);
        forecastor.forecast(param);
    }

}