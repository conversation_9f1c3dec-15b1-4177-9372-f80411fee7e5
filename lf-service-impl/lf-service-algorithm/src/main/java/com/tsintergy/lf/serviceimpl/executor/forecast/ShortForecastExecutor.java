/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司 Author: wangfeng Date: 2018/7/31 13:36 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.executor.forecast;

import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.algorithm.alginv.core.console.ConsoleCmdOption;
import com.tsintergy.algorithm.alginv.serviceimpl.client.parameter.SimpleClientInvokeParametersBuilder;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.constants.CacheConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.ShortConstants;
import com.tsintergy.lf.serviceapi.algorithm.dto.Result;
import com.tsintergy.lf.serviceapi.algorithm.dto.ShortForecastParam;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHis288DO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceimpl.executor.AbstractBaseExecutor;
import com.tsintergy.lf.serviceimpl.support.ALgorithmSupport;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

/**
 * 非每日算法：超短期 SHORT_FORECAST 算法执行器
 *
 * <AUTHOR>
 * @create 2020/6/16
 * @since 1.0.0
 */
@Slf4j
public class ShortForecastExecutor extends AbstractBaseExecutor<ShortForecastParam, Result> {


    @Override
    public void fillCmdOptio(ShortForecastParam param,
        SimpleClientInvokeParametersBuilder simpleClientInvokeParametersBuilder) {
        super.fillCmdOptio(param, simpleClientInvokeParametersBuilder);
        //补充超短期命令
        List<ConsoleCmdOption> consoleCmdOptions = ALgorithmSupport
            .addConsoleCmdShortOption(param);
        for (ConsoleCmdOption consoleCmdOption : consoleCmdOptions) {
            simpleClientInvokeParametersBuilder.addOption(consoleCmdOption);
        }
    }

    @Override
    public void fillBackParam(ShortForecastParam param, Map<String, String> paramInCallBack) {
        super.fillBackParam(param, paramInCallBack);
        //回调使用；用于超短期算法拼接file in &out的时间前缀以命中文件
        paramInCallBack.put("startTimePoint", param.getStartTimePoint());
        paramInCallBack.put("timeSpan", param.getTimeSpan());
        paramInCallBack.put("startTimeStr", param.getStartTimeStr());
    }

    @Override
    public void writeData(ShortForecastParam forecastParam, Map<String, Object> datas, Map<String, Object> map)
        throws Exception {
        map.put(AlgorithmConstants.FORECAST_BEGIN_DAY,
            DateUtils.date2String(forecastParam.getForecastDate(), DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR));
        map.put(AlgorithmConstants.TIME_SPAN, forecastParam.getTimeSpan());
        //算法的起始点从1开始；程序中从0开始
        map.put(AlgorithmConstants.FORECAST_BEGIN_POINT,
            String.valueOf(Integer.valueOf(forecastParam.getStartTimePoint()) + 1));
        map.put(AlgorithmConstants.FORECAST_POINT, forecastParam.getForecastPoint());
        if (ShortConstants.MINUTE.equals(Integer.valueOf(forecastParam.getTimeSpan()))) {
            List<LoadCityHis288DO> hisLoadDatas = ALgorithmSupport
                .getListFromMap(datas.get(AlgorithmConstants.HIS_LOAD), LoadCityHis288DO.class);
            map.put(AlgorithmConstants.HIS_LOAD, hisLoadDatas);
        } else {
            //从map里获取96点历史负荷数据
            List<LoadCityHisDO> hisLoadDatas = ALgorithmSupport
                .getListFromMap(datas.get(AlgorithmConstants.HIS_LOAD), LoadCityHisDO.class);
            map.put(AlgorithmConstants.HIS_LOAD, hisLoadDatas);
        }
        map.put(CacheConstants.START_WITH_ZERO, Constants.LOAD_CURVE_START_WITH_ZERO);
    }

    /**
     * 生成file in 文件
     *
     * @param param 算法参数
     * @param inPath in路径
     * @param template 算法template名
     * @param map 数据map
     */
    @Override
    public void writeFile(ShortForecastParam param, String inPath, String template, Map<String, Object> map)
        throws Exception {
        //超短期算法模板区分为96点模板&288点模板
        super.writeFile(param, inPath, ShortConstants.MINUTE.equals(Integer.valueOf(param.getTimeSpan()))
            ? AlgorithmConstants.SHORT_TEMPLATE_288_NAME : AlgorithmConstants.SHORT_TEMPLATE_96_NAME, map);

    }
}
