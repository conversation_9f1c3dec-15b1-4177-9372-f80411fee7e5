/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司 Author: liufeng Date: 2018/6/6 19:26 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.impl;


import com.tsieframework.cloud.security.serviceapi.system.api.RedisService;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.constants.CacheConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.ParamConstants;
import com.tsintergy.lf.core.enums.CompositeDayEnum;
import com.tsintergy.lf.core.enums.CompositeLoadEnum;
import com.tsintergy.lf.core.enums.CompositeWeatherEnum;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.SimilarParam;
import com.tsintergy.lf.serviceapi.algorithm.dto.SimilarResult;
import com.tsintergy.lf.serviceapi.base.analyze.dto.WeatherElement;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceimpl.executor.analysis.SimilarDayExecutor;
import com.tsintergy.lf.serviceimpl.support.ALgorithmSupport;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 综合相似日算法
 *
 * <AUTHOR>
 * @since 1.0.0
 */

@Service(value = "similarDayForecast")
public class SimilarDayForecastImpl extends AbstractForecastService<SimilarParam, SimilarResult> {

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Autowired
    private WeatherCityFcService weatherCityFcService;

    @Autowired
    private CityService cityService;

    @Autowired
    private LoadCityFcService loadCityFcService;

    @Autowired
    public RedisService redisService;

    private static final Logger logger = LoggerFactory.getLogger(SimilarDayForecastImpl.class);

    @Override
    public void mergeData(SimilarParam param, Map<String, Object> srcMap) throws Exception {
        String cityId = param.getCityId();
        String weatherCityId = cityService.findWeatherCityId(cityId);
        List<WeatherElement> elements = param.getElements();
        Date basicDate = param.getDate();
        List<Integer> loadSearch = param.getLoadElements();
        //历史数据
        List<LoadCityHisDO> targetPower = loadCityHisService
            .getLoadCityHisDOS(cityId, param.getCaliberId(), param.getStartDate(),
                param.getEndDate());
        List<Date> dateList = DateUtil.getListBetweenDay(param.getStartDate(),
            param.getEndDate());
        List<WeatherCityHisDO> tempData = weatherCityHisService
            .findWeatherByDates(weatherCityId,
                WeatherEnum.TEMPERATURE.getType(), dateList);
        List<WeatherCityHisDO> rainData = weatherCityHisService
            .findWeatherByDates(weatherCityId, WeatherEnum.RAINFALL.getType(), dateList);
        List<WeatherCityHisDO> humidData = weatherCityHisService
            .findWeatherByDates(weatherCityId, WeatherEnum.HUMIDITY.getType(), dateList);
        if (elements != null) {
            this.mergeRainData(rainData, elements, srcMap, basicDate, weatherCityId);
            this.mergeTempData(tempData, elements, srcMap, basicDate, weatherCityId);
            this.mergeHumidData(humidData, elements, srcMap, basicDate, weatherCityId);
        }
        //负荷选择
        for (Integer loadDayType : loadSearch) {
            srcMap.put(CompositeLoadEnum.getTypeName(loadDayType), 1);
            //自动触发时最新一天填充预测数据
            if (ParamConstants.STRING_COMPOSITE_ON.equals(param.getNewestLoad())) {
                LoadCityFcDO reportLoadCityFcVO = loadCityFcService
                    .getLoadCityFc(cityId, param.getCaliberId(), DateUtil.getMoveDay(basicDate, -loadDayType),
                        DateUtil.getMoveDay(basicDate, -loadDayType), null);
                LoadCityHisDO loadCityHisVO = new LoadCityHisDO();
                BeanUtils.copyProperties(reportLoadCityFcVO, loadCityHisVO);
                targetPower.add(loadCityHisVO);
            } else {
                List<LoadCityHisDO> powerLoadHisCity = loadCityHisService
                    .find24LoadCityHisDO(DateUtil.getMoveDay(basicDate, -loadDayType), cityId, param.getCaliberId());
                targetPower.addAll(powerLoadHisCity);
            }
        }
        targetPower.removeAll(Collections.singleton(null));
        srcMap.put(AlgorithmConstants.ModelType, param.getCompute());
        srcMap.put(AlgorithmConstants.SIMILAR_DAY_NUM, param.getPageSize());
        srcMap.put(AlgorithmConstants.HISTORY_LOAD, targetPower);
        srcMap.put(AlgorithmConstants.TEMPERATURE, tempData);
        srcMap.put(AlgorithmConstants.PRECIPITATION, rainData);
        srcMap.put(AlgorithmConstants.HUMIDITY, humidData);
    }

    /**
     * 处理预测/实际降雨
     *
     * @param srcDatas 近两年的降雨数据
     * @param elements 今日/前一天/前两天 预测/实际降雨参数
     * <AUTHOR>
     */
    private void mergeRainData(List<WeatherCityHisDO> srcDatas,
        List<WeatherElement> elements, Map<String, Object> datas, Date basicDate, String cityId) throws Exception {
        for (WeatherElement element : elements) {
            for (Integer type : element.getWeatherNorm()) {
                //是预测/实际降雨条件
                if (type <= 1) {
                    String templateName =
                        CompositeDayEnum.getTypeName(element.getDayType()) + CompositeWeatherEnum.getTypeName(type
                        );
                    datas.put(templateName, AlgorithmConstants.CompositeOn);
                    //实际降雨 填充该天的实际降雨数据到 srcDatas
                    if (CompositeWeatherEnum.HIS_RAIN.getType().equals(type)) {
                        List<WeatherCityHisDO> basicDayRainData = weatherCityHisService
                            .findWeatherCityHisDOSBySQLDate(cityId, WeatherEnum.RAINFALL.getType(),
                                new java.sql.Date(DateUtil.getMoveDay(basicDate, -element.getDayType()).getTime()),
                                new java.sql.Date(DateUtil.getMoveDay(basicDate, -element.getDayType()).getTime()));
                        if (basicDayRainData.size() != 0) {
                            srcDatas.add(basicDayRainData.get(0));
                        }
                    }
                    if (CompositeWeatherEnum.FC_RAIN.getType().equals(type)) {
                        List<WeatherCityFcDO> basicDayRainData = weatherCityFcService
                            .findWeatherCityFcDOs(cityId, WeatherEnum.RAINFALL.getType(),
                                DateUtil.getMoveDay(basicDate, -element.getDayType()),
                                DateUtil.getMoveDay(basicDate, -element.getDayType())
                            );
                        if (!CollectionUtils.isEmpty(basicDayRainData)) {
                            WeatherCityHisDO weatherCity = new WeatherCityHisDO();
                            BeanUtils.copyProperties(basicDayRainData.get(0), weatherCity);
                            srcDatas.add(weatherCity);
                        }
                    }
                }
            }
        }
    }

    /**
     * 处理预测/实际湿度
     *
     * @param srcDatas 近两年的湿度数据
     * @param elements 今日/前一天/前两天 预测/实际湿度参数
     * <AUTHOR>
     */
    private void mergeHumidData(List<WeatherCityHisDO> srcDatas,
        List<WeatherElement> elements, Map<String, Object> datas, Date basicDate, String cityId) throws Exception {
        for (WeatherElement element : elements) {
            for (Integer type : element.getWeatherNorm()) {
                //是预测/实际湿度条件
                if (type > 3) {
                    String templateName =
                        CompositeDayEnum.getTypeName(element.getDayType()) + CompositeWeatherEnum.getTypeName(type
                        );
                    datas.put(templateName, AlgorithmConstants.CompositeOn);
                    //实际湿度 填充该天的实际降雨数据到 srcDatas
                    if (CompositeWeatherEnum.HIS_HUMID.getType().equals(type)) {
                        List<WeatherCityHisDO> basicDayRainData = weatherCityHisService
                            .findWeatherCityHisDOSBySQLDate(cityId, WeatherEnum.HUMIDITY.getType(),
                                new java.sql.Date(DateUtil.getMoveDay(basicDate, -element.getDayType()).getTime()),
                                new java.sql.Date(DateUtil.getMoveDay(basicDate, -element.getDayType()).getTime()));
                        if (basicDayRainData.size() != 0) {
                            srcDatas.add(basicDayRainData.get(0));
                        }
                    }
                    if (CompositeWeatherEnum.FC_HUMID.getType().equals(type)) {
                        List<WeatherCityFcDO> basicDayRainData = weatherCityFcService
                            .findWeatherCityFcDOs(cityId, WeatherEnum.HUMIDITY.getType(),
                                DateUtil.getMoveDay(basicDate, -element.getDayType()),
                                DateUtil.getMoveDay(basicDate, -element.getDayType())
                            );
                        if (!CollectionUtils.isEmpty(basicDayRainData)) {
                            WeatherCityHisDO weatherCity = new WeatherCityHisDO();
                            BeanUtils.copyProperties(basicDayRainData.get(0), weatherCity);
                            srcDatas.add(weatherCity);
                        }
                    }
                }
            }
        }
    }

    /**
     * 处理预测/实际温度
     *
     * @param srcDatas 近两年的温度数据
     * @param elements 今日/前一天/前两天 预测/实际温度参数
     * <AUTHOR>
     */
    private void mergeTempData(List<WeatherCityHisDO> srcDatas,
        List<WeatherElement> elements, Map<String, Object> datas, Date basicDate, String cityId) throws Exception {
        for (WeatherElement element : elements) {
            for (Integer type : element.getWeatherNorm()) {
                //是预测/实际温度条件
                if (type > 1 && type < 4) {
                    String templateName =
                        CompositeDayEnum.getTypeName(element.getDayType()) + CompositeWeatherEnum.getTypeName(type
                        );
                    datas.put(templateName, AlgorithmConstants.CompositeOn);
                    //实际降雨 填充该天的实际降雨数据到 srcDatas
                    if (CompositeWeatherEnum.HIS_TEMP.getType().equals(type)) {
                        List<WeatherCityHisDO> basicDayRainData = weatherCityHisService
                            .findWeatherCityHisDOSBySQLDate(cityId, WeatherEnum.TEMPERATURE.getType(),
                                new java.sql.Date(DateUtil.getMoveDay(basicDate, -element.getDayType()).getTime()),
                                new java.sql.Date(DateUtil.getMoveDay(basicDate, -element.getDayType()).getTime()));
                        if (basicDayRainData.size() != 0) {
                            srcDatas.add(basicDayRainData.get(0));
                        }
                    }
                    if (CompositeWeatherEnum.FC_TEMP.getType().equals(type)) {
                        List<WeatherCityFcDO> basicDayRainData = weatherCityFcService
                            .findWeatherCityFcDOs(cityId, WeatherEnum.TEMPERATURE.getType(),
                                DateUtil.getMoveDay(basicDate, -element.getDayType()),
                                DateUtil.getMoveDay(basicDate, -element.getDayType())
                            );
                        if (!CollectionUtils.isEmpty(basicDayRainData)) {
                            WeatherCityHisDO weatherCity = new WeatherCityHisDO();
                            BeanUtils.copyProperties(basicDayRainData.get(0), weatherCity);
                            srcDatas.add(weatherCity);
                        }
                    }
                }
            }
        }
    }

    @Override
    public void resolverResult(String tarPath, Map<String, String> supplementData) throws Exception {
        try{
            SimilarResult similarResult = SimilarDayExecutor
                    .parseSimilar(ALgorithmSupport.parseOutEFile(tarPath));
            redisService.redisAdd(supplementData.get(AlgorithmConstants.USER_ID) + Constants.SEPARATOR_BROKEN_LINE
                    + CacheConstants.CACHE_SIMILARRESULT_KEY, similarResult);
        }catch (Exception e){
            logger.error("算法输出文件不存在!" + e);
            redisService.redisAdd(supplementData.get(AlgorithmConstants.USER_ID) + Constants.SEPARATOR_BROKEN_LINE
                    + CacheConstants.CACHE_SIMILARRESULT_KEY, null);
        }
    }
}
