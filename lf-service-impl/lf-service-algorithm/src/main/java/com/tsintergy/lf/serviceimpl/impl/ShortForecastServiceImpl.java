/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司 Author: liufeng Date: 2018/6/6 19:26 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.impl;

import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.ShortConstants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.algorithm.dto.Result;
import com.tsintergy.lf.serviceapi.algorithm.dto.ShortForecastParam;
import com.tsintergy.lf.serviceapi.base.forecast.api.ForecastDataService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFc288DO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityFcShortService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityFcShortDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHis288DO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemData;
import com.tsintergy.lf.serviceimpl.support.ALgorithmSupport;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import net.sf.cglib.beans.BeanMap;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 节假日预测算法 逻辑实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service(value = "shortForecastService")
public class ShortForecastServiceImpl extends AbstractForecastService<ShortForecastParam, Result> {

    @Autowired
    private LoadCityFcService loadCityFcService;

    @Autowired
    private LoadCityFcShortService loadCityFcShortService;

    @Autowired
    private SettingSystemService systemService;

    @Autowired
    private ForecastDataService algorithmForecastDataService;

    @Override
    public void mergeData(ShortForecastParam param, Map<String, Object> dataMap) throws Exception {
        if (String.valueOf(ShortConstants.MINUTE).equals(param.getTimeSpan())) {
            pointHisData288(dataMap, param.getCityId(), param.getCaliberId(), param.getForecastDate(),
                Integer.valueOf(param.getStartTimePoint()));
        } else {
            pointHisData96(dataMap, param.getCityId(), param.getCaliberId(), param.getForecastDate(),
                Integer.valueOf(param.getStartTimePoint()));
        }
    }



    /**
     * 5分钟间隔 构造96点历史数据对象
     *
     * @param datas 数据map
     * @param cityId 城市id
     * @param caliberId 口径id
     * @param startFcDay 开始时间
     * @param timePoint 预测起始点数
     * <AUTHOR>
     */
    private void pointHisData288(Map<String, Object> datas, String cityId, String caliberId, Date startFcDay,
        int timePoint) throws Exception {
        //查询起始日前30天的历史负荷 不包含当天
        Date lastMonthDay = DateUtil.getMoveDay(startFcDay, -31);
        List<LoadCityHis288DO> resultList = new ArrayList<>();
        List<LoadCityHis288DO> lastMonthHisDOS = algorithmForecastDataService.findHis288Load(cityId, caliberId,
            lastMonthDay, DateUtil.getMoveDay(startFcDay, -1));
        resultList.addAll(lastMonthHisDOS);
        Date lastYearDay = DateUtils.addYears(startFcDay, -1);
        //查询去年同期30天的历史负荷数据
        List<LoadCityHis288DO> lastYearHisDOS = algorithmForecastDataService.findHis288Load(cityId, caliberId,
            DateUtil.getMoveDay(lastYearDay, -15), DateUtil.getMoveDay(lastYearDay, 15));
        resultList.addAll(lastYearHisDOS);
        //查询当天的最新采集的数据
        List<LoadCityHis288DO> todayDOS = algorithmForecastDataService.findHis288Load(cityId, caliberId,
            startFcDay, startFcDay);
        if (CollectionUtils.isEmpty(todayDOS)) {
            throw TsieExceptionUtils.newBusinessException("算法执行失败，历史负荷数据为空");
        }
        List<BigDecimal> zeroOrNullList = ColumnUtil.getZeroOrNullList(288, null);
        List<BigDecimal> bigDecimals = BasePeriodUtils.toList(todayDOS.get(0),
            288, Constants.WEATHER_CURVE_START_WITH_ZERO);
        for (int i = 0; i < timePoint; i++) {
            zeroOrNullList.set(i, bigDecimals.get(i));
        }
        LoadCityHis288DO today = new LoadCityHis288DO();
        Map<String, BigDecimal> dataMap = ColumnUtil
            .listToMap(zeroOrNullList, Constants.LOAD_CURVE_START_WITH_ZERO);
        BasePeriodUtils.setAllFiled(today, dataMap);
        today.setDate(new java.sql.Date(startFcDay.getTime()));
        resultList.add(today);
        if (CollectionUtils.isEmpty(resultList)) {
            log.error("城市id:{}算法执行失败，历史负荷数据为空", cityId);
            throw TsieExceptionUtils.newBusinessException("算法执行失败，历史负荷数据为空");
        }
        datas.put(AlgorithmConstants.HIS_LOAD, resultList);
    }

    /**
     * 15分钟间隔 构造96点历史数据对象
     *
     * @param datas 数据map
     * @param cityId 城市id
     * @param caliberId 口径id
     * @param startFcDay 开始时间
     * @param timePoint 预测起始点数
     * <AUTHOR>
     */
    private void pointHisData96(Map<String, Object> datas, String cityId, String caliberId, Date startFcDay,
        int timePoint) throws Exception {
        //查询起始日前30天的历史负荷 不包含当天
        Date lastMonthDay = DateUtil.getMoveDay(startFcDay, -31);
        List<LoadCityHisDO> resultList = new ArrayList<>();
        List<LoadCityHisDO> lastMonthHisDOS = algorithmForecastDataService.findHisLoad(cityId, caliberId,
            lastMonthDay, DateUtil.getMoveDay(startFcDay, -1));
        resultList.addAll(lastMonthHisDOS);
        Date lastYearDay = DateUtils.addYears(startFcDay, -1);
        //查询去年同期30天的历史负荷数据
        List<LoadCityHisDO> lastYearHisDOS = algorithmForecastDataService.findHisLoad(cityId, caliberId,
            DateUtil.getMoveDay(lastYearDay, -15), DateUtil.getMoveDay(lastYearDay, 15));
        resultList.addAll(lastYearHisDOS);
        //查询当天的最新采集的数据
        List<LoadCityHisDO> todayDOS = algorithmForecastDataService.findHisLoad(cityId, caliberId,
            startFcDay, startFcDay);
        List<BigDecimal> bigDecimals = BasePeriodUtils.toList(todayDOS.get(0),
            Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO);
        List<BigDecimal> zeroOrNullList = ColumnUtil.getZeroOrNullList(96, null);
        for (int i = 0; i < timePoint; i++) {
            zeroOrNullList.set(i, bigDecimals.get(i));
        }
        Map<String, BigDecimal> dataMap = ColumnUtil
            .listToMap(zeroOrNullList, Constants.LOAD_CURVE_START_WITH_ZERO);
        LoadCityHisDO today = new LoadCityHisDO();
        today.setDate(new java.sql.Date(startFcDay.getTime()));
        BasePeriodUtils.setAllFiled(today, dataMap);
        resultList.add(today);
        if (CollectionUtils.isEmpty(resultList)) {
            log.error("城市id:{}算法执行失败，历史负荷数据为空", cityId);
            throw TsieExceptionUtils.newBusinessException("算法执行失败，历史负荷数据为空");
        }
        datas.put(AlgorithmConstants.HIS_LOAD, resultList);
    }

    @Override
    public void resolverResult(String tarPath, Map<String, String> supplementData) throws Exception {
        String cityId = supplementData.get(AlgorithmConstants.CITY_ID);
        String caliberId = supplementData.get(AlgorithmConstants.CALIBER_ID);
        String startTimePoint = supplementData.get("startTimePoint");
        String timeSpan = supplementData.get("timeSpan");
        String startTimeStr = supplementData.get("startTimeStr");
        Result algorithmResult = ALgorithmSupport.parseShortOutFile(tarPath, startTimePoint, timeSpan);
        this.insertShortData(algorithmResult, cityId, caliberId, startTimeStr, timeSpan);

    }


    public void insertShortData(Result result, String cityId, String caliberId, String startTimePoint, String timeSpan)
        throws Exception {
        String hour;
        //系统设置的超短期预测的时长 默认4小时
        SystemData systemSetting = systemService.getSystemSetting();
        if (cityId.equals(CityConstants.PROVINCE_ID)) {
            if (ShortConstants.MINUTE.equals(Integer.valueOf(timeSpan))) {
                hour = systemSetting.getProvinceShortFiveTime();
            } else {
                hour = systemSetting.getProvinceShortFifteenTime();
            }
        } else {
            if (ShortConstants.MINUTE.equals(Integer.valueOf(timeSpan))) {
                hour = systemSetting.getCityShortFiveTime();
            } else {
                hour = systemSetting.getCityShortFifteenTime();
            }
        }
        List<BigDecimal> dataList = result.getData().get(0).getValue();
        LoadCityFcShortDO loadCityFcShortDO = new LoadCityFcShortDO();
        loadCityFcShortDO.setAlgorithmId(AlgorithmEnum.SHORT_FORECAST.getId());
        loadCityFcShortDO.setCaliberId(caliberId);
        loadCityFcShortDO.setCityId(cityId);
        loadCityFcShortDO.setDate(new java.sql.Date(result.getData().get(0).getDate().getTime()));
        loadCityFcShortDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
        loadCityFcShortDO.setStartTime(startTimePoint);
        //1：5分钟间隔 2：十分钟间隔
        loadCityFcShortDO.setType(
            ShortConstants.MINUTE.equals(Integer.valueOf(timeSpan)) ? ShortConstants.FIVE_MINUTE_CODE
                : ShortConstants.FIFTEEN_MINUTE_CODE);
        Map<String, BigDecimal> stringBigDecimalMap = ColumnUtil.shortListToMap(dataList);
        BeanMap beanMap = BeanMap.create(loadCityFcShortDO);
        beanMap.putAll(stringBigDecimalMap);
        //算法结果保存到超短期数据记录表
        loadCityFcShortService.saveOrUpdate(loadCityFcShortDO);
        //结果处理后保存最新的一点到fcBasic表 后面的点循环覆盖
        if (ShortConstants.MINUTE.equals(Integer.valueOf(timeSpan))) {
            this.saveShortToFcBasic288(cityId, caliberId, loadCityFcShortDO.getDate(), startTimePoint, dataList,
                Integer.valueOf(hour));
        } else {
            this.saveShortToFcBasic96(cityId, caliberId, loadCityFcShortDO.getDate(), startTimePoint, dataList,
                Integer.valueOf(hour));
        }
    }


    public void saveShortToFcBasic96(String cityId, String caliberId, Date date, String fcStartTime,
        List<BigDecimal> dataList,
        Integer hour) throws Exception {
        List<String> columns = ColumnUtil.getColumns(96, Constants.LOAD_CURVE_START_WITH_ZERO, false);
        int index = 0;
        for (int i = 0; i < columns.size(); i++) {
            if (columns.get(i).equals(fcStartTime)) {
                index = i;
            }
        }
        int remain = 96 - (index);
        if (remain >= ((60 / 15) * hour)) {
            List<BigDecimal> zeroOrNullList = ColumnUtil.getZeroOrNullList(96, null);
            for (int i = 0; i < dataList.size(); i++) {
                zeroOrNullList.set(index + i, dataList.get(i));
            }
            saveDesignatedPoint96(cityId, caliberId, date, zeroOrNullList);
        } else {
            //预测出的数据包含跨天，存储今天数据
            List<BigDecimal> todayNullList = ColumnUtil.getZeroOrNullList(96, null);
            for (int i = 0; i < remain; i++) {
                todayNullList.set(index + i, dataList.get(i));
            }
            //存储今日数据
            saveDesignatedPoint96(cityId, caliberId, date, todayNullList);
            List<BigDecimal> tomorrowNullList = ColumnUtil.getZeroOrNullList(96, null);
            //去除今日的数据
            for (int i = 0; i < remain; i++) {
                dataList.remove(0);
            }
            //填充明日数据到tomorrowList中
            for (int i = 0; i < dataList.size(); i++) {
                tomorrowNullList.set(i, dataList.get(i));
            }
            //存储明天的数据
            saveDesignatedPoint96(cityId, caliberId, DateUtil.getMoveDay(date, 1), tomorrowNullList);
        }
    }


    public void saveDesignatedPoint96(String cityId, String caliberId, Date date, List<BigDecimal> zeroOrNullList)
        throws Exception {
        LoadCityFcDO fcDO = new LoadCityFcDO();
        fcDO.setAlgorithmId(AlgorithmEnum.SHORT_FORECAST.getId());
        fcDO.setCaliberId(caliberId);
        fcDO.setCityId(cityId);
        fcDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
        fcDO.setDate(new java.sql.Date(date.getTime()));
        fcDO.setReport(false);
        fcDO.setSucceed(false);
        fcDO.setRecommend(false);
        BasePeriodUtils.setAllFiled(fcDO, ColumnUtil.listToMap(zeroOrNullList, Constants.LOAD_CURVE_START_WITH_ZERO));
        this.loadCityFcService.doSaveOrUpdateLoadCityFcDO96(fcDO);
    }


    public void saveDesignatedPoint288(String cityId, String caliberId, Date date, List<BigDecimal> zeroOrNullList)
        throws Exception {
        LoadCityFc288DO fcDO = new LoadCityFc288DO();
        fcDO.setAlgorithmId(AlgorithmEnum.SHORT_FORECAST.getId());
        fcDO.setCaliberId(caliberId);
        fcDO.setCityId(cityId);
        fcDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
        fcDO.setDate(new java.sql.Date(date.getTime()));
        BasePeriodUtils.setAllFiled(fcDO, ColumnUtil.listToMap(zeroOrNullList, Constants.LOAD_CURVE_START_WITH_ZERO));
        this.loadCityFcService.doSaveOrUpdateLoadCityFcDO288(fcDO);
    }

    public void saveShortToFcBasic288(String cityId, String caliberId, Date date, String fcStartTime,
        List<BigDecimal> dataList,
        Integer hour) throws Exception {
        List<String> columns = ColumnUtil.getColumns(288, Constants.LOAD_CURVE_START_WITH_ZERO, false);
        int index = 0;
        for (int i = 0; i < columns.size(); i++) {
            if (columns.get(i).equals(fcStartTime)) {
                index = i;
            }
        }
        int remain = 288 - (index);
        if (remain >= ((60 / 5) * hour)) {
            List<BigDecimal> zeroOrNullList = ColumnUtil.getZeroOrNullList(288, null);
            for (int i = 0; i < dataList.size(); i++) {
                zeroOrNullList.set(index + i, dataList.get(i));
            }
            saveDesignatedPoint288(cityId, caliberId, date, zeroOrNullList);
        } else {
            //预测出的数据包含跨天，存储今天数据
            List<BigDecimal> todayNullList = ColumnUtil.getZeroOrNullList(288, null);
            for (int i = 0; i < remain; i++) {
                todayNullList.set(index + i, dataList.get(i));
            }
            //存储今日数据
            saveDesignatedPoint288(cityId, caliberId, date, todayNullList);
            List<BigDecimal> tomorrowNullList = ColumnUtil.getZeroOrNullList(288, null);
            //去除今日的数据
            for (int i = 0; i < remain; i++) {
                dataList.remove(0);
            }
            //填充明日数据到tomorrowList中
            for (int i = 0; i < dataList.size(); i++) {
                tomorrowNullList.set(i, dataList.get(i));
            }
            //存储明天的数据
            saveDesignatedPoint288(cityId, caliberId, DateUtil.getMoveDay(date, 1), tomorrowNullList);
        }
    }

}
