/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.impl;

import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.modelfusion.ModelFusionBasicData;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.FcLoad;
import com.tsintergy.aif.algorithm.serviceimpl.client.facade.impl.AbstractModelFusionInvokeClient;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.algorithm.dto.ModelFusionParam;
import com.tsintergy.lf.serviceapi.base.base.api.AlgorithmParamService;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.base.pojo.AlgorithmParamDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcBatchService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcBatchDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: 综合模型 <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/5/25 11:00
 * @Version: 1.0.0
 */
@Component
public class ModelFusionClient extends AbstractModelFusionInvokeClient<ModelFusionParam> {

    @Autowired
    LoadCityHisService loadCityHisService;

    @Autowired
    LoadCityFcService loadCityFcService;

    @Autowired
    HolidayService holidayService;

    @Autowired
    AlgorithmParamService algorithmParamService;

    @Autowired
    LoadCityFcBatchService loadCityFcBatchService;

    @SneakyThrows
    @Override
    protected ModelFusionBasicData generateForecastBasicData(ModelFusionParam param) {
        Date hisStart = DateUtils.string2Date("2018-01-01", DateFormatType.SIMPLE_DATE_FORMAT_STR);
        Date fcStart = DateUtils.addDays(param.getBeginDate(), -400);
        Date end = param.getEndDate();
        List<LoadCityHisDO> loadCityHisDOS = loadCityHisService
                .getLoadCityHisDOS(param.getCityId(), param.getCaliberId(), hisStart, end);
        List<FcLoad> dataList = new ArrayList<>();
        List<AlgorithmParamDO> algorithmParamList = algorithmParamService.getListByAlgorithmId(AlgorithmEnum.COMPREHENSIVE_MODEL.getType());
        String defaultValue;
        String cityId = param.getCityId();
        AlgorithmParamDO algorithmParamDO;
        if (Constants.PROVINCE_ID.equals(cityId)) {
            algorithmParamDO = algorithmParamList.stream().filter(t -> t.getParamEn().equals("provinceUse")).collect(Collectors.toList()).get(0);
        } else {
            algorithmParamDO = algorithmParamList.stream().filter(t -> t.getParamEn().equals("cityUse")).collect(Collectors.toList()).get(0);
        }
        defaultValue = algorithmParamDO.getDefaultValue();
        //新版综合模型算法所需算法
        List<String> algoList;
        if (defaultValue.contains(",")) {
            algoList = Arrays.asList(defaultValue.split(","));
        } else {
            algoList = Collections.singletonList(defaultValue);
        }
        List<LoadCityFcBatchDO> loadCityFcBatchDOS = loadCityFcBatchService.findByBatchTime(param.getCityId(), fcStart, end, param.getCaliberId(), algoList, "1");
        if (!CollectionUtils.isEmpty(loadCityFcBatchDOS)) {
            Map<String, List<LoadCityFcBatchDO>> fcMap = loadCityFcBatchDOS.stream()
                    .collect(Collectors.groupingBy(LoadCityFcBatchDO::getAlgorithmId));

            for (String algorithmId : algoList) {
                List<LoadCityFcBatchDO> fcDOList = fcMap.get(algorithmId);
                dataList.addAll(fcDOList);
            }
        }
        List<HolidayDO> holidayDOS = holidayService.getAllHolidayVOS();
        return new ModelFusionBasicData(null, loadCityHisDOS, dataList, holidayDOS);
    }
}