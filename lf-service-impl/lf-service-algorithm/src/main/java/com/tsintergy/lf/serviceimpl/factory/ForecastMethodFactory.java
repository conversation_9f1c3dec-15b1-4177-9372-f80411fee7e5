/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司 Author: liufeng Date: 2018/5/14 16:48 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.factory;

import com.tsieframework.core.base.SpringContextManager;
import com.tsintergy.lf.core.LfConfigConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.algorithm.api.CustomizationForecastService;
import com.tsintergy.lf.serviceapi.algorithm.api.Executor;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.algorithm.dto.Param;
import com.tsintergy.lf.serviceapi.algorithm.dto.Result;
import com.tsintergy.lf.serviceimpl.executor.analysis.AnalysisExecutor;
import com.tsintergy.lf.serviceimpl.executor.analysis.LongCompositeExecutor;
import com.tsintergy.lf.serviceimpl.executor.analysis.LongVecEnergyExecutor;
import com.tsintergy.lf.serviceimpl.executor.analysis.LongVecMaxExecutor;
import com.tsintergy.lf.serviceimpl.executor.analysis.LongVecMinExecutor;
import com.tsintergy.lf.serviceimpl.executor.analysis.SensitivityExecutor;
import com.tsintergy.lf.serviceimpl.executor.analysis.SimilarDayExecutor;
import com.tsintergy.lf.serviceimpl.executor.forecast.BaseMultiMethodExecutor;
import com.tsintergy.lf.serviceimpl.executor.forecast.ModelFusionExecutorBase;
import com.tsintergy.lf.serviceimpl.executor.forecast.ReplenishXGForecastExecutorBase;
import com.tsintergy.lf.serviceimpl.executor.forecast.ShortForecastExecutor;
import com.tsintergy.lf.serviceimpl.executor.holiday.FestSVMExecutor;
import com.tsintergy.lf.serviceimpl.executor.prePropess.LoadPrePropessExecutor;
import com.tsintergy.lf.serviceimpl.executor.typhoon.TyphoonForecastExecutor;
import com.tsintergy.lf.serviceimpl.impl.AnalysisForecastServiceImpl;
import com.tsintergy.lf.serviceimpl.impl.Forecastor;
import com.tsintergy.lf.serviceimpl.impl.HolidayForecastServiceImpl;
import com.tsintergy.lf.serviceimpl.impl.LongCompositeForecastServiceImpl;
import com.tsintergy.lf.serviceimpl.impl.LongMonthForecastAlgorithmServiceImpl;
import com.tsintergy.lf.serviceimpl.impl.PreProcessServiceImpl;
import com.tsintergy.lf.serviceimpl.impl.SensitivityForecastServiceImpl;
import com.tsintergy.lf.serviceimpl.impl.ShortForecastServiceImpl;
import com.tsintergy.lf.serviceimpl.impl.SimilarDayForecastImpl;
import com.tsintergy.lf.serviceimpl.impl.TyphoonForecastImpl;

/**
 * 简单工厂，在factory下会陆续定义其他组件部分工厂
 *
 * <AUTHOR>
 * @create 2020/8/27
 * @since 1.0.0
 */
public class ForecastMethodFactory {

    public static <E extends Param, T extends Result> Executor<E, T> create(Param param) {
        Executor executor;
        AlgorithmEnum methodType = param.getAlgorithmEnum();
        switch (methodType) {
            //台风算法
            case TYPHOON_SVM:
                executor = (TyphoonForecastExecutor) SpringContextManager.getApplicationContext()
                    .getBean("typhoonForecastExecutor");
                break;
            //超短期
            case SHORT_FORECAST:
                executor = (ShortForecastExecutor) SpringContextManager.getApplicationContext()
                    .getBean("shortForecastExecutor");
                break;
            //决策树
            case REPLENISH_LGB:
                executor = (ReplenishXGForecastExecutorBase) SpringContextManager.getApplicationContext()
                    .getBean("replenishXGForecastExecutor");
                break;
            //节假日预测
            case HOLIDAY_FEST_SVM:
                executor = (FestSVMExecutor) SpringContextManager.getApplicationContext().getBean("festSVMExecutor");
                break;
            //数据清洗
            case LOAD_PRE_PROCESS:
                executor = (LoadPrePropessExecutor) SpringContextManager.getApplicationContext()
                    .getBean("loadPrePropessExecutor");
                break;
            //灵敏度
            case SENSITIVITY:
                executor = (SensitivityExecutor) SpringContextManager.getApplicationContext()
                    .getBean("sensitivityExecutor");
                break;
            //稳定度分析
            case STABILITY:
                executor = (AnalysisExecutor) SpringContextManager.getApplicationContext()
                    .getBean("analysisExecutor");
                break;
            //综合模型
            case COMPREHENSIVE_MODEL:
                executor = (ModelFusionExecutorBase) SpringContextManager.getApplicationContext()
                    .getBean("modelFusionExecutor");
                break;
            //中长期综合模型算法
            case LONE_COMPOSITE:
                executor = (LongCompositeExecutor) SpringContextManager.getApplicationContext()
                    .getBean("longSensitivityExecutor");
                break;

//            case LONE_SENSITIVITY_ENERGY:
//                executor = (LongSensitivityExecutor) SpringContextManager.getApplicationContext()
//                    .getBean("longSensitivityExecutor");
//                break;

            //综合相似日查找
            case SIMILAR_DAY_SEARCH:
                executor = (SimilarDayExecutor) SpringContextManager.getApplicationContext()
                    .getBean("similarDayExecutor");
                break;
            case LONG_VEC_MAX:
                executor = (LongVecMaxExecutor)SpringContextManager.getApplicationContext()
                    .getBean("longVecMaxExecutor");
                break;

            case LONG_VEC_MIN:
                executor = (LongVecMinExecutor)SpringContextManager.getApplicationContext()
                    .getBean("longVecMinExecutor");
                break;

            case LONG_VEC_ENERGY:
                executor = (LongVecEnergyExecutor)SpringContextManager.getApplicationContext()
                    .getBean("longVecEnergyExecutor");
                break;
            //相似日&偏差补偿&支持向量机&梯度提升&神经网络(原来的新息算法)&标点 共用一个Executor；其中前三个共用一个算法exe梯度提升单独一个
            default:
                executor = (BaseMultiMethodExecutor) SpringContextManager.getApplicationContext()
                    .getBean("baseMultiMethodExecutor");
                break;
        }
        return executor;
    }

    /**
     * 适配算法模块的数据service，目前主要用于命中算法解析&入库的方法
     *
     * @param algorithmEnum 算法枚举
     * @param <E> 数据service的父类
     * @return 实际处理的service
     */
    public static <E extends CustomizationForecastService> CustomizationForecastService createService(
        AlgorithmEnum algorithmEnum) {
        CustomizationForecastService baseService;
        switch (algorithmEnum) {
            case LONG_VEC_MAX:
                baseService = (LongMonthForecastAlgorithmServiceImpl)SpringContextManager.getApplicationContext()
                    .getBean(LfConfigConstants.PROJECT_NAME + Constants.SEPARATOR_BROKEN_LINE + "longMonthForecastAlgorithmService");
                break;


            case LONG_VEC_MIN:
                baseService = (LongMonthForecastAlgorithmServiceImpl)SpringContextManager.getApplicationContext()
                    .getBean(LfConfigConstants.PROJECT_NAME + Constants.SEPARATOR_BROKEN_LINE + "longMonthForecastAlgorithmService");
                break;


            case LONG_VEC_ENERGY:
                baseService = (LongMonthForecastAlgorithmServiceImpl)SpringContextManager.getApplicationContext()
                    .getBean(LfConfigConstants.PROJECT_NAME + Constants.SEPARATOR_BROKEN_LINE + "longMonthForecastAlgorithmService");
                break;
            //台风算法
            case TYPHOON_SVM:
                baseService = (TyphoonForecastImpl) SpringContextManager.getApplicationContext()
                    .getBean(LfConfigConstants.PROJECT_NAME + Constants.SEPARATOR_BROKEN_LINE +"typhoonForecast");
                break;
            //超短期
            case SHORT_FORECAST:
                baseService = (ShortForecastServiceImpl) SpringContextManager.getApplicationContext()
                    .getBean(LfConfigConstants.PROJECT_NAME + Constants.SEPARATOR_BROKEN_LINE + "shortForecastService");
                break;
            //节假日预测
            case HOLIDAY_FEST_SVM:
                baseService = (HolidayForecastServiceImpl) SpringContextManager.getApplicationContext()
                    .getBean(
                        LfConfigConstants.PROJECT_NAME + Constants.SEPARATOR_BROKEN_LINE + "holidayForecastService");
                break;
            //数据清洗
            case LOAD_PRE_PROCESS:
                baseService = (PreProcessServiceImpl) SpringContextManager.getApplicationContext()
                    .getBean(LfConfigConstants.PROJECT_NAME + Constants.SEPARATOR_BROKEN_LINE + "preProcessService");
                break;
            //灵敏度
            case SENSITIVITY:
                baseService = (SensitivityForecastServiceImpl) SpringContextManager.getApplicationContext()
                    .getBean(LfConfigConstants.PROJECT_NAME + Constants.SEPARATOR_BROKEN_LINE
                        + "sensitivityForecastService");
                break;
            //稳定度分析
            case STABILITY:
                baseService = (AnalysisForecastServiceImpl) SpringContextManager.getApplicationContext()
                    .getBean(
                        LfConfigConstants.PROJECT_NAME + Constants.SEPARATOR_BROKEN_LINE + "analysisForecastService");
                break;
            //中长期综合模型
            case LONE_COMPOSITE:
                baseService = (LongCompositeForecastServiceImpl) SpringContextManager.getApplicationContext()
                    .getBean(LfConfigConstants.PROJECT_NAME + Constants.SEPARATOR_BROKEN_LINE
                        + "longCompositeForecastService");

                //中长期相似日算法
//            case LONE_SENSITIVITY_ENERGY:
//                baseService = (LongSensitivityForecastServiceImpl) SpringContextManager.getApplicationContext()
//                    .getBean(LfConfigConstants.PROJECT_NAME + Constants.SEPARATOR_BROKEN_LINE
//                        + "longSensitivityForecastService");


                break;
            //综合相似日查找
            case SIMILAR_DAY_SEARCH:
                baseService = (SimilarDayForecastImpl) SpringContextManager.getApplicationContext()
                    .getBean(LfConfigConstants.PROJECT_NAME + Constants.SEPARATOR_BROKEN_LINE + "similarDayForecast");
                break;
            //支持向量机&梯度提升&神经网络(原来的新息算法)&标点&决策树&综合模型使用一个入库逻辑
            default:
                baseService = (Forecastor) SpringContextManager.getApplicationContext()
                    .getBean(LfConfigConstants.PROJECT_NAME + Constants.SEPARATOR_BROKEN_LINE + "forecastable");
                break;
        }
        return baseService;
    }

}
