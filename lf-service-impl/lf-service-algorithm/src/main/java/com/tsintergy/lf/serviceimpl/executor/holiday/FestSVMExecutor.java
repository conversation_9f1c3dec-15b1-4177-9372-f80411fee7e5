/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司 Author: wangfeng Date: 2018/9/30 10:59 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.executor.holiday;

import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.algorithm.dto.GeneralResult;
import com.tsintergy.lf.serviceapi.algorithm.dto.HolidayInfo;
import com.tsintergy.lf.serviceapi.algorithm.dto.HolidayParam;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceimpl.executor.AbstractBaseExecutor;
import com.tsintergy.lf.serviceimpl.factory.LoadCityHisFileNameHealper;
import com.tsintergy.lf.serviceimpl.support.ALgorithmSupport;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;


/**
 * 节假日预测
 *
 * <AUTHOR>
 * @create 2020/8/24
 * @since 1.0.0
 */
@Slf4j
public class FestSVMExecutor extends AbstractBaseExecutor<HolidayParam, GeneralResult> {

    @Override
    public void writeData(HolidayParam param, Map<String, Object> datas, Map<String, Object> map)
        throws Exception {
        Date startDay = param.getForecastBeginDate();
        Date endDay = param.getForecastEndDate();
        map.put(AlgorithmConstants.ALGORITHM_TYPE, AlgorithmEnum.HOLIDAY_FEST_SVM.getType());
        map.put(AlgorithmConstants.NEW_POINT, 0);


        map.put(AlgorithmConstants.CITYNAME, param.getCityName());
        map.put(AlgorithmConstants.FORECAST_START_DAY, DateUtils.date2String(startDay,
            DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR));
        map.put(AlgorithmConstants.FORECAST_END_DAY,
            DateUtils.date2String(endDay, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR));
        //===============================节假日数据=================================
        List<HolidayDO> holidays = ALgorithmSupport
            .getListFromMap(datas.get(AlgorithmConstants.HOLIDAY_INFO), HolidayDO.class);
        Map<String, Object> mapData;
        try {
            mapData = ALgorithmSupport.getHolidayInfo(holidays);
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }

        List<String> offDates = (List<String>) mapData.get(AlgorithmConstants.OFFDATES);
        List<HolidayInfo> holidayInfos = ALgorithmSupport
            .getListFromMap(mapData.get(AlgorithmConstants.HOLIDAY_INFO), HolidayInfo.class);

        map.put(AlgorithmConstants.OFFDATES, offDates == null ? new ArrayList<>() : offDates);
        map.put(AlgorithmConstants.HOLIDAY_INFO, holidayInfos == null ? new ArrayList<>() : holidayInfos);

        List<LoadCityHisDO> hisLoads = ALgorithmSupport
            .getListFromMap(datas.get(AlgorithmConstants.HIS_LOAD), LoadCityHisDO.class);
        Date baseDate = DateUtils.addDays(param.getForecastBeginDate(), -3);
        map.put(AlgorithmConstants.BASEDAY,
            DateUtils.date2String(baseDate, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR));
        hisLoads.sort(Comparator.comparing(LoadCityHisDO::getDate, Comparator.nullsFirst(Comparator.naturalOrder())));
        if (CollectionUtils.isNotEmpty(hisLoads)) {
            //基准日 不定死   有最新的历史数据的最后一天  改为 取有完整数据的最近一天数据
            for (int i = 1; ; i++) {
                if (hisLoads.get(hisLoads.size() - i).getT2345() != null) {
                    baseDate = hisLoads.get(hisLoads.size() - i).getDate();
                    break;
                }
            }
            map.put(AlgorithmConstants.BASEDAY,
                DateUtils.date2String(baseDate, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR));
        }
        map.put(AlgorithmConstants.HIS_LOAD, hisLoads);
        map.put(AlgorithmConstants.START_WITH_ZERO, Constants.LOAD_CURVE_START_WITH_ZERO);


        Date afterDate = DateUtils.addDays(baseDate, 1);

        List<LoadCityHisDO> loadCityHisDOS = hisLoads.stream()
            .filter(LoadCityHisDO -> LoadCityHisDO.getDate().before(param.getForecastBeginDate()))
            .collect(Collectors.toList());
        try {
            putNewPoint(map, loadCityHisDOS, afterDate, false);
        }catch (Exception e){
            e.printStackTrace();
        }



        //写入气象数据(基准日（包含）前365 到预测结束日（预测起始日+连续预测天数-1）的逐时刻温度)
        Date end = param.getForecastEndDate();
        List<WeatherCityHisDO> temperature = new ArrayList<>();
        List<WeatherCityHisDO> precipitation = new ArrayList<>();
        List<WeatherCityHisDO> wind = new ArrayList<>();
        List<WeatherCityHisDO> humiditys = new ArrayList<>();
        List<WeatherCityHisDO> hisWeather = ALgorithmSupport
            .getListFromMap(datas.get(AlgorithmConstants.DATA_WEATHER_KEY), WeatherCityHisDO.class);
        if (!CollectionUtils.isEmpty(hisWeather)) {
            for (WeatherCityHisDO vo : hisWeather) {
                int type = vo.getType();
                if (type == WeatherEnum.TEMPERATURE.getType()) {
                    temperature.add(vo);
                } else if (type == WeatherEnum.RAINFALL.getType()) {
                    precipitation.add(vo);
                } else if(type == WeatherEnum.HUMIDITY.getType()){
                    humiditys.add(vo);
                } else if(type == WeatherEnum.WINDSPEED.getType()){
                    wind.add(vo);
                }
            }
        }
        List<WeatherFeatureCityDayHisDO> hisFeature = ALgorithmSupport
            .getListFromMap(datas.get(AlgorithmConstants.WEATHER_FEATURE_HIS), WeatherFeatureCityDayHisDO.class);

        map.put(AlgorithmConstants.TEMPERATURE_DATA, temperature);
        map.put(AlgorithmConstants.PRECIPITATION_DATA, precipitation);
        map.put(AlgorithmConstants.WIND_DATA, wind);
        map.put(AlgorithmConstants.HUMIDITY_DATA, humiditys);
        map.put(AlgorithmConstants.MAX_TEMPERATURE, hisFeature);
    }

    /**
     * 功能描述: <br> 计算新息点数
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    public void putNewPoint(Map<String, Object> map, List<LoadCityHisDO> hisLoad, Date afterDate,
        Boolean shortForecast) throws Exception {
        LoadCityHisDO hisVO = hisLoad.get(hisLoad.size() - 1);
        if (hisVO.getDate().equals(afterDate)) {
            List<BigDecimal> list = BasePeriodUtils
                .toList(hisVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
            //新息点数之前直接去除空，如果采集有空数据或者漏数据则会有问题
            // 新的方式是：超短期预测是从00点到现在的时刻点，都算新息点数，如果在这期间有null，则在file_in写NULL
            List<String> columns;
            if (shortForecast) {
                LoadCityHisFileNameHealper fileNameHealper = LoadCityHisFileNameHealper.newInstance();
                //获取临近现在时刻点的时间
                String timeStr = fileNameHealper.getTimeCode(new Date());
                String str = timeStr.substring(0, 3);
                columns = ColumnUtil.getColumnsBetween("0000", str, Constants.LOAD_CURVE_POINT_NUM,
                    Constants.LOAD_CURVE_START_WITH_ZERO, true);
            } else {
                columns = ColumnUtil.getColumnsBetween("0000", "2345", Constants.LOAD_CURVE_POINT_NUM,
                    Constants.LOAD_CURVE_START_WITH_ZERO, true);
            }
            int columnSize = columns.size();
            List<BigDecimal> dataSize = list.subList(0, columnSize);
            int index = 0;
            for (int i = 1; i < dataSize.size(); i++) {
                int data = dataSize.size() - i;
                BigDecimal value = dataSize.get(data);
                if (value != null) {
                    index = data + 1;
                    break;
                }
            }
            map.put(AlgorithmConstants.NEW_POINT, index);
        }
    }



}
