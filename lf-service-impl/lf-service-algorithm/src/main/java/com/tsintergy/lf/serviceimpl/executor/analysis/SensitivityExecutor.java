/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangfeng Date:  2019/8/13 3:55 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.executor.analysis;

import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.enums.StatisticalEnum;
import com.tsintergy.lf.serviceapi.algorithm.dto.SensitivityParam;
import com.tsintergy.lf.serviceapi.algorithm.dto.SensitivityResult;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceimpl.executor.AbstractBaseExecutor;
import com.tsintergy.lf.serviceimpl.support.ALgorithmSupport;
import java.util.Map;

/**
 * 灵敏度
 *
 * <AUTHOR>
 * @create 2019/8/13
 * @since 1.0.0
 */
public class SensitivityExecutor extends AbstractBaseExecutor<SensitivityParam, SensitivityResult> {

    private static final String BEGIN_DAY = "BeginDay";

    private static final String END_DAY = "EndDay";

    private static final String MIN_TEMP = "MinTemp";

    private static final String MAX_TEMP = "MaxTemp";

    private static final String STEP = "Step";

    private static final String LOAD_TYPE = "LoadType";

    private static final String WEATHER_TYPE = "WeatherType";

    private static final String DATE_NOT_INCLUDED = "DateNotIncluded";

    private static final String KEY_LOAD_INFO = "keyLoadInfo";

    private static final String POLY_ORDER = "PolyOrder";

    private static final String ACCUMULATE = "Accumulate";

    @Override
    public void writeData(SensitivityParam param, Map<String, Object> datas, Map<String, Object> map) throws Exception {
        map.put(BEGIN_DAY,
            DateUtils.date2String(param.getBeginDate(), DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR));
        map.put(END_DAY,
            DateUtils.date2String(param.getEndDate(), DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR));
        map.put(MIN_TEMP, param.getMin());
        map.put(MAX_TEMP, param.getMax());
        map.put(STEP, param.getStep());
        map.put(LOAD_TYPE, StatisticalEnum.getValue(Integer.valueOf(param.getLoadType())));
        map.put(WEATHER_TYPE, datas.get(AlgorithmConstants.INDVARI_TYPE));
        map.put(AlgorithmConstants.DATA_WEATHER_KEY, datas.get(AlgorithmConstants.DATA_WEATHER_KEY));
        map.put(AlgorithmConstants.START_WITH_ZERO, Constants.LOAD_CURVE_START_WITH_ZERO);
        map.put(AlgorithmConstants.HIS_LOAD, datas.get(AlgorithmConstants.HIS_LOAD));
        map.put(DATE_NOT_INCLUDED, param.getDateNotIncluded());
        map.put(KEY_LOAD_INFO,datas.get(AlgorithmConstants.KEY_LOAD_INFO));
        map.put(POLY_ORDER, param.getPolyOrder());
        map.put(ACCUMULATE, param.getAccumulate());
        fetchHoliday(map, ALgorithmSupport.getListFromMap(datas.get(AlgorithmConstants.HOLIDAY_INFO), HolidayDO.class));
    }
}