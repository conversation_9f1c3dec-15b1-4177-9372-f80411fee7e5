/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司 Author: liufeng Date: 2018/6/6 19:26 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.impl;

import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.ParamConstants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.algorithm.dto.GeneralResult;
import com.tsintergy.lf.serviceapi.algorithm.dto.HolidayParam;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.ForecastDataService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcBatchService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.dto.AutoForecastDTO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemData;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceimpl.support.ALgorithmSupport;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 节假日预测算法 逻辑实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service(value = "holidayForecastService")
@Slf4j
public class HolidayForecastServiceImpl extends AbstractForecastService<HolidayParam, GeneralResult> {

    @Autowired
    LoadCityFcBatchService loadCityFcBatchService;
    @Autowired
    private ForecastDataService algorithmForecastDataService;
    @Autowired
    private SettingSystemService settingSystemService;
    @Autowired
    private LoadCityFcService loadCityFcService;
    @Autowired
    private CityService cityService;
    @Autowired
    private HolidayService holidayService;

    @Override
    public void mergeData(HolidayParam param, Map<String, Object> srcMap) throws Exception {
        Date startDate = param.getForecastBeginDate();
        Date endDate = param.getForecastEndDate();
        String cityId = param.getCityId();
        String caliberId = param.getCaliberId();
        Date searchEndDate = DateUtils.addDays(startDate, -1);
        //===============================构造的历史负荷数据===========节假日 改成1200天===================
        Date lastBeginDay = DateUtil.getMoveDay(startDate, -1200);
        List<LoadCityHisDO> hisLoads = algorithmForecastDataService.findHisLoad(cityId, caliberId, lastBeginDay,
                searchEndDate);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(hisLoads)) {
            throw TsieExceptionUtils
                    .newBusinessException("城市id为" + cityId + ",口径id为" + caliberId + "的历史数据为空,节假日算法执行失败......");
        }
        srcMap.put(AlgorithmConstants.HIS_LOAD, hisLoads);
        //===============================构造的气象数据 ======================================
        cityId = cityService.findWeatherCityId(cityId);
        List<WeatherCityHisDO> weatherCityHisVOS = algorithmForecastDataService
                .findHisWeather(cityId, null, lastBeginDay,
                        endDate);
        //补充节假日气象
        if (weatherCityHisVOS != null && weatherCityHisVOS.size() > 0) {
            Date supplementStartDate;
            for (int i = 1; ; i++) {
                //转换需要补充气象日期的判断方式
                List<BigDecimal> list = BasePeriodUtils
                        .toList(weatherCityHisVOS.get((weatherCityHisVOS.size() - i)), 96,
                                Constants.LOAD_CURVE_START_WITH_ZERO);
                List<BigDecimal> collect = list.stream().filter(t -> t != null).collect(Collectors.toList());
                //最后一个点有值
                if (collect.size() == 96) {
                    supplementStartDate = weatherCityHisVOS.get(weatherCityHisVOS.size() - i).getDate();
                    weatherCityHisVOS = weatherCityHisVOS.subList(0, weatherCityHisVOS.size() - i + 1);
                    //删掉有缺点日期的那一天的数据
                    break;
                }
            }
            if (supplementStartDate.compareTo(endDate) < 0) {
                List<WeatherCityFcDO> fcWeather = algorithmForecastDataService
                        .findFcWeather(cityId, DateUtils.addDays(supplementStartDate, 1),
                                endDate);
                if (fcWeather != null && CollectionUtils.isNotEmpty(fcWeather)) {
                    for (WeatherCityFcDO t : fcWeather) {
                        WeatherCityHisDO vo = new WeatherCityHisDO();
                        BeanUtils.copyProperties(t, vo);
                        //最新日期的数据放在开头
                        weatherCityHisVOS.add(vo);
                    }
                }
            }

        }
        srcMap.put(AlgorithmConstants.DATA_WEATHER_KEY, weatherCityHisVOS);
        //==============================构造的节假日数据==========================================
        List<HolidayDO> holidayVOS = algorithmForecastDataService.findAllHolidays();
        srcMap.put(AlgorithmConstants.HOLIDAY_INFO, holidayVOS);
    }


    @Override
    public void resolverResult(String tarPath, Map<String, String> supplementData) throws Exception {
        GeneralResult result = ALgorithmSupport.postGeneraResult(tarPath, null);
        this
                .extractedSaveHoliday(supplementData.get(AlgorithmConstants.CITY_ID),
                        supplementData.get(AlgorithmConstants.CALIBER_ID), supplementData.get(AlgorithmConstants.ALGORITHM_ID),
                        result);
    }


    public void extractedSaveHoliday(String cityId, String caliberId, String algorithmId, GeneralResult result)
            throws Exception {
        List<LoadCityFcDO> result1 = result.getResult();
        for (LoadCityFcDO metaData : result1) {
            AutoForecastDTO dto = reportSwitch(cityId, ParamConstants.HOLIDAY, result1.get(0).getDate(),
                    result1.get(result1.size() - 1).getDate());
            if (dto.isReportSwitch()) {
                //查询库中是否有 人工修正和台风预测的结果
                List<String> algorithmIds = new ArrayList<>();
                //查询库里是否有人工修正  台风预测结果
                algorithmIds.add(AlgorithmConstants.MD_ALGORITHM_ID);
                algorithmIds.add(AlgorithmEnum.TYPHOON_SVM.getId());
                List<LoadCityFcDO> fcVOList = loadCityFcService.getLoadFcByAlgorithmId(metaData.getDate(),
                        cityId, caliberId,
                        algorithmIds);
                //如果库里既没有人工修正和台风预测的结果并且数据库中设置的是自动上报
                if (fcVOList == null && dto.isReportSwitch()) {
                    dto.setReportSwitch(true);
                } else {
                    //上述有一条不满足，则不上报
                    dto.setReportSwitch(false);
                }
            }
            List<LoadCityFcDO> metaDataList = new ArrayList<>();
            metaDataList.add(metaData);
            doSaveOrUpdateResult(cityId, caliberId, algorithmId, metaData.getDate(), metaDataList, dto);
        }
    }

    /**
     * 是否自动上报
     */
    private AutoForecastDTO reportSwitch(String cityId, Integer dayType, Date startDate, Date endDate)
            throws Exception {
        boolean autoReport;
        SystemData systemSetting = settingSystemService.getSystemSetting();
        String systemAlgorithmId;
        Date cuteEndDate;
        //获取自动上报算法id
        if (cityId.equals(CityConstants.PROVINCE_ID)) {
            systemAlgorithmId = systemSetting.getProvinceNormalAlgorithm();
            autoReport = systemSetting.getProvinceAutoReportSwitch().equals(ParamConstants.STRING_COMPOSITE_ON);
            if (ParamConstants.HOLIDAY.equals(dayType)) {
                systemAlgorithmId = systemSetting.getProvinceHolidayAlgorithm();
            }
        } else {
            systemAlgorithmId = systemSetting.getCityNormalAlgorithm();
            autoReport = systemSetting.getCityAutoReportSwitch().equals(ParamConstants.STRING_COMPOSITE_ON);
            if (ParamConstants.HOLIDAY.equals(dayType)) {
                systemAlgorithmId = systemSetting.getCityHolidayAlgorithm();
            }
        }
        //获取上报的截止日期（自动上报几天）
        Integer autoReportDays = Integer.valueOf(systemSetting.getForecastDay());
        //节假日时 自动上报 全节假日+节假日后一天
        if (ParamConstants.HOLIDAY.equals(dayType)) {
            cuteEndDate = DateUtils.addDays(endDate, 1);
        } else {
            cuteEndDate = DateUtils.addDays(startDate, autoReportDays);
        }
        AutoForecastDTO dto = new AutoForecastDTO();
        dto.setReportSwitch(autoReport);
        dto.setCuteDate(cuteEndDate);
        dto.setSelectAlgorithmId(systemAlgorithmId);
        return dto;
    }

    /**
     * 保存上报结果
     */
    public void doSaveOrUpdateResult(String cityId, String caliberId, String algorithmId, Date date,
                                     List<LoadCityFcDO> dataList,
                                     AutoForecastDTO autoForecastDTO) {
        for (LoadCityFcDO metaData : dataList) {
            try {
                //查询库里是否有此算法，如果有，则覆盖，如果没有，则创建
                LoadCityFcDO fcVO = loadCityFcService.getLoadFc(date, cityId, caliberId, algorithmId);
                if (fcVO == null) {
                    LoadCityFcDO loadCityFcDO = new LoadCityFcDO(new java.sql.Date(date.getTime()), cityId,
                            caliberId,
                            algorithmId);
                    loadCityFcDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
                    ColumnUtil.copyPropertiesIgnoreNull(metaData, loadCityFcDO);
                    loadCityFcDO.setRecommend(false);
                    //需要默认上报开启&&算法id和上报id一致&&上报天数的范围内才上报
                    if (autoForecastDTO.isReportSwitch() && autoForecastDTO.getSelectAlgorithmId()
                            .equals(algorithmId) && date.before(autoForecastDTO.getCuteDate())) {
                        RepeatReport(cityId, caliberId, date);
                        //如果日期是节假日,才能设置为上报
                        loadCityFcDO.setReport(holidayService.isHoliday(date));
                        loadCityFcDO.setSucceed(true);
                        loadCityFcDO.setReportTime(new Timestamp(System.currentTimeMillis()));
                    } else {
                        loadCityFcDO.setReport(false);
                        loadCityFcDO.setSucceed(false);
                    }
                    //装载 推荐算法标识
                    if (algorithmId.equals(autoForecastDTO.getSelectAlgorithmId())) {
                        loadCityFcDO.setRecommend(true);
                    }
                    loadCityFcService.doCreateAndFlush(loadCityFcDO);
                    //插入算法预测批次表
                    loadCityFcBatchService.doSave(loadCityFcDO);
                } else {
                    //防止覆盖人工修正上报的数据
                    if (fcVO.getReport() && AlgorithmEnum.FORECAST_MODIFY.getId().equals(fcVO.getAlgorithmId())) {
                        continue;
                    }
                    fcVO.setRecommend(false);
                    ColumnUtil.copyPropertiesIgnoreNull(metaData, fcVO);
                    if (autoForecastDTO.isReportSwitch() && autoForecastDTO.getSelectAlgorithmId()
                            .equals(fcVO.getAlgorithmId()) && date.before(autoForecastDTO.getCuteDate())) {
                        RepeatReport(cityId, caliberId, date);
                        //如果日期是节假日,才能设置为上报
                        fcVO.setReport(holidayService.isHoliday(date));
                        fcVO.setSucceed(true);
                        fcVO.setReportTime(new Timestamp(System.currentTimeMillis()));
                    } else {
                        fcVO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                        fcVO.setReport(false);
                        fcVO.setSucceed(false);
                    }
                    if (fcVO.getAlgorithmId().equals(autoForecastDTO.getSelectAlgorithmId())) {
                        fcVO.setRecommend(true);
                    }
                    loadCityFcService.doUpdateLoadCityFcDO(fcVO);
                    //插入算法预测批次表
                    loadCityFcBatchService.doSave(fcVO);
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                continue;
            }
        }
    }

    /**
     * 判断重复上报
     */
    public void RepeatReport(String cityId, String caliberId, Date date) throws Exception {
        //以防万一有两个上报结果，所以这里在判断一下是否有正常日已经上报了,如果已经上报了正常日的算法并且不是人工修正，则把上报取消掉
        LoadCityFcDO report = loadCityFcService.getReport(cityId, caliberId, date);
        if (report != null && !report.getAlgorithmId().equals(AlgorithmConstants.MD_ALGORITHM_ID)) {
            report.setReport(false);
            report.setSucceed(false);
            report.setRecommend(false);
            loadCityFcService.doUpdateLoadCityFcDO(report);
        }
    }

}
