/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.AlgorithmEnum;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityFcLongService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityFcLongDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayLongFcService;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherFeatureCityDayLongDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherFeatureCityDayLongListVO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherFeatureCommonLongDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherFeatureCommonLongVO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherFeatureLongDTO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayLongFcDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherFeatureCityDayLongFcDAO;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/5/17 15:02
 * @Version: 1.0.0
 */
@Service("weatherFeatureCityDayLongFcService")
public class WeatherFeatureCityDayLongFcServiceImpl implements WeatherFeatureCityDayLongFcService {

    @Autowired
    WeatherFeatureCityDayLongFcDAO weatherFeatureCityDayLongFcDAO;

    @Autowired
    LoadCityFcLongService loadCityFcLongService;

    @Autowired
    LoadFeatureCityDayHisService loadFeatureCityDayHisService;

    @Autowired
    WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Override
    public WeatherFeatureLongDTO findMonthFcPageByParam(String cityId, Date startDate, Date endDate, String caliberId)
        throws Exception {
        WeatherFeatureLongDTO weatherFeatureLongDTO = new WeatherFeatureLongDTO();
        //查询相关数据
        List<WeatherFeatureCityDayLongFcDO> byParam = this.findByParam(cityId, startDate, endDate);

        List<LoadCityFcLongDO> loadCityFcLongDO = loadCityFcLongService
            .findLoadCityFcLongDO(cityId, AlgorithmEnum.LONG_FORECAST.getId(), caliberId, startDate, endDate);

        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOS = weatherFeatureCityDayHisService
            .listWeatherFeatureCityDayHisDO(cityId, DateUtils.addYears(startDate, -1), DateUtils.addYears(endDate, -1));
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = loadFeatureCityDayHisService
            .findLoadFeatureCityDayHisDOS(cityId, DateUtils.addYears(startDate, -1), DateUtils.addYears(endDate, -1),
                caliberId);
        Map<java.sql.Date, LoadCityFcLongDO> loadCityFcLongDOMap = new HashMap<>();
        Map<java.sql.Date, WeatherFeatureCityDayLongFcDO> weatherFeatureCityDayLongFcDOMap = new HashMap<>();
        Map<java.sql.Date, WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOMap = new HashMap<>();
        Map<java.sql.Date, LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOMap = new HashMap<>();
        if (byParam == null || loadCityFcLongDO == null || weatherFeatureCityDayHisDOS == null
            || loadFeatureCityDayHisDOS == null) {

        } else {
            loadCityFcLongDOMap = loadCityFcLongDO.stream()
                .collect(Collectors.toMap(LoadCityFcLongDO::getDate, Function.identity(), (o, v) -> v));

            weatherFeatureCityDayLongFcDOMap = byParam.stream()
                .collect(Collectors.toMap(WeatherFeatureCityDayLongFcDO::getDate, Function.identity(), (o, v) -> v));

            weatherFeatureCityDayHisDOMap = weatherFeatureCityDayHisDOS
                .stream()
                .collect(Collectors.toMap(WeatherFeatureCityDayHisDO::getDate, Function.identity(), (o, v) -> v));

            loadFeatureCityDayHisDOMap = loadFeatureCityDayHisDOS.stream()
                .collect(Collectors.toMap(LoadFeatureCityDayHisDO::getDate, Function.identity(), (o, v) -> v));
        }

        List<Date> listBetweenDay = DateUtil.getListBetweenDay(startDate, endDate);
        List<WeatherFeatureCityDayLongDTO> longData = new ArrayList<>();
        for (Date date : listBetweenDay) {
            WeatherFeatureCityDayLongDTO weatherFeatureCityDayLongDTO = new WeatherFeatureCityDayLongDTO();
            LoadCityFcLongDO cityFcLongDO = loadCityFcLongDOMap.get(date);
            WeatherFeatureCityDayLongFcDO weatherFeatureCityDayLongFcDO = weatherFeatureCityDayLongFcDOMap
                .get(date);
            WeatherFeatureCityDayHisDO weatherFeatureCityDayHisDO = weatherFeatureCityDayHisDOMap
                .get(DateUtils.addYears(date, -1));
            LoadFeatureCityDayHisDO loadFeatureCityDayHisDO = loadFeatureCityDayHisDOMap
                .get(DateUtils.addYears(date, -1));

            WeatherFeatureCommonLongDTO fcObj = new WeatherFeatureCommonLongDTO();
            WeatherFeatureCommonLongDTO hisObj = new WeatherFeatureCommonLongDTO();
            WeatherFeatureCommonLongDTO modifyObj = new WeatherFeatureCommonLongDTO();
            if (cityFcLongDO != null) {
                fcObj.setAvgLoad(cityFcLongDO.getMeanLoad());
                fcObj.setDayEnergy(cityFcLongDO.getEleLoad());
                fcObj.setMaxLoad(cityFcLongDO.getMaxLoad());
                fcObj.setMinLoad(cityFcLongDO.getMinLoad());
                weatherFeatureCityDayLongDTO.setUpdateTime(
                    DateUtil.getStrDate(cityFcLongDO.getUpdatetime(), DateUtil.DATE_FORMAT11));
            }
            if (weatherFeatureCityDayLongFcDO != null) {
                fcObj.setHighestTemperature(weatherFeatureCityDayLongFcDO.getHighestTemperature());
                fcObj.setLowestTemperature(weatherFeatureCityDayLongFcDO.getLowestTemperature());
                fcObj.setId(weatherFeatureCityDayLongFcDO.getId());
            }
            if (loadFeatureCityDayHisDO != null) {
                hisObj.setAvgLoad(loadFeatureCityDayHisDO.getAveLoad());
                hisObj.setDayEnergy(loadFeatureCityDayHisDO.getEnergy());
                hisObj.setMaxLoad(loadFeatureCityDayHisDO.getMaxLoad());
                hisObj.setMinLoad(loadFeatureCityDayHisDO.getMinLoad());
            }
            if (weatherFeatureCityDayHisDO != null) {
                hisObj.setDate(DateUtil.getStrDate(weatherFeatureCityDayHisDO.getDate(), null));
                hisObj.setWeek(DateUtil.getWeek(weatherFeatureCityDayHisDO.getDate()));
                hisObj.setHighestTemperature(weatherFeatureCityDayHisDO.getHighestTemperature());
                hisObj.setLowestTemperature(weatherFeatureCityDayHisDO.getLowestTemperature());
            }
            if (loadFeatureCityDayHisDO != null) {
                modifyObj.setAvgLoad(loadFeatureCityDayHisDO.getAveLoad());
                modifyObj.setDayEnergy(loadFeatureCityDayHisDO.getEnergy());
                modifyObj.setMaxLoad(loadFeatureCityDayHisDO.getMaxLoad());
                modifyObj.setMinLoad(loadFeatureCityDayHisDO.getMinLoad());
            }
            if (weatherFeatureCityDayHisDO != null) {
                modifyObj.setWeek(DateUtil.getWeek(weatherFeatureCityDayHisDO.getDate()));
                modifyObj.setDate(DateUtil.getStrDate(weatherFeatureCityDayHisDO.getDate(), null));
                modifyObj.setHighestTemperature(weatherFeatureCityDayHisDO.getHighestTemperature());
                modifyObj.setLowestTemperature(weatherFeatureCityDayHisDO.getLowestTemperature());
            }
            fcObj.setDate(DateUtil.getStrDate(date, null));
            fcObj.setWeek(DateUtil.getWeek(date));
            weatherFeatureCityDayLongDTO.setFcObj(fcObj);
            weatherFeatureCityDayLongDTO.setHisObj(hisObj);
            weatherFeatureCityDayLongDTO.setModifyObj(modifyObj);
            longData.add(weatherFeatureCityDayLongDTO);
        }
        if (longData != null) {
            Optional<WeatherFeatureCityDayLongDTO> max = longData.stream().filter(e -> e.getUpdateTime() != null)
                .max(Comparator.comparing(WeatherFeatureCityDayLongDTO::getUpdateTime));
            weatherFeatureLongDTO.setLongData(longData);
            if (!max.equals(Optional.empty())) {
                weatherFeatureLongDTO.setUpdateTime(max.get().getUpdateTime());
            }
        }
        return weatherFeatureLongDTO;
    }

    @Override
    public List<WeatherFeatureCityDayLongFcDO> findByParam(String cityId, Date startDate, Date endDate) throws
        Exception {
        List<WeatherFeatureCityDayLongFcDO> weatherFeatureCityDayFcDOs = weatherFeatureCityDayLongFcDAO
            .getWeatherFeatureCityDayFcDOs(cityId, startDate, endDate);
        return weatherFeatureCityDayFcDOs;
    }

    @Override
    public void saveOrUpdate(WeatherFeatureCityDayLongFcDO weatherFeatureCityDayLongFcDO) throws Exception {
        List<WeatherFeatureCityDayLongFcDO> weatherFeatureCityDayFcDOs = weatherFeatureCityDayLongFcDAO
            .getWeatherFeatureCityDayFcDOs(weatherFeatureCityDayLongFcDO.getCityId(),
                weatherFeatureCityDayLongFcDO.getDate(), weatherFeatureCityDayLongFcDO.getDate());

        if (CollectionUtils.isEmpty(weatherFeatureCityDayFcDOs)) {
            weatherFeatureCityDayLongFcDAO.createAndFlush(weatherFeatureCityDayLongFcDO);
        } else {
            WeatherFeatureCityDayLongFcDO oldVo = weatherFeatureCityDayFcDOs.get(0);
            String id = oldVo.getId();
            BeanUtils.copyProperties(weatherFeatureCityDayLongFcDO, oldVo);
            oldVo.setId(id);
            weatherFeatureCityDayLongFcDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            weatherFeatureCityDayLongFcDAO.updateAndFlush(oldVo);
        }
    }

    @Override
    public void saveOrUpdate(WeatherFeatureCityDayLongListVO weatherFeatureCityDayLongListVO) throws Exception {
        for (WeatherFeatureCommonLongVO weatherFeatureCommonLongDTO : weatherFeatureCityDayLongListVO.getFcList()) {
            WeatherFeatureCityDayLongFcDO weatherFeatureCityDayLongFcDO = new WeatherFeatureCityDayLongFcDO();
            weatherFeatureCityDayLongFcDO.setId(weatherFeatureCommonLongDTO.getId());
            weatherFeatureCityDayLongFcDO
                .setDate(new java.sql.Date(DateUtil.getDate(weatherFeatureCommonLongDTO.getDate(), null).getTime()));
            weatherFeatureCityDayLongFcDO.setCityId(weatherFeatureCommonLongDTO.getCityId());
            weatherFeatureCityDayLongFcDO.setHighestTemperature(weatherFeatureCommonLongDTO.getHighestTemperature());
            weatherFeatureCityDayLongFcDO.setLowestTemperature(weatherFeatureCommonLongDTO.getLowestTemperature());
            weatherFeatureCityDayLongFcDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            if (weatherFeatureCommonLongDTO.getId() == null) {
                weatherFeatureCityDayLongFcDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
                weatherFeatureCityDayLongFcDAO.save(weatherFeatureCityDayLongFcDO);
            } else {
                weatherFeatureCityDayLongFcDAO.update(weatherFeatureCityDayLongFcDO);
            }


        }


    }
}
