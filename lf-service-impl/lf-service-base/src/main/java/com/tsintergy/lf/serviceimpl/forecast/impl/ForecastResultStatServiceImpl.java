package com.tsintergy.lf.serviceimpl.forecast.impl;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.tsieframework.core.base.SpringContextManager;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.JsonObject;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.ParamConstants;
import com.tsintergy.lf.core.constants.SystemConstant;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.BaseLoadCityDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.BaseLoadFcCityDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.check.api.SettingCheckService;
import com.tsintergy.lf.serviceapi.base.check.api.SettingReportService;
import com.tsintergy.lf.serviceapi.base.check.pojo.SettingCheckDO;
import com.tsintergy.lf.serviceapi.base.check.pojo.SettingReportDO;
import com.tsintergy.lf.serviceapi.base.evalucation.api.DeviationLoadCityFcService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.DispersionLoadCityFcService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsCityDayFcBatchService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsCityDayFcService;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyLoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.DeviationLoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.DispersionLoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcBatchDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.ForecastResultStatService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcBatchService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcBatchDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadAccuracyCityMonthFcService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.recall.api.AccuracyLoadCityFcRecallService;
import com.tsintergy.lf.serviceapi.base.recall.api.LoadCityFcRecallService;
import com.tsintergy.lf.serviceapi.base.recall.api.StatisticsCityDayFcRecallService;
import com.tsintergy.lf.serviceapi.base.recall.pojo.AccuracyLoadCityFcServiceRecallDO;
import com.tsintergy.lf.serviceapi.base.recall.pojo.LoadCityFcRecallDO;
import com.tsintergy.lf.serviceapi.base.recall.pojo.StatisticsCityDayFcRecallDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingSystemDO;
import com.tsintergy.lf.serviceimpl.base.dao.CityDAO;
import com.tsintergy.lf.serviceimpl.check.dao.SettingCheckDAO;
import com.tsintergy.lf.serviceimpl.check.dao.SettingReportDAO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.*;
import com.tsintergy.lf.serviceimpl.forecast.dao.LoadCityFcDAO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadCityHisDAO;
import com.tsintergy.lf.serviceimpl.system.dao.SettingSystemDAO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 负荷特性统计接口实现
 * User:taojingui
 * Date:18-2-23
 * Time:下午12:04
 */
@Slf4j
@Service("forecastResultStatService")
public class ForecastResultStatServiceImpl extends BaseServiceImpl implements ForecastResultStatService {

    @Autowired
    LoadCityFcRecallService loadCityFcRecallService;

    @Autowired
    LoadCityHisDAO loadCityHisDAO;

    @Autowired
    LoadCityFcDAO loadCityFcDAO;

    @Autowired
    AccuracyLoadCityFcDAO accuracyLoadCityFcDAO;

    @Autowired
    DeviationLoadCityFcDAO deviationLoadCityFcDAO;

    @Autowired
    DispersionLoadCityFcDAO dispersionLoadCityFcDAO;

    @Autowired
    PassLoadCityFcDAO passLoadCityFcDAO;

    @Autowired
    StatisticsCityDayFcDAO statisticsCityDayFcDAO;

    @Autowired
    SettingSystemDAO settingSystemDAO;

    @Autowired
    SettingReportDAO settingReportDAO;

    @Autowired
    SettingCheckDAO settingCheckDAO;

    @Autowired
    CityDAO cityDAO;

    @Autowired
    CityService cityService;

    @Autowired
    LoadCityHisService loadCityHisService;

    @Autowired
    LoadCityFcService loadCityFcService;

    @Autowired
    DeviationLoadCityFcService deviationLoadCityFcService;

    @Autowired
    DispersionLoadCityFcService dispersionLoadCityFcService;

    @Autowired
    StatisticsCityDayFcService statisticsCityDayFcService;

    @Autowired
    SettingReportService settingReportService;

    @Autowired
    SettingCheckService settingCheckService;

    @Autowired
    AccuracyLoadCityFcRecallService accuracyLoadCityFcRecallService;

    @Autowired
    StatisticsCityDayFcRecallService statisticsCityDayFcRecallService;

    @Autowired
    LoadAccuracyCityMonthFcService loadAccuracyCityMonthFcService;

    @Autowired
    SettingSystemService settingSystemService;

    @Autowired
    StatisticsCityDayFcBatchDAO statisticsCityDayFcBatchDAO;

    @Autowired
    LoadCityFcBatchService loadCityFcBatchService;

    @Autowired
    StatisticsCityDayFcBatchService statisticsCityDayFcBatchService;

    @Autowired
    ForecastResultStatService forecastResultStatService;

    @Override
    public void statForecastResult(String cityId, String algorithmId, String caliberId, Date startDate, Date endDate) throws Exception {

        try {
            // 历史负荷
            List<LoadCityHisDO> loadCityHisDOS = loadCityHisService.getLoadCityHisDOS(cityId, caliberId, startDate, endDate);

            // 预测负荷
            List<LoadCityFcDO> loadCityFcDOS = loadCityFcService.findFcByAlgorithmId(cityId, caliberId, algorithmId, startDate, endDate);

            // 统计准确率
            List<? extends BaseLoadCityDO> baseLoadCityDOS = this.calculateAccuracy(loadCityHisDOS, loadCityFcDOS);
//            ForecastResultStatService forecastResultStatService = (ForecastResultStatServiceImpl) SpringContextManager.getApplicationContext().getBean("forecastResultStatService");
            List<AccuracyLoadCityFcDO> accuracyLoadCityFcDOS = JSONArray
                    .parseArray(JsonObject.toJSONString(baseLoadCityDOS), AccuracyLoadCityFcDO.class);
            accuracyLoadCityFcDOS = forecastResultStatService.doSaveOrUpdateAccuracyLoadCityFcDOs(accuracyLoadCityFcDOS);

            // 统计偏差值
            List<DeviationLoadCityFcDO> deviationLoadCityFcVOS = this.calculateDeviation(
                    loadCityHisDOS, loadCityFcDOS);

            deviationLoadCityFcVOS = deviationLoadCityFcService.doSaveOrUpdateDeviationLoadCityFcDOs(deviationLoadCityFcVOS);


            // 统计离散度
            List<DispersionLoadCityFcDO> dispersionLoadCityFcDOS = dispersionLoadCityFcDAO.calculateDispersion(
                    accuracyLoadCityFcDOS);
            dispersionLoadCityFcDOS = dispersionLoadCityFcService.doSaveOrUpdateDispersionLoadCityFcVOs(
                    dispersionLoadCityFcDOS);
            // 所有地市考核设置
            List<String> cityIds = null;
            if (cityId != null) {
                String tempId = cityId;
                CityDO cityVO = cityService.findCityById(tempId);
                if (cityVO.getType() == 2) {
                    tempId = cityVO.getBelongId();
                }
                cityIds = cityService.findCityIdsByBelongId(tempId);
                cityIds.add(tempId);
            }
            List<SettingReportDO> settingReportDOS = settingReportService.getSettingReportDOs(cityIds);

            // 获取已经通过的地市免考申请
            List<SettingCheckDO> settingCheckDOS = settingCheckService.getSettingCheckVOs(cityIds, startDate, endDate, ParamConstants.CHECK_STATUS);

            // 统计合格率
//            List<PassLoadCityFcDO> passLoadCityFcVOS = getPassLoadCityFcBO().calculatePass(accuracyLoadCityFcDOS, settingReportDOS, settingCheckDOS);
//            passLoadCityFcVOS = getPassLoadCityFcBO().doSaveOrUpdatePassLoadCityFcVOs(passLoadCityFcVOS);

            SettingSystemDO settingSystemDO = settingSystemService.findByFieldId(SystemConstant.ACCURACY_DENOMINATOR);
            JSONObject denominatorMap = null;
            if (settingSystemDO != null) {
                String value = settingSystemDO.getValue();
                if (!"".equals(value)) {
                    denominatorMap = JSONObject.parseObject(value);
                }
            }

            // 统计一天的准确率情况
            List<StatisticsCityDayFcDO> statisticsCityDayFcDOS = statisticsCityDayFcDAO.statistics(loadCityHisDOS,
                    loadCityFcDOS,
                    accuracyLoadCityFcDOS, dispersionLoadCityFcDOS, deviationLoadCityFcVOS,
                    settingReportDOS);
            //statistics_city_day_fc_service
            statisticsCityDayFcService.doSaveOrUpdateStatisticsCityDayFcDOs(statisticsCityDayFcDOS);

            // 批次预测负荷 load_city_fc_batch
            List<LoadCityFcBatchDO> loadCityFcBatchDOS = loadCityFcBatchService
                .findByCondition(cityId, startDate, endDate, caliberId, algorithmId);

            //统计一天各个批次的准确率情况 statistics_city_day_fc_service_batch
            List<StatisticsCityDayFcBatchDO> statistics = statisticsCityDayFcBatchDAO.statistics(loadCityHisDOS,
                loadCityFcBatchDOS, settingReportDOS, denominatorMap);
            statisticsCityDayFcBatchService.doSaveOrUpdateStatisticsCityDayFcDOs(statistics);

        } catch (Exception e) {
            throw new BusinessException("T706", "统计预测结果出错了", e);
        }
        loadAccuracyCityMonthFcService.doMonthAccuracyData(DateUtils.date2String(startDate, DateFormatType.SIMPLE_DATE_FORMAT_STR), cityId);
    }

    /**
     * 计算预测准确率
     *
     * @param loadCityHisDOS 历史数据
     * @param loadCityFcDOS  预测数据
     */
    public List<DeviationLoadCityFcDO> calculateDeviation(List<LoadCityHisDO> loadCityHisDOS,
                                                          List<? extends BaseLoadFcCityDO> loadCityFcDOS) throws Exception {

        Map<String, LoadCityHisDO> loadCityHisVOMap = new HashMap<String, LoadCityHisDO>();
        for (LoadCityHisDO loadCityHisDO : loadCityHisDOS) {
            String key = loadCityHisDO.getCityId() + "-" + loadCityHisDO.getCaliberId() + "-" + loadCityHisDO.getDate()
                    .getTime();
            loadCityHisVOMap.put(key, loadCityHisDO);
        }

        Map<String, List<BaseLoadFcCityDO>> loadCityFcVOMap = new HashMap<String, List<BaseLoadFcCityDO>>();
        for (BaseLoadFcCityDO loadCityFcDO : loadCityFcDOS) {
            String key =
                    loadCityFcDO.getCityId() + "-" + loadCityFcDO.getCaliberId() + "-" + loadCityFcDO.getDate().getTime();
            if (loadCityFcVOMap.get(key) == null) {
                loadCityFcVOMap.put(key, new ArrayList<BaseLoadFcCityDO>());
            }
            loadCityFcVOMap.get(key).add(loadCityFcDO);
        }

        List<DeviationLoadCityFcDO> deviationLoadCityFcVOs = new ArrayList<DeviationLoadCityFcDO>();
        for (String key : loadCityHisVOMap.keySet()) {
            if (loadCityFcVOMap.get(key) != null) {
                for (BaseLoadFcCityDO loadCityFcDO : loadCityFcVOMap.get(key)) {
                    try {
                        deviationLoadCityFcVOs.add(this.calculateDeviation(loadCityHisVOMap.get(key), loadCityFcDO));
                    } catch (Exception e) {
                        log.error("计算偏差值出错了", e);
                    }
                }
            }
        }

        return deviationLoadCityFcVOs;
    }

    /**
     * 计算预测偏差值
     *
     * @param loadCityHisVO 历史数据
     * @param loadCityFcDO  预测数据
     */
    public DeviationLoadCityFcDO calculateDeviation(LoadCityHisDO loadCityHisVO, BaseLoadFcCityDO loadCityFcDO)
            throws Exception {

        if (!loadCityHisVO.getCityId().equals(loadCityFcDO.getCityId())) {
            log.error("历史数据和预测数据的城市不同，不可以计算");
            return null;
        }

        if (!loadCityHisVO.getDate().equals(loadCityFcDO.getDate())) {
            log.error("历史数据和预测数据的日期不同，不可以计算");
            return null;
        }

        Map<String, BigDecimal> hisDataMap = BasePeriodUtils
                .toMap(loadCityHisVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        Map<String, BigDecimal> fcDataMap = BasePeriodUtils
                .toMap(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        Map<String, BigDecimal> deviationMap = new HashMap<String, BigDecimal>();

        for (String column : hisDataMap.keySet()) {
            deviationMap.put(column, LoadCalUtil.getDevication(hisDataMap.get(column), fcDataMap.get(column)));
        }

        DeviationLoadCityFcDO deviationLoadCityFcVO = new DeviationLoadCityFcDO();
        deviationLoadCityFcVO.setAlgorithmId(loadCityFcDO.getAlgorithmId());
        deviationLoadCityFcVO.setCityId(loadCityFcDO.getCityId());
        deviationLoadCityFcVO.setDate(loadCityFcDO.getDate());
        deviationLoadCityFcVO.setCaliberId(loadCityFcDO.getCaliberId());
        BasePeriodUtils.setAllFiled(deviationLoadCityFcVO, deviationMap);
        deviationLoadCityFcVO.setReport(loadCityFcDO.getReport());

        return deviationLoadCityFcVO;
    }

    /**
     * 保存或更新
     *
     * @param accuracyLoadCityFcDOS
     * @return
     */
    @Override
    public List<AccuracyLoadCityFcDO> doSaveOrUpdateAccuracyLoadCityFcDOs(List<AccuracyLoadCityFcDO> accuracyLoadCityFcDOS) throws Exception {
        List<AccuracyLoadCityFcDO> vos = new ArrayList<AccuracyLoadCityFcDO>();
        for (AccuracyLoadCityFcDO accuracyLoadCityFcDO : accuracyLoadCityFcDOS) {
            try {
                vos.add(doSaveOrUpdateAccuracyLoadCityFcDO(accuracyLoadCityFcDO));
            } catch (Exception e) {
                log.error("保存准确率出错了", e);
            }
        }
        return vos;
    }

    /**
     * 保存或更新
     *
     * @param accuracyLoadCityFcVO
     * @return
     */
    public AccuracyLoadCityFcDO doSaveOrUpdateAccuracyLoadCityFcDO(AccuracyLoadCityFcDO accuracyLoadCityFcVO) throws Exception {
        AccuracyLoadCityFcDO oldVO = getAccuracyLoadCityFcDO(accuracyLoadCityFcVO.getCityId(),
                accuracyLoadCityFcVO.getCaliberId(), accuracyLoadCityFcVO.getAlgorithmId(),
                accuracyLoadCityFcVO.getDate());
        if (oldVO != null) {
            String id = oldVO.getId();
            BeanUtils.copyProperties(accuracyLoadCityFcVO, oldVO);
            oldVO.setId(id);
            return accuracyLoadCityFcDAO.updateAndFlush(oldVO);
        } else {
            return accuracyLoadCityFcDAO.createAndFlush(accuracyLoadCityFcVO);
        }
    }

    /**
     *    
     *  功能描述: <br> 
     * 获取准确率
     *
     * @param cityId      城市ID
     * @param caliberId   口径ID
     * @param algorithmId 算法ID
     * @param date        日期
     * @return   @Author:zhaoml   
     *  @Date: 2018/4/23 14:21   
     */
    public AccuracyLoadCityFcDO getAccuracyLoadCityFcDO(String cityId, String caliberId, String algorithmId, Date date) throws Exception {

        if (cityId == null) {
            throw new BusinessException("T706", "城市ID不可为空");
        }
        if (caliberId == null) {
            throw new BusinessException("T706", "口径ID不可为空");
        }
        if (algorithmId == null) {
            throw new BusinessException("T706", "算法ID不可为空");
        }
        if (date == null) {
            throw new BusinessException("T706", "日期不可为空");
        }
        List<AccuracyLoadCityFcDO> accuracyLoadCityFcDOS = accuracyLoadCityFcDAO
                .getAccuracyLoadCityFcDO(cityId, caliberId, algorithmId, date);
        if (accuracyLoadCityFcDOS.size() > 0) {
            return accuracyLoadCityFcDOS.get(0);
        }
        return null;
    }


    /**
     * 计算预测准确率
     *
     * @param loadCityHisDOS 历史数据
     * @param loadCityFcDOS  预测数据
     * @return
     */
    public List<? extends BaseLoadFcCityDO> calculateAccuracy(List<LoadCityHisDO> loadCityHisDOS, List<? extends BaseLoadFcCityDO> loadCityFcDOS) throws Exception {

        Map<String, LoadCityHisDO> loadCityHisVOMap = new HashMap<String, LoadCityHisDO>();
        for (LoadCityHisDO loadCityHisDO : loadCityHisDOS) {
            String key = loadCityHisDO.getCityId() + "-" + loadCityHisDO.getCaliberId() + "-" + loadCityHisDO.getDate().getTime();
            loadCityHisVOMap.put(key, loadCityHisDO);
        }

        Map<String, List<BaseLoadFcCityDO>> loadCityFcVOMap = new HashMap();
        for (BaseLoadFcCityDO loadCityFcDO : loadCityFcDOS) {
            String key = loadCityFcDO.getCityId() + "-" + loadCityFcDO.getCaliberId() + "-" + loadCityFcDO.getDate().getTime();
            if (loadCityFcVOMap.get(key) == null) {
                loadCityFcVOMap.put(key, new ArrayList<>());
            }
            loadCityFcVOMap.get(key).add(loadCityFcDO);
        }

        List<AccuracyLoadCityFcDO> accuracyLoadCityFcDOS = new ArrayList();
        for (String key : loadCityHisVOMap.keySet()) {
            if (loadCityFcVOMap.get(key) != null) {
                for (BaseLoadFcCityDO loadCityFcDO : loadCityFcVOMap.get(key)) {
                    try {
                        accuracyLoadCityFcDOS.add(this.calculateAccuracy(loadCityHisVOMap.get(key), loadCityFcDO));
                    } catch (Exception e) {
                        log.error("统计准确率出错了", e);
                    }
                }
            }
        }

        return accuracyLoadCityFcDOS;
    }

    /**
     * 计算预测准确率
     *
     * @param loadCityHisVO 历史数据
     * @param loadCityFcDO  预测数据
     * @return
     */
    public AccuracyLoadCityFcDO calculateAccuracy(LoadCityHisDO loadCityHisVO, BaseLoadFcCityDO loadCityFcDO) throws Exception {

        if (!loadCityHisVO.getCityId().equals(loadCityFcDO.getCityId())) {
            log.error("历史数据和预测数据的城市不同，不可以计算");
            return null;
        }

        if (!loadCityHisVO.getDate().equals(loadCityFcDO.getDate())) {
            log.error("历史数据和预测数据的日期不同，不可以计算");
            return null;
        }

        Map<String, BigDecimal> hisDataMap = BasePeriodUtils
                .toMap(loadCityHisVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        Map<String, BigDecimal> fcDataMap = BasePeriodUtils.toMap(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        Map<String, BigDecimal> accuracyMap = new HashMap<String, BigDecimal>();

        for (String column : hisDataMap.keySet()) {
            accuracyMap.put(column, LoadCalUtil.getAccuracy(hisDataMap.get(column), fcDataMap.get(column)));
        }

        AccuracyLoadCityFcDO accuracyLoadCityFcVO = new AccuracyLoadCityFcDO();
        accuracyLoadCityFcVO.setAlgorithmId(loadCityFcDO.getAlgorithmId());
        accuracyLoadCityFcVO.setCityId(loadCityFcDO.getCityId());
        accuracyLoadCityFcVO.setDate(loadCityFcDO.getDate());
        accuracyLoadCityFcVO.setCaliberId(loadCityFcDO.getCaliberId());
        accuracyLoadCityFcVO.setReport(loadCityFcDO.getReport());
        BasePeriodUtils.setAllFiled(accuracyLoadCityFcVO, accuracyMap);

        return accuracyLoadCityFcVO;
    }

    @Override
    public void statForecastRecallResult(String cityId, String algorithmId, String caliberId, Date startDate,
                                         Date endDate) throws Exception {
        // 历史负荷
        List<LoadCityHisDO> loadCityHisDOS = loadCityHisService.getLoadCityHisDOS(cityId, caliberId, startDate, endDate);

        // 预测负荷
        List<LoadCityFcRecallDO> loadCityFcRecallDOS = loadCityFcRecallService
                .getLoadCityFc(cityId, caliberId, startDate, endDate, algorithmId);

        // 统计准确率
        List<? extends BaseLoadFcCityDO> baseLoadCityDOS = this.calculateAccuracy(loadCityHisDOS, loadCityFcRecallDOS);

        List<AccuracyLoadCityFcServiceRecallDO> accuracyLoadCityFcDOS = JSONArray
                .parseArray(JsonObject.toJSONString(baseLoadCityDOS), AccuracyLoadCityFcServiceRecallDO.class);
        accuracyLoadCityFcDOS = accuracyLoadCityFcRecallService.doSaveOrUpdateBatch(accuracyLoadCityFcDOS);

        List<DeviationLoadCityFcDO> deviationLoadCityFcVOS = this.calculateDeviation(loadCityHisDOS, loadCityFcRecallDOS);

        // 统计离散度
        List<DispersionLoadCityFcDO> dispersionLoadCityFcDOS = dispersionLoadCityFcDAO.calculateDispersion(
                accuracyLoadCityFcDOS);

        // 统计一天的准确率情况loadCityFcRecallDOS = {ArrayList@16550}  size = 6
        List<StatisticsCityDayFcDO> statisticsCityDayFcDOS = statisticsCityDayFcDAO.statistics(loadCityHisDOS,
                loadCityFcRecallDOS,
                accuracyLoadCityFcDOS, dispersionLoadCityFcDOS, deviationLoadCityFcVOS,
                null);

        List<StatisticsCityDayFcRecallDO> statisticsCityDayFcVOS = JSONArray
                .parseArray(JsonObject.toJSONString(statisticsCityDayFcDOS), StatisticsCityDayFcRecallDO.class);

        statisticsCityDayFcRecallService.doSaveOrUpdateBatch(statisticsCityDayFcVOS);
    }


}
