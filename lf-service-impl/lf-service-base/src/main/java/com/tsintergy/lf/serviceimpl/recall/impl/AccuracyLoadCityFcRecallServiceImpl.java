/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.recall.impl;

import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.evalucation.api.AccuracyLoadCityFcService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsCityDayFcService;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyLoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.recall.api.AccuracyLoadCityFcRecallService;
import com.tsintergy.lf.serviceapi.base.recall.api.LoadCityFcRecallService;
import com.tsintergy.lf.serviceapi.base.recall.api.StatisticsCityDayFcRecallService;
import com.tsintergy.lf.serviceapi.base.recall.dto.AccuracyLoadFcRecallDTO;
import com.tsintergy.lf.serviceapi.base.recall.pojo.AccuracyLoadCityFcServiceRecallDO;
import com.tsintergy.lf.serviceapi.base.recall.pojo.LoadCityFcRecallDO;
import com.tsintergy.lf.serviceapi.base.recall.pojo.StatisticsCityDayFcRecallDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceimpl.recall.dao.AccuracyLoadCityFcRecallDAO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/1 14:27
 * @Version: 1.0.0
 */
@Service("accuracyLoadCityFcRecallService")
@Slf4j
public class AccuracyLoadCityFcRecallServiceImpl implements AccuracyLoadCityFcRecallService {

    @Autowired
    AccuracyLoadCityFcRecallDAO accuracyLoadCityFcRecallDAO;

    @Autowired
    StatisticsCityDayFcService statisticsCityDayFcService;

    @Autowired
    StatisticsCityDayFcRecallService statisticsCityDayFcRecallService;

    @Autowired
    WeatherFeatureCityDayFcService weatherFeatureCityDayFcService;

    @Autowired
    WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    LoadFeatureCityDayHisService loadFeatureCityDayHisService;

    @Autowired
    LoadCityHisService loadCityHisService;

    @Autowired
    LoadCityFcService loadCityFcService;

    @Autowired
    LoadCityFcRecallService loadCityFcRecallService;

    @Autowired
    AccuracyLoadCityFcService accuracyLoadCityFcService;

    @Autowired
    AccuracyLoadCityFcRecallService accuracyLoadCityFcRecallService;


    @Override
    public AccuracyLoadCityFcServiceRecallDO doSaveOrUpdate(AccuracyLoadCityFcServiceRecallDO accuracyLoadCityFcServiceRecallDO) {
        List<AccuracyLoadCityFcServiceRecallDO> oldVO = getAccuracyLoadCityFcRecallDO(accuracyLoadCityFcServiceRecallDO.getCityId(),
                accuracyLoadCityFcServiceRecallDO.getCaliberId(), accuracyLoadCityFcServiceRecallDO.getAlgorithmId(),
                accuracyLoadCityFcServiceRecallDO.getDate());
        if (!CollectionUtils.isEmpty(oldVO)) {
            AccuracyLoadCityFcServiceRecallDO tempDo = oldVO.get(0);
            String id = tempDo.getId();
            BeanUtils.copyProperties(accuracyLoadCityFcServiceRecallDO, tempDo);
            tempDo.setId(id);
            return accuracyLoadCityFcRecallDAO.updateAndFlush(tempDo);
        } else {
            return accuracyLoadCityFcRecallDAO.createAndFlush(accuracyLoadCityFcServiceRecallDO);
        }
    }

    @Override
    public List<AccuracyLoadCityFcServiceRecallDO> getAccuracyLoadCityFcRecallDO(String cityId, String caliberId,
                                                                                 String algorithmId, Date date) {
        List<AccuracyLoadCityFcServiceRecallDO> accuracyLoadCityFcDOS = accuracyLoadCityFcRecallDAO
                .getAccuracyLoadCityFcDO(cityId, caliberId, algorithmId, date);
        return accuracyLoadCityFcDOS;
    }

    @Override
    public List<AccuracyLoadCityFcServiceRecallDO> doSaveOrUpdateBatch(
            List<AccuracyLoadCityFcServiceRecallDO> accuracyLoadCityFcServiceRecallDOs) {
        List<AccuracyLoadCityFcServiceRecallDO> vos = new ArrayList();
        for (AccuracyLoadCityFcServiceRecallDO accuracyLoadCityFcDO : accuracyLoadCityFcServiceRecallDOs) {
            try {
                vos.add(doSaveOrUpdate(accuracyLoadCityFcDO));
            } catch (Exception e) {
                log.error("保存准确率出错了", e);
            }
        }
        return vos;
    }

    @Override
    public List<AccuracyLoadCityFcServiceRecallDO> getAccuracyLoadCityFcServiceRecallDOS(String cityId, String caliberId, Date startDate, Date endDate, String algorithmId) throws Exception {
        return accuracyLoadCityFcRecallDAO.getAccuracyLoadCityFcDOs(cityId, caliberId, algorithmId, startDate, endDate, null);
    }

    @Override
    public List<AccuracyLoadFcRecallDTO> getDTOS(String cityId, String caliberId, Date startDate, Date endDate, String algorithmId) {
        try {
            List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
            List<StatisticsCityDayFcDO> statisticsCityDayFcDOList = statisticsCityDayFcService.getDayAccuracyList(cityId, caliberId, algorithmId, startDate, endDate);
            Map<java.sql.Date, List<StatisticsCityDayFcDO>> statisticsCityDayFcDOMap = new HashMap<>(dateList.size());
            if (statisticsCityDayFcDOList != null) {
                statisticsCityDayFcDOMap = statisticsCityDayFcDOList.stream().collect(Collectors.groupingBy(StatisticsCityDayFcDO::getDate));
            }

            List<StatisticsCityDayFcRecallDO> statisticsCityDayFcRecallDOList = statisticsCityDayFcRecallService.getStatisticsCityDayFcRecallDOList(cityId, caliberId, startDate, endDate, algorithmId);
            Map<java.sql.Date, List<StatisticsCityDayFcRecallDO>> statisticsCityDayFcRecallDOMap = new HashMap<>(dateList.size());
            if (statisticsCityDayFcRecallDOList != null) {
                statisticsCityDayFcRecallDOMap = statisticsCityDayFcRecallDOList.stream().collect(Collectors.groupingBy(StatisticsCityDayFcRecallDO::getDate));
            }

            List<WeatherFeatureCityDayFcDO> weatherFeatureCityDayFcDOList = weatherFeatureCityDayFcService.getWeatherFeatureCityDayFcDOList(cityId, startDate, endDate);
            Map<java.sql.Date, List<WeatherFeatureCityDayFcDO>> weatherFeatureCityDayFcDOMap = new HashMap<>(dateList.size());
            if (weatherFeatureCityDayFcDOList != null) {
                weatherFeatureCityDayFcDOMap = weatherFeatureCityDayFcDOList.stream().collect(Collectors.groupingBy(WeatherFeatureCityDayFcDO::getDate));
            }

            List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOList = weatherFeatureCityDayHisService.listWeatherFeatureCityDayHisDO(cityId, startDate, endDate);
            Map<java.sql.Date, List<WeatherFeatureCityDayHisDO>> weatherFeatureCityDayHisDOMap = new HashMap<>(dateList.size());
            if (weatherFeatureCityDayHisDOList != null) {
                weatherFeatureCityDayHisDOMap = weatherFeatureCityDayHisDOList.stream().collect(Collectors.groupingBy(WeatherFeatureCityDayHisDO::getDate));
            }

            List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOList = loadFeatureCityDayHisService.findLoadFeatureCityDayHisDOS(cityId, startDate, endDate, caliberId);
            Map<java.sql.Date, List<LoadFeatureCityDayHisDO>> loadFeatureCityDayHisDOMap = new HashMap<>(dateList.size());
            if (loadFeatureCityDayHisDOList != null) {
                loadFeatureCityDayHisDOMap = loadFeatureCityDayHisDOList.stream().collect(Collectors.groupingBy(LoadFeatureCityDayHisDO::getDate));
            }


            List<LoadCityHisDO> loadCityHisDOList = loadCityHisService.getLoadCityHisDOS(cityId, caliberId, startDate, endDate);
            Map<java.sql.Date, List<LoadCityHisDO>> loadCityHisDOMap = new HashMap<>(dateList.size());
            if (loadCityHisDOList != null) {
                loadCityHisDOMap = loadCityHisDOList.stream().collect(Collectors.groupingBy(LoadCityHisDO::getDate));
            }

            List<LoadCityFcDO> loadCityFcDOList = loadCityFcService.listLoadCityFc(cityId, caliberId, startDate, endDate, algorithmId);
            Map<java.sql.Date, List<LoadCityFcDO>> loadCityFcDOMap = new HashMap<>(dateList.size());
            if (loadCityFcDOList != null) {
                loadCityFcDOMap = loadCityFcDOList.stream().collect(Collectors.groupingBy(LoadCityFcDO::getDate));
            }

            List<LoadCityFcRecallDO> loadCityFcRecallDOList = loadCityFcRecallService.getLoadCityFc(cityId, caliberId, startDate, endDate, algorithmId);
            Map<java.sql.Date, List<LoadCityFcRecallDO>> loadCityFcRecallDOMap = new HashMap<>(dateList.size());
            if (loadCityFcRecallDOList != null) {
                loadCityFcRecallDOMap = loadCityFcRecallDOList.stream().collect(Collectors.groupingBy(LoadCityFcRecallDO::getDate));
            }

            List<AccuracyLoadCityFcDO> accuracyLoadCityFcDOList = accuracyLoadCityFcService.getAccuracyLoadCityFcDOList(cityId, caliberId, algorithmId, startDate, endDate, null);
            Map<java.sql.Date, List<AccuracyLoadCityFcDO>> accuracyLoadCityFcDOMap = new HashMap<>(dateList.size());
            if (accuracyLoadCityFcDOList != null) {
                accuracyLoadCityFcDOMap = accuracyLoadCityFcDOList.stream().collect(Collectors.groupingBy(AccuracyLoadCityFcDO::getDate));
            }

            List<AccuracyLoadCityFcServiceRecallDO> accuracyLoadCityFcDoList = accuracyLoadCityFcRecallService.getAccuracyLoadCityFcServiceRecallDOS(cityId, caliberId, startDate, endDate, algorithmId);
            Map<java.sql.Date, List<AccuracyLoadCityFcServiceRecallDO>> accuracyLoadCityFcDoMap = new HashMap<>(dateList.size());
            if (accuracyLoadCityFcDoList != null) {
                accuracyLoadCityFcDoMap = accuracyLoadCityFcDoList.stream().collect(Collectors.groupingBy(AccuracyLoadCityFcServiceRecallDO::getDate));
            }


            ArrayList<AccuracyLoadFcRecallDTO> dtoList = new ArrayList<>(dateList.size());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            for (Date date : dateList) {
                java.sql.Date sqlDate = new java.sql.Date(date.getTime());
                AccuracyLoadFcRecallDTO dto = new AccuracyLoadFcRecallDTO();
                dto.setDate(sdf.format(date));
                BigDecimal accuracyFc = null;
                if (statisticsCityDayFcDOMap.get(sqlDate) != null) {
                    StatisticsCityDayFcDO cityDayFcDO = statisticsCityDayFcDOMap.get(sqlDate).get(0);
                    if (cityDayFcDO != null) {
                        accuracyFc = cityDayFcDO.getAccuracy();
                        if (accuracyFc != null) {
                            dto.setFcAccuracy(accuracyFc);
                        }
                    }
                }

                BigDecimal accuracyRecall = null;
                if (statisticsCityDayFcRecallDOMap.get(sqlDate) != null) {
                    StatisticsCityDayFcRecallDO fcRecallDO = statisticsCityDayFcRecallDOMap.get(sqlDate).get(0);
                    if (fcRecallDO != null) {
                        accuracyRecall = fcRecallDO.getAccuracy();
                        if (accuracyRecall != null) {
                            dto.setRecallAccuracy(accuracyRecall);
                        }
                    }
                }

                if (accuracyFc != null && accuracyRecall != null) {
                    dto.setRecallDeviation(accuracyRecall.subtract(accuracyFc));
                }

                BigDecimal highestTemperatureFc = null;
                if (weatherFeatureCityDayFcDOMap.get(sqlDate) != null) {
                    WeatherFeatureCityDayFcDO cityDayFcDO = weatherFeatureCityDayFcDOMap.get(sqlDate).get(0);
                    if (cityDayFcDO != null) {
                        highestTemperatureFc = cityDayFcDO.getHighestTemperature();
                    }
                }

                BigDecimal highestTemperatureHis = null;
                if (weatherFeatureCityDayHisDOMap.get(sqlDate) != null) {
                    WeatherFeatureCityDayHisDO cityDayHisDO = weatherFeatureCityDayHisDOMap.get(sqlDate).get(0);
                    if (cityDayHisDO != null) {
                        highestTemperatureHis = cityDayHisDO.getHighestTemperature();
                    }
                }

                if (highestTemperatureFc != null && highestTemperatureHis != null) {
                    dto.setHighestTemperatureFcDeviation(highestTemperatureFc.subtract(highestTemperatureHis));
                }

                String maxTime = "";
                if (loadFeatureCityDayHisDOMap.get(sqlDate) != null) {
                    LoadFeatureCityDayHisDO cityDayHisDO = loadFeatureCityDayHisDOMap.get(sqlDate).get(0);
                    if (cityDayHisDO != null) {
                        maxTime = cityDayHisDO.getMaxTime();
                    }
                }

                String methodName = "";
                if (maxTime != null) {
                    methodName = this.getMethodName(maxTime);
                }

                BigDecimal loadHis = null;
                if (loadCityHisDOMap.get(sqlDate) != null) {
                    LoadCityHisDO loadCityHisDO = loadCityHisDOMap.get(sqlDate).get(0);
                    if (loadCityHisDO != null && methodName != null) {
                        loadHis = getBigDecimalVal(loadCityHisDO, methodName);
                    }
                }

                BigDecimal loadFc = null;
                if (loadCityFcDOMap.get(sqlDate) != null) {
                    LoadCityFcDO loadCityFcDO = loadCityFcDOMap.get(sqlDate).get(0);
                    if (loadCityFcDO != null && methodName != null) {
                        loadFc = getBigDecimalVal(loadCityFcDO, methodName);
                    }
                }

                if (loadFc != null && loadHis != null) {
                    dto.setPeakFcDeviation(loadFc.subtract(loadHis));
                }

                BigDecimal loadFcRecall = null;
                if (loadCityFcRecallDOMap.get(sqlDate) != null) {
                    LoadCityFcRecallDO fcRecallDO = loadCityFcRecallDOMap.get(sqlDate).get(0);
                    if (fcRecallDO != null && methodName != null) {
                        loadFcRecall = getBigDecimalVal(fcRecallDO, methodName);
                    }
                }

                if (loadFcRecall != null && loadHis != null) {
                    dto.setPeakRecallDeviation(loadFcRecall.subtract(loadHis));
                }

                if (accuracyLoadCityFcDOMap.get(sqlDate) != null) {
                    AccuracyLoadCityFcDO cityFcDO = accuracyLoadCityFcDOMap.get(sqlDate).get(0);
                    if (cityFcDO != null && methodName != null) {
                        BigDecimal maxTimeAccuracyFc = getBigDecimalVal(cityFcDO, methodName);
                        if (maxTimeAccuracyFc != null) {
                            dto.setPeakFcAccuracy(maxTimeAccuracyFc);
                        }
                    }
                }

                if (accuracyLoadCityFcDoMap.get(sqlDate) != null) {
                    AccuracyLoadCityFcServiceRecallDO recallDO = accuracyLoadCityFcDoMap.get(sqlDate).get(0);
                    if (recallDO != null && methodName != null) {
                        BigDecimal maxTimeAccuracyRecall = getBigDecimalVal(recallDO, methodName);
                        if (maxTimeAccuracyRecall != null) {
                            dto.setPeakRecallAccuracy(maxTimeAccuracyRecall);
                        }
                    }
                }
                dtoList.add(dto);
            }
            return dtoList;

        } catch (Exception e) {
            e.printStackTrace();
        }

        return new ArrayList<>();
    }

    @Override
    public String getMethodName(String time) {
        String[] arr = time.split(":");
        int length = 2;
        if (arr.length >= length) {
            return "getT" + arr[0] + arr[1];
        }
        return null;

    }

    @Override
    public BigDecimal getBigDecimalVal(Object obj, String methodName) {
        try {
            Method method = obj.getClass().getMethod(methodName);
            return (BigDecimal) method.invoke(obj);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


}