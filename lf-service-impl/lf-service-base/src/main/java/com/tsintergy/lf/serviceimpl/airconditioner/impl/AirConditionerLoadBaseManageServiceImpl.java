/**
 * Copyright (C), 2015‐2022, 北京清能互联科技有限公司 Author:  <EMAIL> Date:  2022/5/24 9:35 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.airconditioner.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.BeanCopierUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.enums.SeasonEnum;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.AcLoadReportD5000Service;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.AirConditionerLoadBaseManageService;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.LoadSolutionManageDetailService;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.LoadSolutionManageEntityService;
import com.tsintergy.lf.serviceapi.base.airconditioner.dto.*;
import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.LoadSolutionManageDO;
import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.LoadSolutionManageDetailDO;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.common.enumeration.DateType2;
import com.tsintergy.lf.serviceapi.base.common.enumeration.TemperatureIndex;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceimpl.load.impl.LoadAcHisBasicServiceimpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: 空调负荷基础负荷管理 <br>
 *
 * <AUTHOR>
 * @create 2022/5/24
 * @since 1.0.0
 */
@Service("airConditionerLoadBaseManageService")
public class AirConditionerLoadBaseManageServiceImpl extends BaseServiceImpl implements
    AirConditionerLoadBaseManageService {

    @Autowired
    private LoadSolutionManageEntityService loadSolutionManageEntityService;

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    private HolidayService holidayService;


    @Autowired
    CityService cityService;

    private static final String SPRING = "spring";

    private static final String AUTUMN = "autumn";
    @Autowired
    private LoadAcHisBasicServiceimpl loadAcHisBasicService;

    @Autowired
    private LoadSolutionManageDetailService loadSolutionManageDetailService;

    @Override
    public List<LoadSolutionManageRespDTO> queryLoadSolutionManage(LoadSolutionManageDTO loadSolutionManageDTO) {
        if ("all".equals(loadSolutionManageDTO.getSeason())) {
            loadSolutionManageDTO.setSeason(null);
        }
        List<LoadSolutionManageDO> allByQueryWrapper = loadSolutionManageEntityService.findAllByQueryWrapper(
            JpaWrappers.<LoadSolutionManageDO>lambdaQuery()
                .eq(LoadSolutionManageDO::getCityId, loadSolutionManageDTO.getCityId())
                .eq(StringUtils.isNotEmpty(loadSolutionManageDTO.getCaliberId()), LoadSolutionManageDO::getCaliberId,
                    loadSolutionManageDTO.getCaliberId())
                .eq(loadSolutionManageDTO.getDateType() != null, LoadSolutionManageDO::getDateType,
                    loadSolutionManageDTO.getDateType())
                .eq(loadSolutionManageDTO.getSeason() != null, LoadSolutionManageDO::getSeason,
                            loadSolutionManageDTO.getSeason())
                .ge(LoadSolutionManageDO::getSolutionYear, loadSolutionManageDTO.getStartYear())
                .le(LoadSolutionManageDO::getSolutionYear, loadSolutionManageDTO.getEndYear())
        );
        List<LoadSolutionManageRespDTO> loadSolutionManageRespDTOS = new ArrayList<>();
        for (LoadSolutionManageDO loadSolutionManageDO : allByQueryWrapper) {
            LoadSolutionManageRespDTO loadSolutionManageRespDTO = new LoadSolutionManageRespDTO();
            BeanUtils.copyProperties(loadSolutionManageDO, loadSolutionManageRespDTO);
            loadSolutionManageRespDTOS.add(loadSolutionManageRespDTO);
        }
        return loadSolutionManageRespDTOS;
    }

    @Override
    public void saveOrUpdateLoadSolutionManage(SaveLoadSolutionManageDTO loadSolutionManage) {
        String cityId = loadSolutionManage.getCityId();
        String caliberId = loadSolutionManage.getCaliberId();
        if (StringUtils.isEmpty(cityId)) {
            throw new BusinessException("", "城市id不能为空");
        }
        if (StringUtils.isEmpty(caliberId)) {
            throw new BusinessException("", "口径id不能为空");
        }

        LoadSolutionManageDO loadSolutionManageDO = BeanCopierUtils.copyProperties(loadSolutionManage,
            LoadSolutionManageDO.class);
        loadSolutionManageEntityService.saveOrUpdateByTemplate(loadSolutionManageDO);
    }

    @Override
    public void saveOrUpdateAcLoadSolutionManage(SaveLoadSolutionCongfigManageDTO loadSolutionManageDTO) {
        String id = UUID.randomUUID().toString().replace("-","").toLowerCase();
        if (loadSolutionManageDTO.getId() == null) {
            List<LoadSolutionManageDO> allByQueryWrapper = loadSolutionManageEntityService.findAllByQueryWrapper(
                    JpaWrappers.<LoadSolutionManageDO>lambdaQuery()
                            .eq(LoadSolutionManageDO::getCityId, loadSolutionManageDTO.getCityId())
                            .eq(StringUtils.isNotEmpty(loadSolutionManageDTO.getCaliberId()), LoadSolutionManageDO::getCaliberId,
                                    loadSolutionManageDTO.getCaliberId())
                            .eq(loadSolutionManageDTO.getDateType() != null, LoadSolutionManageDO::getDateType,
                                    loadSolutionManageDTO.getDateType())
                            .eq(loadSolutionManageDTO.getSeason() != null, LoadSolutionManageDO::getSeason,
                                    loadSolutionManageDTO.getSeason())
                            .eq(loadSolutionManageDTO.getWeatherType() != null, LoadSolutionManageDO::getWeatherType,
                                    loadSolutionManageDTO.getWeatherType())
                            .ge(LoadSolutionManageDO::getSolutionYear, loadSolutionManageDTO.getSolutionYear())
                            .le(LoadSolutionManageDO::getSolutionYear, loadSolutionManageDTO.getSolutionYear())
            );
            id = allByQueryWrapper.get(0).getId();
        } else {
            id = loadSolutionManageDTO.getId();
        }
        List<AcLoadManagePostDataDTO> data = loadSolutionManageDTO.getData();
        if (CollectionUtils.isNotEmpty(data)) {
            for (AcLoadManagePostDataDTO datum : data) {
                LoadSolutionManageDetailDO loadSolutionManageDetailDO = new LoadSolutionManageDetailDO();
                BeanUtils.copyProperties(datum, loadSolutionManageDetailDO);
                loadSolutionManageDetailDO.setManageId(id);
                loadSolutionManageDetailService.saveOrUpdateLoadSolutionManageDetail(loadSolutionManageDetailDO);
            }
        }
    }

    @Override
    public void saveAcLoadSolutionManage(AcLoadCityConfigDataDTO acLoadCityConfigDataDTO, Integer weatherType) {
        Integer type = acLoadCityConfigDataDTO.getType();
        if (type == 1) {
            // 删除
            loadSolutionManageDetailService.saveLoadSolutionManageDetail(acLoadCityConfigDataDTO.getId(),
                    acLoadCityConfigDataDTO.getDate(), weatherType == 2?"福州":acLoadCityConfigDataDTO.getCityName());
        } else if (type == 2) {
            // 恢复
            loadSolutionManageDetailService.saveLoadSolutionManageDetailDate(acLoadCityConfigDataDTO.getId(),
                    acLoadCityConfigDataDTO.getDate(), weatherType == 2?"福州":acLoadCityConfigDataDTO.getCityName());
        }
    }

    @Override
    public void deleteLoadSolutionManage(String solutionId) {
        if (StringUtils.isEmpty(solutionId)) {
            throw new BusinessException("", "方案id不能为空");
        }
        LoadSolutionManageDO oneByQueryWrapper = loadSolutionManageEntityService.findOneByQueryWrapper(
                JpaWrappers.<LoadSolutionManageDO>lambdaQuery()
                        .eq(LoadSolutionManageDO::getId, solutionId));
        if (oneByQueryWrapper != null && oneByQueryWrapper.getEnableCurve()) {
            throw new BusinessException("", "方案已启用，无法删除");
        } else {
            loadSolutionManageEntityService.deleteById(solutionId);
            loadSolutionManageDetailService.deleteLoadSolutionManageDetail(solutionId);
        }
    }

    @Autowired
    AcLoadReportD5000Service acLoadReportD5000Service;

    @Autowired
    SettingSystemService settingSystemService;

    @Override
    public BaseLoadCurveRespDTO queryBaseLoadCurve(String solutionId) throws Exception {

        //根据方案id查询方案
        LoadSolutionManageDO loadSolutionManageDO = loadSolutionManageEntityService.findOneByQueryWrapper(
            JpaWrappers.<LoadSolutionManageDO>lambdaQuery()
                .eq(LoadSolutionManageDO::getId, solutionId));



//   todo wangchen     暂时注销从D5000拿数据
//        SettingSystemDO importDataSendSwitch =
//            settingSystemService.findByFieldId(SystemConstant.IMPORTDATASHOW_SENDD5000);
//
//        //todo wangche 这里月份是写死的？
//        List<AcLoadReportD5000DO> acLoadReportD5000DOS = acLoadReportD5000Service
//            .getAcLoadReportD5000DO(cityId, caliberId, loadSolutionManageDO.getSolutionYear(), "04",
//                dateType.getId(),null);
//
//        //表明今年的基础负荷是有导入进去的，展示导入的。
//        if(CollectionUtils.isNotEmpty(acLoadReportD5000DOS) && importDataSendSwitch.getValue().equals("1")){
//            AcLoadReportD5000DO acLoadReportD5000DO = acLoadReportD5000DOS.get(0);
//            List<BigDecimal> bigDecimals = BasePeriodUtils
//                .toList(acLoadReportD5000DO, Constants.LOAD_CURVE_POINT_NUM,
//                    Constants.WEATHER_CURVE_START_WITH_ZERO);
//            return new BaseLoadCurveRespDTO(solutionId,bigDecimals);
//        }


        Map<String, List<BigDecimal>> basicLoadMap = calcBasicLoad(loadSolutionManageDO,null);


        // 根据基础春秋季节负荷的增长率计算目标季节的基础负荷
        if (loadSolutionManageDO.getSpringTargetRangeStartDate().after(loadSolutionManageDO.getFallTargetRangeStartDate())) {
            //如果春季的开始日期比秋季的开始日期早（说明跨年了）,说明这个方案是给夏秋季节设置的
            Date nextFallEndDate = DateUtils.addYears(loadSolutionManageDO.getFallTargetRangeEndDate(), 1);
            if (loadSolutionManageDO.getSeason().equals(String.valueOf(SeasonEnum.Autumn.getSeason())) && new Date().after(nextFallEndDate)) {
                //如果秋季的方案，且当前日期已经过了秋季目标日期范围，那么直接用当年秋季算出来的基础负荷，不再考虑长率
                LoadSolutionManageDO newSolution = new LoadSolutionManageDO();
                BeanUtils.copyProperties(loadSolutionManageDO, newSolution);
                newSolution.setFallTargetRangeStartDate(DateUtils.addYears(loadSolutionManageDO.getFallTargetRangeStartDate(),1));
                newSolution.setFallTargetRangeEndDate(DateUtils.addYears(loadSolutionManageDO.getFallTargetRangeEndDate(),1));
                Map<String, List<BigDecimal>> newBasicLoadMap = calcBasicLoad(newSolution, AUTUMN);
                return new BaseLoadCurveRespDTO(solutionId, newBasicLoadMap.get(AUTUMN));
            } else {
                BigDecimal aveIncreaseRate = calcAveIncreaseRate(basicLoadMap.get(AUTUMN),basicLoadMap.get(SPRING));
                if (loadSolutionManageDO.getSeason().equals(String.valueOf(SeasonEnum.Summer.getSeason()))) {
                    // 如果是夏季或者冬季，说明增长率只跨了一个季度，需要除以2
                    aveIncreaseRate = aveIncreaseRate.divide(new BigDecimal(2));
                }
                List<BigDecimal> reslut = calcIncreaseLoad(basicLoadMap.get(SPRING), aveIncreaseRate);
                return new BaseLoadCurveRespDTO(solutionId,reslut);

            }
        } else {
            //没有跨年，说明这个方案是给冬春季节设置的
            Date nextSpringEndDate = DateUtils.addYears(loadSolutionManageDO.getSpringTargetRangeEndDate(), 1);
            if (loadSolutionManageDO.getSeason().equals(String.valueOf(SeasonEnum.Spring.getSeason())) && new Date().after(nextSpringEndDate)) {
                //如果春季的方案，且当前日期已经过了春季目标日期范围，那么直接用当年春季算出来的基础负荷，不再考虑长率
                LoadSolutionManageDO newSolution = new LoadSolutionManageDO();
                BeanUtils.copyProperties(loadSolutionManageDO, newSolution);
                newSolution.setSpringTargetRangeStartDate(DateUtils.addYears(loadSolutionManageDO.getSpringTargetRangeStartDate(),1));
                newSolution.setSpringTargetRangeEndDate(DateUtils.addYears(loadSolutionManageDO.getSpringTargetRangeEndDate(),1));
                Map<String, List<BigDecimal>> newBasicLoadMap = calcBasicLoad(newSolution, SPRING);
                return new BaseLoadCurveRespDTO(solutionId, newBasicLoadMap.get(SPRING));
            } else {
                BigDecimal aveIncreaseRate = calcAveIncreaseRate(basicLoadMap.get(SPRING), basicLoadMap.get(AUTUMN));
                if (loadSolutionManageDO.getSeason().equals(String.valueOf(SeasonEnum.Winter.getSeason()))) {
                    // 如果是夏季或者冬季，说明增长率只跨了一个季度，需要除以2
                    aveIncreaseRate = aveIncreaseRate.divide(new BigDecimal(2));
                }
                List<BigDecimal> reslut = calcIncreaseLoad(basicLoadMap.get(AUTUMN), aveIncreaseRate);
                return new BaseLoadCurveRespDTO(solutionId,reslut);
            }
        }
    }

    @Override
    public BaseLoadCurveRespDTO queryBaseNewLoadCurve(String solutionId) throws Exception {
        BaseLoadCurveRespDTO baseLoadCurveRespDTO = new BaseLoadCurveRespDTO();
        baseLoadCurveRespDTO.setSolutionId(solutionId);
        LoadSolutionManageDO loadSolutionManageDO = loadSolutionManageEntityService.findOneByQueryWrapper(
                JpaWrappers.<LoadSolutionManageDO>lambdaQuery().eq(LoadSolutionManageDO::getId, solutionId));
        List<LoadSolutionManageDetailDO> loadSolutionManageDetailByManageId = loadSolutionManageDetailService.getLoadSolutionManageDetailByManageId(loadSolutionManageDO.getId());
        Map<String, List<CityDO>> collect1 = cityService.findAllCitys().stream().collect(Collectors.groupingBy(t -> t.getCity()));
        if (loadSolutionManageDO.getWeatherType() == 1) {
            // 福建气象
            // 1九个地市所有日期基础负荷负荷求平均累加
            // 2全省-地市累加负荷累加求平均
            // 1+2
            List<List<BigDecimal>> result = new ArrayList<>();
            List<BigDecimal> cityAddBigDecimals = new ArrayList<>();
            List<List<BigDecimal>> allBigDecimals = new ArrayList<>();
            List<String> excludeDate = new ArrayList<>();
            if (!CollectionUtils.isEmpty(loadSolutionManageDetailByManageId)) {
                loadSolutionManageDetailByManageId.forEach(
                        item -> {
                            // todo 单个地市满足日期的基础负荷
                            try {
                                CityDO cityDO = collect1.get(item.getCityName()).get(0);
                                AcLoadCityAddDataDTO acLoadCityAddDataDTO = calcNewBasicLoad(item, loadSolutionManageDO, cityDO.getId(),
                                        cityDO.getId());
                                excludeDate.addAll(acLoadCityAddDataDTO.getDateStrList());
                                allBigDecimals.add(acLoadCityAddDataDTO.getData());
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                        }
                );
                cityAddBigDecimals = allBigDecimals.stream().map(x -> x).reduce((k1, k2) -> BigDecimalFunctions.listAdd(k1, k2)).get();
            }
            if (CollectionUtils.isNotEmpty(excludeDate)) {
                // 全省负荷-地市累加 求平均
                List<String> collect = excludeDate.stream().distinct().collect(Collectors.toList());
                List<Date> dateList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(collect)) {
                    for (String s : collect) {
                        dateList.add(DateUtil.getDateFromString(s, "yyyyMMdd"));
                    }
                }
                List<BigDecimal> networkLossByDateList = loadCityHisService.getNetworkLossByDateList(dateList);
                if (CollectionUtils.isNotEmpty(networkLossByDateList) && CollectionUtils.isNotEmpty(cityAddBigDecimals)) {
                    result.add(networkLossByDateList);
                    result.add(cityAddBigDecimals);
                    baseLoadCurveRespDTO.setDataValue(result.stream().map(x -> x).reduce((k1, k2) -> BigDecimalFunctions.listAdd(k1, k2)).get());
                }
            }
            return baseLoadCurveRespDTO;
        } else {
            // 福州地市气象
            AcLoadCityAddDataDTO acLoadCityAddDataDTO = calcNewBasicLoad(loadSolutionManageDetailByManageId.get(0),
                    loadSolutionManageDO, "1", "2");
            baseLoadCurveRespDTO.setDataValue(acLoadCityAddDataDTO.getData());
            return baseLoadCurveRespDTO;
        }
    }

    @Override
    public List<BigDecimal> queryBaseLoadCurveData(String solutionId, Date date) throws Exception {
        List<BigDecimal> result = new ArrayList<>();
        // 1.拿到样本统计
        SampleStatisticsRespDTO sampleStatisticsRespDTO = querySampleNewStatistics(solutionId);
        if (sampleStatisticsRespDTO != null) {
            List<SampleRespDTO> sampleList = sampleStatisticsRespDTO.getSampleList();
            if (CollectionUtils.isNotEmpty(sampleList)) {
                List<Date> collect = sampleList.stream().map(SampleRespDTO::getDate).distinct().collect(Collectors.toList());
                // 2.计算厂用电+网损
                List<BigDecimal> networkLossByDateList = loadCityHisService.getNetworkLossByDateList(collect);
                // 3.计算当前日期厂用电+网损
                List<BigDecimal> networkLossByDate = loadCityHisService.getNetworkLossByDateList(Arrays.asList(date));
                if (CollectionUtils.isNotEmpty(networkLossByDate) && CollectionUtils.isNotEmpty(networkLossByDateList)) {
                    int minSize = Math.min(networkLossByDate.size(), networkLossByDateList.size());
                    for (int i = 0; i < minSize; i++) {
                        BigDecimal dateValue = networkLossByDate.get(i);
                        BigDecimal dateListValue = networkLossByDateList.get(i);
                        if (dateValue != null && dateListValue != null) {
                            result.add(dateValue.subtract(dateListValue));
                        } else {
                            result.add(null);
                        }
                    }
                }
            }
        }
        return result;
    }

    private AcLoadCityAddDataDTO calcNewBasicLoad(LoadSolutionManageDetailDO loadSolutionManageDetailDO,
                                                  LoadSolutionManageDO loadSolutionManageDO, String loadCityId, String weatherCityId) throws Exception {
        AcLoadCityAddDataDTO acLoadCityAddDataDTO = new AcLoadCityAddDataDTO();
        String caliberId = loadSolutionManageDO.getCaliberId();
        String excludeDate = loadSolutionManageDetailDO.getExcludeDate();
        // 统计气象满足要求的日期
        loadSolutionManageDO.setTemperatureRangeEnd(loadSolutionManageDetailDO.getHighestTemperature());
        loadSolutionManageDO.setTemperatureRangeStart(loadSolutionManageDetailDO.getLowestTemperature());
        loadSolutionManageDO.setMaxRain(loadSolutionManageDetailDO.getRainfall());
        loadSolutionManageDO.setCityId(weatherCityId);
        List<String> excludeWeatherDate = excludeWeatherDate(loadSolutionManageDO);
        // 去除排除日期
        if (StringUtils.isNotBlank(excludeDate)) {
            List<String> excludeDateList = Arrays.asList(excludeDate.split(","));
            excludeWeatherDate.removeIf(item -> excludeDateList.contains(item));
        }
        List<LoadCityHisDO> loadCityHisSpringMeetWeather = getNewLoadMeetWeather(
                loadSolutionManageDO.getSpringTargetRangeStartDate(), loadSolutionManageDO.getSpringTargetRangeEndDate(), loadCityId, caliberId, excludeWeatherDate);
        List<LoadCityHisDO> loadCityHisFallMeetWeather = getNewLoadMeetWeather(
                loadSolutionManageDO.getFallTargetRangeStartDate(), loadSolutionManageDO.getFallTargetRangeEndDate(), loadCityId, caliberId, excludeWeatherDate);
        // 计算基础负荷
        loadCityHisSpringMeetWeather.addAll(loadCityHisFallMeetWeather);
        acLoadCityAddDataDTO.setData(calculateAvgValue(loadCityHisSpringMeetWeather));
        acLoadCityAddDataDTO.setDateStrList(excludeWeatherDate);
        return acLoadCityAddDataDTO;
    }

    private Map<String,List<BigDecimal>> calcBasicLoad(LoadSolutionManageDO loadSolutionManageDO, String season) throws Exception {
        String cityId = loadSolutionManageDO.getCityId();
        String caliberId = loadSolutionManageDO.getCaliberId();
        DateType2 dateType = loadSolutionManageDO.getDateType();

        // 统计气象满足要求的日期
        List<String> excludeWeatherDate = excludeWeatherDate(loadSolutionManageDO);

        Map<String,List<BigDecimal>> reslut = new HashMap<>();

        //这里做判断主要是提高计算效率
        if (SPRING.equals(season) || StringUtils.isBlank(season)) {
            //获取方案中春季满足条件的负荷
            List<LoadCityHisDO> loadCityHisSpringMeetWeather = getLoadMeetWeather(
                loadSolutionManageDO.getSpringTargetRangeStartDate(), loadSolutionManageDO.getSpringTargetRangeEndDate(), cityId, caliberId, excludeWeatherDate);
            //排除节假日不满足要求的数据
            List<LoadCityHisDO> loadCityHisSpringMeetHoliday = excludeDate(loadCityHisSpringMeetWeather, dateType,
                loadSolutionManageDO.getSolutionYear());
            // 春季的均值
            List<BigDecimal> avgValueSpring = calculateAvgValue(loadCityHisSpringMeetHoliday);
            reslut.put(SPRING, avgValueSpring);
        }

        if (AUTUMN.equals(season) || StringUtils.isBlank(season)) {
            // 查询秋季的负荷
            List<LoadCityHisDO> loadCityHisFallMeetWeather = getLoadMeetWeather(
                loadSolutionManageDO.getFallTargetRangeStartDate(), loadSolutionManageDO.getFallTargetRangeEndDate(), cityId, caliberId, excludeWeatherDate);
            //排除节假日不满足要求的数据
            List<LoadCityHisDO> loadCityHisFallMeetHoliday = excludeDate(loadCityHisFallMeetWeather, dateType,
                loadSolutionManageDO.getSolutionYear());
            // 秋季的均值
            List<BigDecimal> avgValueFall = calculateAvgValue(loadCityHisFallMeetHoliday);
            reslut.put(AUTUMN, avgValueFall);
        }
        return reslut;
    }

    private List<LoadCityHisDO> getNewLoadMeetWeather(Date startDate, Date endDate, String cityId,
                                                   String caliberId, List<String> excludeWeatherDate) throws Exception {
        List<LoadCityHisDO> loadCityHisResult = new ArrayList<>();
        List<LoadCityHisDO> loadCityHis = loadCityHisService.findLoadCityHisDOS(cityId, startDate, endDate, caliberId);
        if (CollectionUtils.isNotEmpty(loadCityHis)) {
            for (LoadCityHisDO loadCityHi : loadCityHis) {
                String yyyyMMdd = DateUtil.getDateToStrFORMAT(loadCityHi.getDate(), "yyyyMMdd");
                if (excludeWeatherDate.contains(yyyyMMdd)) {
                    loadCityHisResult.add(loadCityHi);
                }
            }
        }
        return loadCityHisResult;
    }

    private List<LoadCityHisDO> getLoadMeetWeather(Date startDate, Date endDate, String cityId,
        String caliberId, List<String> excludeWeatherDate) throws Exception {
        //根据方案中的日期范围，查询历史负荷信息
        List<LoadCityHisDO> loadCityHis = loadCityHisService.findLoadCityHisDOS(cityId,
            startDate, endDate, caliberId);
        //排除气象不满足要求的数据
        Iterator<LoadCityHisDO> iterator = loadCityHis.iterator();
        while (iterator.hasNext()) {
            LoadCityHisDO next = iterator.next();
            java.sql.Date date = next.getDate();
            String dateStr = DateUtils.date2String(new Date(date.getTime()), DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
            if (!excludeWeatherDate.contains(dateStr)) {
                iterator.remove();
            }
        }
        return loadCityHis;
    }

    private static List<BigDecimal> calcIncreaseLoad(List<BigDecimal> loadList, BigDecimal aveIncreaseRate) {
        List<BigDecimal> reslut = new ArrayList<>();
        for (BigDecimal load : loadList) {
            BigDecimal resultLoad = load.multiply(new BigDecimal(1).add(aveIncreaseRate)).setScale(4,BigDecimal.ROUND_UP);
            reslut.add(resultLoad);
        }
        return reslut;
    }

    /**
     * 计算平均listA到listB的平均增长率
     * @param listA
     * @param listB
     */
    private static BigDecimal calcAveIncreaseRate(List<BigDecimal> listA, List<BigDecimal> listB) {
        if (CollectionUtils.isEmpty(listA) || CollectionUtils.isEmpty(listB)) {
            return BigDecimal.ZERO;
        }
        List<BigDecimal> rateList = new ArrayList<>();
        for (int i = 0; i < listA.size(); i++) {
            BigDecimal a = listA.get(i);
            BigDecimal b = listB.get(i);
            if (a != null && b != null) {
                BigDecimal rate = b.subtract(a).divide(a, 4, BigDecimal.ROUND_UP);
                rateList.add(rate);
            }
        }
        if (CollectionUtils.isNotEmpty(rateList)) {
            BigDecimal sum = rateList.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal ave = sum.divide(BigDecimal.valueOf(rateList.size()),4,BigDecimal.ROUND_UP);
            return ave;
        } else {
            throw TsieExceptionUtils.newBusinessException("平均增长率计算异常");
        }
    }


    private List<String> excludeWeatherDate(LoadSolutionManageDO loadSolutionManageDO) throws Exception{
        String cityId = loadSolutionManageDO.getCityId();
        String weatherCityId = cityService.findWeatherCityId(cityId);
        String year1 = DateUtil.getDateToStrFORMAT(loadSolutionManageDO.getSpringTargetRangeStartDate(), "yyyy-MM-dd").split("-")[0];
        String year2 = DateUtil.getDateToStrFORMAT(loadSolutionManageDO.getFallTargetRangeStartDate(), "yyyy-MM-dd").split("-")[0];
        List<Date> holidays = holidayService.findHolidayByYear(year1);
        holidays.addAll(holidayService.findHolidayByYear(year2));
        List<Date> offDates = holidayService.findHolidayByYearAndOffDates(year1);
        offDates.addAll(holidayService.findHolidayByYearAndOffDates(year2));
        Set<Date> dateSet = new HashSet<>(holidays);
        if(loadSolutionManageDO.getCaliberId().equals("10") && loadSolutionManageDO.getCityId().equals("1")){
            weatherCityId = "1";
        }

        List<WeatherFeatureCityDayHisDO> springWeatherFeatureCityDayHisDOS = weatherFeatureCityDayHisService.findWeatherFeatureCityDayHisDO(Arrays.asList(weatherCityId),
                loadSolutionManageDO.getSpringTargetRangeStartDate(), loadSolutionManageDO.getSpringTargetRangeEndDate());
        List<WeatherFeatureCityDayHisDO>fallWeatherFeatureCityDayHisDOS = weatherFeatureCityDayHisService.findWeatherFeatureCityDayHisDO(Arrays.asList(weatherCityId),
                loadSolutionManageDO.getFallTargetRangeStartDate(), loadSolutionManageDO.getFallTargetRangeEndDate());
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOS = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(springWeatherFeatureCityDayHisDOS)) {
            weatherFeatureCityDayHisDOS.addAll(springWeatherFeatureCityDayHisDOS);
        }
        if(CollectionUtils.isNotEmpty(fallWeatherFeatureCityDayHisDOS)) {
            weatherFeatureCityDayHisDOS.addAll(fallWeatherFeatureCityDayHisDOS);
        }
        BigDecimal temperatureRangeStart = loadSolutionManageDO.getTemperatureRangeStart();
        BigDecimal temperatureRangeEnd = loadSolutionManageDO.getTemperatureRangeEnd();
        List<String> result = new ArrayList<>();
        for(WeatherFeatureCityDayHisDO weatherFeatureCityDayHisDO:weatherFeatureCityDayHisDOS){
            // 日期
            java.sql.Date date = weatherFeatureCityDayHisDO.getDate();
            // 日期类型为休息日时，排除掉工作日   日期类型为工作日时，排除掉周六日\
            DateType2 dateType = loadSolutionManageDO.getDateType();
            // 节假日
            if (DateType2.REST == dateType) {
                if (!dateSet.contains(date)) {
                    continue;
                }
            } else if (DateType2.SATURDAY == dateType) {
                // 周六且去除调休
                boolean weekendOrSaturday = DateUtil.isWeekendOrSaturday(date, 1);
                if (!weekendOrSaturday || offDates.contains(date)) {
                    continue;
                }
            } else if (DateType2.WEEKEND == dateType) {
                // 周日且去除调休
                boolean weekendOrSaturday = DateUtil.isWeekendOrSaturday(date, 2);
                if (!weekendOrSaturday || offDates.contains(date)) {
                    continue;
                }
            } else if (DateType2.WORKDAY == dateType) {
                // 去除节假日
                if (dateSet.contains(date)) {
                    continue;
                }
                // 去除周六周末（不含调休的）
                boolean weekendOrSaturday = DateUtil.isWeekendOrSaturday(date, 1);
                boolean weekendOrSaturday1 = DateUtil.isWeekendOrSaturday(date, 2);
                if (!offDates.contains(date)) {
                    if (weekendOrSaturday || weekendOrSaturday1) {
                        continue;
                    }
                }
            }
            //降水量
            BigDecimal rainfall = weatherFeatureCityDayHisDO.getRainfall();
            // 降水量条件限制
            if(rainfall != null && rainfall.compareTo(loadSolutionManageDO.getMaxRain())<=0){
                BigDecimal highestTemperature = weatherFeatureCityDayHisDO.getHighestTemperature();
                BigDecimal lowestTemperature = weatherFeatureCityDayHisDO.getLowestTemperature();
                // 实际气象最大温度要小于配置的最大温度，实际气象最小温度大于配置的最小温度
                if (highestTemperature != null && lowestTemperature != null && temperatureRangeEnd.compareTo(highestTemperature) >= 0
                        && temperatureRangeStart.compareTo(lowestTemperature) <= 0) {
                    result.add(DateUtils.date2String(new Date(date.getTime()), DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR));
                }
            }
        }
        return result;
    }

    /**
     * 计算城市实际负荷对应时刻的均值
     *
     * @param loadCityHisDOList 城市实际负荷
     * @return 城市实际负荷对应时刻的均值
     */
    private List<BigDecimal> calculateAvgValue(List<LoadCityHisDO> loadCityHisDOList) {
        List<List<BigDecimal>> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(loadCityHisDOList)) {
            for (LoadCityHisDO loadCityHisDO : loadCityHisDOList) {
                List<BigDecimal> bigDecimals = BasePeriodUtils.toList(loadCityHisDO, Constants.LOAD_CURVE_POINT_NUM,
                    Constants.LOAD_CURVE_START_WITH_ZERO);
                list.add(bigDecimals);
            }
        }
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return ColumnUtil.avgList(list);
    }

    /**
     * 根据方案管理中的日期类型排除掉不符合条件的日期
     *
     * @param loadCityHisDOS 城市历史负荷信息
     * @param dateType 方案日期类型
     * @param year 要剔除的节假日的年份
     * @return 排除掉不符合条件的日期的数据
     */
    private List<LoadCityHisDO> excludeDate(List<LoadCityHisDO> loadCityHisDOS, DateType2 dateType, String year)
        throws Exception {

        // 当前方案年份的所有节假日（当前方案可能包含去年的节假日）
        List<Date> holidays = holidayService.findHolidayByYear(year);
        holidays.addAll(holidayService.findHolidayByYear(String.valueOf(Integer.valueOf(year) - 1)));
        Set<Date> dateSet = new HashSet<>(holidays);

        List<LoadCityHisDO> result = new ArrayList<>();
        // 最终需要按日期类型排除掉对应的日期
        if (CollectionUtils.isNotEmpty(loadCityHisDOS)) {
            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS) {
                java.sql.Date date = loadCityHisDO.getDate();
                if (dateSet.contains(date)) {
                    continue;
                }
                // 日期类型为休息日时，排除掉工作日   日期类型为工作日时，排除掉周六日
                if(DateType2.SATURDAY == dateType){
                    boolean weekend = DateUtil.isWeekend(date);
                    if(weekend){
                        result.add(loadCityHisDO);
                    }
                }else if(DateType2.WORKDAY == dateType){
                    boolean weekend = DateUtil.isWeekend(date);
                    if(!weekend){
                        result.add(loadCityHisDO);
                    }
                }
            }
        }
        return result;
    }

    @Override
    public SampleStatisticsRespDTO querySampleStatistics(String solutionId) throws Exception {
        SampleStatisticsRespDTO result = new SampleStatisticsRespDTO();
        result.setSolutionId(solutionId);
        /*
        实现思路：
        1.使用方案id查询方案信息，获取到春秋两季的时间区间，然后根据时间区间获取气象信息
        2.判断温度在方案温度范围取值内，则将温度和降雨量一起获取
        3.将温度区间分为步长为1℃的值进行统计占比
         */
        LoadSolutionManageDO loadSolutionManageDO = loadSolutionManageEntityService.findOneByQueryWrapper(
            JpaWrappers.<LoadSolutionManageDO>lambdaQuery().eq(LoadSolutionManageDO::getId, solutionId));
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOS = this.getWeatherFeatureCityDayHisDO(
            loadSolutionManageDO);
        if (CollectionUtils.isNotEmpty(weatherFeatureCityDayHisDOS)) {
            // 当前方案年份的所有节假日
            List<Date> holidays = holidayService.findHolidayByYear(loadSolutionManageDO.getSolutionYear());
            Set<Date> dateSet = new HashSet<>(holidays);
            // 温度范围低
            BigDecimal temperatureRangeStart = loadSolutionManageDO.getTemperatureRangeStart();
            // 温度范围高
            BigDecimal temperatureRangeEnd = loadSolutionManageDO.getTemperatureRangeEnd();


            List<SampleRespDTO> resultSample = new ArrayList();
            //遍历气象特性，判断温度是否在范围之内
            for (WeatherFeatureCityDayHisDO weatherFeatureCityDayHisDO : weatherFeatureCityDayHisDOS) {
                // 日期
                java.sql.Date date = weatherFeatureCityDayHisDO.getDate();
                if (dateSet.contains(date)) {
                    continue;
                }

                // 日期类型为休息日时，排除掉工作日   日期类型为工作日时，排除掉周六日\
                DateType2 dateType = loadSolutionManageDO.getDateType();
                if(DateType2.SATURDAY == dateType){
                    boolean weekend = DateUtil.isWeekend(date);
                    if(!weekend){
                       continue;
                    }
                }else if(DateType2.WORKDAY == dateType){
                    boolean weekend = DateUtil.isWeekend(date);
                    if(weekend){
                        continue;
                    }
                }

                //降水量
                BigDecimal rainfall = weatherFeatureCityDayHisDO.getRainfall();
                // 降水量条件限制
                boolean rainfallBoolean = conditionRainfall(loadSolutionManageDO, rainfall);
                TemperatureIndex temperatureIndex = loadSolutionManageDO.getTemperatureIndex();
                BigDecimal highestTemperature = weatherFeatureCityDayHisDO.getHighestTemperature();
                BigDecimal lowestTemperature = weatherFeatureCityDayHisDO.getLowestTemperature();
                if (highestTemperature != null && lowestTemperature != null && temperatureRangeEnd.compareTo(highestTemperature) >= 0
                        && temperatureRangeStart.compareTo(lowestTemperature) <= 0 && rainfallBoolean) {
                    resultSample.add(new SampleRespDTO(date, highestTemperature, highestTemperature, lowestTemperature, "", rainfall));
                }
                /*if (TemperatureIndex.MAX_TEMPERATURE == temperatureIndex) {
                    //最高温
                    BigDecimal highestTemperature = weatherFeatureCityDayHisDO.getHighestTemperature();
                    if (highestTemperature != null && temperatureRangeStart.compareTo(highestTemperature) <= 0
                        && temperatureRangeEnd.compareTo(highestTemperature) >= 0 && rainfallBoolean) {
                        resultSample.add(new SampleRespDTO(date, highestTemperature, rainfall));
                    }
                } else if (TemperatureIndex.MIN_TEMPERATURE == temperatureIndex) {
                    //最低温
                    BigDecimal lowestTemperature = weatherFeatureCityDayHisDO.getLowestTemperature();
                    if (lowestTemperature != null && temperatureRangeStart.compareTo(lowestTemperature) <= 0
                        && temperatureRangeEnd.compareTo(lowestTemperature) >= 0 && rainfallBoolean) {
                        resultSample.add(new SampleRespDTO(date, lowestTemperature, rainfall));
                    }
                } else if (TemperatureIndex.AVG_TEMPERATURE == temperatureIndex) {
                    //平均温度
                    BigDecimal aveTemperature = weatherFeatureCityDayHisDO.getAveTemperature();
                    if (aveTemperature != null && temperatureRangeStart.compareTo(aveTemperature) <= 0
                        && temperatureRangeEnd.compareTo(aveTemperature) >= 0 && rainfallBoolean) {
                        resultSample.add(new SampleRespDTO(date, aveTemperature, rainfall));
                    }
                }*/
            }
            if (CollectionUtils.isNotEmpty(resultSample)) {
                result.setSampleList(resultSample);
                List<StatisticsRespDTO> statisticsRespDTO = this.getStatisticsRespDTO(resultSample,
                    loadSolutionManageDO);
                result.setDataValue(statisticsRespDTO);
            }
        }
        return result;
    }

    @Override
    public SampleStatisticsRespDTO querySampleNewStatistics(String solutionId) throws Exception {
        SampleStatisticsRespDTO sampleStatisticsRespDTO = new SampleStatisticsRespDTO();
        List<SampleRespDTO> list = new ArrayList<>();
        LoadSolutionManageDO loadSolutionManageDO = loadSolutionManageEntityService.findOneByQueryWrapper(
                JpaWrappers.<LoadSolutionManageDO>lambdaQuery().eq(LoadSolutionManageDO::getId, solutionId));
        String year1 = DateUtil.getDateToStrFORMAT(loadSolutionManageDO.getSpringTargetRangeStartDate(), "yyyy-MM-dd").split("-")[0];
        String year2 = DateUtil.getDateToStrFORMAT(loadSolutionManageDO.getFallTargetRangeStartDate(), "yyyy-MM-dd").split("-")[0];
        if (loadSolutionManageDO == null) {
            return null;
        }
        List<CityDO> allCitys = cityService.findAllCitys();
        Map<String, List<CityDO>> collect = allCitys.stream().collect(Collectors.groupingBy(t -> t.getCity()));
        List<String> collectCity = allCitys.stream().map(CityDO::getId).collect(Collectors.toList());
        List<Date> holidays = holidayService.findHolidayByYear(year1);
        holidays.addAll(holidayService.findHolidayByYear(year2));
        List<Date> offDates = holidayService.findHolidayByYearAndOffDates(year1);
        offDates.addAll(holidayService.findHolidayByYearAndOffDates(year2));
        List<LoadSolutionManageDetailDO> loadSolutionManageDetailByManageId = loadSolutionManageDetailService.getLoadSolutionManageDetailByManageId(solutionId);
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOS = weatherFeatureCityDayHisService.findWeatherFeatureCityDayHisDO(collectCity, loadSolutionManageDO.getSpringTargetRangeStartDate(), loadSolutionManageDO.getSpringTargetRangeEndDate());
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDO1 = weatherFeatureCityDayHisService.findWeatherFeatureCityDayHisDO(collectCity, loadSolutionManageDO.getFallTargetRangeStartDate(),
                loadSolutionManageDO.getFallTargetRangeEndDate());
        weatherFeatureCityDayHisDOS.addAll(weatherFeatureCityDayHisDO1);
        Map<String, List<WeatherFeatureCityDayHisDO>> collectWeather = new HashMap<>();
        if (CollectionUtils.isNotEmpty(weatherFeatureCityDayHisDOS)) {
            collectWeather = weatherFeatureCityDayHisDOS.stream().collect(Collectors.groupingBy(t -> t.getCityId()));
        }
        for (LoadSolutionManageDetailDO loadConfigDataDTO : loadSolutionManageDetailByManageId) {
            List<SampleRespDTO> sampleList = new ArrayList<>();
            String cityId = collect.get(loadConfigDataDTO.getCityName()).get(0).getId();
            if (loadSolutionManageDO != null && loadSolutionManageDO.getWeatherType() == 2) {
                cityId = "2";
            }
            Set<Date> dateSet = new HashSet<>(holidays.stream().distinct().collect(Collectors.toList()));
            BigDecimal temperatureRangeStart = loadConfigDataDTO.getLowestTemperature();
            BigDecimal temperatureRangeEnd = loadConfigDataDTO.getHighestTemperature();
            List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOS1 = collectWeather.get(cityId);
            List<SampleRespDTO> resultSample = new ArrayList();
            if (CollectionUtils.isNotEmpty(weatherFeatureCityDayHisDOS1)) {
                for (WeatherFeatureCityDayHisDO weatherFeatureCityDayHisDO : weatherFeatureCityDayHisDOS1) {
                    // 日期
                    java.sql.Date date = weatherFeatureCityDayHisDO.getDate();
                    // 日期类型为休息日时，排除掉工作日   日期类型为工作日时，排除掉周六日\
                    DateType2 dateType = loadSolutionManageDO.getDateType();
                    // 节假日
                    if (DateType2.REST == dateType) {
                        if (!dateSet.contains(date)) {
                            continue;
                        }
                    } else if (DateType2.SATURDAY == dateType) {
                        // 周六且去除调休
                        boolean weekendOrSaturday = DateUtil.isWeekendOrSaturday(date, 1);
                        if (!weekendOrSaturday || offDates.contains(date)) {
                            continue;
                        }
                    } else if (DateType2.WEEKEND == dateType) {
                        // 周日且去除调休
                        boolean weekendOrSaturday = DateUtil.isWeekendOrSaturday(date, 2);
                        if (!weekendOrSaturday || offDates.contains(date)) {
                            continue;
                        }
                    } else if (DateType2.WORKDAY == dateType) {
                        // 去除节假日
                        if (dateSet.contains(date)) {
                            continue;
                        }
                        // 去除周六周末（不含调休的）
                        boolean weekendOrSaturday = DateUtil.isWeekendOrSaturday(date, 1);
                        boolean weekendOrSaturday1 = DateUtil.isWeekendOrSaturday(date, 2);
                        if (!offDates.contains(date)) {
                            if (weekendOrSaturday || weekendOrSaturday1) {
                                continue;
                            }
                        }
                    }
                    //降水量
                    BigDecimal rainfall = weatherFeatureCityDayHisDO.getRainfall();
                    // 降水量条件限制
                    if (rainfall != null && loadConfigDataDTO.getRainfall().compareTo(rainfall) >= 0) {
                        BigDecimal highestTemperature = weatherFeatureCityDayHisDO.getHighestTemperature();
                        BigDecimal lowestTemperature = weatherFeatureCityDayHisDO.getLowestTemperature();
                        // 实际气象最大温度要小于配置的最大温度，实际气象最小温度大于配置的最小温度
                        if (highestTemperature != null && lowestTemperature != null && temperatureRangeEnd.compareTo(highestTemperature) >= 0
                                && temperatureRangeStart.compareTo(lowestTemperature) <= 0) {
                            resultSample.add(new SampleRespDTO(date, highestTemperature, highestTemperature, lowestTemperature, loadSolutionManageDO.getWeatherType() == 2?"福建":loadConfigDataDTO.getCityName(), rainfall));
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(resultSample)) {
                    for (SampleRespDTO sampleRespDTO : resultSample) {
                        sampleList.add(sampleRespDTO);
                    }
                }
                // 去掉排除日期
                String excludeDate = loadConfigDataDTO.getExcludeDate();
                if (StringUtils.isNotBlank(excludeDate)) {
                    List<String> excludeDateList = Arrays.asList(excludeDate.split(","));
                    if (CollectionUtils.isNotEmpty(sampleList)) {
                        for (SampleRespDTO sampleRespDTO : sampleList) {
                            if (!excludeDateList.contains(DateUtils.date2String(sampleRespDTO.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR))) {
                                list.add(sampleRespDTO);
                            }
                        }
                    }
                } else {
                    list.addAll(sampleList);
                }
            }
        }
        sampleStatisticsRespDTO.setSampleList(list.stream().distinct().collect(Collectors.toList()));
        return sampleStatisticsRespDTO;
    }

    @Override
    public List<SampleDateDataDTO> queryWeatherSampleStatistics(List<SaveLoadConfigDataDTO> loadConfigDataDTOS) throws Exception {
        // 查询一下保存的方案id
        List<LoadSolutionManageDetailDO> loadSolutionManageDetailByManageIdS = loadSolutionManageDetailService.getLoadSolutionManageDetailByManageIdAndCityName(null, null);
        Map<String, List<LoadSolutionManageDetailDO>> collect = new HashMap<>();
        if (CollectionUtils.isNotEmpty(loadSolutionManageDetailByManageIdS)) {
            collect = loadSolutionManageDetailByManageIdS.stream().collect(Collectors.groupingBy(t -> t.getManageId() + "-" + t.getCityName()));
        }
        LoadSolutionManageDO loadSolutionManageDO = loadSolutionManageEntityService.findOneByQueryWrapper(
                JpaWrappers.<LoadSolutionManageDO>lambdaQuery()
                        .eq(loadConfigDataDTOS.get(0).getManageId() != null, LoadSolutionManageDO::getId, loadConfigDataDTOS.get(0).getManageId())
                        .eq(LoadSolutionManageDO::getSolutionYear, loadConfigDataDTOS.get(0).getSolutionYear())
                        .eq(LoadSolutionManageDO::getSeason, loadConfigDataDTOS.get(0).getSeason())
                        .eq(LoadSolutionManageDO::getDateType, loadConfigDataDTOS.get(0).getDateType())
                        .eq(LoadSolutionManageDO::getWeatherType, loadConfigDataDTOS.get(0).getWeatherType())
        );
        Integer weatherType = null;
        List<CityDO> allCitys = cityService.findAllCitys();
        Map<String, List<CityDO>> collect1 = allCitys.stream().collect(Collectors.groupingBy(t -> t.getCity()));
        List<String> collect2 = allCitys.stream().map(CityDO::getId).collect(Collectors.toList());
        Date springTargetRangeStartDate = null;
        Date springTargetRangeEndDate = null;
        Date fallTargetRangeStartDate = null;
        Date fallTargetRangeEndDate = null;
        if (loadSolutionManageDO == null) {
            springTargetRangeStartDate = loadConfigDataDTOS.get(0).getSpringTargetRangeStartDate();
            springTargetRangeEndDate = loadConfigDataDTOS.get(0).getSpringTargetRangeEndDate();
            fallTargetRangeStartDate = loadConfigDataDTOS.get(0).getFallTargetRangeStartDate();
            fallTargetRangeEndDate = loadConfigDataDTOS.get(0).getFallTargetRangeEndDate();
            weatherType = loadConfigDataDTOS.get(0).getWeatherType();
        } else {
            springTargetRangeStartDate = loadSolutionManageDO.getSpringTargetRangeStartDate();
            springTargetRangeEndDate = loadSolutionManageDO.getSpringTargetRangeEndDate();
            fallTargetRangeStartDate = loadSolutionManageDO.getFallTargetRangeStartDate();
            fallTargetRangeEndDate = loadSolutionManageDO.getFallTargetRangeEndDate();
            weatherType = loadSolutionManageDO.getWeatherType();
        }
        String year1 = DateUtil.getDateToStrFORMAT(springTargetRangeStartDate, "yyyy-MM-dd").split("-")[0];
        String year2 = DateUtil.getDateToStrFORMAT(fallTargetRangeStartDate, "yyyy-MM-dd").split("-")[0];
        List<Date> holidays = holidayService.findHolidayByYear(year1);
        holidays.addAll(holidayService.findHolidayByYear(year2));
        List<Date> offDates = holidayService.findHolidayByYearAndOffDates(year1);
        offDates.addAll(holidayService.findHolidayByYearAndOffDates(year2));
        Set<Date> dateSet = new HashSet<>(holidays.stream().distinct().collect(Collectors.toList()));
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOS = weatherFeatureCityDayHisService.findWeatherFeatureCityDayHisDO(collect2, springTargetRangeStartDate, springTargetRangeEndDate);
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDO1 = weatherFeatureCityDayHisService.findWeatherFeatureCityDayHisDO(collect2, fallTargetRangeStartDate, fallTargetRangeEndDate);
        weatherFeatureCityDayHisDOS.addAll(weatherFeatureCityDayHisDO1);
        Map<String, List<WeatherFeatureCityDayHisDO>> collect3 = new HashMap<>();
        if (CollectionUtils.isNotEmpty(weatherFeatureCityDayHisDOS)) {
            collect3 = weatherFeatureCityDayHisDOS.stream().collect(Collectors.groupingBy(t -> t.getCityId()));
        }
        List<SampleDateDataDTO> list = new ArrayList<>();
        for (SaveLoadConfigDataDTO loadConfigDataDTO : loadConfigDataDTOS) {
            List<SampleDateDataDTO> result = new ArrayList<>();
            List<LoadSolutionManageDetailDO> loadSolutionManageDetailDOS = new ArrayList<>();
            if (loadSolutionManageDO != null) {
                loadSolutionManageDetailDOS = collect.get(loadSolutionManageDO.getId() + "-" + loadConfigDataDTO.getCityName());
            }
            List<SampleRespDTO> resultSample = new ArrayList();
            BigDecimal temperatureRangeStart = loadConfigDataDTO.getLowestTemperature();
            BigDecimal temperatureRangeEnd = loadConfigDataDTO.getHighestTemperature();
            String cityId = collect1.get(loadConfigDataDTO.getCityName()).get(0).getId();
            if (loadSolutionManageDO != null && loadSolutionManageDO.getWeatherType() == 2) {
                cityId = "2";
            }
            List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOS1 = collect3.get(cityId);
            if (CollectionUtils.isNotEmpty(weatherFeatureCityDayHisDOS1)) {
                for (WeatherFeatureCityDayHisDO weatherFeatureCityDayHisDO : weatherFeatureCityDayHisDOS1) {
                    // 日期
                    java.sql.Date date = weatherFeatureCityDayHisDO.getDate();
                    // 日期类型为休息日时，排除掉工作日   日期类型为工作日时，排除掉周六日\
                    DateType2 dateType = loadConfigDataDTO.getDateType();
                    // 节假日
                    if (DateType2.REST == dateType) {
                        if (!dateSet.contains(date)) {
                            continue;
                        }
                    } else if (DateType2.SATURDAY == dateType) {
                        // 周六且去除调休
                        boolean weekendOrSaturday = DateUtil.isWeekendOrSaturday(date, 1);
                        if (!weekendOrSaturday || offDates.contains(date)) {
                            continue;
                        }
                    } else if (DateType2.WEEKEND == dateType) {
                        // 周日且去除调休
                        boolean weekendOrSaturday = DateUtil.isWeekendOrSaturday(date, 2);
                        if (!weekendOrSaturday || offDates.contains(date)) {
                            continue;
                        }
                    } else if (DateType2.WORKDAY == dateType) {
                        // 去除节假日
                        if (dateSet.contains(date)) {
                            continue;
                        }
                        // 去除周六周末（不含调休的）
                        boolean weekendOrSaturday = DateUtil.isWeekendOrSaturday(date, 1);
                        boolean weekendOrSaturday1 = DateUtil.isWeekendOrSaturday(date, 2);
                        if (!offDates.contains(date)) {
                            if (weekendOrSaturday || weekendOrSaturday1) {
                                continue;
                            }
                        }
                    }
                    //降水量
                    BigDecimal rainfall = weatherFeatureCityDayHisDO.getRainfall();
                    // 降水量条件限制
                    if(rainfall != null && rainfall.compareTo(loadConfigDataDTO.getRainfall())<=0){
                        BigDecimal highestTemperature = weatherFeatureCityDayHisDO.getHighestTemperature();
                        BigDecimal lowestTemperature = weatherFeatureCityDayHisDO.getLowestTemperature();
                        // 实际气象最大温度要小于配置的最大温度，实际气象最小温度大于配置的最小温度
                        if (highestTemperature != null && lowestTemperature != null && temperatureRangeEnd.compareTo(highestTemperature) >= 0
                                && temperatureRangeStart.compareTo(lowestTemperature) <= 0) {
                            resultSample.add(new SampleRespDTO(date, highestTemperature, highestTemperature, lowestTemperature, "", rainfall));
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(resultSample)) {
                    for (SampleRespDTO sampleRespDTO : resultSample) {
                        result.add(new SampleDateDataDTO(UUID.randomUUID().toString().toLowerCase().replace("-", ""),
                                loadSolutionManageDO == null? (UUID.randomUUID().toString().toLowerCase().replace("-", "")) : loadSolutionManageDO.getId(),
                                sampleRespDTO.getDate(),
                                weatherType == 2?"福建":loadConfigDataDTO.getCityName(),
                                sampleRespDTO.getHighestTemperature(),
                                sampleRespDTO.getLowestTemperature(),
                                sampleRespDTO.getRainfall(),
                                loadConfigDataDTOS.get(0).getWeatherType()));
                    }
                }
                // 去掉排除日期
                if (CollectionUtils.isNotEmpty(loadSolutionManageDetailDOS)) {
                    String excludeDate = loadSolutionManageDetailDOS.get(0).getExcludeDate();
                    if (StringUtils.isNotBlank(excludeDate)) {
                        List<String> excludeDateList = Arrays.asList(excludeDate.split(","));
                        if (CollectionUtils.isNotEmpty(result)) {
                            for (SampleDateDataDTO sampleRespDTO : result) {
                                if (!excludeDateList.contains(DateUtils.date2String(sampleRespDTO.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR))) {
                                    list.add(sampleRespDTO);
                                }
                            }
                        }
                    } else {
                        list.addAll(result);
                    }
                } else {
                    list.addAll(result);
                }
            }
        }
        return list;
    }

    @Override
    public List<SampleDateDataDTO> queryWeatherSampleNewStatistics(List<SaveLoadConfigDataDTO> loadConfigDataDTOS) throws Exception {
        List<LoadSolutionManageDetailDO> loadSolutionManageDetailByManageIdS = loadSolutionManageDetailService.getLoadSolutionManageDetailByManageIdAndCityName(null, null);
        Map<String, List<LoadSolutionManageDetailDO>> collect = new HashMap<>();
        if (CollectionUtils.isNotEmpty(loadSolutionManageDetailByManageIdS)) {
            collect = loadSolutionManageDetailByManageIdS.stream().collect(Collectors.groupingBy(t -> t.getManageId() + "-" + t.getCityName()));
        }
        LoadSolutionManageDO loadSolutionManageDO = loadSolutionManageEntityService.findOneByQueryWrapper(
                JpaWrappers.<LoadSolutionManageDO>lambdaQuery()
                        .eq(LoadSolutionManageDO::getSolutionYear, loadConfigDataDTOS.get(0).getSolutionYear())
                        .eq(LoadSolutionManageDO::getSeason, loadConfigDataDTOS.get(0).getSeason())
                        .eq(LoadSolutionManageDO::getDateType, loadConfigDataDTOS.get(0).getDateType())
                        .eq(LoadSolutionManageDO::getWeatherType, loadConfigDataDTOS.get(0).getWeatherType())
        );
        List<CityDO> allCitys = cityService.findAllCitys();
        Map<String, List<CityDO>> collect1 = allCitys.stream().collect(Collectors.groupingBy(t -> t.getCity()));
        List<String> collect2 = allCitys.stream().map(CityDO::getId).collect(Collectors.toList());
        String year1 = DateUtil.getDateToStrFORMAT(loadSolutionManageDO.getSpringTargetRangeStartDate(), "yyyy-MM-dd").split("-")[0];
        String year2 = DateUtil.getDateToStrFORMAT(loadSolutionManageDO.getFallTargetRangeStartDate(), "yyyy-MM-dd").split("-")[0];
        List<Date> holidays = holidayService.findHolidayByYear(year1);
        holidays.addAll(holidayService.findHolidayByYear(year2));
        List<Date> offDates = holidayService.findHolidayByYearAndOffDates(year1);
        offDates.addAll(holidayService.findHolidayByYearAndOffDates(year2));
        Set<Date> dateSet = new HashSet<>(holidays);
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOS = weatherFeatureCityDayHisService.findWeatherFeatureCityDayHisDO(collect2, loadSolutionManageDO.getSpringTargetRangeStartDate(), loadSolutionManageDO.getSpringTargetRangeEndDate());
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDO1 = weatherFeatureCityDayHisService.findWeatherFeatureCityDayHisDO(collect2, loadSolutionManageDO.getFallTargetRangeStartDate(),
                loadSolutionManageDO.getFallTargetRangeEndDate());
        weatherFeatureCityDayHisDOS.addAll(weatherFeatureCityDayHisDO1);
        Map<String, List<WeatherFeatureCityDayHisDO>> collect3 = new HashMap<>();
        if (CollectionUtils.isNotEmpty(weatherFeatureCityDayHisDOS)) {
            collect3 = weatherFeatureCityDayHisDOS.stream().collect(Collectors.groupingBy(t -> t.getCityId()));
        }
        List<SampleDateDataDTO> result = new ArrayList<>();
        for (SaveLoadConfigDataDTO loadConfigDataDTO : loadConfigDataDTOS) {
            List<LoadSolutionManageDetailDO> loadSolutionManageDetailDOS = collect.get(loadSolutionManageDO.getId() + "-" + loadConfigDataDTO.getCityName());
            List<SampleRespDTO> resultSample = new ArrayList();
            BigDecimal temperatureRangeStart = loadConfigDataDTO.getLowestTemperature();
            BigDecimal temperatureRangeEnd = loadConfigDataDTO.getHighestTemperature();
            String cityId = collect1.get(loadConfigDataDTO.getCityName()).get(0).getId();
            if (loadSolutionManageDO != null && loadSolutionManageDO.getWeatherType() == 2) {
                cityId = "2";
            }
            List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOS1 = collect3.get(cityId);
            if (CollectionUtils.isNotEmpty(weatherFeatureCityDayHisDOS1)) {
                for (WeatherFeatureCityDayHisDO weatherFeatureCityDayHisDO : weatherFeatureCityDayHisDOS1) {
                    // 日期
                    java.sql.Date date = weatherFeatureCityDayHisDO.getDate();
                    // 日期类型为休息日时，排除掉工作日   日期类型为工作日时，排除掉周六日\
                    DateType2 dateType = loadConfigDataDTO.getDateType();
                    if (DateType2.REST == dateType) {
                        if (!dateSet.contains(date)) {
                            continue;
                        }
                    } else if (DateType2.SATURDAY == dateType) {
                        // 周六且去除调休
                        boolean weekendOrSaturday = DateUtil.isWeekendOrSaturday(date, 1);
                        if (!weekendOrSaturday || offDates.contains(date)) {
                            continue;
                        }
                    } else if (DateType2.WEEKEND == dateType) {
                        // 周日且去除调休
                        boolean weekendOrSaturday = DateUtil.isWeekendOrSaturday(date, 2);
                        if (!weekendOrSaturday || offDates.contains(date)) {
                            continue;
                        }
                    } else if (DateType2.WORKDAY == dateType) {
                        // 去除节假日
                        if (dateSet.contains(date)) {
                            continue;
                        }
                        // 去除周六周末（不含调休的）
                        boolean weekendOrSaturday = DateUtil.isWeekendOrSaturday(date, 1);
                        boolean weekendOrSaturday1 = DateUtil.isWeekendOrSaturday(date, 2);
                        if (!offDates.contains(date)) {
                            if (weekendOrSaturday || weekendOrSaturday1) {
                                continue;
                            }
                        }
                    }
                    //降水量
                    BigDecimal rainfall = weatherFeatureCityDayHisDO.getRainfall();
                    // 降水量条件限制
                    if(rainfall != null && rainfall.compareTo(loadConfigDataDTO.getRainfall())<=0){
                        BigDecimal highestTemperature = weatherFeatureCityDayHisDO.getHighestTemperature();
                        BigDecimal lowestTemperature = weatherFeatureCityDayHisDO.getLowestTemperature();
                        // 实际气象最大温度要小于配置的最大温度，实际气象最小温度大于配置的最小温度
                        if (highestTemperature != null && lowestTemperature != null && temperatureRangeEnd.compareTo(highestTemperature) >= 0
                                && temperatureRangeStart.compareTo(lowestTemperature) <= 0) {
                            resultSample.add(new SampleRespDTO(date, highestTemperature, highestTemperature, lowestTemperature, "", rainfall));
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(resultSample)) {
                    for (SampleRespDTO sampleRespDTO : resultSample) {
                        result.add(new SampleDateDataDTO(UUID.randomUUID().toString().toLowerCase().replace("-", ""),
                                loadSolutionManageDO.getId(),sampleRespDTO.getDate(),loadConfigDataDTO.getCityName(),
                                sampleRespDTO.getHighestTemperature(),sampleRespDTO.getLowestTemperature(),sampleRespDTO.getRainfall(),
                                loadConfigDataDTOS.get(0).getWeatherType()));
                    }
                }
            }
        }
        return result;
    }

    @Override
    public List<SampleDateDataDTO> queryWeatherSampleReturnStatistics(List<SaveLoadConfigDataDTO> loadConfigDataDTOS) throws Exception {
        List<SampleDateDataDTO> result = new ArrayList<>();
        List<SampleDateDataDTO> sampleDateDataDTOS = queryWeatherSampleNewStatistics(loadConfigDataDTOS);
        List<LoadSolutionManageDetailDO> loadSolutionManageDetailByManageIdS = loadSolutionManageDetailService.getLoadSolutionManageDetailByManageIdAndCityName(null, null);
        Map<String, List<LoadSolutionManageDetailDO>> collect = new HashMap<>();
        if (CollectionUtils.isNotEmpty(loadSolutionManageDetailByManageIdS)) {
            collect = loadSolutionManageDetailByManageIdS.stream().collect(Collectors.groupingBy(t -> t.getManageId() + "-" + t.getCityName()));
        }
        Map<String, List<SampleDateDataDTO>> collect1 = new HashMap<>();
        if (CollectionUtils.isNotEmpty(sampleDateDataDTOS)) {
            collect1 = sampleDateDataDTOS.stream().collect(Collectors.groupingBy(t -> t.getId() + "-" + t.getCityName()));
        }
        for (Map.Entry<String, List<LoadSolutionManageDetailDO>> stringListEntry : collect.entrySet()) {
            String key = stringListEntry.getKey();
            List<SampleDateDataDTO> sampleDateDataDTOS1 = collect1.get(key);
            if (CollectionUtils.isNotEmpty(sampleDateDataDTOS1)) {
                LoadSolutionManageDetailDO loadSolutionManageDetailDO = stringListEntry.getValue().get(0);
                String excludeDate = loadSolutionManageDetailDO.getExcludeDate();
                for (SampleDateDataDTO sampleDateDataDTO : sampleDateDataDTOS1) {
                    if (StringUtils.isNotBlank(excludeDate)) {
                        List<String> excludeDateList = Arrays.asList(excludeDate.split(","));
                        String yyyyMMdd = DateUtil.getDateToStrFORMAT(sampleDateDataDTO.getDate(), "yyyyMMdd");
                        if (excludeDateList.contains(yyyyMMdd)) {
                            result.add(sampleDateDataDTO);
                        }
                    }
                }
            }



            /*List<LoadSolutionManageDetailDO> value = stringListEntry.getValue();
            LoadSolutionManageDetailDO loadSolutionManageDetailDO = value.get(0);
            for (SampleDateDataDTO sampleDateDataDTO : sampleDateDataDTOS) {
                //List<LoadSolutionManageDetailDO> loadSolutionManageDetailDOS = collect.get(sampleDateDataDTO.getId() + "-" + sampleDateDataDTO.getCityName());
                if ((sampleDateDataDTO.getId() + "-" + sampleDateDataDTO.getCityName()).equals(stringListEntry.getKey())) {
                    String excludeDate = loadSolutionManageDetailDO.getExcludeDate();

                    if (StringUtils.isNotBlank(excludeDate)) {
                        List<String> excludeDateList = Arrays.asList(excludeDate.split(","));
                        for (String s : excludeDateList) {

                        }

                        String yyyyMMdd = DateUtil.getDateToStrFORMAT(sampleDateDataDTO.getDate(), "yyyyMMdd");
                        if (excludeDateList.contains(yyyyMMdd)) {
                            result.add(sampleDateDataDTO);
                        }
                    }
                }
            }*/
        }
        return result;
    }

    @Override
    public List<BigDecimal> queryFcHisData(Date date, String cityName, String caliberId, Integer weatherType) {
        List<BigDecimal> result = new ArrayList<>();
        String cityId = cityService.findCityByName(cityName).getId();
        if (weatherType == 2) {
            // 福州气象
            cityId = Constants.PROVINCE_ID;
        }
        try {
            result = loadCityHisService.findLoadCityHisDO(date, cityId, caliberId);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return result;
    }

    private List<StatisticsRespDTO> getStatisticsRespDTO(List<SampleRespDTO> sampleList,
        LoadSolutionManageDO loadSolutionManageDO) {
        if (CollectionUtils.isEmpty(sampleList)) {
            return Collections.emptyList();
        }
        List<StatisticsRespDTO> statisticsRespDTOS = new ArrayList<>();

        // 步长为1的温度范围
        List<BigDecimal> listBetweenTemperatureRange = this.getListBetweenTemperatureRange(loadSolutionManageDO);
        // 存放某个温度出现的次数
        Map<BigDecimal, Integer> temperatureMap = new HashMap<>();
        int temperatureSize = 0;
        for (BigDecimal temperature : listBetweenTemperatureRange) {
            // 温度范围内出现的次数，用来计算百分比
            int num = 0;
            for (SampleRespDTO sampleRespDTO : sampleList) {
                // 温度 <= 当前温度 < 温度+1
                if (temperature.compareTo(sampleRespDTO.getTemperature()) <= 0
                    && (temperature.add(BigDecimal.ONE)).compareTo(sampleRespDTO.getTemperature()) > 0) {
                    num++;
                    temperatureSize++;
                }
            }
            if (num == 0) {
                continue;
            }
            temperatureMap.put(temperature, num);
        }
        if (temperatureSize != 0) {
            for (Map.Entry<BigDecimal, Integer> entry : temperatureMap.entrySet()) {
                BigDecimal percentage = new BigDecimal(entry.getValue()).divide(new BigDecimal(temperatureSize), 4,
                    RoundingMode.HALF_UP);
                StatisticsRespDTO statisticsRespDTO = new StatisticsRespDTO();
                statisticsRespDTO.setTemperature(entry.getKey());
                statisticsRespDTO.setPercentage(percentage);
                statisticsRespDTOS.add(statisticsRespDTO);
            }
        }
        return statisticsRespDTOS;
    }

    /**
     * 判断降水量是否满足条件
     *
     * @param loadSolutionManageDO 方案信息
     * @param rainfall 降水量
     * @return 降水量满足条件返回true，否则false
     */
    private boolean conditionRainfall(LoadSolutionManageDO loadSolutionManageDO, BigDecimal rainfall) {
        // 是否限制降水
        boolean isLimitRain = loadSolutionManageDO.getLimitRain();
        // 最大降水量
        BigDecimal maxRain = loadSolutionManageDO.getMaxRain();
        if (!isLimitRain) {
            return true;
        }
        // 如果超过最大降水量，则不满足条件
        if (maxRain.compareTo(rainfall) < 0) {
            return false;
        }
        return true;
    }

    private List<WeatherFeatureCityDayHisDO> getWeatherFeatureCityDayHisDO(LoadSolutionManageDO loadSolutionManageDO)
        throws Exception {
        String cityId = loadSolutionManageDO.getCityId();
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOS = weatherFeatureCityDayHisService.listWeatherFeatureCityDayHisDO(
            cityId, loadSolutionManageDO.getSpringTargetRangeStartDate(),
            loadSolutionManageDO.getSpringTargetRangeEndDate());
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOS1 = weatherFeatureCityDayHisService.listWeatherFeatureCityDayHisDO(
            cityId, loadSolutionManageDO.getFallTargetRangeStartDate(),
            loadSolutionManageDO.getFallTargetRangeEndDate());
        weatherFeatureCityDayHisDOS.addAll(weatherFeatureCityDayHisDOS1);
        return weatherFeatureCityDayHisDOS;
    }

    /**
     * 获取温度范围内步长为1的所有值
     *
     * @param loadSolutionManageDO 方案信息
     * @return 温度范围内步长为1的所有值
     */
    private List<BigDecimal> getListBetweenTemperatureRange(LoadSolutionManageDO loadSolutionManageDO) {
        BigDecimal temperatureRangeStart = loadSolutionManageDO.getTemperatureRangeStart();
        BigDecimal temperatureRangeEnd = loadSolutionManageDO.getTemperatureRangeEnd();
        List<BigDecimal> result = new ArrayList<>();
        result.add(temperatureRangeStart);
        while (true) {
            temperatureRangeStart = temperatureRangeStart.add(BigDecimal.ONE);
            if (temperatureRangeStart.compareTo(temperatureRangeEnd) >= 0) {
                break;
            }
            result.add(temperatureRangeStart);
        }
        result.add(temperatureRangeEnd);
        return result;
    }
}
