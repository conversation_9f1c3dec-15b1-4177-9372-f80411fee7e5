
package com.tsintergy.lf.serviceimpl.base.impl;

import static com.tsieframework.core.base.exception.TsieExceptionUtils.newBusinessException;

import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.base.dto.HolidayDTO;
import com.tsintergy.lf.serviceapi.base.base.dto.SimpleHolidayDTO;
import com.tsintergy.lf.serviceapi.base.base.dto.SkipHolidayDTO;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceimpl.base.dao.HolidayDAO;
import java.io.Serializable;
import java.util.*;

import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version $Id: HolidayServiceImpl.java, v 0.1 2018-01-31 09:46:04 tao Exp $$
 */
@Service("holidayService")
public class HolidayServiceImpl extends BaseServiceImpl implements HolidayService {


    @Autowired
    private HolidayDAO holidayDAO;

    @Override
    public HolidayDO doCreate(HolidayDO vo) throws Exception {
        try {
            return  holidayDAO.createAndFlush(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public void doRemoveHolidayVO(HolidayDO vo) throws Exception {
        try {
            holidayDAO.removeAndFlush(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public void doRemoveHolidayVOByPK(Serializable pk) throws Exception {
        try {
            holidayDAO.deleteByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public HolidayDO doUpdateHolidayVO(HolidayDO vo) throws Exception {
        try {
          return holidayDAO.update(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public HolidayDO findHolidayVOByPk(Serializable pk) throws Exception {
        try {
           return holidayDAO.findVOByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public Boolean isHoliday(Date date) throws Exception {
        List<Date> allHolidays = holidayDAO.getAllHolidays();
        return allHolidays.contains(date);
    }

    @Override
    public List<Date> getAllHolidays() throws Exception {
        return holidayDAO.getAllHolidays();
    }

    @Override
    public List<HolidayDO> getAllHolidayVOS() throws BusinessException {
        try {
            return holidayDAO.findVOList();
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public List<HolidayDTO> findHolidayVOsSortByYear() throws BusinessException {
        List<HolidayDTO> holidayDTOS = new ArrayList<HolidayDTO>(5);
        Map<String, List<SimpleHolidayDTO>> holidayMap = new HashMap<String, List<SimpleHolidayDTO>>(10);
        List<HolidayDO> holidayVOS = this.getAllHolidayVOS();
        for (HolidayDO holidayVO : holidayVOS) {
            SimpleHolidayDTO simpleHolidayDTO = new SimpleHolidayDTO();
            simpleHolidayDTO.setId(holidayVO.getId());
            simpleHolidayDTO.setName(holidayVO.getHoliday());
            if (null != holidayMap.get(DateUtil.getYearByDate(holidayVO.getDate()))) {
                holidayMap.get(DateUtil.getYearByDate(holidayVO.getDate())).add(simpleHolidayDTO);
            } else {
                List<SimpleHolidayDTO> holidays = new ArrayList<SimpleHolidayDTO>();
                holidays.add(simpleHolidayDTO);
                holidayMap.put(DateUtil.getYearByDate(holidayVO.getDate()), holidays);
            }
        }
        for (String year : holidayMap.keySet()) {
            HolidayDTO holidayDTO = new HolidayDTO();
            holidayDTO.setYear(year);
            holidayDTO.setHolidays(holidayMap.get(year));
            holidayDTOS.add(holidayDTO);
        }
        return holidayDTOS;
    }


    @Override
    public List<HolidayDO> findByYear(String year) throws Exception {
        return holidayDAO.findHolidayByYear(year);
    }

    @Override
    public void doImportHolidays(List<Map<Integer, Object>> list) throws Exception {
        this.holidayDAO.importHolidays(list);
    }

    @Override
    public List<Date> findHoliday(Date startDate, Date endDate) throws BusinessException {
        try {
            List<Date> dates = new ArrayList<Date>();
            List<HolidayDO> holidayVOS = null;
            if (startDate == null && endDate == null) {
                holidayVOS = this.getAllHolidayVOS();
            } else {
                holidayVOS = this.findHolidayVOS(startDate, endDate);
            }
            for (HolidayDO holidayVO : holidayVOS) {
                dates.addAll(DateUtil.getListBetweenDay(holidayVO.getStartDate(), holidayVO.getEndDate()));
            }
            return dates;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public List<HolidayDO> findHolidayVOS(Date startDate, Date endDate) throws BusinessException {
        try {
            return holidayDAO.findHoliday(startDate, endDate);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public List<Map<String, Date>> skipHoliday(Date startDate, Date endDate) throws BusinessException {
        List<HolidayDO> holidayVOS = this.getAllHolidayVOS();
        List<Map<String, Date>> forecastDays = new ArrayList<Map<String, Date>>(5);
        if (CollectionUtils.isNotEmpty(holidayVOS)) {
            for (HolidayDO holidayVO : holidayVOS) {
                int dateDistance = DateUtil.differentDaysByCalendar(startDate, endDate);
                if (holidayVO.getStartDate().after(startDate) && holidayVO.getStartDate().before(endDate)) {
                    Map<String, Date> forecastDay = new HashMap<String, Date>(2);
                    forecastDay.put("startDate", startDate);
                    forecastDay.put("endDate", DateUtil.getMoveDay(holidayVO.getStartDate(), -1));
                    forecastDays.add(forecastDay);
                    startDate = DateUtil.getMoveDay(holidayVO.getEndDate(), 1);
                    endDate = DateUtil.getMoveDay(startDate, dateDistance - DateUtil.differentDaysByCalendar(forecastDay.get("startDate"), forecastDay.get("endDate")) - 1);
                } else if (!holidayVO.getStartDate().after(startDate) && holidayVO.getEndDate().after(startDate)) {
                    startDate = DateUtil.getMoveDay(holidayVO.getEndDate(), 1);
                    endDate = DateUtil.getMoveDay(startDate, dateDistance);
                }
            }
        }
        Map<String, Date> forecastLastDay = new HashMap<String, Date>(2);
        forecastLastDay.put("startDate", startDate);
        forecastLastDay.put("endDate", endDate);
        forecastDays.add(forecastLastDay);
        return forecastDays;
    }

    @Override
    public SkipHolidayDTO findSkipHolidays(Date startDate, Date endDate) throws BusinessException {
        try {
            List<Date> holidays = this.getAllHolidays();
            SkipHolidayDTO skipHolidayDTO = new SkipHolidayDTO();
            for (; startDate.compareTo(endDate) < 1; startDate = DateUtil.getMoveDay(startDate, 1)) {
                if (holidays.contains(startDate)) {
                    skipHolidayDTO.addholiday(startDate);
                    endDate = DateUtil.getMoveDay(endDate, 1);
                } else {
                    skipHolidayDTO.addNormalDay(startDate);
                }
            }
            return skipHolidayDTO;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw newBusinessException("T706", e);
        }

    }

    @Override
    public void doRemoveHolidaysByIds(List<String> ids) throws BusinessException {
        try {
            if (ids != null) {
                for (Object id : ids) {
                    if (StringUtils.isNotBlank(id.toString())) {
                        this.doRemoveHolidayVOByPK(id.toString());
                    }
                }
            }
        } catch (Exception e) {
            throw newBusinessException("批量删除节假日出错了", e);
        }
    }


    @Override
    public List<HolidayDO> findByYearAndCode(String year, Integer code) throws Exception {
        return holidayDAO.findByCodeAndYear(year, code);
    }

    public List<HolidayDO> findByYearsAndName(String startYear, String endYear, String holidayName) throws Exception {
        List<HolidayDO> holidayByYearAndName = holidayDAO
                .findHolidayByYearAndName(startYear, endYear, holidayName);
        return holidayByYearAndName;

    }

    @Override
    public List<HolidayDO> getListHolidayByCode(String startYear, String endYear, Integer code) throws Exception {
        return holidayDAO.listHolidayByCode(startYear, endYear, code);
    }


    @Override
    public List<HolidayDO> getListHoliday(Date startDate, Date endDate) throws Exception {
        return holidayDAO.listHolidayList(startDate, endDate);
    }

    @Override
    public List<Date> findHolidayByYear(String year) throws Exception {
        // 当前方案年份的所有节假日
        List<HolidayDO> holidays = this.findByYear(year);
        List<Date> result = new ArrayList<>();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(holidays)) {
            for (HolidayDO holidayDO : holidays) {
                java.sql.Date startDate = holidayDO.getStartDate();
                java.sql.Date endDate = holidayDO.getEndDate();
                List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
                result.addAll(dateList);
            }
        }
        return result;
    }

    @Override
    public List<Date> findHolidayByYearAndOffDates(String year) throws Exception {
        return holidayDAO.findHolidayDataByYear(year);
    }

    @Override
    public DataPackage queryHolidayVO(DBQueryParam param) throws Exception {
        try {
            return holidayDAO.query(param);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw newBusinessException(e.getMessage(), e);
        }
    }
}
