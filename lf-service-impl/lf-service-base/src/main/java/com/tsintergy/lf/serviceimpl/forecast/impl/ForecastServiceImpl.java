package com.tsintergy.lf.serviceimpl.forecast.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.tsieframework.cloud.security.serviceapi.system.api.RedisService;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.core.constants.*;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DataUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.serviceapi.algorithm.api.CustomizationForecastService;
import com.tsintergy.lf.serviceapi.algorithm.dto.*;
import com.tsintergy.lf.serviceapi.base.base.api.AcBaseDateService;
import com.tsintergy.lf.serviceapi.base.base.api.AcBaseService;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.AcBaseDateDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.AcBaseInitDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.common.enumeration.ForecastCommentType;
import com.tsintergy.lf.serviceapi.base.common.enumeration.SimilarityCommentType;
import com.tsintergy.lf.serviceapi.base.evalucation.api.DeviationLoadCityFcService;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyAlgorithmValueDataDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.DeviationLoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.*;
import com.tsintergy.lf.serviceapi.base.forecast.dto.*;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcBatchDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.industry.api.IndustryCityLoadDayFcAlgoService;
import com.tsintergy.lf.serviceapi.base.industry.api.IndustryCityLoadDayHisClctNewService;
import com.tsintergy.lf.serviceapi.base.industry.api.IndustryCityLoadFeatureDayHisStatsService;
import com.tsintergy.lf.serviceapi.base.industry.pojo.IndustryCityLoadDayFcAlgoDO;
import com.tsintergy.lf.serviceapi.base.industry.pojo.IndustryCityLoadDayHisClctNewDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityFcShortService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadDecomposeCityWeekService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadDecomposeCityWeekStabilityService;
import com.tsintergy.lf.serviceapi.base.load.pojo.*;
import com.tsintergy.lf.serviceapi.base.system.api.SettingAlgorithmMultiStrategyService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.dto.SettingAlgorithmMultiStrategyDTO;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemData;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcLoadForecastService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherStationFcBasicWgService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationFcBasicWgDO;
import com.tsintergy.lf.serviceimpl.base.dao.HolidayDAO;
import com.tsintergy.lf.serviceimpl.common.util.AlgorithmUtil;
import com.tsintergy.lf.serviceimpl.datamanage.dao.InterfaceInfoDAO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.AccuracyLoadCityFcDAO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.DeviationLoadCityFcDAO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.StatisticsCityDayFcDAO;
import com.tsintergy.lf.serviceimpl.forecast.dao.ForecastInfoDAO;
import com.tsintergy.lf.serviceimpl.forecast.dao.ForecastLoadDAO;
import com.tsintergy.lf.serviceimpl.forecast.dao.LoadCityFcDAO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadCityHisDAO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadFeatureCityDayHisDAO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcDAO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherFeatureCityDayFcDAO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherFeatureCityDayHisDAO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.tsintergy.lf.core.constants.Constants.TRADE_ID_All;
import static com.tsintergy.lf.serviceimpl.industry.impl.IndustryServiceImpl.K;

/**
 * 预测服务 User:taojingui Date:18-2-10 Time:下午2:23
 */
@Service("forecastService")
@Slf4j
public class ForecastServiceImpl extends BaseServiceImpl implements ForecastService {


    @Autowired
    private LoadCityFcShortService loadCityFcShortService;

    @Autowired
    private LoadDecomposeCityWeekService loadDecomposeCityWeekService;

    @Autowired
    private LoadDecomposeCityWeekStabilityService loadDecomposeCityWeekStabilityService;

    @Autowired
    private CustomizationForecastService<PreProcessParam, GeneralResult> preProcessService;

    @Autowired
    private CustomizationForecastService<HolidayParam, GeneralResult> holidayForecastService;

    @Autowired
    private CustomizationForecastService<TyphoonParam, GeneralResult> typhoonForecast;

    @Autowired
    private CustomizationForecastService<AnalysisParam, AnalysisResult> analysisForecastService;

    @Autowired
    private CustomizationForecastService<ShortForecastParam, Result> shortForecastService;

    @Autowired
    private CustomizationForecastService<SensitivityParam, SensitivityResult> sensitivityForecastService;

    @Autowired
    private CustomizationForecastService<ForecastParam, GeneralResult> forecastable;

    @Autowired
    private LoadCityFcDAO loadCityFcDAO;

    @Autowired
    AccuracyLoadCityFcDAO accuracyLoadCityFcDAO;

    @Autowired
    StatisticsCityDayFcDAO statisticsCityDayFcDAO;

    @Autowired
    LoadFeatureCityDayHisDAO loadFeatureCityDayHisDAO;

    @Autowired
    LoadCityHisDAO loadCityHisDAO;

    @Autowired
    DeviationLoadCityFcDAO deviationLoadCityFcDAO;

    @Autowired
    SettingSystemService systemService;

    @Autowired
    AlgorithmService algorithmService;

    @Autowired
    InterfaceInfoDAO interfaceInfoDAO;

    @Autowired
    HolidayDAO holidayDAO;

    @Autowired
    WeatherFeatureCityDayHisDAO weatherFeatureCityDayHisDAO;

    @Autowired
    WeatherFeatureCityDayFcDAO weatherFeatureCityDayFcDAO;

    @Autowired
    ForecastInfoService forecastInfoService;

    @Autowired
    ForecastLoadDAO forecastLoadDAO;

    @Autowired
    CityService cityService;

    @Autowired
    DeviationLoadCityFcService deviationLoadCityFcService;

    @Autowired
    LoadCityFcService loadCityFcService;

    @Autowired
    ForecastInfoDAO forecastInfoDAO;

    @Autowired
    LoadCityFcBatchService loadCityFcBatchService;

    @Autowired
    RedisService redisService;

    @Autowired
    WeatherCityFcDAO weatherCityFcDAO;


    @Autowired
    WeatherCityFcLoadForecastService weatherCityFcLoadForecastService;

    @Autowired
    IndustryCityLoadDayHisClctNewService industryCityLoadDayHisClctNewService;

    @Autowired
    IndustryCityLoadFeatureDayHisStatsService industryCityLoadFeatureDayHisStatsService;

    @Autowired
    IndustryCityLoadDayFcAlgoService industryCityLoadDayFcAlgoService;

    @Autowired
    LoadCityHisService loadCityHisService;

    @Autowired
    WeatherStationFcBasicWgService weatherStationFcBasicWgService;

    @Autowired
    AcBaseService acBaseService;

    @Autowired
    AcBaseDateService acBaseDateService;

    @Autowired
    SettingAlgorithmMultiStrategyService settingAlgorithmMultiStrategyService;

    public static BigDecimal CONFIDENCE;

    public static BigDecimal RANGE;

    static {
        CONFIDENCE = BigDecimal.valueOf(Double.valueOf(0.95));
        RANGE = BigDecimal.valueOf(Double.valueOf(0.001));
    }

    private final Object sensitivityForecastLock = new Object();

    /**
     * @param cityId 城市ID
     * @param date 日期
     * @param caliberId 口径id
     */
    @Override
    public ForecastOverviewDTO getForecastOverview(String cityId, Date date, String caliberId) throws Exception {
        ForecastOverviewDTO forecastOverviewDTO = new ForecastOverviewDTO();
        // 获取预测统计结果
        StatisticsCityDayFcDO statisticsCityDayFcDO = statisticsCityDayFcDAO
            .getReportStatisticsCityDayFcDO(cityId, caliberId, date);
        if (statisticsCityDayFcDO == null) {
            return null;
        }
        // 预测准确率
        forecastOverviewDTO.setAccuracy(statisticsCityDayFcDO.getAccuracy());
        // 获取历史最大负荷
        LoadFeatureCityDayHisDO maxLoadDay = loadFeatureCityDayHisDAO
            .getLoadFeatureOfMaxLoadDay(cityId, caliberId);
        if (maxLoadDay != null) {
            forecastOverviewDTO.setForecastMax(maxLoadDay.getMaxLoad());
            forecastOverviewDTO.setRealMax(maxLoadDay.getMaxLoad());
        }
        // 负荷特性
        LoadFeatureCityDayHisDO loadFeatureCityDayHisDO = loadFeatureCityDayHisDAO
            .getLoadFeatureCityDayHisDO(cityId, date, caliberId);
        if (loadFeatureCityDayHisDO != null) {
            forecastOverviewDTO.setRealMaxLoad(loadFeatureCityDayHisDO.getMaxLoad());
        }
        // 预测负荷
        LoadCityFcDO loadCityFcDO = loadCityFcDAO.getReportLoadCityFcDO(cityId, caliberId, date);
        if (loadCityFcDO != null) {
            BigDecimal maxFcLoad = BasePeriodUtils.getMaxMinAvg(BasePeriodUtils
                    .toList(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM,
                        Constants.LOAD_CURVE_START_WITH_ZERO),
                4).get("max");
            forecastOverviewDTO.setForecastMaxLoad(maxFcLoad);
        }
        // 预测评价
        ForecastCommentaryDTO commentaryDTO = new ForecastCommentaryDTO();
        // 预测准确率上限
        BigDecimal accuracyMax = new BigDecimal(systemService.getValue("accuracy_max"));
        // 预测准确率下限
        BigDecimal accuracyMin = new BigDecimal(systemService.getValue("accuracy_min"));

        // 准确率评价
        if (forecastOverviewDTO != null && forecastOverviewDTO.getAccuracy() != null && accuracyMax != null
            && accuracyMin != null) {
            if (forecastOverviewDTO.getAccuracy().compareTo(accuracyMax) >= 0) {
                commentaryDTO.setCommentType(ForecastCommentType.LEVEL1.value());
            } else if (forecastOverviewDTO.getAccuracy().compareTo(accuracyMin) >= 0) {
                commentaryDTO.setCommentType(ForecastCommentType.LEVEL2.value());
            } else {
                commentaryDTO.setCommentType(ForecastCommentType.LEVEL3.value());
            }
        }

        // 曲线评价
        LoadCityHisDO loadCityHisDO = loadCityHisDAO.getLoadCityHisDOByOneDate(cityId, date, caliberId); // 实际曲线
        BigDecimal similarity = LoadCalUtil.calSimilarity(loadCityHisDO, loadCityFcDO); // 相似度
        BigDecimal similarityMax = new BigDecimal(systemService.getValue("similarity_max")); // 相似度高阈值
        BigDecimal similarityMin = new BigDecimal(systemService.getValue("similarity_min")); // 相似度地阈值

        if (similarity != null && similarityMax != null && similarityMin != null) {
            if (similarity.compareTo(similarityMax) >= 0) {
                commentaryDTO.setCurveType(SimilarityCommentType.LEVEL1.value());
            } else if (similarity.compareTo(similarityMin) >= 0) {
                commentaryDTO.setCurveType(SimilarityCommentType.LEVEL2.value());
            } else {
                commentaryDTO.setCurveType(SimilarityCommentType.LEVEL3.value());
            }
        }
        // 误差评价
        if (loadCityFcDO != null && loadFeatureCityDayHisDO != null) {
            String maxTimeFc = null; // 预测最大负荷发生时刻
            String minTimeFc = null; // 预测最小负荷发生时刻
            Map<String, BigDecimal> maxMixAvgFc = BasePeriodUtils.getMaxMinAvg(BasePeriodUtils
                    .toList(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM,
                        Constants.LOAD_CURVE_START_WITH_ZERO),
                4);
            Map<String, BigDecimal> loadFcMap = BasePeriodUtils
                .toMap(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM,
                    Constants.LOAD_CURVE_START_WITH_ZERO);

            for (String column : loadFcMap.keySet()) {
                BigDecimal load = loadFcMap.get(column);
                if (null != load) {
                    if (load.compareTo(maxMixAvgFc.get("max")) == 0) {
                        maxTimeFc = column.replace("t", "");
                    }
                    if (load.compareTo(maxMixAvgFc.get("min")) == 0) {
                        minTimeFc = column.replace("t", "");
                    }
                }
            }
            // 最大负荷预测偏高/偏低数值
            commentaryDTO
                .setMaxHigh(BigDecimalUtils.sub(maxMixAvgFc.get("max"), loadFeatureCityDayHisDO.getMaxLoad()));
            // 最小负荷预测偏高/偏低数值
            commentaryDTO
                .setMinHigh(BigDecimalUtils.sub(maxMixAvgFc.get("min"), loadFeatureCityDayHisDO.getMinLoad()));
            // 计算最大/小负荷提前/滞后分钟数
            commentaryDTO.setMaxEarlyTime(DateUtil.getMinutes(maxTimeFc, loadFeatureCityDayHisDO.getMaxTime()));
            commentaryDTO.setMinEarlyTime(DateUtil.getMinutes(minTimeFc, loadFeatureCityDayHisDO.getMinTime()));
        }
        forecastOverviewDTO.setCommentary(commentaryDTO);
        return forecastOverviewDTO;

    }

    @Override
    public List<BigDecimal> smoothLine(List<BigDecimal> singleLoadCityVOList) throws Exception {
        return AlgorithmUtil.smoothLine(singleLoadCityVOList, 5);
    }

    @Override
    public List<BigDecimal> smoothLineByAssess(List<BigDecimal> originLoad) throws Exception {
        int[] originalAssessIndex = getOriginalAssessIndex(originLoad);
        return AlgorithmUtil.smoothLineByAssess(originLoad, originalAssessIndex);
    }

    @Override
    public List<BigDecimal> recorrectLoad(List<BigDecimal> originLoad, BigDecimal distMax, BigDecimal distMin) throws BusinessException {
        return AlgorithmUtil.recorrectLoad(originLoad, distMax, distMin);
    }

    @SneakyThrows
    private int[] getOriginalAssessIndex(List<BigDecimal> originLoad) throws BusinessException {
        // 参数校验
        if (originLoad == null || originLoad.size() != 96) {
            throw TsieExceptionUtils.newBusinessException("原始负荷数据必须包含96个点");
        }

        // 1. 划分时段并归类负荷点
        List<BigDecimal> earlyPeakPoints = new ArrayList<>();
        List<BigDecimal> noonPeakPoints = new ArrayList<>();
        List<BigDecimal> eveningPeakPoints = new ArrayList<>();
        List<BigDecimal> waistLoadPoints = new ArrayList<>();
        List<BigDecimal> troughPoints = new ArrayList<>();

        List<String> earlyPeakList = getEarlyPeakList();
        List<String> noonPeakList = getNoonPeakList();
        List<String> eveningPeakList = getEveningPeakList();
        List<String> waistLoadList = getWaistLoadList();
        List<String> troughList = getTroughList();

        Map<String, BigDecimal> loadMap = ColumnUtil.listToMap(originLoad, Constants.LOAD_CURVE_START_WITH_ZERO);

        for (Map.Entry<String, BigDecimal> entry : loadMap.entrySet()) {
            String column = entry.getKey();
            BigDecimal load = entry.getValue();

            if (load != null) {
                String hourStr = column.substring(1);

                if (earlyPeakList != null && earlyPeakList.contains(hourStr)) {
                    earlyPeakPoints.add(load);
                }
                if (noonPeakList != null && noonPeakList.contains(hourStr)) {
                    noonPeakPoints.add(load);
                }
                if (eveningPeakList != null && eveningPeakList.contains(hourStr)) {
                    eveningPeakPoints.add(load);
                }
                if (waistLoadList != null && waistLoadList.contains(hourStr)) {
                    waistLoadPoints.add(load);
                }
                if (troughList != null && troughList.contains(hourStr)) {
                    troughPoints.add(load);
                }
            }
        }

        // 2. 计算原始典型负荷值
        BigDecimal originalEarlyPeak = BigDecimalUtils.getMax(earlyPeakPoints);
        BigDecimal originalNoonPeak = BigDecimalUtils.getMax(noonPeakPoints);
        BigDecimal originalEveningPeak = BigDecimalUtils.getMax(eveningPeakPoints);
        BigDecimal originalWaistLoad = BigDecimalUtils.getMin(waistLoadPoints);
        BigDecimal originalTrough = BigDecimalUtils.getMin(troughPoints);

        // 3. 找出五个关键点的位置索引
        int earlyPeakIndex = findIndex(originLoad, originalEarlyPeak);
        int noonPeakIndex = findIndex(originLoad, originalNoonPeak);
        int eveningPeakIndex = findIndex(originLoad, originalEveningPeak);
        int waistLoadIndex = findIndex(originLoad, originalWaistLoad);
        int troughIndex = findIndex(originLoad, originalTrough);

        // 4. 准备固定点数据
        int[] fixedIndices = {troughIndex, earlyPeakIndex, waistLoadIndex, noonPeakIndex, eveningPeakIndex};
        return fixedIndices;
    }


    /**
     * 在指定时段列表中查找负荷值的索引位置
     */
    @SneakyThrows
    private int findIndex(List<BigDecimal> loadList, BigDecimal targetValue) {
        // 直接遍历负荷列表查找目标值
        for (int i = 0; i < loadList.size(); i++) {
            if (loadList.get(i).equals(targetValue)) {
                return i;
            }
        }
        // 如果未找到，返回第一个元素的索引（通常不应该发生，需确保targetValue来自loadList）
        if (!loadList.isEmpty()) {
            return 0;
        }
        throw TsieExceptionUtils.newBusinessException("原始负荷数据为空");
    }


    /**
     * 高级正常日 ----》获取最近一次预测
     */
    @Override
    public List<ForecastNormalDTO> getForecastNormal(String cityId, String caliberId, Date startDate, Date endDate) throws BusinessException {
        try {
            List<LoadCityFcDO> loadCityFcDOS = new ArrayList<>();
            List<AlgorithmDO> allAlgorithmsNotCache = algorithmService.getAllAlgorithmsNotCache();
            List<AlgorithmDO> pageAlgorithms = allAlgorithmsNotCache.stream().filter(t -> AlgorithmConstants.NORMAL_ALGORITHM_TYPE.equals(t.getType())).collect(Collectors.toList());

            List<AlgorithmDO> viewAlgorithms = null;
            CityDO cityDO = cityService.findCityById(cityId);
            if (CityConstants.PROVINCE_TYPE.equals(cityDO.getType())) {
                viewAlgorithms = pageAlgorithms.stream().filter(t -> t.getProvinceView() == true).collect(Collectors.toList());
            } else if (CityConstants.CITY_TYPE.equals(cityDO.getType())) {
                viewAlgorithms = pageAlgorithms.stream().filter(t -> t.getCityView() == true)
                    .collect(Collectors.toList());
            }


            for (AlgorithmDO algorithmId : viewAlgorithms) {
                LoadCityFcDO loadCityFcDO = loadCityFcDAO
                    .getLoadCityFcDO(cityId, caliberId, algorithmId.getId(), startDate);
                if (loadCityFcDO != null) {
                    loadCityFcDOS.add(loadCityFcDO);
                }
            }
            if (loadCityFcDOS.size() < 1) {
                throw new BusinessException("T706", "");
            }
            List<ForecastNormalDTO> forecastNormalDTOS = new ArrayList<ForecastNormalDTO>();
            forecastNormalDTOS.addAll(this.forecastLoadVOsToForecastNormalDTOS(loadCityFcDOS));
            return forecastNormalDTOS;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    /**
     * 功能描述: <br> 高级正常日预测
     *
     * @param cityId 城市id
     * @param caliberId 口径id
     * @param forecastDays 预测日
     * @return:
     * @since: 1.0.0
     * @Author:wangfeng
     * @Date: 2018/9/13 10:38
     */
    @Override
    @Deprecated
    public List<ForecastNormalDTO> doAdvancedForecastNormal(String cityId, String caliberId, List<Date> forecastDays,
        List<AlgorithmEnum> algorithmEnums)
        throws Exception {
        List<LoadCityFcDO> forecastLoadVOS = doForecastNormal(cityId, caliberId, forecastDays, algorithmEnums);
        if (!CollectionUtils.isEmpty(forecastLoadVOS)) {
            List<ForecastNormalDTO> forecastNormalDTOS = new ArrayList<>();
            //重新组装数据形式
            forecastNormalDTOS.addAll(this.forecastLoadVOsToForecastNormalDTOS(forecastLoadVOS));
            return forecastNormalDTOS;
        }
        return null;
    }

    @Override
    @Deprecated
    public List<LoadCityFcDO> doForecastNormal(String cityId, String caliberId, List<Date> forecastDays,
        List<AlgorithmEnum> algorithmEnums)
        throws Exception {
        this.doForecastNormal(cityId, caliberId, forecastDays, algorithmEnums, "102");
        return null;
    }


    @Override
    public void doForecastNormal(String cityId, String caliberId, List<Date> forecastDays,
        List<AlgorithmEnum> algorithmEnums, String weatherCode)
        throws Exception {
        String city = cityService.findCityById(cityId).getCity();
        ForecastParam param = new ForecastParam(AlgorithmUtil.getUid(), cityId, city, caliberId, algorithmEnums,
            forecastDays);
        param.setForecastType(ForecastParam.FORECAST_CYCLIC_TYPE);
        param.setFcstDayWeatherType(
            weatherCode.equals(SystemConstant.WEATHER_CODE_MANUAL) ? SystemConstant.FCST_DAY_WEATHER_FC
                : SystemConstant.FCST_DAY_WEATHER_HIS);
        forecastable.forecast(param);
    }


    @Override
    public List<LoadCityFcDO> insertNormalData(String cityId, String caliberId,
        GeneralResult result)
        throws Exception {
        List<LoadCityFcDO> forecastLoadVOS = new ArrayList<>();
        String[] normalAlgorithmId = systemService.findByFieldId(SystemConstant.NORMAL_ALGORITHM).getValue()
            .split(Constants.SEPARATOR_PUNCTUATION);
        String[] autoReportData = systemService.findByFieldId(SystemConstant.AUTO_REPORT).getValue()
            .split(Constants.SEPARATOR_PUNCTUATION);
        //默认的系统算法id为相似日
        String systemAlgorithmId;
        //自动上报
        boolean autoReport;
        //正常日系统设置的算法id
        if (cityId.equals(CityConstants.PROVINCE_ID)) {
            systemAlgorithmId = normalAlgorithmId[0];
            autoReport = autoReportData[0].equals(ParamConstants.STRING_COMPOSITE_ON);
        } else {
            systemAlgorithmId = normalAlgorithmId[1];
            autoReport = autoReportData[1].equals(ParamConstants.STRING_COMPOSITE_ON);
        }

        List<Date> holidays = holidayDAO.getAllHolidays();
        for (LoadCityFcDO metaData : result.getResult()) {
            try {
                LoadCityFcDO loadCityFcDO = new LoadCityFcDO(new java.sql.Date(metaData.getDate().getTime()), cityId,
                    caliberId,
                    metaData.getAlgorithmId());
                ColumnUtil.copyPropertiesIgnoreNull(metaData, loadCityFcDO);
                forecastLoadVOS.add(loadCityFcDO);
                insertOrUpdateData(cityId, caliberId, metaData, holidays, systemAlgorithmId, autoReport);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                continue;
            }
        }
        return forecastLoadVOS;
    }

    /**
     * <AUTHOR>  每日算法 多算法手动预测 只预测单天
     */
    @Override
    public void doForecast(String cityId, String caliber, List<Date> forecastDates,
        AlgorithmEnum algorithmEnums) throws Exception {
        List<AlgorithmEnum> enums = new ArrayList<>();
        enums.add(algorithmEnums);
        String city = this.cityService.findCityById(cityId).getCity();
        ForecastParam param = new ForecastParam(AlgorithmUtil.getUid(), cityId, city, caliber, enums, forecastDates);
        param.setForecastType(ForecastParam.FORECAST_TYPE);
        forecastable.forecast(param);
    }


    private void insertOrUpdateData(String cityId, String caliberId, LoadCityFcDO metaData, List<Date> holidays,
        String systemAlgorithmId, boolean autoReport) throws Exception {
        String algorithmId = metaData.getAlgorithmId();
        List<LoadCityFcDO> loadCityFcVOS = loadCityFcDAO
            .getLoadCityFcDOs(cityId, caliberId, algorithmId, metaData.getDate(), metaData.getDate());
        //如果预测的日期是节假日，则需要将默认上报的是节假日算法，所以这里让所有正常日预测出的结果都不上报
        if (holidays.contains(metaData.getDate())) {
            systemAlgorithmId = AlgorithmEnum.HOLIDAY_FEST_SVM.getType();
        }
        LoadCityFcDO report = loadCityFcService.getReport(cityId, caliberId, metaData.getDate());
        if (loadCityFcVOS.size() < 1) {
            //如果数据库中没有此算法id的数据  则创建，
            LoadCityFcDO loadCityFcVO1 = new LoadCityFcDO(new java.sql.Date(metaData.getDate().getTime()), cityId,
                caliberId, algorithmId);
            //如果和系统推荐算法id一致
            if (algorithmId.equals(systemAlgorithmId)) {
                //如果没有上报记录
                if (autoReport && report == null) {
                    loadCityFcVO1.setReport(true);
                    loadCityFcVO1.setReportTime(new Timestamp(System.currentTimeMillis()));
                    loadCityFcVO1.setSucceed(true);
                } else {
                    loadCityFcVO1.setReport(false);
                    loadCityFcVO1.setSucceed(false);
                }
                loadCityFcVO1.setRecommend(true);
            } else {
                loadCityFcVO1.setRecommend(false);
                loadCityFcVO1.setReport(false);
                loadCityFcVO1.setSucceed(false);
            }
            loadCityFcVO1.setCreatetime(new Timestamp(System.currentTimeMillis()));
            ColumnUtil.copyPropertiesIgnoreNull(metaData, loadCityFcVO1);
            loadCityFcDAO.create(loadCityFcVO1);
            loadCityFcBatchService.doSave(loadCityFcVO1);
        } else {
            //如果数据库中有数据的话 直接做update处理
            LoadCityFcDO loadCityFcVO1 = loadCityFcVOS.get(0);
            ColumnUtil.copyPropertiesIgnoreNull(metaData, loadCityFcVO1);
            if (algorithmId.equals(systemAlgorithmId)) {
                //如果没有上报记录
                if (autoReport && report == null || report.getAlgorithmId().equals(algorithmId)) {
                    loadCityFcVO1.setReport(true);
                    loadCityFcVO1.setSucceed(true);
                    loadCityFcVO1.setReportTime(new Timestamp(System.currentTimeMillis()));
                } else if (autoReport && report.getAlgorithmId().equals(algorithmId)) {
                    loadCityFcVO1.setReport(true);
                    loadCityFcVO1.setSucceed(true);
                    loadCityFcVO1.setReportTime(new Timestamp(System.currentTimeMillis()));
                } else {
                    loadCityFcVO1.setReport(false);
                    loadCityFcVO1.setSucceed(false);
                }
                loadCityFcVO1.setRecommend(true);
            } else {
                loadCityFcVO1.setRecommend(false);
                loadCityFcVO1.setReport(false);
                loadCityFcVO1.setSucceed(false);
            }
            loadCityFcVO1.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            loadCityFcDAO.update(loadCityFcVO1);
            loadCityFcBatchService.doSave(loadCityFcVO1);
        }
    }

    /**
     * 页面调用节假日预测
     */
    @Override
    @Deprecated
    public List<ForecastNormalDTO> doForecastHoliday(String uid, String cityId, String caliberId, Date startDate,
        Date endDate)
        throws Exception {
        HolidayParam param = new HolidayParam(uid, startDate, endDate, cityId, caliberId,
            AlgorithmEnum.HOLIDAY_FEST_SVM, cityService.findCityById(cityId).getCity());
        holidayForecastService.forecast(param);
        //todo  现在没有手动预测页面 暂时屏蔽方法
        return null;
    }

    @Override
    public void insertOrUpdateHoliday(String cityId, String caliberId, GeneralResult result) throws Exception {
        List<LoadCityFcDO> metaDataList = result.getResult();
        for (LoadCityFcDO metaData : metaDataList) {
            //节假日预测入库
            String algorithmId = metaData.getAlgorithmId();
            List<LoadCityFcDO> loadCityFcDOList = loadCityFcDAO
                .getLoadCityFcDOs(cityId, caliberId, algorithmId, metaData.getDate(), metaData.getDate());
            if (loadCityFcDOList.size() < 1) {
                //如果数据库中没有 则创建，
                LoadCityFcDO loadCityFcDO1 = new LoadCityFcDO(new java.sql.Date(metaData.getDate().getTime()), cityId,
                    caliberId, algorithmId);
                loadCityFcDO1.setRecommend(false);
                loadCityFcDO1.setReport(true);
                loadCityFcDO1.setReportTime(new Timestamp(System.currentTimeMillis()));
                loadCityFcDO1.setSucceed(true);
                ColumnUtil.copyPropertiesIgnoreNull(metaData, loadCityFcDO1);
                loadCityFcDO1.setCreatetime(new Timestamp(System.currentTimeMillis()));
                loadCityFcDAO.create(loadCityFcDO1);
            } else {
                //如果数据库中有数据的话  做更新处理
                LoadCityFcDO loadCityFcDO2 = loadCityFcDOList.get(0);
                loadCityFcDO2.setCreatetime(new Timestamp(System.currentTimeMillis()));
                ColumnUtil.copyPropertiesIgnoreNull(metaData, loadCityFcDO2);
                loadCityFcDAO.update(loadCityFcDO2);
            }
        }
    }


    @Override
    public void doForecastTyphoon(String cityId, String caliberId, Date date, Integer type)
        throws Exception {
        Date loginDay = date;
        //台风预测 起始日是台风登陆日，结束日是登陆日后两天
        Date startDay = loginDay;
        Date baseDay;
        //历史台风
        if (loginDay.before(new Date())) {
            baseDay = DateUtils.addDays(loginDay, -1);
        } else {
            //未来的台风 基准日选取昨天（拥有完整96点数据的最近的一天）
            baseDay = DateUtils.addDays(new Date(), -1);
        }
        Date endDay = DateUtils.addDays(loginDay, 2);
        TyphoonParam param = new TyphoonParam(cityId, caliberId, loginDay, startDay, endDay, baseDay,
            AlgorithmEnum.TYPHOON_SVM);
        this.typhoonForecast.forecast(param);

    }


    /**
     * 转换返回数据格式  暂不维护 by wangh
     */
    @Deprecated
    private List<ForecastNormalDTO> forecastLoadVOsToForecastNormalDTOS(List<LoadCityFcDO> loadCityFcDOS) {
        List<ForecastNormalDTO> forecastNormalDTOS = new ArrayList<ForecastNormalDTO>();
        //1.先将源列表变成map,key为日期
        Map<Date, List<LoadCityFcDO>> listMap = new HashMap<Date, List<LoadCityFcDO>>();
        for (LoadCityFcDO loadCityFcDO : loadCityFcDOS) {
            if (null != listMap.get(loadCityFcDO.getDate())) {
                listMap.get(loadCityFcDO.getDate()).add(loadCityFcDO);
            } else {
                List<LoadCityFcDO> forecastLoadVOList = new ArrayList<LoadCityFcDO>();
                forecastLoadVOList.add(loadCityFcDO);
                listMap.put(loadCityFcDO.getDate(), forecastLoadVOList);
            }
        }
        //2.遍历map,将map转为List<ForecastNormalDTO>
        for (Date date : listMap.keySet()) {
            ForecastNormalDTO forecastNormalDTO = new ForecastNormalDTO();
            forecastNormalDTO.setTargetDay(date);
            List<ForecastResultDTO> forecastList = new ArrayList<ForecastResultDTO>();
            for (LoadCityFcDO loadCityFcDO : listMap.get(date)) {
                List<BigDecimal> loadList = BasePeriodUtils
                    .toList(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM,
                        Constants.LOAD_CURVE_START_WITH_ZERO);
                ForecastResultDTO forecastResultDTO = new ForecastResultDTO();
                forecastResultDTO.setAlgorithmId(loadCityFcDO.getAlgorithmId());
                forecastResultDTO.setAlgorithmName(
                    algorithmService.getAlgorithmDOById(loadCityFcDO.getAlgorithmId()).getAlgorithmCn());
                forecastResultDTO.setValue(loadList);
                forecastResultDTO.setMaxLoad(LoadCalUtil.max(loadList));
                forecastResultDTO.setMinLoad(LoadCalUtil.min(loadList));
                forecastResultDTO.setGradient(
                    LoadCalUtil.calGradient(forecastResultDTO.getMaxLoad(), forecastResultDTO.getMinLoad()));
                forecastResultDTO.setLoadGradient(LoadCalUtil
                    .calLoadGradient(BigDecimalUtils.avgList(loadList, Constants.SCALE, false),
                        forecastResultDTO.getMaxLoad()));
                forecastList.add(forecastResultDTO);
            }
            forecastNormalDTO.setForecastList(forecastList);
            forecastNormalDTOS.add(forecastNormalDTO);
        }
        return forecastNormalDTOS;
    }

    /**
     * 功能描述: <br> 调用数据修正算法
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    @Override
    public void doDataRepairAlgorithm(String cityId, String caliberId, Date startDate, Date endDate) throws Exception {
        try {
            PreProcessParam param = new PreProcessParam();
            param.setPreProcessBeginDay(startDate);
            param.setBaseDay(endDate);
            param.setCityId(cityId);
            param.setAlgorithmEnum(AlgorithmEnum.LOAD_PRE_PROCESS);
            param.setCaliberId(caliberId);
            preProcessService.forecast(param);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }


    /**
     * 调用稳定度分析算法 直接入库
     */
    @Override
    public LoadDecomposeCityWeekStabilityDO doPrecisionAnalysize(String caliberId, String cityId, Date date, int week,
        String userId)
        throws Exception {
        log.error("caliberId=" + caliberId + ",cityId=" + cityId + "date=" + DateUtils
            .date2String(date, DateFormatType.DATE_FORMAT_STR) + "week" + week);
        AnalysisParam param = new AnalysisParam(caliberId, cityId, date, week);
        param.setAlgorithmEnum(AlgorithmEnum.STABILITY);
        param.setUserId(userId);
        analysisForecastService.forecast(param);
        AnalysisResult bean = (AnalysisResult) redisService
            .redisGet(userId + Constants.SEPARATOR_BROKEN_LINE + CacheConstants.CACHE_ANALYSISRESULT_KEY,
                AnalysisResult.class);

        if (bean == null) {
            log.error("稳定度分析结果为空。。。。");
            return null;
        }
        //周曲线数据
        List<LoadDecomposeCityWeekDO> loadDecomposeCityWeekVOS = bean.getLoadDecomposeCityWeekDO();
        if (loadDecomposeCityWeekVOS == null || loadDecomposeCityWeekVOS.size() < 1) {
            log.error("稳定度周曲线数据为空。。。。。。。。。");
        }
        //稳定度上下限
        LoadDecomposeCityWeekStabilityDO loadDecomposeCityWeekStabilityVO = bean.getVo();

        if (loadDecomposeCityWeekStabilityVO != null) {
            loadDecomposeCityWeekStabilityService.doCreate(loadDecomposeCityWeekStabilityVO);
        }

        for (LoadDecomposeCityWeekDO vo : loadDecomposeCityWeekVOS) {
            List<LoadDecomposeCityWeekDO> vos = loadDecomposeCityWeekService.findLoadDecomposeCityWeekDO(vo);
            if (vos.size() < 1) {
                vo.setCreatetime(new Timestamp(System.currentTimeMillis()));
                loadDecomposeCityWeekService.doCreate(vo);
            } else {
                LoadDecomposeCityWeekDO vo2 = vos.get(0);
                BasePeriodUtils.setAllFiled(vo2, ColumnUtil
                    .listToMap(BasePeriodUtils.toList(vo, 96, Constants.LOAD_CURVE_START_WITH_ZERO),
                        Constants.LOAD_CURVE_START_WITH_ZERO));
                loadDecomposeCityWeekService.doUpdateLoadDecomposeCityWeekDO(vo2);
            }
        }

        return loadDecomposeCityWeekStabilityVO;
    }

    /**
     * 功能描述: <br> 获取置信最大最小
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    @Override
    public Map<String, List<BigDecimal>> getMaxMinConfidence(String cityId, String caliberId, String algorithmId,
        Date date) throws Exception {
        LoadCityFcDO loadCityFcDO = loadCityFcService.getLoadCityFcDO(date, cityId, caliberId, algorithmId);
        if (loadCityFcDO == null) {
            return null;
        }
        Date startDate = DateUtils.addYears(date, -1);
        List<DeviationLoadCityFcDO> deviationLoadCityFcVOList = deviationLoadCityFcService
            .findDeviationLoadCityFcDO(cityId, caliberId, algorithmId, startDate, date);
        if (CollectionUtils.isEmpty(deviationLoadCityFcVOList)) {
            return null;
        }
        // <时刻点,List<Bigdecimal>>
        Map<String, List<BigDecimal>> listMap = LoadCalUtil
            .toColumnList(deviationLoadCityFcVOList, Constants.LOAD_CURVE_POINT_NUM,
                Constants.LOAD_CURVE_START_WITH_ZERO);
        //<时刻点,区间范围>
        Map<String, BigDecimal> map = new HashMap<>();
        for (Map.Entry<String, List<BigDecimal>> entry : listMap.entrySet()) {
            //每一个时刻点的list集合
            List<BigDecimal> list = entry.getValue();
            //时刻的最大误差值
            BigDecimal max = LoadCalUtil.getMax(list);
            if (list == null || max == null) {
                continue;
            }
            //每一个时刻点得置信区间
            BigDecimal bound = this.calculate(0, list, max, new BigDecimal(0));
            log.info(entry.getKey() + ":置信区间为" + bound);
            map.put(entry.getKey(), bound);
        }
        Map<String, BigDecimal> loadMap = BasePeriodUtils
            .toMap(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        Map<String, List<BigDecimal>> dataListMap = new HashMap<>();
        List<BigDecimal> maxList = new ArrayList<>();
        List<BigDecimal> minList = new ArrayList<>();
        //map 排序
        Map<String, BigDecimal> sort = DataUtil.sortMapByKey(loadMap);
        for (String key : sort.keySet()) {
            if (loadMap.get(key) == null) {
                continue;
            }
            BigDecimal value = map.get(key);
            //置信最大限
            BigDecimal max = loadMap.get(key).add(value);
            maxList.add(max);
            //置信最下限
            BigDecimal min = loadMap.get(key).subtract(value);
            minList.add(min);
        }
        dataListMap.put("max", maxList);
        dataListMap.put("min", minList);
        return dataListMap;
    }


    /**
     * 功能描述: <br> 计算每个时刻点的置信区间
     *
     * @param list list集合
     * @param maxValue 最大值
     * @param minValue 最小值
     * <AUTHOR>
     * @since 1.0.0
     */
    private BigDecimal calculate(int count, List<BigDecimal> list, BigDecimal maxValue, BigDecimal minValue)
        throws Exception {
        //二分法取最大最小误差中间值
        BigDecimal intervalValue = minValue.add(maxValue).divide(BigDecimal.valueOf(2), 2, BigDecimal.ROUND_DOWN);
        //计算置信度
        BigDecimal bound = this.calculateBound(intervalValue, list);
        // 置信度和0.95相比
        BigDecimal gap = bound.subtract(CONFIDENCE);
        BigDecimal b = RANGE.multiply(new BigDecimal(-1));
        if (gap.compareTo(BigDecimal.ZERO) < 0 && gap.compareTo(b) >= 0) {//如果是负数
            log.info("------" + count + "次数");
            log.info("置信度为" + bound);
            return intervalValue;
        }
        if (gap.compareTo(BigDecimal.ZERO) >= 0 && gap.compareTo(RANGE) <= 0) {//如果是正数
            log.info("------" + count + "次数");
            return intervalValue;
        }
        if (count > 15) {//判断如果次数超过了15次,取最后一次计算结果
            log.info("------" + count + "次数");
            return intervalValue;
        }
        while (true) {
            count++;
            if (bound.compareTo(CONFIDENCE) > 0) {//大于0.95情况
                maxValue = intervalValue;
                return calculate(count, list, maxValue, minValue);
            }
            if (bound.compareTo(CONFIDENCE) < 0) {//小于0.95的情况
                minValue = intervalValue;
                return calculate(count, list, maxValue, minValue);
            }
        }
    }

    /**
     * 功能描述: <br> 计算置信度
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    public BigDecimal calculateBound(BigDecimal intervalValue, List<BigDecimal> list) throws Exception {
        int count = 0;
        for (BigDecimal number : list) {
            if (number == null) {
                continue;
            }
            if (number.compareTo(BigDecimal.ZERO) < 0
                && number.compareTo(intervalValue.multiply(new BigDecimal(-1))) >= 0) {
                count++;
            }
            if (number.compareTo(BigDecimal.ZERO) >= 0 && number.compareTo(intervalValue) <= 0) {
                //计算一年的点在区间的范围
                count++;
            }
        }
        BigDecimal p = new BigDecimal(count).divide(BigDecimal.valueOf(list.size()), 4, BigDecimal.ROUND_DOWN);
        return p;
    }


    /**
     * 获取平均偏差率
     */
    @Override
    public List<BigDecimal> getAvgDeviation(String cityId, String caliberId, String algorithmId, Date date)
        throws Exception {
        Date startDate = DateUtils.addDays(date, -7);
        List<LoadCityHisDO> loadCityHisDOS = loadCityHisDAO.getLoadCityHisDO(cityId, startDate, date, caliberId);
        List<LoadCityFcDO> loadCityFcDOS = loadCityFcDAO
            .getLoadCityFcDOs(cityId, caliberId, algorithmId, startDate, date);
        if (CollectionUtils.isEmpty(loadCityFcDOS) || CollectionUtils.isEmpty(
            loadCityHisDOS) || loadCityFcDOS.size() != loadCityHisDOS.size()) {
            return null;
        }
        Map<String, LoadCityHisDO> loadCityHisVOMap = new HashMap<>();
        for (LoadCityHisDO loadCityHisDO : loadCityHisDOS) {
            String key = loadCityHisDO.getCityId() + "-" + loadCityHisDO.getCaliberId() + "-" + loadCityHisDO.getDate()
                .getTime();
            loadCityHisVOMap.put(key, loadCityHisDO);
        }
        Map<String, List<LoadCityFcDO>> loadCityFcVOMap = new HashMap<>();
        for (LoadCityFcDO loadCityFcDO : loadCityFcDOS) {
            String key =
                loadCityFcDO.getCityId() + "-" + loadCityFcDO.getCaliberId() + "-" + loadCityFcDO.getDate().getTime();
            if (loadCityFcVOMap.get(key) == null) {
                loadCityFcVOMap.put(key, new ArrayList<>());
            }
            loadCityFcVOMap.get(key).add(loadCityFcDO);
        }
        Map<String, BigDecimal> bigDecimalMap = new HashMap<>();
        for (String key : loadCityHisVOMap.keySet()) {
            if (loadCityFcVOMap.get(key) != null) {
                for (LoadCityFcDO loadCityFcDO : loadCityFcVOMap.get(key)) {
                    //计算一天得偏差率
                    Map<String, BigDecimal> map = deviationLoadCityFcDAO
                        .calculateDeviationRatio(loadCityHisVOMap.get(key), loadCityFcDO);
                    for (Map.Entry<String, BigDecimal> entry : map.entrySet()) {
                        if (bigDecimalMap.get(entry.getKey()) == null) {
                            bigDecimalMap.put(entry.getKey(), entry.getValue());
                        } else {
                            BigDecimal value = entry.getValue();
                            if (value == null) {
                                value = BigDecimal.ZERO;
                            }
                            bigDecimalMap.put(entry.getKey(), bigDecimalMap.get(entry.getKey()).add(value));
                        }
                    }
                }
            }
        }
        Map<String, BigDecimal> sortMap = DataUtil.sortMapByKey(bigDecimalMap);
        List<BigDecimal> list = new ArrayList<>();
        if (sortMap == null) {
            return null;
        }
        for (Map.Entry<String, BigDecimal> entry : sortMap.entrySet()) {
            BigDecimal value = entry.getValue();
            BigDecimal ratio =
                value == null ? null : value.divide(new BigDecimal(loadCityFcDOS.size()), 4, BigDecimal.ROUND_DOWN)
                    .multiply(new BigDecimal(100));
            list.add(ratio);
        }
        return list;
    }


    @Override
    public ResultDTO doSensitivityAlgorithm(SensitivityAlgorithmDTO algorithmDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        try {
            SensitivityParam sensitivityParam = new SensitivityParam();
            sensitivityParam.setBeginDate(algorithmDTO.getStartDate());
            sensitivityParam.setEndDate(algorithmDTO.getEndDate());
            sensitivityParam.setMax(algorithmDTO.getMax());
            sensitivityParam.setMin(algorithmDTO.getMin());
            sensitivityParam.setStep(algorithmDTO.getStep());
            sensitivityParam.setLoadType(algorithmDTO.getLoadType());
            sensitivityParam.setWeatherType(algorithmDTO.getWeatherType());
            sensitivityParam.setCityId(algorithmDTO.getCityId());
            sensitivityParam.setAlgorithmEnum(AlgorithmEnum.SENSITIVITY);
            sensitivityParam.setDateNotIncluded(algorithmDTO.getDateNotIncluded());
            sensitivityParam.setCityId(algorithmDTO.getCityId());
            sensitivityParam.setUserId(algorithmDTO.getUserId());
            sensitivityParam.setCaliberId(algorithmDTO.getCaliberId());
            //调用灵敏度分析算法
            sensitivityForecastService.forecast(sensitivityParam);

            SensitivityResult result = (SensitivityResult) redisService
                .redisGet(algorithmDTO.getUserId() + Constants.SEPARATOR_BROKEN_LINE
                    + CacheConstants.CACHE_SENSITIVITYRESULT_KEY, SensitivityResult.class);
            //String year = DateUtil.getDateToStrFORMAT(algorithmDTO.getEndDate(), "yyyy-MM-dd").split("-")[0];
            resultDTO.setAnalyze(result.getBeanList());
            FeatureDTO feature = new FeatureDTO();
            feature.setFeaturesLine(result.getFeaturesLine());
            feature.setFeaturesPoint(result.getFeaturesPoint());
            feature.setFittingAccuracy(result.getFittingAccuracy());
            resultDTO.setFeature(feature);
            resultDTO.setComment(generateSensitivityComment(result));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return resultDTO;
    }

    @Override
    public ResultDTO doAcSensitivityAlgorithm(SensitivityAlgorithmDTO algorithmDTO) throws Exception {
        String min = String.valueOf(Integer.valueOf(algorithmDTO.getMin()) - 1);
        ResultDTO resultData = new ResultDTO();
        String year = DateUtil.getDateToStrFORMAT(algorithmDTO.getStartDate(), "yyyy-MM-dd").split("-")[0];
        String nowYear = DateUtil.getDateToStrFORMAT(new Date(), "yyyy-MM-dd").split("-")[0];
        AcBaseInitDO acBaseData = acBaseService.getAcBaseInitDO(algorithmDTO.getCityId(), nowYear, Integer.valueOf(algorithmDTO.getSeasonType()),
                algorithmDTO.getType(), Integer.valueOf(algorithmDTO.getLoadType()));
        List<AccuracyAlgorithmValueDataDTO> accuracy = new ArrayList<>();
        // 需要连续跑四年的数据
        List<ResultDTO> listData = new ArrayList<>();
        List<ResultDTO> list = new ArrayList<>();
        AtomicReference<ResultDTO> resultDTO1 = new AtomicReference<>(new ResultDTO());
        List<String> years = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            try {
                int finalI = i;
                List<ResultDTO> finalList = list;
                List<SensitivityBean> beanList = new ArrayList<>();
                ResultDTO resultDTO = new ResultDTO();
                Date startDate = DateUtils.addYears(algorithmDTO.getStartDate(), -finalI);
                Date endDate = DateUtils.addYears(algorithmDTO.getEndDate(), -finalI);
                try {
                    SensitivityParam sensitivityParam = new SensitivityParam();
                    if (algorithmDTO.getPolyOrder() == null) {
                        sensitivityParam.setPolyOrder("4");
                    } else {
                        sensitivityParam.setPolyOrder(algorithmDTO.getPolyOrder());
                    }
                    sensitivityParam.setBeginDate(startDate);
                    sensitivityParam.setEndDate(endDate);
                    sensitivityParam.setMax(algorithmDTO.getMax());
                    sensitivityParam.setMin(min);
                    sensitivityParam.setStep(algorithmDTO.getStep());
                    sensitivityParam.setWeatherId(algorithmDTO.getWeatherId());
                    sensitivityParam.setWeatherType(algorithmDTO.getWeatherType());
                    if (Integer.valueOf(algorithmDTO.getLoadType()) == 1) {
                        sensitivityParam.setLoadType(algorithmDTO.getLoadType());
                    } else if (Integer.valueOf(algorithmDTO.getLoadType()) == 2) {
                        sensitivityParam.setLoadType(String.valueOf(5));
                    } else if (Integer.valueOf(algorithmDTO.getLoadType()) == 3) {
                        sensitivityParam.setLoadType(String.valueOf(6));
                    } else if (Integer.valueOf(algorithmDTO.getLoadType()) == 4) {
                        sensitivityParam.setLoadType(String.valueOf(4));
                    }
                    sensitivityParam.setCityId(algorithmDTO.getCityId());
                    sensitivityParam.setAlgorithmEnum(AlgorithmEnum.SENSITIVITY);
                    // 1、先获取数据库里的
                    String subYear = DateUtil.getDateToStrFORMAT(startDate, "yyyy-MM-dd").split("-")[0];
                    List<String> dateList = new ArrayList<>();
                    dateList = acBaseDateService.getDateList(algorithmDTO.getCityId(), subYear, Integer.valueOf(algorithmDTO.getSeasonType()),
                            algorithmDTO.getType(), Integer.valueOf(algorithmDTO.getLoadType()));
                    List<String> dateNotIncluded = algorithmDTO.getDateNotIncluded();
                    if (!CollectionUtils.isEmpty(dateNotIncluded)) {
                        for (String s1 : dateNotIncluded) {
                            String[] split = s1.split(",");
                            for (String s : split) {
                                String[] split1 = s.split("~");
                                Date date = org.apache.commons.lang3.time.DateUtils.parseDate(split1[0], "yyyy-MM-dd");
                                Date date1 = org.apache.commons.lang3.time.DateUtils.parseDate(split1[1], "yyyy-MM-dd");
                                for (Date date2 : com.tsintergy.lf.serviceimpl.common.util.DateUtils.getListBetweenDay(date, date1)) {
                                    dateList.add(DateUtil.getDateToStrFORMAT(date2, "yyyy-MM-dd"));
                                }
                            }
                        }
                    }
                    sensitivityParam.setDateNotIncluded(dateList.stream().distinct().collect(Collectors.toList()));
                    sensitivityParam.setCityId(algorithmDTO.getCityId());
                    sensitivityParam.setUserId(algorithmDTO.getUserId());
                    sensitivityParam.setCaliberId(algorithmDTO.getCaliberId());
                    sensitivityParam.setType(algorithmDTO.getType());
                    //调用灵敏度分析算法
                    if (DateUtil.getDate("2021-12-31", "yyyy-MM-dd").compareTo(startDate) >= 0) {
                        continue;
                    }
                    synchronized (sensitivityForecastLock) {
                        sensitivityForecastService.forecast(sensitivityParam);
                    }
                    SensitivityResult result = (SensitivityResult) redisService
                            .redisGet(algorithmDTO.getUserId() + Constants.SEPARATOR_BROKEN_LINE
                                    + CacheConstants.CACHE_SENSITIVITYRESULT_KEY, SensitivityResult.class);
                    if (!CollectionUtils.isEmpty(result.getBeanList())) {
                        for (SensitivityBean bean : result.getBeanList()){
                            bean.setYear(DateUtil.getDateToStrFORMAT(startDate, "yyyy-MM-dd").split("-")[0]);
                            beanList.add(bean);
                        }
                    }
                    resultDTO.setAnalyze(result.getBeanList());
                    FeatureDTO feature = new FeatureDTO();
                    feature.setFeaturesLine(result.getFeaturesLine());
                    feature.setFeaturesPoint(result.getFeaturesPoint());
                    feature.setFittingAccuracy(result.getFittingAccuracy());
                    resultDTO.setFeature(feature);
                    resultDTO.setComment(generateSensitivityComment(result));
                    resultDTO.setYear(DateUtil.getDateToStrFORMAT(startDate, "yyyy-MM-dd").split("-")[0]);
                    if (i != 3) {
                        years.add(DateUtil.getDateToStrFORMAT(startDate, "yyyy-MM-dd").split("-")[0]);
                    }
                    if (finalI == 1) {
                        resultDTO1.set(new ResultDTO(resultDTO));
                    }
                    finalList.add(new ResultDTO(resultDTO));
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        if (year.equals(nowYear)) {
            if (acBaseData != null && acBaseData.getValue() != null) {
                if (resultDTO1 != null) {
                    List<SensitivityBean> sensitivityData = getSensitivityData(resultDTO1.get().getAnalyze(), acBaseData.getValue().divide(BigDecimal.valueOf(100)));
                    List<List<BigDecimal>> sensitivityFeaturesData = getSensitivityFeaturesData(resultDTO1.get().getFeature().getFeaturesLine(), acBaseData.getValue().divide(BigDecimal.valueOf(100)));
                    List<List<BigDecimal>> sensitivityFeaturesData1 = getSensitivityFeaturesData(resultDTO1.get().getFeature().getFeaturesPoint(), acBaseData.getValue().divide(BigDecimal.valueOf(100)));
                    resultDTO1.get().setYear(nowYear + "年（配置）");
                    resultDTO1.get().setAnalyze(sensitivityData);
                    resultDTO1.get().getFeature().setFeaturesLine(sensitivityFeaturesData);
                    resultDTO1.get().getFeature().setFeaturesPoint(sensitivityFeaturesData1);
                    listData.add(resultDTO1.get());
                }
            } else {
                if (resultDTO1.get() != null) {
                    resultDTO1.get().setYear(nowYear + "年（配置）");
                    resultDTO1.get().setAnalyze(null);
                    if (resultDTO1.get().getFeature() != null) {
                        resultDTO1.get().getFeature().setFeaturesLine(null);
                        resultDTO1.get().getFeature().setFeaturesPoint(null);
                    }
                    listData.add(resultDTO1.get());
                }
            }
        }
        // 重新计算灵敏度曲线
        list.forEach(t->{
            List<SensitivityBean> analyze = t.getAnalyze();
            if (!CollectionUtils.isEmpty(analyze)) {
                for (int i = 0; i < analyze.size(); i++) {
                    // 用i为后一个对象里的fittingValue值减去前一个对象的
                    if (i > 0) {
                        analyze.get(i).setValue(analyze.get(i).getFittingValue().subtract(analyze.get(i - 1).getFittingValue()));
                    }
                }
            }
        });
        if (!CollectionUtils.isEmpty(listData)) {
            listData.forEach(t->{
                List<SensitivityBean> analyze = t.getAnalyze();
                if (!CollectionUtils.isEmpty(analyze)) {
                    for (int i = 0; i < analyze.size(); i++) {
                        // 用i为后一个对象里的fittingValue值减去前一个对象的
                        if (i > 0) {
                            analyze.get(i).setValue(analyze.get(i).getFittingValue().subtract(
                                    analyze.get(i - 1).getFittingValue()));
                        }
                    }
                }
            });
        }
        // 计算增长率
        Map<String, List<ResultDTO>> collect = list.stream().collect(Collectors.groupingBy(t -> t.getYear()));
        for (String y : years) {
            List<ResultDTO> resultDTOS = collect.get(String.valueOf(Integer.valueOf(y) - 1));
            List<ResultDTO> resultDTOS1 = collect.get(y);
            if (!CollectionUtils.isEmpty(resultDTOS1) && !CollectionUtils.isEmpty(resultDTOS)) {
                AccuracyAlgorithmValueDataDTO accuracyAlgorithmValueDataDTO = new AccuracyAlgorithmValueDataDTO();
                accuracyAlgorithmValueDataDTO.setDateStr(y);
                List<SensitivityBean> analyze1 = resultDTOS1.get(0).getAnalyze();
                List<SensitivityBean> analyze = resultDTOS.get(0).getAnalyze();
                List<BigDecimal> bigDecimals = new ArrayList<>();
                accuracyAlgorithmValueDataDTO.setColorType(false);
                if (!CollectionUtils.isEmpty(analyze1) && !CollectionUtils.isEmpty(analyze) && analyze1.size() == analyze.size() && bigDecimals != null) {
                    // 从1开始，因为步长手动加了1
                    for (int i = 1; i < analyze1.size(); i++) {
                        SensitivityBean sensitivityBean1 = analyze1.get(i);
                        SensitivityBean sensitivityBean = analyze.get(i);
                        if (sensitivityBean1 != null && sensitivityBean != null
                                && sensitivityBean1.getFittingValue() != null && sensitivityBean.getFittingValue() != null
                                && !sensitivityBean.getFittingValue().equals(BigDecimal.ZERO)) {
                            // 计算差值并进行除法运算
                            BigDecimal divide = sensitivityBean1.getFittingValue().subtract(sensitivityBean.getFittingValue())
                                    .divide(sensitivityBean.getFittingValue(), 4, RoundingMode.UP);
                            bigDecimals.add(divide);
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(bigDecimals)) {
                    accuracyAlgorithmValueDataDTO.setValue(BigDecimalUtils.addAllValue(bigDecimals).divide(
                            BigDecimal.valueOf(bigDecimals.size()), 4, RoundingMode.UP).multiply(new BigDecimal(100)));
                }
                accuracy.add(accuracyAlgorithmValueDataDTO);
            } else if (!CollectionUtils.isEmpty(resultDTOS1) && CollectionUtils.isEmpty(resultDTOS)) {
                AccuracyAlgorithmValueDataDTO accuracyAlgorithmValueDataDTO = new AccuracyAlgorithmValueDataDTO();
                accuracyAlgorithmValueDataDTO.setDateStr(y);
                accuracyAlgorithmValueDataDTO.setColorType(false);
                accuracy.add(accuracyAlgorithmValueDataDTO);
            }
        }
        // 计算配置的（当前年）
        if (!CollectionUtils.isEmpty(listData) && nowYear.equals(year)) {
            AccuracyAlgorithmValueDataDTO accuracyAlgorithmValueDataDTO = new AccuracyAlgorithmValueDataDTO();
            accuracyAlgorithmValueDataDTO.setDateStr(nowYear);
            accuracyAlgorithmValueDataDTO.setColorType(true);
            accuracyAlgorithmValueDataDTO.setValue(acBaseData == null?null:acBaseData.getValue());
            accuracy.add(accuracyAlgorithmValueDataDTO);
        }
        list.addAll(listData);
        if (!CollectionUtils.isEmpty(list)) {
            list = list.stream().filter(t -> !t.getYear().equals(String.valueOf(Integer.valueOf(year) - 3))).collect(Collectors.toList());
            List<SensitivityBean> sensitivityData = new ArrayList<>();
            FeatureDTO featureDTO = new FeatureDTO();
            List<List<BigDecimal>> featuresLine = new ArrayList<>();
            List<List<BigDecimal>> featuresPoint = new ArrayList<>();
            for (ResultDTO resultDTO : list) {
                if (resultDTO.getAnalyze() != null && resultDTO.getAnalyze().size() > 0) {
                    for (SensitivityBean sensitivityBean : resultDTO.getAnalyze()) {
                        BigDecimal weatherNorm = sensitivityBean.getWeatherNorm();
                        // weatherNorm取整
                        if (weatherNorm.setScale(0, RoundingMode.HALF_UP).toString().equals(min)) {
                            continue;
                        }
                        sensitivityBean.setYear(resultDTO.getYear());
                        sensitivityData.add(sensitivityBean);
                    }
                }
            }
            featureDTO.setFeaturesLine(featuresLine);
            featureDTO.setFeaturesPoint(featuresPoint);
            resultData.setAnalyze(sensitivityData);
            resultData.setFeature(featureDTO);
        }
        resultData.setAccuracy(accuracy);
        return resultData;
    }

    @Override
    public ResultMonthDTO doAcMonthSensitivityAlgorithm(SensitivityAlgorithmDTO algorithmDTO) throws Exception {
        ResultMonthDTO resultMonthDTO = new ResultMonthDTO();
        List<SensitivityMonthBeanDTO> sensitivityMonthBeanDTOList = new ArrayList<>();
        SensitivityParam sensitivityParam = new SensitivityParam();
        if (algorithmDTO.getPolyOrder() == null) {
            sensitivityParam.setPolyOrder("4");
        } else {
            sensitivityParam.setPolyOrder(algorithmDTO.getPolyOrder());
        }
        String min = String.valueOf(Integer.valueOf(algorithmDTO.getMin()) - 1);
        sensitivityParam.setBeginDate(algorithmDTO.getStartDate());
        sensitivityParam.setEndDate(algorithmDTO.getEndDate());
        sensitivityParam.setMax(algorithmDTO.getMax());
        sensitivityParam.setMin(min);
        sensitivityParam.setStep(algorithmDTO.getStep());
        sensitivityParam.setWeatherId(algorithmDTO.getWeatherId());
        sensitivityParam.setWeatherType(algorithmDTO.getWeatherType());
        if (Integer.valueOf(algorithmDTO.getLoadType()) == 1) {
            sensitivityParam.setLoadType(algorithmDTO.getLoadType());
        } else if (Integer.valueOf(algorithmDTO.getLoadType()) == 2) {
            sensitivityParam.setLoadType(String.valueOf(5));
        } else if (Integer.valueOf(algorithmDTO.getLoadType()) == 3) {
            sensitivityParam.setLoadType(String.valueOf(6));
        } else if (Integer.valueOf(algorithmDTO.getLoadType()) == 4) {
            sensitivityParam.setLoadType(String.valueOf(4));
        }
        sensitivityParam.setCityId(algorithmDTO.getCityId());
        sensitivityParam.setAlgorithmEnum(AlgorithmEnum.SENSITIVITY);
        String subYear = DateUtil.getDateToStrFORMAT(algorithmDTO.getStartDate(), "yyyy-MM-dd").split("-")[0];
        List<String> dateList = new ArrayList<>();
        dateList = acBaseDateService.getDateList(algorithmDTO.getCityId(), subYear, Integer.valueOf(algorithmDTO.getSeasonType()),
                algorithmDTO.getType(), Integer.valueOf(algorithmDTO.getLoadType()));
        List<String> dateNotIncluded = algorithmDTO.getDateNotIncluded();
        if (!CollectionUtils.isEmpty(dateNotIncluded)) {
            for (String s1 : dateNotIncluded) {
                String[] split = s1.split(",");
                for (String s : split) {
                    String[] split1 = s.split("~");
                    Date date = org.apache.commons.lang3.time.DateUtils.parseDate(split1[0], "yyyy-MM-dd");
                    Date date1 = org.apache.commons.lang3.time.DateUtils.parseDate(split1[1], "yyyy-MM-dd");
                    for (Date date2 : com.tsintergy.lf.serviceimpl.common.util.DateUtils.getListBetweenDay(date, date1)) {
                        dateList.add(DateUtil.getDateToStrFORMAT(date2, "yyyy-MM-dd"));
                    }
                }
            }
        }
        sensitivityParam.setDateNotIncluded(dateList.stream().distinct().collect(Collectors.toList()));
        sensitivityParam.setCityId(algorithmDTO.getCityId());
        sensitivityParam.setUserId(algorithmDTO.getUserId());
        sensitivityParam.setCaliberId(algorithmDTO.getCaliberId());
        sensitivityParam.setType(algorithmDTO.getType());
        synchronized (sensitivityForecastLock) {
            sensitivityForecastService.forecast(sensitivityParam);
        }
        SensitivityResult result = (SensitivityResult) redisService
                .redisGet(algorithmDTO.getUserId() + Constants.SEPARATOR_BROKEN_LINE
                        + CacheConstants.CACHE_SENSITIVITYRESULT_KEY, SensitivityResult.class);
        resultMonthDTO.setFeaturesLine(result.getFeaturesLine());
        resultMonthDTO.setFeaturesPoint(result.getFeaturesPoint());
        resultMonthDTO.setFittingAccuracy(result.getFittingAccuracy());
        // 处理灵敏度数据
        List<SensitivityBean> listData = result.getBeanList();
        if (!CollectionUtils.isEmpty(listData)) {
            for (int i = 0; i < listData.size(); i++) {
                if (i > 0) {
                    listData.get(i).setValue(listData.get(i).getFittingValue().subtract(
                            listData.get(i - 1).getFittingValue()));
                }
            }
            listData.remove(0);
            listData.forEach(t->{
                SensitivityMonthBeanDTO sensitivityMonthBeanDTO = new SensitivityMonthBeanDTO();
                sensitivityMonthBeanDTO.setWeatherNorm(t.getWeatherNorm());
                sensitivityMonthBeanDTO.setLoad(t.getFittingValue());
                sensitivityMonthBeanDTO.setSensitivityValue(t.getValue());
                sensitivityMonthBeanDTOList.add(sensitivityMonthBeanDTO);
            });
            resultMonthDTO.setAnalyze(sensitivityMonthBeanDTOList);
        }
        return resultMonthDTO;
    }

    @Override
    public FeatureDTO doAcSensitivityFeatureAlgorithm(SensitivityAlgorithmDTO algorithmDTO) throws Exception {
        FeatureDTO featureDTO = new FeatureDTO();
        List<SensitivityBean> analyzeDate = new ArrayList<>();
        String min = String.valueOf(Integer.valueOf(algorithmDTO.getMin()) - 1);
        FeatureDTO resultData = new FeatureDTO();
        FeatureDTO resultLastData = new FeatureDTO();
        String nowYear = DateUtil.getDateToStrFORMAT(new Date(), "yyyy-MM-dd").split("-")[0];
        Boolean colorType = algorithmDTO.getColorType();
        AcBaseInitDO acBaseInitDO = acBaseService.getAcBaseInitDO(algorithmDTO.getCityId(), nowYear, Integer.valueOf(algorithmDTO.getSeasonType()),
                algorithmDTO.getType(), Integer.valueOf(algorithmDTO.getLoadType()));
        for (int i = 0; i < 2; i++) {
            Date startDate = DateUtils.addYears(algorithmDTO.getStartDate(), -i);
            Date endDate = DateUtils.addYears(algorithmDTO.getEndDate(), -i);
            try {
                SensitivityParam sensitivityParam = new SensitivityParam();
                sensitivityParam.setBeginDate(startDate);
                sensitivityParam.setEndDate(endDate);
                sensitivityParam.setMax(algorithmDTO.getMax());
                sensitivityParam.setMin(min);
                sensitivityParam.setStep(algorithmDTO.getStep());
                sensitivityParam.setWeatherId(algorithmDTO.getWeatherId());
                sensitivityParam.setWeatherType(algorithmDTO.getWeatherType());
                if (Integer.valueOf(algorithmDTO.getLoadType()) == 1) {
                    sensitivityParam.setLoadType(algorithmDTO.getLoadType());
                } else if (Integer.valueOf(algorithmDTO.getLoadType()) == 2) {
                    sensitivityParam.setLoadType(String.valueOf(5));
                } else if (Integer.valueOf(algorithmDTO.getLoadType()) == 3) {
                    sensitivityParam.setLoadType(String.valueOf(6));
                } else if (Integer.valueOf(algorithmDTO.getLoadType()) == 4) {
                    sensitivityParam.setLoadType(String.valueOf(4));
                }
                sensitivityParam.setCityId(algorithmDTO.getCityId());
                sensitivityParam.setAlgorithmEnum(AlgorithmEnum.SENSITIVITY);
                sensitivityParam.setDateNotIncluded(algorithmDTO.getDateNotIncluded());
                sensitivityParam.setCityId(algorithmDTO.getCityId());
                sensitivityParam.setUserId(algorithmDTO.getUserId());
                sensitivityParam.setCaliberId(algorithmDTO.getCaliberId());
                sensitivityParam.setType(algorithmDTO.getType());
                if (algorithmDTO.getPolyOrder() == null) {
                    sensitivityParam.setPolyOrder("4");
                } else {
                    sensitivityParam.setPolyOrder(algorithmDTO.getPolyOrder());
                }
                String subYear = DateUtil.getDateToStrFORMAT(startDate, "yyyy-MM-dd").split("-")[0];
                List<String> dateList = acBaseDateService.getDateList(algorithmDTO.getCityId(), subYear, Integer.valueOf(algorithmDTO.getSeasonType()),
                        algorithmDTO.getType(), Integer.valueOf(algorithmDTO.getLoadType()));
                List<String> dateNotIncluded = algorithmDTO.getDateNotIncluded();
                if (!CollectionUtils.isEmpty(dateNotIncluded)) {
                    for (String s1 : dateNotIncluded) {
                        String[] split = s1.split(",");
                        for (String s : split) {
                            String[] split1 = s.split("~");
                            Date date = org.apache.commons.lang3.time.DateUtils.parseDate(split1[0], "yyyy-MM-dd");
                            Date date1 = org.apache.commons.lang3.time.DateUtils.parseDate(split1[1], "yyyy-MM-dd");
                            for (Date date2 : com.tsintergy.lf.serviceimpl.common.util.DateUtils.getListBetweenDay(date, date1)) {
                                dateList.add(DateUtil.getDateToStrFORMAT(date2, "yyyy-MM-dd"));
                            }
                        }
                    }
                }
                sensitivityParam.setDateNotIncluded(dateList.stream().distinct().collect(Collectors.toList()));
                //调用灵敏度分析算法
                sensitivityForecastService.forecast(sensitivityParam);
                SensitivityResult result = (SensitivityResult) redisService
                        .redisGet(algorithmDTO.getUserId() + Constants.SEPARATOR_BROKEN_LINE
                                + CacheConstants.CACHE_SENSITIVITYRESULT_KEY, SensitivityResult.class);
                if (i == 1) {
                    resultLastData.setFeaturesLine(result.getFeaturesLine());
                    resultLastData.setFeaturesPoint(result.getFeaturesPoint());
                    resultLastData.setFittingAccuracy(result.getFittingAccuracy());
                    resultLastData.setAnalyze(result.getBeanList());
                } else {
                    resultData.setFeaturesLine(result.getFeaturesLine());
                    resultData.setFeaturesPoint(result.getFeaturesPoint());
                    resultData.setFittingAccuracy(result.getFittingAccuracy());
                    resultData.setAnalyze(result.getBeanList());
                    featureDTO.setFeaturesLine(result.getFeaturesLine());
                    featureDTO.setFeaturesPoint(result.getFeaturesPoint());
                    featureDTO.setFittingAccuracy(result.getFittingAccuracy());
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        if (resultData != null && resultLastData != null) {
            List<SensitivityBean> analyze = resultData.getAnalyze();
            List<SensitivityBean> analyzeLast = resultLastData.getAnalyze();
            if (!CollectionUtils.isEmpty(analyze) && !CollectionUtils.isEmpty(analyzeLast)) {
                for (SensitivityBean sensitivityBean : analyze) {
                    for (SensitivityBean sensitivityBeanLast : analyzeLast) {
                        if (sensitivityBean.getWeatherNorm().equals(sensitivityBeanLast.getWeatherNorm())) {
                            BigDecimal subtract = sensitivityBean.getFittingValue().subtract(sensitivityBeanLast.getFittingValue());
                            sensitivityBean.setLoad(subtract);
                            sensitivityBean.setAccuracy(subtract.divide(sensitivityBeanLast.getFittingValue(), 4, BigDecimal.ROUND_DOWN));
                            sensitivityBean.setValue(sensitivityBean.getFittingValue());
                            if (acBaseInitDO != null) {
                                BigDecimal fittingValue = sensitivityBeanLast.getFittingValue();
                                if (acBaseInitDO.getValue() != null) {
                                    sensitivityBean.setFittingValueCompare(BigDecimal.valueOf(1).add(
                                            acBaseInitDO.getValue().divide(BigDecimal.valueOf(100))).multiply(
                                                    fittingValue).setScale(4, BigDecimal.ROUND_DOWN));
                                }
                            }
                            if (colorType != null && colorType && acBaseInitDO != null) {
                                // 配置
                                sensitivityBean.setValue(sensitivityBean.getFittingValueCompare());
                                BigDecimal subtract1 = sensitivityBean.getFittingValue().subtract(sensitivityBeanLast.getFittingValue());
                                sensitivityBean.setLoad(subtract1);
                                sensitivityBean.setAccuracy(subtract1.divide(sensitivityBeanLast.getFittingValue(), 4, BigDecimal.ROUND_DOWN));
                            }
                            analyzeDate.add(sensitivityBean);
                        }
                    }
                }
            } else if (!CollectionUtils.isEmpty(analyze) && CollectionUtils.isEmpty(analyzeLast)) {
                for (SensitivityBean sensitivityBean : analyze) {
                    sensitivityBean.setValue(sensitivityBean.getFittingValue());
                    analyzeDate.add(sensitivityBean);
                }
            }
            if (analyzeDate.size() > 0) {
                analyzeDate.remove(0);
            }
            featureDTO.setAnalyze(analyzeDate);
        }
        return featureDTO;
    }

    private List<SensitivityBean> getSensitivityData(List<SensitivityBean> sensitivityBean, BigDecimal value) {
        List<SensitivityBean> result = new ArrayList<>();
        sensitivityBean.forEach(item -> {
            BigDecimal add = BigDecimal.valueOf(1).add(value);
            BigDecimal multiply1 = item.getValue().multiply(add);
            item.setValue(multiply1.setScale(2, BigDecimal.ROUND_DOWN));
            item.setFittingValue(item.getFittingValue().multiply(add).setScale(2, BigDecimal.ROUND_DOWN));
            result.add(item);
        });
        return result;
    }

    private List<SensitivityBean> getSensitivityAnalyzeData(List<SensitivityBean> analyze, AcBaseInitDO acBaseInitDO,
                                                            List<List<BigDecimal>> featuresPoint, String nowYear,
                                                            String queryYear) {
        BigDecimal value = null;
        if (acBaseInitDO != null && acBaseInitDO.getValue()!= null && nowYear.equals(queryYear)) {
            value = acBaseInitDO.getValue().divide(BigDecimal.valueOf(100));
        }
        List<SensitivityBean> resultAll = new ArrayList<>();
        List<SensitivityBean> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(analyze)) {
            for (int i = analyze.size() - 1; i >= 0; i--) {
                // 增加 null 检查
                if (analyze != null) {
                    SensitivityBean sensitivityBean = analyze.get(i);
                    if (value != null) {
                        sensitivityBean.setValue(sensitivityBean.getValue().multiply(BigDecimal.valueOf(1).add(value)));
                    }
                    if ((i - 1) >= 0) {
                        // 增加 null 检查
                        if (analyze != null) {
                            SensitivityBean sensitivityBean1 = analyze.get(i - 1);
                            BigDecimal bigDecimal = sensitivityBean.getFittingValue().subtract(sensitivityBean1.getFittingValue())
                                    .divide(sensitivityBean1.getFittingValue(), 4, BigDecimal.ROUND_DOWN);
                            //sensitivityBean.setLoad(sensitivityBean.getValue().subtract(sensitivityBean1.getValue()));
                            sensitivityBean.setAccuracy(bigDecimal);
                        }
                    }
                    result.add(sensitivityBean);
                }
            }
        }
        for (SensitivityBean sensitivityBean : result) {
            sensitivityBean.setLoad(sensitivityBean.getValue());
            sensitivityBean.setValue(sensitivityBean.getFittingValue());
            if (sensitivityBean.getFittingValue() != null && value != null) {
                sensitivityBean.setValue(sensitivityBean.getFittingValue().multiply(BigDecimal.valueOf(1).add(value)));
                sensitivityBean.setFittingValue(sensitivityBean.getFittingValue().multiply(BigDecimal.valueOf(1).add(value)));
            }
            resultAll.add(sensitivityBean);
        }
        // load 由差值计算
        List<SensitivityBean> collect = resultAll.stream().sorted(Comparator.comparing(SensitivityBean::getWeatherNorm)).collect(Collectors.toList());
        for (int i = 0; i < collect.size(); i++) {
            // 用i为后一个对象里的fittingValue值减去前一个对象的
            if (i > 0) {
                SensitivityBean sensitivityBean = collect.get(i);
                SensitivityBean sensitivityBean1 = collect.get(i - 1);
                sensitivityBean.setLoad(sensitivityBean.getFittingValue().subtract(sensitivityBean1.getFittingValue()));
            }
        }
        return collect;
    }

    private List<List<BigDecimal>> getSensitivityFeaturesData(List<List<BigDecimal>> featuresLine, BigDecimal value) {
        List<List<BigDecimal>> result = new ArrayList<>();
        for (List<BigDecimal> bigDecimals : featuresLine) {
            List<BigDecimal> features = new ArrayList<>();
            BigDecimal bigDecimal = bigDecimals.get(0);
            BigDecimal multiply = bigDecimals.get(1).multiply(BigDecimal.valueOf(1).add(value)).setScale(2, BigDecimal.ROUND_DOWN);
            features.add(bigDecimal);
            features.add(multiply);
            result.add(features);
        }
        return result;
    }

    @Override
    public void doAcSensitivitySaveConfig(String cityId, String year, Integer seasonType, Integer type, Integer loadTyp,
                                          BigDecimal value) throws Exception {
        AcBaseInitDO acBaseInitDO = new AcBaseInitDO();
        acBaseInitDO.setCityId(cityId);
        acBaseInitDO.setYear(year);
        acBaseInitDO.setSeasonType(seasonType);
        acBaseInitDO.setType(type);
        acBaseInitDO.setLoadType(loadTyp);
        acBaseInitDO.setValue(value);
        if (value == null) {
            // 删除方案
            acBaseService.doDelete(acBaseInitDO);
        } else {
            // 保存方案
            acBaseService.doSave(acBaseInitDO);
        }
    }

    @Override
    public List<String> queryAcSensitivitySaveConfig(String cityId, String year, Integer seasonType, Integer type, Integer loadTyp, BigDecimal value) throws Exception {
        List<String> dateList = acBaseDateService.getAcBaseDateDO(cityId, year, seasonType, type, loadTyp);
        return dateList;
    }

    @Override
    public void acSaveDateConfig(String cityId, String year, Integer seasonType, Integer type, Integer loadTyp,
                                 List<String> dateList, Integer acType) throws Exception {
        List<AcBaseDateDO> acBaseDateDOS = acBaseDateService.getAcBaseDateDOS(cityId, year, seasonType, type, loadTyp);
        if (!CollectionUtils.isEmpty(acBaseDateDOS) && acType == 2) {
            // 删除方案
            AcBaseDateDO acBaseDateDO = acBaseDateDOS.get(0);
            if (acBaseDateDO.getData() != null) {
                String all = acBaseDateDO.getData();
                String[] split = all.split(",");
                for (String s : split) {
                    if (dateList.contains(s)) {
                        // 如果包含s，那原all删除s
                        all = all.replace(s, "");
                        // 可是逗号还没有去除
                        all = all.replace(",,", ",");
                        // 检查并去除首部的逗号
                        if (all.startsWith(",")) {
                            all = all.substring(1);
                        }
                        // 检查并去除尾部的逗号
                        if (all.endsWith(",")) {
                            all = all.substring(0, all.length() - 1);
                        }
                    }
                }
                acBaseDateDO.setData(all);
            }
            acBaseDateService.doSaveOrUpdate(acBaseDateDO);
        } else if (!CollectionUtils.isEmpty(acBaseDateDOS) && acType == 1) {
            // 保存方案
            AcBaseDateDO acBaseDateDO = acBaseDateDOS.get(0);
            String data = acBaseDateDO.getData();
            for (String s : dateList) {
                String[] split = data.split(",");
                if (!Arrays.asList(split).contains(s)) {
                    data = data + "," + s;
                }
            }
            if (data.startsWith(",")) {
                data = data.substring(1);
            }
            acBaseDateDO.setData(data);
            acBaseDateService.doSaveOrUpdate(acBaseDateDO);
        }
    }

    private String generateSensitivityComment(SensitivityResult result) {
        if (!CollectionUtils.isEmpty(result.getFeaturesPoint())) {
            BigDecimal minTemp = result.getFeaturesPoint().get(0).get(0);
            BigDecimal maxTemp = result.getFeaturesPoint().get(result.getFeaturesPoint().size() - 1).get(0);
            StringBuilder comment = new StringBuilder();
            comment.append("可参考灵敏度区间为：温度大于等于")
                .append(minTemp)
                .append(",小于等于")
                .append(maxTemp);
            return comment.toString();
        }
        return "";
    }


    private List<LoadFeatureCityDayHisDO> mergeHistoryLoadFeature(String cityId, String caliberId, Date startDate,
        Date endDate) throws Exception {
        return loadFeatureCityDayHisDAO.getLoadFeatureCityDayHisDOs(cityId, startDate, endDate, caliberId);
    }

    private List<WeatherFeatureCityDayHisDO> mergeSensitivityWeather(String cityId, Date startDate, Date endDate,
        List<SearchDTO> searchDTOList) throws Exception {
        return weatherFeatureCityDayHisDAO.findWeatherFeature(cityId, startDate, endDate, searchDTOList);
    }

    /**
     * 超短期预测
     *
     * <AUTHOR>
     */
    @Override
    public void doShortForecast(String cityId, String caliberId, Date date, Integer timeSpan,
        Integer startTimePoint)
        throws Exception {
        List<String> columns = ColumnUtil
            .getColumns(ShortConstants.MINUTE.equals(timeSpan) ? 288 : 96, Constants.LOAD_CURVE_START_WITH_ZERO, false);
        if (startTimePoint == 96 && !ShortConstants.MINUTE.equals(timeSpan)) {
            startTimePoint = 0;
        }
        if (startTimePoint == 288) {
            startTimePoint = 0;
        }
        String fcStartTime = columns.get(startTimePoint);
        //系统设置的超短期预测的时长 默认4小时
        SystemData systemSetting = systemService.getSystemSetting();
        String hour;
        if (cityId.equals(CityConstants.PROVINCE_ID)) {
            if (ShortConstants.MINUTE.equals(timeSpan)) {
                hour = systemSetting.getProvinceShortFiveTime();
            } else {
                hour = systemSetting.getProvinceShortFifteenTime();
            }
        } else {
            if (ShortConstants.MINUTE.equals(timeSpan)) {
                hour = systemSetting.getCityShortFiveTime();
            } else {
                hour = systemSetting.getCityShortFifteenTime();
            }
        }
        ShortForecastParam param = new ShortForecastParam();
        param.setForecastDate(date);
        param.setTimeSpan(String.valueOf(timeSpan));
        param.setStartTimePoint(String.valueOf(startTimePoint));
        param.setForecastPoint(String.valueOf((Integer.valueOf(hour) * 60) / timeSpan));
        param.setCityId(cityId);
        param.setAlgorithmEnum(AlgorithmEnum.SHORT_FORECAST);
        param.setCaliberId(caliberId);
        param.setStartTimeStr(fcStartTime);
        //调用超短期算法
        shortForecastService.forecast(param);
    }

    @Override
    public ForecastLoadFeatureDTO getFcLoadFeatureData(Date targetDate, String cityId, String algorithmId)
        throws Exception {
        ForecastLoadFeatureDTO forecastLoadFeatureDTO = new ForecastLoadFeatureDTO();
        CityDO cityById = cityService.findCityById(cityId);
        // 营销作日实际
        List<IndustryCityLoadFeatureDayHisStatsDO> all = industryCityLoadFeatureDayHisStatsService.findALL(
            DateUtils.addDays(targetDate, -1), DateUtils.addDays(targetDate, -1), cityById.getOrgNo(),
            TRADE_ID_All);
        if (!CollectionUtils.isEmpty(all)) {
            IndustryCityLoadFeatureDayHisStatsDO industryCityLoadFeatureDayHisStatsDO = all.get(0);
            forecastLoadFeatureDTO.setMaxLoad(industryCityLoadFeatureDayHisStatsDO.getMaxLoad());
            forecastLoadFeatureDTO.setMaxTime(industryCityLoadFeatureDayHisStatsDO.getMaxTime());
            forecastLoadFeatureDTO.setAvgLoad(industryCityLoadFeatureDayHisStatsDO.getAveLoad());
            forecastLoadFeatureDTO.setMinLoad(industryCityLoadFeatureDayHisStatsDO.getMinLoad());
            forecastLoadFeatureDTO.setMinTime(industryCityLoadFeatureDayHisStatsDO.getMinTime());
            forecastLoadFeatureDTO.setPeak(industryCityLoadFeatureDayHisStatsDO.getMaxLoad().subtract(
                industryCityLoadFeatureDayHisStatsDO.getMinLoad()));
        }
        List<IndustryCityLoadDayHisClctNewDO> byDate = industryCityLoadDayHisClctNewService.findByDate(cityById.getOrgNo(),
                TRADE_ID_All, DateUtils.addDays(targetDate, -1), DateUtils.addDays(targetDate, -1));
        if (!CollectionUtils.isEmpty(byDate)) {
            IndustryCityLoadDayHisClctNewDO industryCityLoadDayHisClctDO = byDate.get(0);
            forecastLoadFeatureDTO.setYesterdayHisLoad(
                    BigDecimalFunctions.listDivideValue(industryCityLoadDayHisClctDO.getloadList(), K));
        }
        // 营销预测
        List<IndustryCityLoadDayFcAlgoDO> byDateCodes = industryCityLoadDayFcAlgoService.findByDateOneCodes(
                cityById.getOrgNo(), TRADE_ID_All, targetDate);
        if (!CollectionUtils.isEmpty(byDateCodes)) {
            IndustryCityLoadDayFcAlgoDO industryCityLoadDayFcAlgoDO = byDateCodes.get(0);
            forecastLoadFeatureDTO.setTodayFcLoad(
                    BigDecimalFunctions.listDivideValue(industryCityLoadDayFcAlgoDO.getloadList(), K));
        }
        return forecastLoadFeatureDTO;
    }

    @Override
    public void copyForecastResult(String sourceAlgoId, String targetAlgoId, Date startDate, Date endDate) throws Exception {
        log.error("------开始复制load_city_fc_batch， 开始时间：{}, 结束时间：{}", startDate, endDate);
        List<LoadCityFcBatchDO> sourceBatchList = loadCityFcBatchService.findByAllAlgorithmCondition(Constants.PROVINCE_ID, startDate, endDate,
                Constants.CALIBER_CITY_ID, Arrays.asList(sourceAlgoId));
        if (CollectionUtil.isNotEmpty(sourceBatchList)) {
            List<LoadCityFcBatchDO> targetBatchList = new ArrayList<>();
            for (LoadCityFcBatchDO loadCityFcBatchDO : sourceBatchList) {
                LoadCityFcBatchDO copyBatch = new LoadCityFcBatchDO();
                BeanUtils.copyProperties(loadCityFcBatchDO, copyBatch, "id");
                copyBatch.setAlgorithmId(targetAlgoId);
                targetBatchList.add(copyBatch);
                loadCityFcBatchService.doSaveOrUpdate(copyBatch);
            }
        }
        log.error("------复制load_city_fc_batch结束， 开始时间：{}, 结束时间：{}", startDate, endDate);
        log.error("------开始复制load_city_fc_basic， 开始时间：{}, 结束时间：{}", startDate, endDate);
        List<LoadCityFcDO> sourceLoadCityFcList = loadCityFcService.findFcByAlgorithmId(Constants.PROVINCE_ID, Constants.CALIBER_CITY_ID, sourceAlgoId, startDate, endDate);
        if (CollectionUtil.isNotEmpty(sourceLoadCityFcList)) {
            List<LoadCityFcDO> targetLoadCityFcList = new ArrayList<>();
            for (LoadCityFcDO loadCityFcDO : sourceLoadCityFcList) {
                LoadCityFcDO copyLoadCityFc = new LoadCityFcDO();
                BeanUtils.copyProperties(loadCityFcDO, copyLoadCityFc, "id");
                copyLoadCityFc.setAlgorithmId(targetAlgoId);
                targetLoadCityFcList.add(copyLoadCityFc);
                loadCityFcService.doSaveOrUpdateLoadCityFcDO96(copyLoadCityFc);
            }
        }
        log.error("------复制load_city_fc_basic结束， 开始时间：{}, 结束时间：{}", startDate, endDate);
    }

    @Override
    public List<ForecastBpCheckDTO> getForecastBpCheckData(String cityId, String caliberId, Date date) throws Exception {
        List<ForecastBpCheckDTO> result = new ArrayList<>();
        String weekName = DateUtil.dateToWeek(date);
        Date startDate = DateUtils.addDays(date, 1);
        Date endDate = DateUtils.addDays(date, 10);
        String algorithmId = "820";
        SettingAlgorithmMultiStrategyDTO effectStrategySetting = settingAlgorithmMultiStrategyService.getDailyStrategySetting(cityId, date);
        if (effectStrategySetting != null) {
            algorithmId = effectStrategySetting.getAlgorithmIds().get(0);
        }
        // 获取天气数据
        List<WeatherStationFcBasicWgDO> standardStationFcWeatherBasic = weatherStationFcBasicWgService.getStandardStationFcWeatherBasic(cityId, null, startDate, endDate);
        Map<String, List<WeatherStationFcBasicWgDO>> collectWeather = new HashMap<>();
        if (CollectionUtil.isNotEmpty(standardStationFcWeatherBasic)) {
            collectWeather = standardStationFcWeatherBasic.stream()
                    .collect(Collectors.groupingBy(t -> t.getType() + "-" + DateUtils.date2String(t.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR)));
        }

        // 获取负荷预测数据
        List<LoadCityFcBatchDO> byAllAlgorithmCondition = loadCityFcBatchService.findByAllAlgorithmCondition(cityId,
                startDate, endDate, caliberId, Arrays.asList(AlgorithmEnum.BP_CHECK_OPTIMIZE.getId(),
                        AlgorithmConstants.MD_ALGORITHM_ID, algorithmId));
        Map<String, List<LoadCityFcBatchDO>> collect = new HashMap<>();
        if (CollectionUtil.isNotEmpty(byAllAlgorithmCondition)) {
            List<LoadCityFcBatchDO> fcList = byAllAlgorithmCondition.stream()
                    .filter(t -> DateUtil.getDate(DateUtils.date2String(t.getCreatetime(), DateFormatType.SIMPLE_DATE_FORMAT_STR), "yyyy-MM-dd").compareTo(date) == 0)
                    .collect(Collectors.toList());
            collect = fcList.stream()
                    .collect(Collectors.groupingBy(t -> t.getAlgorithmId() + "-" + DateUtils.date2String(t.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR)));
        }

        // 获取D+1到D+10的所有日期
        List<Date> listBetweenDay = DateUtil.getListBetweenDay(startDate, endDate);
        Date today = DateUtil.getDate(DateUtils.date2String(new Date(), DateFormatType.SIMPLE_DATE_FORMAT_STR), "yyyy-MM-dd");
        // 基础flag设置：大于等于今天为true可以修改数据，否则为false不能修改
        boolean baseFlag = date.compareTo(today) >= 0;

        for (Date date1 : listBetweenDay) {
            String dateStr = DateUtils.date2String(date1, DateFormatType.SIMPLE_DATE_FORMAT_STR);
            Integer dateDiff = getDateDiff(date1, date);
            ForecastBpCheckDTO forecastBpCheckDTO = new ForecastBpCheckDTO();
            forecastBpCheckDTO.setDate(date1);
            // 根据星期几和日期偏移量调整flag
            if ("星期五".equals(weekName)) {
                // 周五：D+1、D+2、D+3的flag设为false
                forecastBpCheckDTO.setFlag(dateDiff >= 4 && baseFlag);
            } else if ("星期六".equals(weekName)) {
                // 周六：D+1、D+2的flag设为false
                forecastBpCheckDTO.setFlag(dateDiff >= 3 && baseFlag);
            } else {
                // 其他日期：仅D+1的flag设为false
                forecastBpCheckDTO.setFlag(dateDiff >= 2 && baseFlag);
            }

            forecastBpCheckDTO.setWeek(DateUtil.getWeek(date1));

            // 天气数据处理
            List<WeatherStationFcBasicWgDO> weatherStationFcBasicWgDOS = collectWeather.get(2 + "-" + dateStr);
            List<WeatherStationFcBasicWgDO> weatherStationFcBasicWgDOS1 = collectWeather.get(3 + "-" + dateStr);
            BigDecimal max = null;
            BigDecimal min = null;
            BigDecimal rainAll = null;

            if (CollectionUtil.isNotEmpty(weatherStationFcBasicWgDOS)) {
                List<BigDecimal> bigDecimals = weatherStationFcBasicWgDOS.get(0).getloadList();
                max = BigDecimalFunctions.listMax(bigDecimals);
                min = BigDecimalFunctions.listMin(bigDecimals);
            }

            if (CollectionUtil.isNotEmpty(weatherStationFcBasicWgDOS1)) {
                List<BigDecimal> bigDecimals = weatherStationFcBasicWgDOS1.get(0).getloadList();
                rainAll = BigDecimalFunctions.listSum(bigDecimals);
            }

            String maxTemp = (max != null) ? max.setScale(0, BigDecimal.ROUND_HALF_UP).toString() : "--";
            forecastBpCheckDTO.setTemp("最高气温<span style=\"color:red\">" + maxTemp + "</span>℃，最低气温" +
                    (min != null ? min.setScale(0, BigDecimal.ROUND_HALF_UP) : "--") + "℃，降水量" + (rainAll != null ? rainAll.setScale(0, BigDecimal.ROUND_HALF_UP) : "--") + "mm");

            forecastBpCheckDTO.setDateStr(dateDiff + "（" + DateUtil.getDateToStrFORMAT(date1, "yyyy-MM-dd") + "）");

            // 预测数据处理
            List<LoadCityFcBatchDO> weatherDTOS = collect.get(AlgorithmEnum.BP_CHECK_OPTIMIZE.getId() + "-" + dateStr);
            if (CollectionUtil.isNotEmpty(weatherDTOS)) {
                List<LoadCityFcBatchDO> collect1 = weatherDTOS.stream()
                        //.sorted(Comparator.comparing(LoadCityFcBatchDO::getCreatetime).reversed())
                        .sorted(Comparator.comparing(LoadCityFcBatchDO::getBatchId).reversed())
                        .collect(Collectors.toList());
                LoadCityFcBatchDO weatherDTO = collect1.get(0);
                forecastBpCheckDTO = getBpFcFeatureData(forecastBpCheckDTO, weatherDTO);
                forecastBpCheckDTO.setFcBigDecimalList(weatherDTO.getloadList());
            }

            List<LoadCityFcBatchDO> loadCityFcDOS = collect.get(AlgorithmConstants.MD_ALGORITHM_ID + "-" + dateStr);
            if (CollectionUtil.isNotEmpty(loadCityFcDOS)) {
                List<LoadCityFcBatchDO> collect1 = loadCityFcDOS.stream()
                        //.sorted(Comparator.comparing(LoadCityFcBatchDO::getCreatetime).reversed())
                        .sorted(Comparator.comparing(LoadCityFcBatchDO::getBatchId).reversed())
                        .collect(Collectors.toList());
                forecastBpCheckDTO.setMdBigDecimalList(collect1.get(0).getloadList());
                forecastBpCheckDTO = getBpFcFeatureData(forecastBpCheckDTO, collect1.get(0));
            }
            if (forecastBpCheckDTO.getEarlyPeak() == null) {
                List<LoadCityFcBatchDO> loadCityFcDOS1 = collect.get(algorithmId + "-" + dateStr);
                if (CollectionUtil.isNotEmpty(loadCityFcDOS1)) {
                    List<LoadCityFcBatchDO> collect1 = loadCityFcDOS1.stream()
                            .sorted(Comparator.comparing(LoadCityFcBatchDO::getBatchId).reversed())
                            .collect(Collectors.toList());
                    //forecastBpCheckDTO.setMdBigDecimalList(collect1.get(0).getloadList());
                    forecastBpCheckDTO = getBpFcFeatureData(forecastBpCheckDTO, collect1.get(0));
                }
            }
            if (CollectionUtils.isEmpty(forecastBpCheckDTO.getFcBigDecimalList())) {
                List<LoadCityFcBatchDO> loadCityFcDOS1 = collect.get(algorithmId + "-" + dateStr);
                if (CollectionUtil.isNotEmpty(loadCityFcDOS1)) {
                    List<LoadCityFcBatchDO> collect1 = loadCityFcDOS1.stream()
                            .sorted(Comparator.comparing(LoadCityFcBatchDO::getBatchId).reversed())
                            .collect(Collectors.toList());
                    forecastBpCheckDTO.setFcBigDecimalList(collect1.get(0).getloadList());
                }
            }
            result.add(forecastBpCheckDTO);
        }
        return result;
    }

    @Override
    public List<ForecastBpCheckDTO> getForecastBpCheckFeatureData(String cityId, String caliberId, Date date, String algorithmId) throws Exception {
        List<ForecastBpCheckDTO> result = new ArrayList<>();
        Date startDate = DateUtils.addDays(date, 1);
        Date endDate = DateUtils.addDays(date, 10);
        String algorithmIds;
        SettingAlgorithmMultiStrategyDTO effectStrategySetting = settingAlgorithmMultiStrategyService.getDailyStrategySetting(cityId, date);
        if (effectStrategySetting != null) {
            algorithmIds = effectStrategySetting.getAlgorithmIds().get(0);
        } else {
            algorithmIds = "820";
        }
        List<WeatherStationFcBasicWgDO> standardStationFcWeatherBasic = weatherStationFcBasicWgService.getStandardStationFcWeatherBasic(cityId, null, startDate, endDate);
        Map<String, List<WeatherStationFcBasicWgDO>> collectWeather = new HashMap<>();
        if (CollectionUtil.isNotEmpty(standardStationFcWeatherBasic)) {
            collectWeather = standardStationFcWeatherBasic.stream()
                    .collect(Collectors.groupingBy(t -> t.getType() + "-" + DateUtils.date2String(t.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR)));
        }
        List<LoadCityFcBatchDO> byAllAlgorithmCondition = loadCityFcBatchService.findByAllAlgorithmCondition(cityId,
                startDate, endDate, caliberId, Arrays.asList(algorithmId, algorithmIds));
        Map<String, List<LoadCityFcBatchDO>> collect = new HashMap<>();
        if (CollectionUtil.isNotEmpty(byAllAlgorithmCondition)) {
            List<LoadCityFcBatchDO> fcList = byAllAlgorithmCondition.stream()
                    .filter(t -> DateUtil.getDate(DateUtils.date2String(t.getUpdatetime(), DateFormatType.SIMPLE_DATE_FORMAT_STR), "yyyy-MM-dd").compareTo(date) == 0)
                    .collect(Collectors.toList());
            collect = fcList.stream().collect(Collectors.groupingBy(t -> DateUtils.date2String(t.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR) + "-" + t.getAlgorithmId()));
        }
        Map<String, List<LoadCityFcBatchDO>> finalCollect = collect;
        Map<String, List<WeatherStationFcBasicWgDO>> finalCollectWeather = collectWeather;
        DateUtil.getListBetweenDay(startDate, endDate).forEach(date1 -> {
            String dateStr = DateUtils.date2String(date1, DateFormatType.SIMPLE_DATE_FORMAT_STR);
            ForecastBpCheckDTO forecastBpCheckDTO = new ForecastBpCheckDTO();
            List<WeatherStationFcBasicWgDO> weatherStationFcBasicWgDOS = finalCollectWeather.get(2 + "-" + dateStr);
            List<WeatherStationFcBasicWgDO> weatherStationFcBasicWgDOS1 = finalCollectWeather.get(3 + "-" + dateStr);
            BigDecimal max = null;
            BigDecimal min = null;
            BigDecimal rainAll = null;
            if (CollectionUtil.isNotEmpty(weatherStationFcBasicWgDOS)) {
                List<BigDecimal> bigDecimals = weatherStationFcBasicWgDOS.get(0).getloadList();
                max = BigDecimalFunctions.listMax(bigDecimals);
                min = BigDecimalFunctions.listMin(bigDecimals);
            }
            if (CollectionUtil.isNotEmpty(weatherStationFcBasicWgDOS1)) {
                List<BigDecimal> bigDecimals = weatherStationFcBasicWgDOS1.get(0).getloadList();
                rainAll = BigDecimalFunctions.listSum(bigDecimals);
            }
            String maxTemp = (max != null) ? max.setScale(0, BigDecimal.ROUND_HALF_UP).toString() : "--";
            forecastBpCheckDTO.setTemp("最高气温<span style=\"color:red\">" + maxTemp + "</span>℃，最低气温" +
                    (min != null ? min.setScale(0, BigDecimal.ROUND_HALF_UP) : "--") + "℃，降水量" + (rainAll != null ? rainAll.setScale(0, BigDecimal.ROUND_HALF_UP) : "--") + "mm");

            List<LoadCityFcBatchDO> loadCityFcBatchDOS = finalCollect.get(dateStr + "-" + algorithmId);
            List<LoadCityFcBatchDO> loadCityFcBatchDOS1 = finalCollect.get(dateStr + "-" + algorithmIds);

            if (CollectionUtil.isNotEmpty(loadCityFcBatchDOS)) {
                try {
                    getBpFcFeatureData(forecastBpCheckDTO, loadCityFcBatchDOS.get(0));
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
            if (forecastBpCheckDTO.getEarlyPeak() == null) {
                if (CollectionUtil.isNotEmpty(loadCityFcBatchDOS1)) {
                    try {
                        getBpFcFeatureData(forecastBpCheckDTO, loadCityFcBatchDOS.get(0));
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
            }
            forecastBpCheckDTO.setDate(date1);
            result.add(forecastBpCheckDTO);
        });
        return result;
    }

    @Override
    public List<BigDecimal> getBpCheckHisData(String cityId, String caliberId, Date date) throws Exception {
        List<BigDecimal> loadCityHisDO = loadCityHisService.findLoadCityHisDO(cityId, date, date, caliberId);
        return loadCityHisDO;
    }

    @Override
    public void saveBpCheckHisData(String cityId, String caliberId, Date date, List<BigDecimal> data, Date targetDate,
                                   String userId) throws Exception {
        LoadCityFcDO loadCityFcDO = new LoadCityFcDO();
        loadCityFcDO.setCityId(cityId);
        loadCityFcDO.setCaliberId(caliberId);
        loadCityFcDO.setAlgorithmId(AlgorithmConstants.MD_ALGORITHM_ID);
        loadCityFcDO.setDate(new java.sql.Date(date.getTime()));
        Integer dateDiff = getDateDiff(targetDate, new Date());
        loadCityFcDO.setCreatetime(new Timestamp(DateUtils.addDays(new Date(), dateDiff).getTime()));
        loadCityFcDO.setUpdatetime(new Timestamp(DateUtils.addDays(new Date(), dateDiff).getTime()));
        loadCityFcDO.setReport(true);
        loadCityFcDO.setReportTime(new Timestamp(DateUtils.addDays(new Date(), dateDiff).getTime()));
        loadCityFcDO.setSucceed(true);
        loadCityFcDO.setRecommend(false);
        loadCityFcDO.setUserId(userId);
        BasePeriodUtils.setAllFiled(loadCityFcDO,
                ColumnUtil.listToMap(data, Constants.LOAD_CURVE_START_WITH_ZERO));
        loadCityFcService.doSaveOrUpdateLoadCityFcDO96(loadCityFcDO);
        loadCityFcBatchService.doSaveData(loadCityFcDO);
    }

    @Override
    public void saveDayBpCheckHisData(Date date, String cityId, String caliberId, String uid) throws Exception {
        Date yesDate = DateUtils.addDays(date, -1);
        Date startDate = DateUtils.addDays(date, 1);
        Date endDate = DateUtils.addDays(date, 10);
        // 获取负荷预测数据
        List<LoadCityFcBatchDO> byAllAlgorithmCondition = loadCityFcBatchService.findByAllAlgorithmCondition(cityId, startDate, endDate, caliberId, Arrays.asList(AlgorithmEnum.BP_CHECK_OPTIMIZE.getId(), AlgorithmConstants.MD_ALGORITHM_ID));
        Map<String, List<LoadCityFcBatchDO>> collect = new HashMap<>();
        if (CollectionUtil.isNotEmpty(byAllAlgorithmCondition)) {
            List<LoadCityFcBatchDO> fcList = byAllAlgorithmCondition.stream()
                    .filter(t -> DateUtil.getDate(DateUtils.date2String(t.getCreatetime(), DateFormatType.SIMPLE_DATE_FORMAT_STR), "yyyy-MM-dd").compareTo(yesDate) == 0)
                    .collect(Collectors.toList());
            collect = fcList.stream()
                    .collect(Collectors.groupingBy(t -> t.getAlgorithmId() + "-" + DateUtils.date2String(t.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR)));
        }
        // 获取可复制的当前批次数据
        List<ForecastBpCheckDTO> forecastBpCheckData = getForecastBpCheckData(cityId, caliberId, date);
        if (CollectionUtil.isNotEmpty(forecastBpCheckData)) {
            for (ForecastBpCheckDTO forecastBpCheckDTO : forecastBpCheckData) {
                if (forecastBpCheckDTO.getFlag()) {
                    String keyBp = AlgorithmEnum.BP_CHECK_OPTIMIZE.getId() + "-" + DateUtils.date2String(forecastBpCheckDTO.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR);
                    String keyMd = AlgorithmConstants.MD_ALGORITHM_ID + "-" + DateUtils.date2String(forecastBpCheckDTO.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR);
                    List<LoadCityFcBatchDO> loadCityFcBatchDOS = collect.get(keyMd);
                    List<BigDecimal> bigDecimals = new ArrayList<>();
                    if (CollectionUtil.isNotEmpty(loadCityFcBatchDOS)) {
                        List<LoadCityFcBatchDO> collect1 = loadCityFcBatchDOS.stream().sorted(Comparator.comparing(LoadCityFcBatchDO::getBatchId).reversed()).collect(Collectors.toList());
                        bigDecimals = collect1.get(0).getloadList();
                    } else {
                        List<LoadCityFcBatchDO> loadCityFcBatchDOS1 = collect.get(keyBp);
                        if (CollectionUtil.isNotEmpty(loadCityFcBatchDOS1)) {
                            List<LoadCityFcBatchDO> collect1 = loadCityFcBatchDOS1.stream().sorted(Comparator.comparing(LoadCityFcBatchDO::getBatchId).reversed()).collect(Collectors.toList());
                            bigDecimals = collect1.get(0).getloadList();
                        }
                    }
                    if (!CollectionUtil.isEmpty(bigDecimals) && bigDecimals.size() == 96) {
                        saveBpCheckHisData(cityId, caliberId, forecastBpCheckDTO.getDate(), bigDecimals, date, uid);
                    }
                }
            }
        }
    }

    private ForecastBpCheckDTO getBpFcFeatureData(ForecastBpCheckDTO forecastBpCheckDTO, LoadCityFcBatchDO loadCityVO) throws Exception {
        List<BigDecimal> earlyPeak = new ArrayList<BigDecimal>();
        List<BigDecimal> noonPeak = new ArrayList<BigDecimal>();
        List<BigDecimal> eveningPeak = new ArrayList<BigDecimal>();
        List<BigDecimal> waistLoad = new ArrayList<BigDecimal>();
        List<BigDecimal> trough = new ArrayList<BigDecimal>();
        List<String> earlyPeakList = getEarlyPeakList();
        List<String> noonPeakList = getNoonPeakList();
        List<String> eveningPeakList = getEveningPeakList();
        List<String> waistLoadList = getWaistLoadList();
        List<String> troughList = getTroughList();
        Map<String, BigDecimal> loadMap = BasePeriodUtils
                .toMap(loadCityVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        for (String column : loadMap.keySet()) {
            BigDecimal load = loadMap.get(column);
            if (null != load) {
                column = column.substring(1);
                if (earlyPeakList != null && earlyPeakList.contains(column)) {
                    earlyPeak.add(load);
                }
                if (noonPeakList != null && noonPeakList.contains(column)) {
                    noonPeak.add(load);
                }
                if (eveningPeakList != null && eveningPeakList.contains(column)) {
                    eveningPeak.add(load);
                }
                if (waistLoadList != null && waistLoadList.contains(column)) {
                    waistLoad.add(load);
                }
                if (troughList != null && troughList.contains(column)) {
                    trough.add(load);
                }
            }
        }
        // 早高峰
        forecastBpCheckDTO.setEarlyPeak(BigDecimalUtils.getMax(earlyPeak));
        // 午高峰
        forecastBpCheckDTO.setNoonPeak(BigDecimalUtils.getMax(noonPeak));
        // 晚高峰
        forecastBpCheckDTO.setEveningPeak(BigDecimalUtils.getMax(eveningPeak));
        // 腰荷
        forecastBpCheckDTO.setWaistLoad(BigDecimalUtils.getMin(waistLoad));
        // 低谷
        forecastBpCheckDTO.setTrough(BigDecimalUtils.getMin(trough));
        return forecastBpCheckDTO;
    }

    private List<String> getEarlyPeakList() throws Exception {
        List<String> times = new ArrayList<String>();
        String peakTimeStr = "08:00~12:00";
        if (StringUtils.isNotEmpty(peakTimeStr)) {
            for (String time : peakTimeStr.split(",")) {
                String[] startAndEndcolumns = time.split("~");
                if (startAndEndcolumns.length == 2) {
                    List<String> columns = ColumnUtil.getColumnsBetween(startAndEndcolumns[0].replace(":", ""),
                            startAndEndcolumns[1].replace(":", ""), Constants.LOAD_CURVE_POINT_NUM,
                            Constants.LOAD_CURVE_START_WITH_ZERO, false);
                    times.addAll(columns);
                }
            }
        }
        return times;
    }

    private List<String> getNoonPeakList() throws Exception {
        List<String> times = new ArrayList<String>();
        String peakTimeStr = "12:15~18:00";
        if (StringUtils.isNotEmpty(peakTimeStr)) {
            for (String time : peakTimeStr.split(",")) {
                String[] startAndEndcolumns = time.split("~");
                if (startAndEndcolumns.length == 2) {
                    List<String> columns = ColumnUtil.getColumnsBetween(startAndEndcolumns[0].replace(":", ""),
                            startAndEndcolumns[1].replace(":", ""), Constants.LOAD_CURVE_POINT_NUM,
                            Constants.LOAD_CURVE_START_WITH_ZERO, false);
                    times.addAll(columns);
                }
            }
        }
        return times;
    }

    private List<String> getEveningPeakList() throws Exception {
        List<String> times = new ArrayList<String>();
        String peakTimeStr = "18:15~23:30";
        if (StringUtils.isNotEmpty(peakTimeStr)) {
            for (String time : peakTimeStr.split(",")) {
                String[] startAndEndcolumns = time.split("~");
                if (startAndEndcolumns.length == 2) {
                    List<String> columns = ColumnUtil.getColumnsBetween(startAndEndcolumns[0].replace(":", ""),
                            startAndEndcolumns[1].replace(":", ""), Constants.LOAD_CURVE_POINT_NUM,
                            Constants.LOAD_CURVE_START_WITH_ZERO, false);
                    times.addAll(columns);
                }
            }
        }
        return times;
    }

    private List<String> getWaistLoadList() throws Exception {
        List<String> times = new ArrayList<String>();
        String peakTimeStr = "11:00~16:00";
        if (StringUtils.isNotEmpty(peakTimeStr)) {
            for (String time : peakTimeStr.split(",")) {
                String[] startAndEndcolumns = time.split("~");
                if (startAndEndcolumns.length == 2) {
                    List<String> columns = ColumnUtil.getColumnsBetween(startAndEndcolumns[0].replace(":", ""),
                            startAndEndcolumns[1].replace(":", ""), Constants.LOAD_CURVE_POINT_NUM,
                            Constants.LOAD_CURVE_START_WITH_ZERO, false);
                    times.addAll(columns);
                }
            }
        }
        return times;
    }

    private List<String> getTroughList() throws Exception {
        List<String> times = new ArrayList<String>();
        String peakTimeStr = "00:00~07:00";
        if (StringUtils.isNotEmpty(peakTimeStr)) {
            for (String time : peakTimeStr.split(",")) {
                String[] startAndEndcolumns = time.split("~");
                if (startAndEndcolumns.length == 2) {
                    List<String> columns = ColumnUtil.getColumnsBetween(startAndEndcolumns[0].replace(":", ""),
                            startAndEndcolumns[1].replace(":", ""), Constants.LOAD_CURVE_POINT_NUM,
                            Constants.LOAD_CURVE_START_WITH_ZERO, false);
                    times.addAll(columns);
                }
            }
        }
        return times;
    }

    private Integer getDateDiff(Date start, Date end) {
        LocalDate localDate = start.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localDate1 = end.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(localDate1, localDate);
        return (int) daysBetween;
    }

    private static final int[] MAX_POINT_INDEXES = {0, 1, 2};

    /**
     * 根据考核点修正曲线
     *
     * @param originLoad
     * @param earlyPeak
     * @param noonPeak
     * @param eveningPeak
     * @param waistLoad
     * @param trough
     * @return
     * @throws BusinessException
     */
    @Override
    public List<BigDecimal> recorrectLoadByAssess(List<BigDecimal> originLoad, BigDecimal earlyPeak, BigDecimal noonPeak,
                                                  BigDecimal eveningPeak, BigDecimal waistLoad, BigDecimal trough) throws BusinessException {

        // 1. 获取原始五个关键点的索引
        int[] fixedIndices = getOriginalAssessIndex(originLoad);
        boolean[] isMaximumFlags = {false, true, false, true, true};
        List<BigDecimal> originalFixedValues = Arrays.asList(originLoad.get(fixedIndices[0]), originLoad.get(fixedIndices[1]),
                originLoad.get(fixedIndices[2]), originLoad.get(fixedIndices[3]), originLoad.get(fixedIndices[4]));

        // 2. 准备固定点数据（参数为null时使用原始值）
        List<BigDecimal> newFixedValues = Arrays.asList(trough != null ? trough : originalFixedValues.get(0),
                earlyPeak != null ? earlyPeak : originalFixedValues.get(1),
                waistLoad != null ? waistLoad : originalFixedValues.get(2),
                noonPeak != null ? noonPeak : originalFixedValues.get(3),
                eveningPeak != null ? eveningPeak : originalFixedValues.get(4));

        // 3. 根据考核点修正曲线
        List<BigDecimal> bigDecimals = DataUtil.segmentedLoadAdjustmentByExtrema(originLoad, originalFixedValues, newFixedValues, isMaximumFlags);
        return bigDecimals;
    }

    /**
     * 核心优化：对最大值点的邻域进行强化放缩，确保不超过最大值
     */
    private List<BigDecimal> dynamicScaleByFixedPoints(List<BigDecimal> curve, int[] fixedIndices, List<BigDecimal> originalFixedValues, List<BigDecimal> newFixedValues) {

        // 1. 分离最大值点（前3个）和最小值点（最后2个）
        List<Integer> maxIndices = new ArrayList<>();
        List<BigDecimal> maxOriginalValues = new ArrayList<>();
        List<BigDecimal> maxNewValues = new ArrayList<>();

        List<Integer> minIndices = new ArrayList<>();
        List<BigDecimal> minOriginalValues = new ArrayList<>();
        List<BigDecimal> minNewValues = new ArrayList<>();

        // 前3个点视为最大值点，最后2个视为最小值点
        for (int i = 0; i < fixedIndices.length; i++) {
            if (i < 3) { // 前3个为最大值
                maxIndices.add(fixedIndices[i]);
                maxOriginalValues.add(originalFixedValues.get(i));
                maxNewValues.add(newFixedValues.get(i));
            } else { // 最后2个为最小值
                minIndices.add(fixedIndices[i]);
                minOriginalValues.add(originalFixedValues.get(i));
                minNewValues.add(newFixedValues.get(i));
            }
        }

        // 2. 分别对最大值点和最小值点按索引排序
        List<FixedPoint> sortedMaxPoints = sortFixedPoints(maxIndices, maxOriginalValues, maxNewValues);
        List<FixedPoint> sortedMinPoints = sortFixedPoints(minIndices, minOriginalValues, minNewValues);

        // 3. 构建完整的区间端点（顺序：0→最小值点→最大值点→95）
        List<Integer> segmentEnds = new ArrayList<>();
        segmentEnds.add(0); // 起始端点

        // 添加排序后的最小值点
        for (FixedPoint minPoint : sortedMinPoints) {
            segmentEnds.add(minPoint.index);
        }

        // 添加排序后的最大值点
        for (FixedPoint maxPoint : sortedMaxPoints) {
            segmentEnds.add(maxPoint.index);
        }

        segmentEnds.add(95); // 结束端点

        // 4. 提取排序后的所有固定点（用于判断固定点位置）
        List<Integer> allSortedIndices = new ArrayList<>();
        for (FixedPoint minPoint : sortedMinPoints) {
            allSortedIndices.add(minPoint.index);
        }
        for (FixedPoint maxPoint : sortedMaxPoints) {
            allSortedIndices.add(maxPoint.index);
        }

        // 提取排序后的所有固定点的原始值和新值
        List<BigDecimal> allSortedOriginalValues = new ArrayList<>();
        List<BigDecimal> allSortedNewValues = new ArrayList<>();
        for (FixedPoint minPoint : sortedMinPoints) {
            allSortedOriginalValues.add(minPoint.originalValue);
            allSortedNewValues.add(minPoint.newValue);
        }
        for (FixedPoint maxPoint : sortedMaxPoints) {
            allSortedOriginalValues.add(maxPoint.originalValue);
            allSortedNewValues.add(maxPoint.newValue);
        }

        List<BigDecimal> scaledCurve = new ArrayList<>(curve);
        int neighborRange = 3; // 最大值点邻域范围

        // 5. 遍历所有区间进行放缩
        for (int i = 0; i < segmentEnds.size() - 1; i++) {
            int startIdx = segmentEnds.get(i);
            int endIdx = segmentEnds.get(i + 1);

            if (startIdx >= endIdx) continue;

            // 获取区间两端的原始值和新值
            BigDecimal originalStart = getValueByIndex(startIdx, allSortedIndices, allSortedOriginalValues, curve);
            BigDecimal newStart = getValueByIndex(startIdx, allSortedIndices, allSortedNewValues, curve);
            BigDecimal originalEnd = getValueByIndex(endIdx, allSortedIndices, allSortedOriginalValues, curve);
            BigDecimal newEnd = getValueByIndex(endIdx, allSortedIndices, allSortedNewValues, curve);

            // 计算区间两端的放缩比例
            BigDecimal scaleStart = calculateScale(originalStart, newStart);
            BigDecimal scaleEnd = calculateScale(originalEnd, newEnd);

            // 遍历区间内的点进行放缩
            for (int j = startIdx + 1; j < endIdx; j++) {
                if (allSortedIndices.contains(j)) continue; // 跳过固定点

                // 线性插值放缩
                BigDecimal positionRatio = BigDecimal.valueOf(j - startIdx).divide(BigDecimal.valueOf(endIdx - startIdx), 10, RoundingMode.HALF_UP);
                BigDecimal currentScale = scaleStart.add(scaleEnd.subtract(scaleStart).multiply(positionRatio));
                BigDecimal scaledValue = curve.get(j).multiply(currentScale).setScale(4, RoundingMode.HALF_UP);

                // 检查最大值邻域（前3个为最大值点）
                scaledValue = checkMaxNeighbor(scaledValue, j, allSortedIndices.stream().mapToInt(Integer::intValue).toArray(), allSortedNewValues, neighborRange);

                scaledCurve.set(j, scaledValue);
            }
        }

        return scaledCurve;
    }

    // 辅助方法：按索引排序固定点
    private List<FixedPoint> sortFixedPoints(List<Integer> indices, List<BigDecimal> originalValues, List<BigDecimal> newValues) {

        List<FixedPoint> points = new ArrayList<>();
        for (int i = 0; i < indices.size(); i++) {
            points.add(new FixedPoint(indices.get(i), originalValues.get(i), newValues.get(i)));
        }
        points.sort((a, b) -> Integer.compare(a.index, b.index));
        return points;
    }

    // 辅助方法：根据索引获取对应的值
    private BigDecimal getValueByIndex(int index, List<Integer> allIndices, List<BigDecimal> allValues, List<BigDecimal> curve) {

        if (index == 0 || index == 95) {
            return curve.get(index); // 端点使用原始值
        }

        int pos = allIndices.indexOf(index);
        if (pos >= 0) {
            return allValues.get(pos);
        }

        return BigDecimal.ZERO; // 理论上不会执行到这里
    }

    // 辅助方法：计算放缩比例
    private BigDecimal calculateScale(BigDecimal original, BigDecimal target) {
        if (original.compareTo(BigDecimal.ZERO) == 0) {
            return target.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ONE : BigDecimal.ZERO;
        }
        return target.divide(original, 10, RoundingMode.HALF_UP);
    }

    // 检查并修正最大值邻域的值
    private BigDecimal checkMaxNeighbor(BigDecimal scaledValue, int currentIdx, int[] sortedFixedIndices, List<BigDecimal> sortedNewValues, int neighborRange) {

        // 前4个点为最大值点（早/午/晚高峰、腰荷）
        for (int i = 0; i < Math.min(4, sortedFixedIndices.length); i++) {
            int maxPointIdx = sortedFixedIndices[i];
            BigDecimal maxValue = sortedNewValues.get(i);

            if (currentIdx >= maxPointIdx - neighborRange && currentIdx <= maxPointIdx + neighborRange) {
                return scaledValue.min(maxValue); // 确保不超过最大值
            }
        }
        return scaledValue;
    }

    /**
     * 最终锁定：确保最大值点的邻域内无点超过最大值
     */
    private void lockMaxNeighbors(List<BigDecimal> curve, int[] fixedIndices, List<BigDecimal> newFixedValues) {

        int neighborRange = 3;
        for (int maxIdxInFixed : MAX_POINT_INDEXES) {
            int maxPointIdx = fixedIndices[maxIdxInFixed];
            BigDecimal maxValue = newFixedValues.get(maxIdxInFixed);

            // 检查邻域内的点，强制不超过最大值
            int start = Math.max(0, maxPointIdx - neighborRange);
            int end = Math.min(curve.size() - 1, maxPointIdx + neighborRange);

            for (int i = start; i <= end; i++) {
                if (curve.get(i).compareTo(maxValue) > 0) {
                    curve.set(i, maxValue);
                }
            }
        }
    }

    private List<BigDecimal> smoothWithFixedPoints(List<BigDecimal> curve, int[] fixedIndices, int windowSize) {

        List<BigDecimal> smoothed = new ArrayList<>(curve);
        List<Integer> fixedIndexList = Arrays.stream(fixedIndices).boxed().collect(Collectors.toList());

        for (int i = 0; i < curve.size(); i++) {
            if (fixedIndexList.contains(i)) continue;

            int start = Math.max(0, i - 1);
            int end = Math.min(curve.size() - 1, i + 1);
            BigDecimal sum = BigDecimal.ZERO;
            int count = 0;
            for (int j = start; j <= end; j++) {
                sum = sum.add(curve.get(j));
                count++;
            }
            smoothed.set(i, sum.divide(BigDecimal.valueOf(count), 4, RoundingMode.HALF_UP));
        }
        return smoothed;
    }

    private static class FixedPoint {
        int index;
        BigDecimal originalValue;
        BigDecimal newValue;

        public FixedPoint(int index, BigDecimal originalValue, BigDecimal newValue) {
            this.index = index;
            this.originalValue = originalValue;
            this.newValue = newValue;
        }
    }
}
