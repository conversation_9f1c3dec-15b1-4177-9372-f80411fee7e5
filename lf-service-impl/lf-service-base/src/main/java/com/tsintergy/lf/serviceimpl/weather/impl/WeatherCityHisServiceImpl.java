
package com.tsintergy.lf.serviceimpl.weather.impl;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.enums.CityStationEnum;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DataUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherNewEnum;
import com.tsintergy.lf.serviceapi.base.load.dto.CityValueDTO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.weather.api.*;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherNameDTO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.*;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityHisDAO;
import net.sf.cglib.beans.BeanMap;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.MATH_CONTEXT;

/**
 * <AUTHOR>
 * @version $Id: WeatherCityHisServiceImpl.java, v 0.1 2018-01-31 11:00:09 tao Exp $$
 */
@Service("weatherCityHisService")
public class WeatherCityHisServiceImpl extends AbstractBaesWeatherServiceImpl implements WeatherCityHisService {

    private static final Logger logger = LogManager.getLogger(WeatherCityHisServiceImpl.class);

    @Autowired
    private StatisticsSynthesizeWeatherCityDayHisService statisticsSynthesizeWeatherCityDayHisService;

    @Autowired
    CityService cityService;

    @Autowired
    WeatherCityHisDAO weatherCityHisDAO;

    @Autowired
    private BaseWeatherStationHisService baseWeatherStationHisService;

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Autowired
    private BaseWeatherStationWgService baseWeatherStationWgService;

    @Autowired
    private WeatherStationHisClctWgService weatherStationHisClctWgService;

    @Autowired
    private WeatherStationHisBasicWgService weatherStationHisBasicWgService;

    @Autowired
    private SettingSystemService settingSystemService;

    @Override
    protected List<? extends BaseWeatherDO> findWeatherCityVO(String cityId, Integer type, Date startDate, Date endDate)
            throws Exception {
        return weatherCityHisDAO.findWeatherCityHisDO(cityId, type, startDate, endDate);
    }

    @Override
    protected List<? extends BaseWeatherDO> findStatisticsSynthesizeWeatherCityDayVO(String cityId, Integer type,
                                                                                     Date startDate, Date endDate) throws Exception {
        return statisticsSynthesizeWeatherCityDayHisService
                .findStatisticsSynthesizeWeatherCityDayHisDO(cityId, type, startDate, endDate);
    }

    @Override
    public DataPackage queryWeatherCityHisDO(DBQueryParam param) throws Exception {
        try {
            return weatherCityHisDAO.query(param);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public WeatherCityHisDO doCreate(WeatherCityHisDO vo) throws Exception {
        try {
            return weatherCityHisDAO.createAndFlush(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public void doRemoveWeatherCityHisDO(WeatherCityHisDO vo) throws Exception {
        try {
            weatherCityHisDAO.removeAndFlush(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public void doRemoveWeatherCityHisDOByPK(Serializable pk) throws Exception {
        try {
            weatherCityHisDAO.removeByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public WeatherCityHisDO doUpdateWeatherCityHisDO(WeatherCityHisDO vo) throws Exception {
        try {
            return (WeatherCityHisDO) weatherCityHisDAO.update(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public WeatherCityHisDO findWeatherCityHisDOByPk(Serializable pk) throws Exception {
        try {
            return (WeatherCityHisDO) weatherCityHisDAO.findByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public List<WeatherCityHisDO> findWeatherCityHisDOs(String cityId, Integer type, Date startDate, Date endDate)
        throws Exception {
        try {
            return weatherCityHisDAO.findWeatherCityHisDO(cityId, type, startDate, endDate);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public List<CityValueDTO> find24WeahterCityByDateAndCityIds(Date date, List<String> cityIds, Integer weatherType)
        throws Exception {
        List<CityValueDTO> cityValueDTOS = new ArrayList<CityValueDTO>();
        try {
            List<WeatherCityHisDO> weatherCityHisVOS = weatherCityHisDAO
                .getWeatherCityHisDOS(cityIds, weatherType, date, date);
            if (weatherCityHisVOS.size() < 1) {
                throw TsieExceptionUtils.newBusinessException("T706");
            } else {
                for (WeatherCityHisDO weatherCityHisVO : weatherCityHisVOS) {
                    CityValueDTO cityValueDTO = new CityValueDTO();
                    cityValueDTO.setCityId(weatherCityHisVO.getCityId());
                    cityValueDTO.setCity(cityService.findCityById(weatherCityHisVO.getCityId()).getCity());
                    cityValueDTO.setValue(
                        BasePeriodUtils.toList(weatherCityHisVO, 24, Constants.WEATHER_CURVE_START_WITH_ZERO));
                    cityValueDTOS.add(cityValueDTO);
                }
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("01C20180003");
        }
        return cityValueDTOS;
    }

    @Override
    public List<WeatherDTO> findWeatherCityHisDTOs(String cityId, Integer type, Date startDate, Date endDate)
        throws Exception {

        return super.findWeatherDTO(cityId, type, startDate, endDate);
    }


    @Override
    public void doInsertOrUpdate(WeatherCityHisDO weatherCityHisVO) throws Exception {
        if (weatherCityHisVO.getDate() == null || weatherCityHisVO.getCityId() == null
            || weatherCityHisVO.getType() == null) {
            throw new BusinessException("01C20180008", "");
        }
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly()
            .where(QueryOp.DateEqualTo, "date", weatherCityHisVO.getDate())
            .where(QueryOp.StringEqualTo, "cityId", weatherCityHisVO.getCityId())
            .where(QueryOp.NumberEqualTo, "type", weatherCityHisVO.getType());
        List<WeatherCityHisDO> weatherCityHisV0List = null;
        try {
            weatherCityHisV0List = this.queryWeatherCityHisDO(dbQueryParamBuilder.build()).getDatas();
        } catch (Exception e) {
            logger.error("select weatherCityHisVO 异常", e);
            throw new BusinessException("", "select weatherCityHisVO 异常", e);
        }
        if (weatherCityHisV0List != null && weatherCityHisV0List.size() > 0) {
            this.weatherCityHisDAO.getSession().flush();
            this.weatherCityHisDAO.getSession().clear();
            weatherCityHisVO.setId(weatherCityHisV0List.get(0).getId());
            weatherCityHisVO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            try {
                this.doUpdateWeatherCityHisDO(weatherCityHisVO);
            } catch (Exception e) {
                logger.error("update weatherCityHisVO 异常", e);
                throw new BusinessException("", "update weatherCityHisVO 异常", e);
            }
            return;
        }
        try {
            this.doCreate(weatherCityHisVO);
        } catch (Exception e) {
            logger.error("create weatherCityHisVO 异常", e);
            throw new BusinessException("", "create weatherCityHisVO 异常", e);
        }
    }

    @Override
    public void doSaveAllData(WeatherCityHisDO weatherCityHisDO) {
        weatherCityHisDAO.save(weatherCityHisDO);
    }


    @Override
    public List<WeatherCityHisDO> findWeatherCityHisDOSBySQLDate(String cityId, Integer type, java.sql.Date startDate,
        java.sql.Date endDate) throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly()
            .where(QueryOp.StringEqualTo, "cityId", cityId)
            .where(QueryOp.NumberEqualTo, "type", type)
            .where(QueryOp.DateNoLessThan, "date", startDate)
            .where(QueryOp.DateNoMoreThan, "date", endDate);
        if (type != null) {
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "type", type);
        }
        return this.queryWeatherCityHisDO(dbQueryParamBuilder.build()).getDatas();
    }

    @Override
    public List<WeatherCityHisDO> findWeatherCityHisDOSByCityIds(List<String> cityIds, Integer type, Date date)
        throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create()
            .where(QueryOp.DateEqualTo, "date", new java.sql.Date(date.getTime()))
            .where(QueryOp.StringIsIn, "cityId", cityIds);
        if (type != null) {
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "type", type);
        }
        return weatherCityHisDAO.query(dbQueryParamBuilder.build()).getDatas();
    }

    @Override
    public WeatherCityHisDO findWeatherCityHisDO(String cityIds, Integer type, Date date) throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create()
            .where(QueryOp.DateEqualTo, "date", new java.sql.Date(date.getTime()))
            .where(QueryOp.StringEqualTo, "cityId", cityIds);
        if (type != null) {
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "type", type);
        }
        List<WeatherCityHisDO> datas = weatherCityHisDAO.query(dbQueryParamBuilder.build()).getDatas();
        if (datas != null && datas.size() != 0) {
            return datas.get(0);
        }
        return null;
    }

    @Override
    public List<WeatherCityHisDO> findWeatherByDates(String cityId, Integer type, List<Date> dateList)
        throws Exception {
        return weatherCityHisDAO.getWeatherCityHisDOSByDates(dateList, cityId, type);
    }

    @Override
    public List<WeatherCityHisDO> findAllWeather() throws Exception {
        return null;
    }

    @Override
    public List<WeatherCityHisDO> getWeatherCityHisDOs(List<String> cityIdList, Integer type, Date startDate,
        Date endDate) throws Exception {
        return weatherCityHisDAO.getWeatherCityHisDOs(cityIdList, type, startDate, endDate);
    }


    @Override
    public List<WeatherNameDTO> findAllByDateAndCityId(String cityId, Date targetDate) throws Exception {
        List<WeatherNameDTO> weatherNameDTOS = new ArrayList<WeatherNameDTO>(4);
        List<WeatherCityHisDO> weatherCityHisVOS = weatherCityHisDAO
            .findWeatherCityHisDO(cityId, null, targetDate, targetDate);
        if (weatherCityHisVOS.size() < 1) {
            return null;
        }
        for (WeatherCityHisDO weatherCityHisVO : weatherCityHisVOS) {
            WeatherNameDTO weatherNameDTO = new WeatherNameDTO();
            weatherNameDTO.setName(WeatherEnum.getValueByName(weatherCityHisVO.getType()));
            weatherNameDTO.setWeather(BasePeriodUtils
                .toList(weatherCityHisVO, Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO));
            weatherNameDTOS.add(weatherNameDTO);
        }
        return weatherNameDTOS;
    }

    @Override
    public List<BigDecimal> find96WeatherCityHisValue(Date date, String cityId, Integer type) throws Exception {
        cityId = cityService.findWeatherCityId(cityId);
        List<WeatherCityHisDO> weatherCityHisVOS = this
            .findWeatherCityHisDOSBySQLDate(cityId, type, new java.sql.Date(date.getTime()),
                new java.sql.Date(date.getTime()));
        if (!org.springframework.util.CollectionUtils.isEmpty(weatherCityHisVOS)) {
            return BasePeriodUtils.toList(weatherCityHisVOS.get(0), Constants.WEATHER_CURVE_POINT_NUM,
                Constants.WEATHER_CURVE_START_WITH_ZERO);
        }
        return null;
    }

    @Override
    public List<BigDecimal> findWeatherCityHisValueList(String cityId, Date date, Integer weatherType)
        throws Exception {
        List<WeatherCityHisDO> weatherList = findWeatherCityHisDOSBySQLDate(cityId, weatherType,
            new java.sql.Date(date.getTime()), new java.sql.Date(date.getTime()));
        if (!org.springframework.util.CollectionUtils.isEmpty(weatherList)) {
            return BasePeriodUtils
                .toList(weatherList.get(0), Constants.LOAD_CURVE_POINT_NUM,
                    Constants.LOAD_CURVE_START_WITH_ZERO);
        }
        return null;
    }

    @Override
    public List<BigDecimal> findWeatherCityHisValueList(String cityId, Date startDate, Date endDate,
        Integer weatherType) throws Exception {
        cityId = cityService.findWeatherCityId(cityId);
        List<WeatherCityHisDO> weatherCityHisClctDOS = null;
        try {
            weatherCityHisClctDOS = this
                .findWeatherCityHisDOs(cityId, weatherType, startDate, endDate);
        } catch (Exception e) {
            logger.error("历史气象查询异常...", e);
            throw new BusinessException("01C20180003", "");
        }
        List<BigDecimal> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(weatherCityHisClctDOS)) {
            Map<Date, WeatherCityHisDO> weatherMap = weatherCityHisClctDOS.stream()
                .collect(Collectors.toMap(WeatherCityHisDO::getDate, Function.identity()));
            List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
            List nullList = new ArrayList(96);
            for (int i = 0; i < 96; i++) {
                nullList.add(null);
            }
            dateList.forEach(date -> {
                WeatherCityHisDO weatherCityHisClctDO = weatherMap.get(date);
                if (weatherCityHisClctDO == null) {
                    result.addAll(nullList);
                    return;
                }

                result.addAll(BasePeriodUtils
                    .toList(weatherCityHisClctDO, Constants.LOAD_CURVE_POINT_NUM,
                        Constants.LOAD_CURVE_START_WITH_ZERO));
            });

        }
        return result;
    }


    @Override
    public List<BigDecimal> findListPoint(Date startDate, Date endDate, String cityId, Integer type) throws Exception {
        List<BigDecimal> points = new ArrayList<>();
        List<Date> dates = DateUtil.getListBetweenDay(startDate, endDate);
        for (Date date : dates) {
            List<BigDecimal> value = find96WeatherCityHisValue(date, cityId, type);
            if (value != null && value.size() > 0) {
                points.addAll(value);
            } else {
                points.addAll(DataUtil.getNullList(96));
            }
        }
        return points;
    }

    @Override
    public void doClctWeatherStationToCity(List<String> dates, Map<String, String>cityAndStationMap) throws Exception {

        Set<String> strings = cityAndStationMap.keySet();
        for (String dateStr : dates) {
            Date date = DateUtils.string2Date(dateStr, DateFormatType.SIMPLE_DATE_FORMAT_STR);
            LambdaQueryWrapper<BaseWeatherStationHisDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BaseWeatherStationHisDO::getDate, date);
            List<BaseWeatherStationHisDO> list = baseWeatherStationHisService.list(queryWrapper);

            Map<String, List<BaseWeatherStationHisDO>> dayHisStationMap = list.stream()
                .collect(Collectors.groupingBy(BaseWeatherStationHisDO::getStationId));

            Map<String, List<BaseWeatherStationHisDO>> dayHisCitynMap = list.stream()
                .collect(Collectors.groupingBy(BaseWeatherStationHisDO::getCityId));

            for (String stationId : strings) {
                List<BaseWeatherStationHisDO> baseWeatherStationHisDOS = dayHisStationMap.get(stationId);
                String cityId = cityAndStationMap.get(stationId);
                //如果是空证明里面不包含指定的站点任何数据
                if (org.springframework.util.CollectionUtils.isEmpty(baseWeatherStationHisDOS)) {
                    //表名所需要的指定区域站点的数据，只要有一个不为空就可以求到平均数据
                    List<BaseWeatherStationHisDO> baseWeatherStationHisDOSByCity = dayHisCitynMap.get(cityId);
                    if (!org.springframework.util.CollectionUtils.isEmpty(baseWeatherStationHisDOSByCity)) {
                        Map<Integer, List<BaseWeatherStationHisDO>> collect = baseWeatherStationHisDOSByCity.stream()
                            .collect(Collectors.groupingBy(BaseWeatherStationHisDO::getType));
                        for (Map.Entry<Integer, List<BaseWeatherStationHisDO>> typeEntry : collect.entrySet()) {
                            List<BaseWeatherStationHisDO> entryValue = typeEntry.getValue();
                            Map<String, BigDecimal> avg = getAvgByWeatherStationDatas(entryValue);
                            saveOrUpdateWeatherCityHisBasic(cityId, date, typeEntry.getKey(), avg);
                        }
                    }
                    //单个指定站点数据存在，但没有分类型。
                } else {
                    Map<Integer, List<BaseWeatherStationHisDO>> typeBaseWeatherStationHisDOS = baseWeatherStationHisDOS
                        .stream().collect(Collectors.groupingBy(BaseWeatherStationHisDO::getType));
                    for (WeatherNewEnum weatherNewEnum : WeatherNewEnum.values()) {

                        Integer type = weatherNewEnum.getType();
                        List<BaseWeatherStationHisDO> typeBaseWeatherStationHisDO = typeBaseWeatherStationHisDOS
                            .get(type);

                        if (org.springframework.util.CollectionUtils.isEmpty(typeBaseWeatherStationHisDO)) {

                            List<BaseWeatherStationHisDO> baseWeatherStationHisDOSByCity = dayHisCitynMap.get(cityId);
                            //只保留所需类型。
                            baseWeatherStationHisDOS = baseWeatherStationHisDOS.stream().
                                filter(t -> weatherNewEnum.getType().equals(t.getType())).collect(Collectors.toList());

                            if (!org.springframework.util.CollectionUtils.isEmpty(baseWeatherStationHisDOSByCity)) {
                                Map<Integer, List<BaseWeatherStationHisDO>> collect = baseWeatherStationHisDOSByCity
                                    .stream()
                                    .collect(Collectors.groupingBy(BaseWeatherStationHisDO::getType));
                                for (Map.Entry<Integer, List<BaseWeatherStationHisDO>> typeEntry : collect.entrySet()) {
                                    List<BaseWeatherStationHisDO> entryValue = typeEntry.getValue();
                                    Map<String, BigDecimal> avg = getAvgByWeatherStationDatas(entryValue);
                                    saveOrUpdateWeatherCityHisBasic(cityId, date, typeEntry.getKey(), avg);
                                }
                            }

                        } else {
                            BaseWeatherStationHisDO baseWeatherStationHisDO = typeBaseWeatherStationHisDO.get(0);
                            List<BigDecimal> decimalList = BasePeriodUtils.toList(baseWeatherStationHisDO, 96, true);

                            List<BaseWeatherStationHisDO> cityBaseWeatherStationHisDOS = dayHisCitynMap.get(cityId);
                            List<BaseWeatherStationHisDO> collect = cityBaseWeatherStationHisDOS.stream()
                                .filter(t -> /*!stationId.equals(t.getStationId()) &&*/ t.getType()
                                    .equals(weatherNewEnum.getType())).collect(Collectors.toList());

                            for (int i = 0; i < 96; i++) {
                                BigDecimal bigDecimal = decimalList.get(i);
//                                if (bigDecimal == null || decimalList.get(i).compareTo(new BigDecimal("999.9")) == 0) {
                                    List<BigDecimal> decimals = new ArrayList<>();
                                    for (BaseWeatherStationHisDO weatherStationHisDO : collect) {
                                        BigDecimal value = BasePeriodUtils.toList(weatherStationHisDO, 96, true)
                                            .get(i);
                                        if (value != null && value.compareTo(new BigDecimal("999.9")) != 0) {
                                            decimals.add(value);
                                        }
                                    }
                                    if (decimals.isEmpty()) {
                                        decimalList.set(i, null);
                                    } else {
                                        //温度不会是整数0；
                                        List<BigDecimal> bigDecimals = decimals.stream()
                                            .filter(src -> src.compareTo(BigDecimal.ZERO) != 0).collect(
                                                Collectors.toList());
                                        if (bigDecimals.isEmpty()) {
                                            decimalList.set(i, null);
                                        } else {
                                            BigDecimal avg = BigDecimalUtils.listAvg(bigDecimals);
                                            decimalList.set(i, avg);
                                        }
                                    }
//                                }
                            }
                            extracted(decimalList);
                            Map<String, BigDecimal> decimalMap = ColumnUtil.listToMap(decimalList, true);
                            saveOrUpdateWeatherCityHisBasic(cityId, date, type, decimalMap);
                        }
                    }
                }
            }
        }


    }


    public void extracted(List<BigDecimal> decimalList) {
        for (int i = 0; i < decimalList.size(); i++) {
            if (decimalList.get(i) != null
                    && decimalList.get(i).compareTo(new BigDecimal("999.9")) == 0) {
                logger.info("第" + i + "个点的值为999.9,故设置为空");
                decimalList.set(i, null);
                //直接舍弃整点中间的部分
            }
        }
        ColumnUtil.supplimentPoit(decimalList);
    }


    public void saveOrUpdateWeatherCityHisBasic(String cityId, Date date, int type, Map<String, BigDecimal> map)
        throws Exception {

        if (map.get("t0000") != null) {
            logger.info("数据中包含t0000点数据，处理昨日数据");
            Date yesterday = DateUtils.addDays(date, -1);
            Map<String, BigDecimal> yesterdayHashMap = new HashMap<>();
            List<WeatherCityHisDO> yesterDayData = weatherCityHisService
                .findWeatherCityHisDOs(cityId, type, yesterday, yesterday);

            if (org.springframework.util.CollectionUtils.isEmpty(yesterDayData)) {
                yesterdayHashMap.put("t2400", map.get("t0000"));
            } else {
                WeatherCityHisDO weatherCityHisDO = yesterDayData.get(0);
                BigDecimal t2300 = weatherCityHisDO.getT2300();
                BigDecimal t0000 = map.get("t0000");
                if (t0000 != null && t2300 != null) {
                    BigDecimal divide = t0000.subtract(t2300).divide(new BigDecimal(4), 2, BigDecimal.ROUND_HALF_UP);
                    yesterdayHashMap.put("t2315", t2300.add(divide.multiply(new BigDecimal(1))));
                    yesterdayHashMap.put("t2330", t2300.add(divide.multiply(new BigDecimal(2))));
                    yesterdayHashMap.put("t2345", t2300.add(divide.multiply(new BigDecimal(3))));
                    yesterdayHashMap.put("t2400", map.get("t0000"));
                }
            }
            saveOrUpdateWeatherCityHisBasic(cityId, yesterday, type, yesterdayHashMap);
            System.out.println("=================================入库日期："+date);
            logger.info("数据中包含t0000点数据，昨日数据处理完毕");
        }


        List<WeatherCityHisDO> weatherCityHisDOs = weatherCityHisService
            .findWeatherCityHisDOs(cityId, type, date, date);
        if (org.springframework.util.CollectionUtils.isEmpty(weatherCityHisDOs)) {
            WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
            weatherCityHisDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            weatherCityHisDO.setDate(new java.sql.Date(date.getTime()));
            weatherCityHisDO.setCityId(cityId);
            weatherCityHisDO.setType(type);
            weatherCityHisDO.setId(UUID.randomUUID().toString().replace("-", "").toLowerCase());
            BeanMap beanMap = BeanMap.create(weatherCityHisDO);
            beanMap.putAll(map);
            weatherCityHisDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            weatherCityHisService.doCreate(weatherCityHisDO);
        } else {
            WeatherCityHisDO weatherCityHisDO = weatherCityHisDOs.get(0);
            BeanMap beanMap = BeanMap.create(weatherCityHisDO);
            beanMap.putAll(map);
            weatherCityHisDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            weatherCityHisService.doUpdateWeatherCityHisDO(weatherCityHisDO);
        }

    }



    public Map<String, BigDecimal> getAvgByWeatherStationDatas(List<BaseWeatherStationHisDO> value) {
        List<Map<String, BigDecimal>> list = new ArrayList<>();
        for (BaseWeatherStationHisDO baseWeatherStationHisDO : value) {
            Map<String, BigDecimal> decimalMap = BasePeriodUtils
                .toMap(baseWeatherStationHisDO, 96, true);
            list.add(decimalMap);
        }
        List<String> columns = ColumnUtil.getColumns(96, true, true);

        Map<String, BigDecimal> result = new HashMap<>();
        for (String column : columns) {
            int countSize = 0;
            BigDecimal count = null;
            for (Map<String, BigDecimal> map : list) {
                if (map.get(column) != null) {
                    if (count == null) {
                        count = BigDecimal.ZERO;
                    }
                    count = count.add(map.get(column));
                    countSize++;
                }
            }
            if (countSize == 0) {
                result.put(column, null);
            } else {
                BigDecimal avg = count.divide(new BigDecimal(countSize), 2, BigDecimal.ROUND_HALF_UP);
                result.put(column, avg);
            }
        }
        return result;
    }


    @Override
    public void wgStationHisWeatherToCity(String cityId, Integer type, Date startDate, Date endDate) throws Exception {

        //将格点气象站24点数据转化为96点
        List<String> stationWgIdList;
        LambdaQueryWrapper<BaseWeatherStationWgDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BaseWeatherStationWgDO::getCityId, cityId);
        List<BaseWeatherStationWgDO> baseWeatherStationWgDOS = baseWeatherStationWgService.list(queryWrapper);
        stationWgIdList = baseWeatherStationWgDOS.stream().map(BaseWeatherStationWgDO::getId).collect(Collectors.toList());
        List<Date[]> dates = DateUtil.splitDateRange(startDate, endDate, 10);
        for (Date[] dateArr : dates) {
            weatherStationHisClctWgService.clctToBasic(dateArr[0], dateArr[1], stationWgIdList);
        }

        //查询格点气象站96点数据
        List<WeatherStationHisBasicWgDO> weatherStationHisBasicWgDOS = weatherStationHisBasicWgService.findByCondition(stationWgIdList, type, startDate, endDate);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(weatherStationHisBasicWgDOS)) {
            logger.error("统计格点气象失败，格点气象无数据");
            return;
        }
        Map<String, List<WeatherStationHisBasicWgDO>> weatherStationHisBasicWgDOSMap =
                weatherStationHisBasicWgDOS.stream().collect(
                        Collectors.groupingBy(e ->
                                e.getType() + "-" + e.getDate().getTime()
                        ));

        //通过格点气象站96点数据计算城市气象
        List<WeatherCityHisDO> resultList = new ArrayList<>();
        for (Map.Entry<String, List<WeatherStationHisBasicWgDO>> entry : weatherStationHisBasicWgDOSMap.entrySet()) {
            List<BigDecimal> valueList = new ArrayList<>();
            for (int i = 0; i <= 95; i++) {
                List<BigDecimal> dataList = new ArrayList<>();
                for (WeatherStationHisBasicWgDO basicWgDO : entry.getValue()) {
                    if (basicWgDO.getloadList().get(i) != null) {
                        dataList.add(basicWgDO.getloadList().get(i));
                    }
                }
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(dataList)) {
                    BigDecimal bigDecimal = BigDecimalFunctions.listAvg(dataList);
                    valueList.add(bigDecimal);
                } else {
                    valueList.add(null);
                }
            }

            WeatherStationHisBasicWgDO weatherStationHisBasicWgDO = entry.getValue().get(0);
            WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
            weatherCityHisDO.setCityId(cityId);
            weatherCityHisDO.setType(weatherStationHisBasicWgDO.getType());
            weatherCityHisDO.setDate(weatherStationHisBasicWgDO.getDate());
            //通过两侧不为null的数据，计算差值补点
            extracted(valueList);
            Map<String, BigDecimal> stringBigDecimalMap = ColumnUtil.listToMap(valueList, Constants.LOAD_CURVE_START_WITH_ZERO);
            BasePeriodUtils.setAllFiled(weatherCityHisDO, stringBigDecimalMap);
            resultList.add(weatherCityHisDO);
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(resultList)) {
            try {
                for (WeatherCityHisDO weatherCityHisDO : resultList) {
                    weatherCityHisService.doInsertOrUpdate(weatherCityHisDO);
                }
            } catch (Exception e) {
                logger.error("保存气象站历史数据出错了", e);
            }
        }
    }

    @Override
    public void doHisCityWeatherToProvince(Date startDate, Date endDate) throws Exception {
        String str = settingSystemService.getValue("province_weather_express");
        Map map = (Map) JSON.parse(str);
        Set set = map.keySet();

        while (startDate.before(DateUtils.addDays(endDate, 1))) {
            List<WeatherCityHisDO> weatherCityHisDOs = weatherCityHisService.findWeatherCityHisDOs(null, null, startDate, startDate);
            if (org.springframework.util.CollectionUtils.isEmpty(weatherCityHisDOs)) {
                startDate = DateUtils.addDays(startDate, 1);
                continue;
            }
            List<BigDecimal> tem = zero96List();
            List<BigDecimal> hum = zero96List();
            List<BigDecimal> rain = zero96List();
            List<BigDecimal> wind = zero96List();
            weatherCityHisDOs = weatherCityHisDOs.stream().filter(t -> set.contains(t.getCityId())).collect(Collectors.toList());
            Map<Integer, List<WeatherCityHisDO>> typeWeatherMap = weatherCityHisDOs.stream()
                    .collect(Collectors.groupingBy(WeatherCityHisDO::getType));

            for (Map.Entry<Integer, List<WeatherCityHisDO>> entry : typeWeatherMap.entrySet()) {
                Integer type = entry.getKey();
                List<WeatherCityHisDO> value = entry.getValue();
                //需要9个地市只有8个地市的数据,直接不计算气象类型
                if (value.size() != set.size()) {

                } else if (value.size() == set.size()) {
                    List<List<BigDecimal>> lists = new ArrayList<>();
                    // 用于记录每个地市的比率
                    List<BigDecimal> ratioList = new ArrayList<>();
                    for (WeatherCityHisDO weatherCityHisDO : value) {
                        List<BigDecimal> bigDecimals = BasePeriodUtils.toList(weatherCityHisDO, 96, Constants.WEATHER_CURVE_START_WITH_ZERO);
                        String ratioStr = map.get(weatherCityHisDO.getCityId()).toString();
                        BigDecimal ratio = new BigDecimal(ratioStr);
                        ratioList.add(ratio);
                        List<BigDecimal> weather96RatioList = zero96List();
                        for (int i = 0; i < bigDecimals.size(); i++) {
                            BigDecimal bigDecimal = bigDecimals.get(i);
                            if (BigDecimalUtils.isValidValue(bigDecimal)) {
                                BigDecimal scaledValue = bigDecimal.multiply(ratio);
                                weather96RatioList.set(i, scaledValue);
                            }
                        }
                        lists.add(weather96RatioList);
                    }

                    List<BigDecimal> decimalList = zero96List();
                    for (int i = 0; i < 96; i++) {
                        BigDecimal sum = BigDecimal.ZERO;
                        BigDecimal totalRatio = BigDecimal.ZERO;
                        for (int j = 0; j < lists.size(); j++) {
                            List<BigDecimal> cityLoads = lists.get(j);
                            BigDecimal decimal = cityLoads.get(i);
                            if (decimal != null) {
                                sum = sum.add(decimal);
                                totalRatio = totalRatio.add(ratioList.get(j)); // 使用已知的比率
                            }
                        }
                        BigDecimal curValue = totalRatio.equals(BigDecimal.ZERO) ? null : sum.divide(totalRatio, MATH_CONTEXT);
                        decimalList.set(i, curValue);
                    }

                    switch (type) {
                        case 1:
                            hum = decimalList;
                            break;
                        case 2:
                            tem = decimalList;
                            break;
                        case 4:
                            wind = decimalList;
                            break;
                        case 3:
                            rain = decimalList;
                            break;
                    }
                }
            }

            WeatherCityHisDO cityHisDO = new WeatherCityHisDO();
            Map<String, BigDecimal> decimalMap = ColumnUtil.listToMap(tem, Constants.LOAD_CURVE_START_WITH_ZERO);
            BasePeriodUtils.setAllFiled(cityHisDO, decimalMap);
            cityHisDO.setType(2);
            cityHisDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            cityHisDO.setCityId("1");
            cityHisDO.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
            cityHisDO.setDate(new java.sql.Date(startDate.getTime()));
            weatherCityHisService.doInsertOrUpdate(cityHisDO);

            WeatherCityHisDO cityHisDO1 = new WeatherCityHisDO();
            Map<String, BigDecimal> decimalMap1 = ColumnUtil.listToMap(hum, Constants.LOAD_CURVE_START_WITH_ZERO);
            BasePeriodUtils.setAllFiled(cityHisDO1, decimalMap1);
            cityHisDO1.setType(1);
            cityHisDO1.setCreatetime(new Timestamp(System.currentTimeMillis()));
            cityHisDO1.setCityId("1");
            cityHisDO1.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
            cityHisDO1.setDate(new java.sql.Date(startDate.getTime()));
            weatherCityHisService.doInsertOrUpdate(cityHisDO1);


            WeatherCityHisDO cityHisDO2 = new WeatherCityHisDO();
            Map<String, BigDecimal> decimalMap2 = ColumnUtil.listToMap(rain, Constants.LOAD_CURVE_START_WITH_ZERO);
            BasePeriodUtils.setAllFiled(cityHisDO2, decimalMap2);
            cityHisDO2.setType(3);
            cityHisDO2.setCreatetime(new Timestamp(System.currentTimeMillis()));
            cityHisDO2.setCityId("1");
            cityHisDO2.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
            cityHisDO2.setDate(new java.sql.Date(startDate.getTime()));
            weatherCityHisService.doInsertOrUpdate(cityHisDO2);


            WeatherCityHisDO cityHisDO4 = new WeatherCityHisDO();
            Map<String, BigDecimal> decimalMap4 = ColumnUtil.listToMap(wind, Constants.LOAD_CURVE_START_WITH_ZERO);
            BasePeriodUtils.setAllFiled(cityHisDO4, decimalMap4);
            cityHisDO4.setType(4);
            cityHisDO4.setCreatetime(new Timestamp(System.currentTimeMillis()));
            cityHisDO4.setCityId("1");
            cityHisDO4.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
            cityHisDO4.setDate(new java.sql.Date(startDate.getTime()));
            weatherCityHisService.doInsertOrUpdate(cityHisDO4);


            List<WeatherCityHisDO> windDirects = weatherCityHisService.findWeatherCityHisDOs("2", WeatherEnum.WINDDIRECTION.getType(), startDate, startDate);

            if (!org.springframework.util.CollectionUtils.isEmpty(windDirects)) {
                WeatherCityHisDO weatherCityHisDO = windDirects.get(0);
                weatherCityHisDO.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
                weatherCityHisDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                weatherCityHisDO.setCityId("1");
                weatherCityHisDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
                weatherCityHisService.doInsertOrUpdate(weatherCityHisDO);
            }
            startDate = DateUtils.addDays(startDate, 1);
        }
    }

    public List<BigDecimal> zero96List() {
        List<BigDecimal> list = new ArrayList<>(96);
        for (int i = 0; i < 96; i++) {
            list.add(null);
        }
        return list;
    }

    @Override
    public List<WeatherCityHisDO> getProvinceWeightAvgHisWeatherData(Integer type, Date startDate, Date endDate) throws Exception {
        List<WeatherCityHisDO> fcWeatherDOS = new ArrayList<>();
        String str = settingSystemService.getValue("province_weather_express");
        Map map = (Map) JSON.parse(str);

        List<WeatherStationHisBasicWgDO> weatherStationHisBasicWgDOS = weatherStationHisBasicWgService.getStandardStationHisWeatherBasic(null, type, startDate, endDate);
        if (CollectionUtils.isEmpty(weatherStationHisBasicWgDOS)) {
            return Collections.emptyList();
        }

        Map<String, List<WeatherStationHisBasicWgDO>> weatherMap = weatherStationHisBasicWgDOS.stream()
                .collect(Collectors.groupingBy(weatherStationFcBasicWgDO -> weatherStationFcBasicWgDO.getDate() + "_" + weatherStationFcBasicWgDO.getType()));
        for (Map.Entry<String, List<WeatherStationHisBasicWgDO>> entry : weatherMap.entrySet()) {
            String[] split = entry.getKey().split("_");
            String date = split[0];
            String weatherType = split[1];
            List<WeatherStationHisBasicWgDO> value = entry.getValue();
            List<List<BigDecimal>> lists = new ArrayList<>();
            // 用于记录每个地市的比率
            List<BigDecimal> ratioList = new ArrayList<>();
            for (WeatherStationHisBasicWgDO weatherStationHisBasicWgDO : value) {
                List<BigDecimal> bigDecimals = BasePeriodUtils.toList(weatherStationHisBasicWgDO, 96, Constants.WEATHER_CURVE_START_WITH_ZERO);
                String ratioStr = map.get(CityStationEnum.getCityIdByStation(weatherStationHisBasicWgDO.getStationWgId())).toString();
                BigDecimal ratio = new BigDecimal(ratioStr);
                ratioList.add(ratio);
                List<BigDecimal> weather96RatioList = zero96List();
                for (int i = 0; i < bigDecimals.size(); i++) {
                    BigDecimal bigDecimal = bigDecimals.get(i);
                    if (BigDecimalUtils.isValidValue(bigDecimal)) {
                        BigDecimal scaledValue = bigDecimal.multiply(ratio);
                        weather96RatioList.set(i, scaledValue);
                    }
                }
                lists.add(weather96RatioList);
            }

            List<BigDecimal> decimalList = zero96List();
            for (int i = 0; i < 96; i++) {
                BigDecimal sum = BigDecimal.ZERO;
                BigDecimal totalRatio = BigDecimal.ZERO;
                for (int j = 0; j < lists.size(); j++) {
                    List<BigDecimal> cityLoads = lists.get(j);
                    BigDecimal decimal = cityLoads.get(i);
                    if (decimal != null) {
                        sum = sum.add(decimal);
                        totalRatio = totalRatio.add(ratioList.get(j)); // 使用已知的比率
                    }
                }
                BigDecimal curValue = totalRatio.equals(BigDecimal.ZERO) ? null : sum.divide(totalRatio, MATH_CONTEXT);
                decimalList.set(i, curValue);
            }

            WeatherCityHisDO cityHisDO = new WeatherCityHisDO();
            Map<String, BigDecimal> decimalMap = ColumnUtil.listToMap(decimalList, Constants.LOAD_CURVE_START_WITH_ZERO);
            BasePeriodUtils.setAllFiled(cityHisDO, decimalMap);
            cityHisDO.setType(Integer.valueOf(weatherType));
            cityHisDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            cityHisDO.setCityId("1");
            cityHisDO.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
            cityHisDO.setDate(new java.sql.Date(DateUtils.string2Date(date, DateFormatType.SIMPLE_DATE_FORMAT_STR).getTime()));
            fcWeatherDOS.add(cityHisDO);
        }
        return fcWeatherDOS;
    }
}


