package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.ParamConstants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherNewEnum;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityBasedStationFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherSourceFcService;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherFcAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherFcDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherFcFeatureDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherFcPointDTO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityBasedStationFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceimpl.common.util.WeatherCalcUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *  Description:  <br>     <AUTHOR>  @create 2021/7/27  @since 1.0.0 
 */
@Service("weatherSourceFcService")
public class WeatherSourceFcServiceImpl implements WeatherSourceFcService {

    @Autowired
    private CityService cityService;


    @Autowired
    private WeatherCityFcService weatherCityFcService;

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Autowired
    private WeatherCityBasedStationFcService weatherCityBasedStationFcService;


    @Override
    public List<WeatherFcDTO> findSourceWeatherPoint(List<String> cityIdS, List<String> sources, Date startDate,
                                                     Date endDate, String type) throws Exception {
        List<WeatherFcDTO> weatherFcDTOS = new ArrayList<>();
        for (String cityId : cityIdS) {
            CityDO city = cityService.findCityById(cityId);
            cityId = cityService.findWeatherCityId(cityId);
            List<Date> dates = DateUtil.getListBetweenDay(startDate, endDate);
            if (type != null && type.equals(ParamConstants.HIS)) {
                if (!(sources.contains(ParamConstants.HIS))) {
                    sources.add(ParamConstants.HIS);
                }
            }
            for (Date date : dates) {
                for (WeatherNewEnum weatherEnum : WeatherNewEnum.values()) {
                    for (String source : sources) {
                        if (source.equals(ParamConstants.FC)) {
                            WeatherFcDTO weatherFcDTO = new WeatherFcDTO();
                            weatherFcDTO.setCityId(cityId);
                            weatherFcDTO.setCityName(city.getCity());
                            weatherFcDTO.setDate(date);
                            weatherFcDTO.setSource(source);
                            weatherFcDTO.setType(String.valueOf(weatherEnum.getType()));
                            List<BigDecimal> pointList = weatherCityFcService
                                .find96WeatherCityFcValue(date, cityId, weatherEnum.getType());
                            weatherFcDTO.setPoint(pointList);
                            weatherFcDTO.setSourceName("气象局预测气象");
                            weatherFcDTOS.add(weatherFcDTO);
                        }
                        if (source.equals(ParamConstants.HIS)){
                            List<WeatherCityHisDO> weatherCityHisDOs = weatherCityHisService
                                .findWeatherCityHisDOs(cityId, weatherEnum.getType(), date, date);
                            if (weatherCityHisDOs.size()>0){
                                WeatherFcDTO weatherFcDTO = new WeatherFcDTO();
                                weatherFcDTO.setCityId(cityId);
                                weatherFcDTO.setCityName(city.getCity());
                                weatherFcDTO.setDate(date);
                                weatherFcDTO.setSource(source);
                                weatherFcDTO.setType(String.valueOf(weatherEnum.getType()));
                                List<BigDecimal> list = BasePeriodUtils
                                        .toList(weatherCityHisDOs.get(0), Constants.WEATHER_CURVE_POINT_NUM,
                                                Constants.WEATHER_CURVE_START_WITH_ZERO);
                                weatherFcDTO.setPoint(list);
                                weatherFcDTO.setSourceName("气象局实际气象");
                                weatherFcDTOS.add(weatherFcDTO);
                            }
                        }
                        if (source.equals(ParamConstants.DKY)) {
                            List<WeatherCityBasedStationFcDO> weatherCityBasedStationFcDOs = weatherCityBasedStationFcService
                                    .findWeatherCityBasedStationFcDOs(cityId, weatherEnum.getType(), date, date);
                            if (weatherCityBasedStationFcDOs.size() > 0) {
                                WeatherFcDTO weatherFcDTO = new WeatherFcDTO();
                                weatherFcDTO.setCityId(cityId);
                                weatherFcDTO.setCityName(city.getCity());
                                weatherFcDTO.setDate(date);
                                weatherFcDTO.setSource(source);
                                weatherFcDTO.setType(String.valueOf(weatherEnum.getType()));
                                List<BigDecimal> list = BasePeriodUtils
                                        .toList(weatherCityBasedStationFcDOs.get(0), Constants.WEATHER_CURVE_POINT_NUM,
                                                Constants.WEATHER_CURVE_START_WITH_ZERO);
                                weatherFcDTO.setPoint(list);
                                weatherFcDTO.setSourceName("电科院预测气象");
                                weatherFcDTOS.add(weatherFcDTO);
                            }
                        }
                    }
                }
            }
        }
        return weatherFcDTOS;
    }

    @Override
    public List<WeatherFcFeatureDTO> findSourceWeatherFeature(List<String> cityIdS, List<String> sources,
        Date startDate, Date endDate) throws Exception {
        List<WeatherFcFeatureDTO> WeatherFcFeatureDTOs = new ArrayList<>();
        List<Date> dates = DateUtil.getListBetweenDay(startDate, endDate);
        for (String city : cityIdS) {
            CityDO cityDO = cityService.findCityById(city);
            city = cityService.findWeatherCityId(city);
            if (!(sources.contains(ParamConstants.HIS))){
                sources.add(ParamConstants.HIS);
            }
            for (Date date : dates) {
                for (String source : sources) {
                    List<String> list = new ArrayList<>();
                    list.add(source);
                    List<String> cityS = new ArrayList<>();
                    cityS.add(city);
                    List<WeatherFcDTO> weatherFcDTOList = findSourceWeatherPoint(cityS, list,  date,  date,null);
                    if (weatherFcDTOList.size()>0){
                        WeatherFcFeatureDTO weatherFcFeatureDTO = new WeatherFcFeatureDTO();
                        weatherFcFeatureDTO.setCityId(city);
                        weatherFcFeatureDTO.setCityName(cityDO.getCity());
                        weatherFcFeatureDTO.setDate(date);
                        weatherFcFeatureDTO.setSource(source);
                        for (WeatherFcDTO weatherFcDTO : weatherFcDTOList) {
                            weatherFcFeatureDTO.setSourceName(weatherFcDTO.getSourceName());
                            if (weatherFcDTO.getPoint()!= null && weatherFcDTO.getPoint().size()>0){
                                if (WeatherEnum.HUMIDITY.getType() == Integer.parseInt(weatherFcDTO.getType())){
                                    Map<String, BigDecimal> map = BasePeriodUtils.getMaxMinAvg(weatherFcDTO.getPoint(), 2);
                                    if (map.size()>0){
                                        weatherFcFeatureDTO.setHumidityMax(map.get("max"));
                                        weatherFcFeatureDTO.setHumidityMin(map.get("min"));
                                        weatherFcFeatureDTO.setHumidityAvg(map.get("avg"));
                                    }
                                }
                                if (WeatherEnum.TEMPERATURE.getType()==Integer.parseInt(weatherFcDTO.getType())){
                                    Map<String, BigDecimal> map = BasePeriodUtils.getMaxMinAvg(weatherFcDTO.getPoint(), 2);
                                    if (map.size()>0){
                                        weatherFcFeatureDTO.setTemperatureAvg(map.get("avg"));
                                        weatherFcFeatureDTO.setTemperatureMax(map.get("max"));
                                        weatherFcFeatureDTO.setTemperatureMin(map.get("min"));
                                    }
                                }
                                if (WeatherEnum.WINDSPEED.getType()==Integer.parseInt(weatherFcDTO.getType())){
                                    Map<String, BigDecimal> map = BasePeriodUtils.getMaxMinAvg(weatherFcDTO.getPoint(), 2);
                                    if (map.size()>0){
                                        weatherFcFeatureDTO.setSpeedMax(map.get("max"));
                                        weatherFcFeatureDTO.setSpeedMin(map.get("min"));
                                        weatherFcFeatureDTO.setSpeedAvg(map.get("avg"));
                                    }
                                }
                                if (WeatherEnum.RAINFALL.getType()==Integer.parseInt(weatherFcDTO.getType())){
                                    BigDecimal total = BigDecimalUtils.addAllValue(weatherFcDTO.getPoint());
                                    weatherFcFeatureDTO.setAccumulatePrecipitation(total);
                                }
                            }
                        }
                        WeatherFcFeatureDTOs.add(weatherFcFeatureDTO);
                    }
                }
            }
        }
        return WeatherFcFeatureDTOs;
    }

    @Override
    public List<WeatherFcAccuracyDTO> findSourceWeatherAccuracy(String cityId, List<String> sources, Date startDate,
        Date endDate) throws Exception {
        List<WeatherFcAccuracyDTO> weatherFcAccuracyDTOs = new ArrayList<>();
        List<String> cityS = new ArrayList<>();
        cityId = cityService.findWeatherCityId(cityId);
        cityS.add(cityId);
        if (sources.size()>0){
            List<WeatherFcDTO> weatherFcDTOList = findSourceWeatherPoint(cityS, sources, startDate, endDate,null);
            if (weatherFcDTOList.size()>0){
                for (WeatherFcDTO weatherFcDTO: weatherFcDTOList) {
                    //不计算实际气象的准确率
                    if (ParamConstants.HIS.equals(weatherFcDTO.getSource())) {
                        continue;
                    }
                    WeatherFcAccuracyDTO weatherFcAccuracyDTO = new WeatherFcAccuracyDTO();
                    BeanUtils.copyProperties(weatherFcDTO, weatherFcAccuracyDTO);
                    if (weatherFcDTO.getPoint() != null && weatherFcDTO.getPoint().size() > 0) {
                        Map<String, BigDecimal> fcmap = ColumnUtil
                                .listToMap(weatherFcDTO.getPoint(), Constants.WEATHER_CURVE_START_WITH_ZERO);
                        List<BigDecimal> fcList = WeatherCalcUtil.get24Point(fcmap);
                        List<WeatherCityHisDO> weatherCityHisDOs = weatherCityHisService
                                .findWeatherCityHisDOs(weatherFcDTO.getCityId(), Integer.parseInt(weatherFcDTO.getType()),
                                        weatherFcDTO.getDate(), weatherFcDTO.getDate());
                        List<BigDecimal> hisList = new ArrayList<>();
                        if (weatherCityHisDOs.size() > 0) {
                            hisList = BasePeriodUtils.toList(weatherCityHisDOs.get(0), 24,
                                    Constants.WEATHER_CURVE_START_WITH_ZERO);
                        }
                        if (CollectionUtils.isNotEmpty(hisList) && CollectionUtils.isNotEmpty(fcList)){
                            List<BigDecimal> list = WeatherCalcUtil.calcSourceAccuracy(hisList, fcList);
                            Map<String, BigDecimal> maxMinAvg = BasePeriodUtils.getMaxMinAvg(list, 4);
                            BigDecimal accuracy = maxMinAvg.get("avg");
                            weatherFcAccuracyDTO.setAccuracy(accuracy);
                        }
                    }
                    weatherFcAccuracyDTOs.add(weatherFcAccuracyDTO);
                }
            }
        }
        return weatherFcAccuracyDTOs;
    }


    @Override
    public List<WeatherFcPointDTO> findSourceWeatherPointList(String cityId, Date startDate, Date endDate) throws Exception {
        List<WeatherFcPointDTO> weatherFcPointDTOS = new ArrayList<>();
        CityDO city = cityService.findCityById(cityId);
        cityId = cityService.findWeatherCityId(cityId);
        for (WeatherNewEnum weatherEnum : WeatherNewEnum.values()) {
            WeatherFcPointDTO weatherFcPointDTO = new WeatherFcPointDTO();
            weatherFcPointDTO.setCityId(cityId);
            weatherFcPointDTO.setCityName(city.getCity());
            weatherFcPointDTO.setType(String.valueOf(weatherEnum.getType()));
            List<BigDecimal> fc = weatherCityFcService
                .findListPoint(startDate, endDate, cityId, weatherEnum.getType());
            weatherFcPointDTO.setFcPoint(fc);
            List<BigDecimal> his = weatherCityHisService
                .findListPoint(startDate, endDate, cityId, weatherEnum.getType());
            weatherFcPointDTO.setHisPoint(his);
            weatherFcPointDTOS.add(weatherFcPointDTO);
        }
        return weatherFcPointDTOS;
    }
}

