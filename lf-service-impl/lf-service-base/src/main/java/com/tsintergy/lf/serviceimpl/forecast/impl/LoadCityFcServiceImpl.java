
package com.tsintergy.lf.serviceimpl.forecast.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.SystemConstant;
import com.tsintergy.lf.core.properties.D5000GuoDiaoReportProperties;
import com.tsintergy.lf.core.properties.GuodiaoReportConfig;
import com.tsintergy.lf.core.properties.GuodiaoReportFileConfig;
import com.tsintergy.lf.core.util.*;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils;
import com.tsintergy.lf.serviceapi.base.forecast.api.AlgorithmService;
import com.tsintergy.lf.serviceapi.base.forecast.api.ForecastService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcBatchService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.dto.ForecastDTO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFc288DO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcTempDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingAlgorithmMultiStrategyService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.dto.SettingAlgorithmMultiStrategyDTO;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemData;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingSystemDO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherDTO;
import com.tsintergy.lf.serviceimpl.forecast.dao.LoadCityFc288DAO;
import com.tsintergy.lf.serviceimpl.forecast.dao.LoadCityFcDAO;
import lombok.extern.slf4j.Slf4j;
import net.sf.cglib.beans.BeanMap;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.common.TemplateParserContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $Id: LoadCityFcServiceImpl.java, v 0.1 2018-01-31 10:23:57 tao Exp $$
 */

@Service("loadCityFcService")
@Slf4j
@Transactional
public class LoadCityFcServiceImpl extends BaseServiceImpl implements LoadCityFcService {

    @Autowired
    CityService cityService;

    @Autowired
    LoadCityFc288DAO loadCityFc288DAO;

    @Autowired
    AlgorithmService algorithmService;

    @Autowired
    SettingSystemService settingSystemService;


    @Autowired
    LoadCityFcDAO loadCityFcDAO;

    @Autowired
    private D5000GuoDiaoReportProperties d5000GuoDiaoReportProperties;

    @Autowired
    private ForecastService forecastService;

    @Autowired
    private LoadCityFcBatchService loadCityFcBatchService;

    @Autowired
    private SettingAlgorithmMultiStrategyService settingAlgorithmMultiStrategyService;

    @Override
    public DataPackage queryLoadCityFcDO(DBQueryParam param) throws Exception {
        try {
            return loadCityFcDAO.query(param);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public LoadCityFcDO doCreate(LoadCityFcDO vo) throws Exception {
        try {
            return loadCityFcDAO.create(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public void doRemoveLoadCityFcDO(LoadCityFcDO vo) throws Exception {
        try {
            loadCityFcDAO.remove(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public void doRemoveLoadCityFcDOByPK(Serializable pk) throws Exception {
        try {
            loadCityFcDAO.removeByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }


    @Override
    public LoadCityFcDO doUpdateLoadCityFcDO(LoadCityFcDO vo) throws Exception {
        try {
            vo.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            return (LoadCityFcDO) loadCityFcDAO.update(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public LoadCityFcDO findLoadCityFcDOByPk(Serializable pk) throws Exception {
        try {
            return loadCityFcDAO.findVOByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }


    @Override
    public LoadCityFcDO getLoadCityFcDO(Date date, String cityId, String caliberId,
        String algorithmId) throws Exception {
        List<LoadCityFcDO> loadCityFcDOS = loadCityFcDAO
            .getLoadCityFcDOs(cityId, caliberId,
                algorithmId, date, date);
        if (loadCityFcDOS.size() < 1) {
            return null;
        }
        return loadCityFcDOS.get(0);
    }

    @Override
    public List<WeatherDTO> getLoadCityFcByDateVO(Date startDate, Date endDate, String cityId,
        String caliberId,
        String algorithmId) throws Exception {
        List<LoadCityFcDO> loadCityFcDOS = loadCityFcDAO
                .getLoadCityFcDOs(cityId, caliberId,
                        algorithmId, startDate, endDate);

        List<AlgorithmDO> allAlgorithmsNotCache = algorithmService.getAllAlgorithmsNotCache();
        Map<String, String> algorithmMap = allAlgorithmsNotCache.stream().collect(Collectors.toMap(AlgorithmDO::getId,
                AlgorithmDO::getAlgorithmCn));
        List<WeatherDTO> loadCommonDTOS = new ArrayList<WeatherDTO>(10);

        List<AlgorithmDO> pageAlgorithms = allAlgorithmsNotCache.stream().filter(
                        t -> AlgorithmConstants.NORMAL_ALGORITHM_TYPE.equals(t.getType()) || AlgorithmConstants.COMMON_ALGORITHM_TYPE.equals(t.getType())
                                || AlgorithmConstants.SPECIAL_ALGORITHM_TYPE.equals(t.getType()) || AlgorithmConstants.HOLIDAY_ALGORITHM_TYPE.equals(t.getType()))
                .collect(Collectors.toList());

        List<AlgorithmDO> viewAlgorithms = null;
        CityDO cityDO = cityService.findCityById(cityId);
        if (CityConstants.PROVINCE_TYPE.equals(cityDO.getType())) {
            viewAlgorithms = pageAlgorithms.stream().filter(t->t.getProvinceView() == true).collect(Collectors.toList());
        }else if(CityConstants.CITY_TYPE.equals(cityDO.getType())){
            viewAlgorithms = pageAlgorithms.stream().filter(t->t.getCityView() == true).collect(Collectors.toList());
        }

        List<String> collect = viewAlgorithms.stream().map(AlgorithmDO::getId).collect(Collectors.toList());


        for (LoadCityFcDO loadCityFcDO : loadCityFcDOS) {
            //去掉report  == 0 or success == 0
            if ("0".equals(loadCityFcDO.getReport()) || "0".equals(loadCityFcDO.getSucceed())|| !collect.contains(loadCityFcDO.getAlgorithmId())) {
                continue;
            }
            WeatherDTO loadCommonDTO = new WeatherDTO();
            loadCommonDTO.setId(loadCityFcDO.getId());
            loadCommonDTO.setDate(loadCityFcDO.getDate());
            loadCommonDTO.setData(BasePeriodUtils.toList(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM,
                Constants.LOAD_CURVE_START_WITH_ZERO));
            loadCommonDTO.setWeek(DateUtil.getWeek(loadCityFcDO.getDate()));
            loadCommonDTO.setCity(cityDO.getCity());
            loadCommonDTO.setAlgorithm(algorithmMap.get(loadCityFcDO.getAlgorithmId()));
            loadCommonDTOS.add(loadCommonDTO);
        }
        return loadCommonDTOS;
    }

    @Override
    public List<BigDecimal> findLoadCityFcDO(Date date, String cityId, String caliberId,
        String algorithmId) throws Exception {
        LoadCityFcDO loadCityFcDO = this.getLoadCityFcDO(date, cityId, caliberId, algorithmId);
        if (loadCityFcDO != null) {
            return BasePeriodUtils.toList(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM,
                Constants.LOAD_CURVE_START_WITH_ZERO);
        }
        return null;
    }

    @Override
    public ForecastDTO findForecastDTO(Date date, String cityId, String caliberId, String algorithmId)
        throws Exception {
        LoadCityFcDO loadCityFcDO  = this.getLoadCityFcDO(date, cityId, algorithmId, caliberId);
        if (loadCityFcDO == null) {
            return null;
        }
        ForecastDTO forecastDTO = new ForecastDTO();
        forecastDTO.setId(loadCityFcDO.getId());
        forecastDTO.setAlgorithmId(loadCityFcDO.getAlgorithmId());
        forecastDTO.setCaliberId(loadCityFcDO.getCaliberId());
        forecastDTO.setList(BasePeriodUtils.toList(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM,
            Constants.LOAD_CURVE_START_WITH_ZERO));
        return forecastDTO;
    }

    @Override
    public ForecastDTO findReportForecastDTO(Date date, String cityId, String caliberId)
        throws Exception {
        LoadCityFcDO loadCityFcDO = this.getReportLoadCityFcDO(date, cityId, caliberId);
        ForecastDTO forecastDTO = new ForecastDTO();
        forecastDTO.setId(loadCityFcDO.getId());
        forecastDTO.setAlgorithmId(loadCityFcDO.getAlgorithmId());
        forecastDTO.setList(BasePeriodUtils.toList(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM,
            Constants.LOAD_CURVE_START_WITH_ZERO));
        return forecastDTO;
    }

    @Override
    public LoadCityFcDO getReportLoadCityFcDO(Date date, String cityId, String caliberId)
        throws Exception {
        List<LoadCityFcDO> loadCityFcDOS = loadCityFcDAO
            .getReportLoadCityFcDOs(cityId, date, date,
                caliberId, null);
        if (loadCityFcDOS.size() < 1) {
            throw TsieExceptionUtils.newBusinessException("T706", "");
        }
        return loadCityFcDOS.get(0);
    }


    @Override
    public LoadCityFcDO getReportLoadCityFcDOWithOutNull(Date date, String cityId, String caliberId)
        throws Exception {
        List<LoadCityFcDO> loadCityFcDOS = loadCityFcDAO
            .getReportLoadCityFcDOs(cityId, date, date,
                caliberId, null);
        LoadCityFcDO loadCityFcDO = null;
        if (loadCityFcDOS != null && loadCityFcDOS.size() > 0) {
            loadCityFcDO = loadCityFcDOS.get(0);
        }
        return loadCityFcDO;
    }


    @Override
    public LoadCityFcDO doSaveOrUpdateLoadCityFcDO96(LoadCityFcDO loadCityFcDO) throws Exception {
        return loadCityFcDAO.doSaveOrUpdateLoadCityFcDO(loadCityFcDO);
    }

    @Override
    public LoadCityFc288DO doSaveOrUpdateLoadCityFcDO288(LoadCityFc288DO LoadCityFcDO) throws Exception {
        return loadCityFc288DAO.doSaveOrUpdateLoadCityFcDO(LoadCityFcDO);
    }

    @Override
    public String getReportAlgorithmId(String cityId, Date date, String caliberId) throws Exception {

        LoadCityFcDO loadCityFcDO = this.getReportLoadCityFcDO(date, cityId, caliberId);
        if (loadCityFcDO != null) {
            return loadCityFcDO.getAlgorithmId();
        }
        return null;
    }

    @Override
    public List<BigDecimal> findReportLoadCityFcDO(Date date, String cityId, String caliberId,
        String algorithmId, Boolean report) throws Exception {
        LoadCityFcDO loadCityFcDO = loadCityFcDAO
            .getReportLoadCityFcDO(date, cityId, caliberId,
                algorithmId, report);
        if (loadCityFcDO != null) {
            return BasePeriodUtils.toList(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM,
                Constants.LOAD_CURVE_START_WITH_ZERO);
        }
        return new ArrayList<>();
    }

    @Override
    public LoadCityFcDO findReportCityFcDO(Date date, String cityId, String caliberId,
        String algorithmId, Boolean report) throws Exception {
        return  loadCityFcDAO
            .getReportLoadCityFcDO(date, cityId, caliberId,
                algorithmId, report);
    }

    @Override
    public List<BigDecimal> findReportLoadCityFc288DO(Date date, String cityId, String caliberId, String algorithmId)
        throws Exception {
        LoadCityFc288DO loadCityFcDO = loadCityFc288DAO
            .getReportLoadCityFcDO(date, cityId, caliberId,
                algorithmId);
        if (loadCityFcDO != null) {
            return BasePeriodUtils.toList(loadCityFcDO, 288,
                Constants.LOAD_CURVE_START_WITH_ZERO);
        }
        return new ArrayList<>();
    }

    @Override
    public LoadCityFc288DO find288DOByDate(Date date, String cityId, String caliberId, String algorithmId)
        throws Exception {
        return loadCityFc288DAO
            .getReportLoadCityFcDO(date, cityId, caliberId,
                algorithmId);
    }

    @Override
    public LoadCityFcDO doReport(LoadCityFcDO loadCityFcDO) throws Exception {

//        //校验上报 todo wangchen  暂时注掉，演示方便
//        if (getSettingSystemService().isBeforeReportTime(loadCityFcDO.getDate())) {
//            throw TsieExceptionUtils.newBusinessException("03D20180601");
//        }
        // 先把原来已上报的结果设置为未上报
        try {
            LoadCityFcDO oldVO = this
                .getReportLoadCityFcDO(loadCityFcDO.getDate(), loadCityFcDO.getCityId(),
                    loadCityFcDO.getCaliberId());
            oldVO.setReport(false);
            oldVO.setSucceed(false);
            loadCityFcDAO.doSaveOrUpdateLoadCityFcDO(oldVO);
            loadCityFcDO.setReport(true);
            loadCityFcDO.setSucceed(true);
            loadCityFcDO.setReportTime(new Timestamp(System.currentTimeMillis()));
        } catch (BusinessException e) {
            // 将新结果置为上报
            loadCityFcDO.setReport(true);
            loadCityFcDO.setSucceed(true);
        }
        //将forecast_info_basic
        return loadCityFcDAO.doSaveOrUpdateLoadCityFcDO(loadCityFcDO);
    }


    @Override
    public List<LoadCityFcDO> findAll() throws Exception {
        return (List<LoadCityFcDO>) loadCityFcDAO
            .findAll();
    }


    @Override
    public List<LoadCityFcDO> findLoadCityFcDO(String cityId, String caliberId, String algorithmId)
        throws Exception {
        List<LoadCityFcDO> loadCityFcDOS = loadCityFcDAO
            .getLoadCityFcDOs(cityId, caliberId,
                algorithmId, null, null);
        return loadCityFcDOS;
    }

    @Override
    public LoadCityFcDO getLoadCityFc(String cityId, String caliberId, Date date, Date endDate,
        String algorithmId) throws Exception {
        List<LoadCityFcDO> LoadCityFcDOS = new ArrayList<>();
        if (AlgorithmConstants.MD_ALGORITHM_ID.equals(algorithmId)) {
            return loadCityFcDAO
                .getReportLoadCityFcDO(cityId, caliberId, date);
        } else {
            LoadCityFcDOS = loadCityFcDAO
                .getLoadCityFcDOs(cityId, caliberId,
                    algorithmId, date, endDate);
        }

        if (CollectionUtils.isEmpty(LoadCityFcDOS)) {
            return null;
        }
        return LoadCityFcDOS.get(0);
    }


    @Override
    public LoadCityFcDO getRecommendLoadCityFcDO(String cityId, String caliberId, Date date)
        throws Exception {
        return loadCityFcDAO.getRecommendLoadCityFcDO(cityId, caliberId, date);
    }


    @Override
    public List<LoadCityFcDO> listLoadCityFc(String cityId, String caliberId, Date startDate,
        Date endDate,
        String algorithmId) throws Exception {
        return loadCityFcDAO
            .getLoadCityFcDOs(cityId, caliberId, algorithmId, startDate, endDate);
    }


    @Override
    public List<LoadCityFcDO> listReportLoadCityFc(String cityId, String caliberId, Date startDate,
        Date endDate,
        String algorithmId) throws Exception {
        return loadCityFcDAO
            .getReportLoadCityFcDOs(cityId, startDate, endDate, caliberId,
                algorithmId);
    }

    @Override
    public LoadCityFcDO getReport(String cityId, String caliberId, Date date) throws Exception {
        List<LoadCityFcDO> loadCityFcDOS = loadCityFcDAO
            .getReportLoadCityFcDOs(cityId, date, date,
                caliberId, null);
        if (CollectionUtils.isEmpty(loadCityFcDOS) || loadCityFcDOS == null) {
            return null;
        }
        return loadCityFcDOS.get(0);
    }


    @Override
    public List<LoadCityFcDO> getLoadFcByAlgorithmId(Date date, String cityId, String caliberId,
        List<String> algorithmIds) throws Exception {
        List<LoadCityFcDO> loadCityFcDOS = loadCityFcDAO
            .getLoadCityFcDOInAlgorithmId(cityId,
                caliberId,
                algorithmIds, date, date);
        if (loadCityFcDOS.size() < 1) {
            return null;
        }
        return loadCityFcDOS;
    }

    @Override
    public LoadCityFcDO getLoadFc(Date date, String cityId, String caliberId, String algorithmId)
        throws Exception {
        List<LoadCityFcDO> loadCityFcDOS = loadCityFcDAO
            .getLoadCityFcDOs(cityId, caliberId,
                algorithmId, date, date);
        if (loadCityFcDOS.size() < 1) {
            return null;
        }
        return loadCityFcDOS.get(0);
    }


    @Override
    public LoadCityFcTempDO doSaveOrUpdate(List<List<String>> dataList, String cityId,
        String userId,
        String caliberId) throws BusinessException {
        try {
            for (int i = 1; i < dataList.size(); i++) {
                LoadCityFcDO fcVO = new LoadCityFcDO();
                fcVO.setCityId(cityId);
                fcVO.setCaliberId(caliberId);
                fcVO.setDate(java.sql.Date.valueOf(dataList.get(i).get(1)));
                fcVO.setAlgorithmId(AlgorithmConstants.MD_ALGORITHM_ID);
                fcVO.setUserId(userId);
                List<BigDecimal> list = new ArrayList<>();
                for (String dataNumber : dataList.get(i).subList(2, dataList.get(i).size())) {
                    list.add(new BigDecimal(dataNumber));
                }
                BasePeriodUtils.setAllFiled(fcVO,
                    ColumnUtil.listToMap(list, Constants.LOAD_CURVE_START_WITH_ZERO));
                doSaveOrUpdateLoadCityFcDO96(fcVO);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    @Override
    public List<LoadCityFcDO> findReportLoadFc(String cityId, String caliberId, Date startDate, Date endDate)
        throws Exception {
        return loadCityFcDAO
            .getReportFc(startDate, endDate, cityId, caliberId);
    }

    @Override
    public List<LoadCityFcDO> findFcByAlgorithmId(String cityId, String caliberId, String algorithmId, Date startDate,
        Date endDate) throws Exception {
        return loadCityFcDAO.getLoadCityFcDOs(cityId, caliberId, algorithmId, startDate, endDate);
    }


    @Override
    public List<LoadCityFcDO> findLoadCityFcReport(Date date, String caliberId) throws Exception {
        return loadCityFcDAO.findReportFcIfNullUseRecommend(date,date,null,caliberId);
    }

    @Override
    public LoadCityFcDO find96LoadCityFcValue(Date date, String caliberId, String cityId) throws Exception {
        List<LoadCityFcDO> reportLoadFc = this.findReportLoadFc(cityId, caliberId, date, date);
        if (!CollectionUtils.isEmpty(reportLoadFc)) {
            //优先展示上报数据
            return reportLoadFc.get(0);
        } else {
            //没有上报数据 展示推荐算法
            LoadCityFcDO recommendLoadCityFcDO = this.loadCityFcDAO.getRecommendLoadCityFcDO(cityId, caliberId, date);
            if (recommendLoadCityFcDO == null) {
                //如果推荐的也没有 随便返回一条预测数据
                List<LoadCityFcDO> loadCityFcDOs = this.loadCityFcDAO
                    .getLoadCityFcDOs(cityId, caliberId, null, date, date);
                if (!CollectionUtils.isEmpty(loadCityFcDOs)) {
                    return loadCityFcDOs.get(0);
                } else {
                    return null;
                }
            }
            return recommendLoadCityFcDO;
        }
    }

    @Override
    public List<LoadCityFcDO> findLoadCityFc(List<String> cityIds, Date startDate, Date endDate, String caliberId) throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (!CollectionUtils.isEmpty(cityIds)) {
            dbQueryParamBuilder.where(QueryOp.StringIsIn,"cityId",cityIds);
        }
        if (startDate != null) {
            dbQueryParamBuilder.where(QueryOp.DateNoLessThan,"date",new java.sql.Date(startDate.getTime()));
        }
        if (endDate != null) {
            dbQueryParamBuilder.where(QueryOp.DateNoMoreThan,"date",new java.sql.Date(endDate.getTime()));
        }
        if (StringUtils.isNotBlank(caliberId)) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo,"caliberId",caliberId);
        }
        return loadCityFcDAO.query(dbQueryParamBuilder.build()).getDatas();
    }

    @Override
    public List<LoadCityFcDO> getReportLoadCityFcDO(String cityId, String caliberId, Date startDate,
        Date endDate, Integer report) throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (StringUtils.isNotBlank(cityId)) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "cityId", cityId);
        }
        if (startDate != null) {
            dbQueryParamBuilder.where(QueryOp.DateNoLessThan, "date", new java.sql.Date(startDate.getTime()));
        }
        if (endDate != null) {
            dbQueryParamBuilder.where(QueryOp.DateNoMoreThan, "date", new java.sql.Date(endDate.getTime()));
        }
        if (StringUtils.isNotBlank(caliberId)) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (report != null) {
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "report", report == 1 ? true : false);
        }
        return loadCityFcDAO.query(dbQueryParamBuilder.build()).getDatas();
    }


    @Deprecated
    @Override
    public void doIntegratedModelAlgorithmData(CityDO cityDO, Date startDate) throws Exception {

        List<LoadCityFcDO> loadCityFc = findLoadCityFc(Arrays.asList(cityDO.getId()), startDate, startDate, "1");
        Map<String, List<LoadCityFcDO>> listMap = loadCityFc.stream()
            .collect(Collectors.groupingBy(LoadCityFcDO::getAlgorithmId));
        //神经网络
        List<LoadCityFcDO> innov = listMap.get(AlgorithmEnum.FORECAST_INNOVATION.getId());
        //标点
        List<LoadCityFcDO> xg = listMap.get(AlgorithmEnum.FORECAST_XGBOOST.getId());
        //支持向量机
        List<LoadCityFcDO> svm = listMap.get(AlgorithmEnum.FORECAST_SVM.getId());
        //标典
        List<LoadCityFcDO> svmLarge = listMap.get(AlgorithmEnum.FORECAST_SVM_LARGE.getId());

        if (!CollectionUtils.isEmpty(innov) && !CollectionUtils.isEmpty(xg) && !CollectionUtils.isEmpty(svm)
            && !CollectionUtils.isEmpty(svmLarge)) {
            List<SettingSystemDO> allSettingSystemDO = settingSystemService.getAllSettingSystemDO();
            Map<String, List<SettingSystemDO>> map = allSettingSystemDO.stream()
                .collect(Collectors.groupingBy(SettingSystemDO::getField));
            List<SettingSystemDO> scaleComprehensiveModel = map.get(SystemConstant.SCALE_COMPREHENSIVE_MODEL);
            SettingSystemDO SettingSystemDO = scaleComprehensiveModel.get(0);
            String valueStr = SettingSystemDO.getValue();
            JSONObject jsonObject = JSONObject.parseObject(valueStr);
            Set<Map.Entry<String, Object>> entries = jsonObject.entrySet();
            //创建并初始化该96点list
            List<BigDecimal> list = new ArrayList<>();
            for (int i = 0; i < 96; i++) {
                list.add(BigDecimal.ZERO);
            }
            for (Map.Entry<String, Object> entry : entries) {
                String key = entry.getKey();
                String value = entry.getValue().toString();
                if (key.equals(AlgorithmEnum.FORECAST_INNOVATION.getId())) {
                    list = makeCountIntegratedModelAlgorithmDataData(innov, value, list);
                }

                if (key.equals(AlgorithmEnum.FORECAST_XGBOOST.getId())) {
                    list = makeCountIntegratedModelAlgorithmDataData(xg, value, list);
                }

                if (key.equals(AlgorithmEnum.FORECAST_SVM.getId())) {
                    list = makeCountIntegratedModelAlgorithmDataData(svm, value, list);
                }

                if (key.equals(AlgorithmEnum.FORECAST_SVM_LARGE.getId())) {
                    list = makeCountIntegratedModelAlgorithmDataData(svmLarge, value, list);
                }
            }

            //存库
            LoadCityFcDO LoadCityFcDO = new LoadCityFcDO();
            if (listMap.get(AlgorithmEnum.COMPREHENSIVE_MODEL.getId()) == null
                || listMap.get(AlgorithmEnum.COMPREHENSIVE_MODEL.getId()).size() == 0) {

                LoadCityFcDO.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
                LoadCityFcDO.setAlgorithmId(AlgorithmEnum.COMPREHENSIVE_MODEL.getId());
                LoadCityFcDO.setCityId(cityDO.getId());
                LoadCityFcDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
                LoadCityFcDO.setCaliberId("1");
                LoadCityFcDO.setRecommend(false);
                LoadCityFcDO.setReport(false);
                LoadCityFcDO.setSucceed(false);
                LoadCityFcDO.setDate(new java.sql.Date(startDate.getTime()));
                Map<String, BigDecimal> decimalMap = ColumnUtil.listToMap(list, Constants.LOAD_CURVE_START_WITH_ZERO);
                BeanMap beanMap = BeanMap.create(LoadCityFcDO);
                beanMap.putAll(decimalMap);
                doCreate(LoadCityFcDO);

            } else {
                LoadCityFcDO = listMap.get(AlgorithmEnum.COMPREHENSIVE_MODEL.getId()).get(0);
                Map<String, BigDecimal> decimalMap = ColumnUtil.listToMap(list, Constants.LOAD_CURVE_START_WITH_ZERO);
                BeanMap beanMap = BeanMap.create(LoadCityFcDO);
                beanMap.putAll(decimalMap);
                LoadCityFcDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                doUpdateLoadCityFcDO(LoadCityFcDO);
            }
        } else {
            //这里抛异常是为通知调用方某些算法预测可能为空
            //定时任务执行遇到该异常要延迟30min再次尝试
            //实施页面遇到则直接跳过
            throw TsieExceptionUtils.newBusinessException("T999");
        }


    }

    @Override
    public void doCreateAndFlush(LoadCityFcDO loadCityFcDO) {
        loadCityFcDAO.createAndFlush(loadCityFcDO);
    }

    public List<BigDecimal> makeCountIntegratedModelAlgorithmDataData(List<LoadCityFcDO> data, String value,
        List<BigDecimal> list) {
        LoadCityFcDO LoadCityFcDO = data.get(0);
        List<BigDecimal> decimals = BasePeriodUtils.toList(LoadCityFcDO, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
        for (int i = 0; i < decimals.size(); i++) {
            BigDecimal bigDecimal = decimals.get(i);
            BigDecimal scale = new BigDecimal(value.toString());
            decimals.set(i, bigDecimal.multiply(scale));
        }
        List<BigDecimal> countListWithTwoList = DataUtil
            .getCountListWithTwoList(list, decimals);
        list = countListWithTwoList;
        return list;
    }

    @Override
    public List<LoadCityFcDO> findLoadCityFcReportByUserId(String cityId, String caliberId, String userId, Date startDate, Date endDate) {
        List<LoadCityFcDO> loadCityFcDOS = loadCityFcDAO.findLoadCityFcReportByUserId(cityId, caliberId, userId, startDate, endDate);
        return loadCityFcDOS;
    }

    @Override
    public void doStatisticsAllCityLoad(String caliberId, Date date) throws Exception{
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (date != null) {
            dbQueryParamBuilder.where(QueryOp.DateEqualTo, "date", new java.sql.Date(date.getTime()));
        }
        if (StringUtils.isNotBlank(caliberId)) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "report",  true);
        List<LoadCityFcDO> datas = loadCityFcDAO.query(dbQueryParamBuilder.build()).getDatas();
        List<LoadCityFcDO> cityReportDatas = datas.stream().filter(t -> !t.getCityId().equals(CityConstants.PROVINCE_ID))
            .collect(Collectors.toList());

        List<BigDecimal> list = LoadCalUtil.getZeroList(Constants.LOAD_CURVE_POINT_NUM);
        for (LoadCityFcDO loadCityFcDO:cityReportDatas){
            List<BigDecimal> bigDecimals = BasePeriodUtils
                .toList(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
            for(int i=0;i<bigDecimals.size();i++){
                BigDecimal var1 = bigDecimals.get(i);
                BigDecimal var2 = list.get(i);
                if(var1 != null && var2 != null){
                    BigDecimal count = var1.add(var2);
                    list.set(i,count);
                }
            }
        }

        Map<String, BigDecimal> decimalMap = ColumnUtil.listToMap(list, Constants.LOAD_CURVE_START_WITH_ZERO);
        LoadCityFcDO loadCityFcDO = loadCityFcDAO
            .getLoadCityFcDO(CityConstants.PROVINCE_ID, caliberId, AlgorithmEnum.ALL_CITY_FORECAST.getId(), date);
        if(loadCityFcDO == null){
            LoadCityFcDO cityFcDO = new LoadCityFcDO();
            cityFcDO.setId(UUID.randomUUID().toString().replace("-","").toLowerCase());
            BasePeriodUtils.setAllFiled(cityFcDO,decimalMap);
            cityFcDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            cityFcDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            cityFcDO.setReport(false);
            cityFcDO.setCaliberId(caliberId);
            cityFcDO.setUserId(null);
            cityFcDO.setDate(new java.sql.Date(date.getTime()));
            cityFcDO.setCityId(CityConstants.PROVINCE_ID);
            cityFcDO.setAlgorithmId(AlgorithmEnum.ALL_CITY_FORECAST.getId());
            cityFcDO.setReportTime(new Timestamp(System.currentTimeMillis()));
            loadCityFcDAO.save(cityFcDO);
        }else {
            BasePeriodUtils.setAllFiled(loadCityFcDO,decimalMap);
            loadCityFcDO.setReportTime(new Timestamp(System.currentTimeMillis()));
            loadCityFcDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            loadCityFcDAO.updateAndFlush(loadCityFcDO);
        }
    }

    @Override
    public List<BigDecimal> findReportLoad(Date date, String caliberId, String cityId) throws Exception {
        LoadCityFcDO loadCityFcValue = find96LoadCityFcValue(date, caliberId, cityId);
        if (loadCityFcValue != null) {
            return loadCityFcValue.getloadList();
        }
        return new ArrayList<>();
    }

    @Override
    public List<BigDecimal> loadCityFcAccumulation(Date date, String caliberId) throws Exception {
        List<BigDecimal> result = new ArrayList<>();
        // 地市累加
        List<LoadCityFcDO> reportLoadFcList = loadCityFcDAO.findReportFcIfNullUseRecommend(date, date,null, caliberId);
        if (!CollectionUtils.isEmpty(reportLoadFcList)) {
            //筛选九地市的数据
            reportLoadFcList = reportLoadFcList.stream().filter(item->!Constants.PROVINCE_ID.equals(item.getCityId())).collect(Collectors.toList());
            Map<String, BigDecimal> valueAddMap = LoadCalUtil.addListObject(reportLoadFcList,
                Constants.LOAD_CURVE_POINT_NUM,
                Constants.LOAD_CURVE_START_WITH_ZERO);
            LoadCityFcDO loadCityHisDO = new LoadCityFcDO();
            BasePeriodUtils.setAllFiled(loadCityHisDO, valueAddMap);
            result = loadCityHisDO.getloadList();
        }
        return result;
    }

    @Override
    public List<BigDecimal> findCityFcAdd(String caliberId, String algorithmId, Date targetDay) {
        List<LoadCityFcDO> datas = loadCityFcDAO.query(DBQueryParamBuilder.create().queryDataOnly()
            .where(QueryOp.DateEqualTo, "date", new java.sql.Date(targetDay.getTime()))
            .where(QueryOp.StringEqualTo, "caliberId", caliberId)
            .where(QueryOp.StringEqualTo, "algorithmId", algorithmId)
            .build()).getDatas();
        if (!CollectionUtils.isEmpty(datas)) {
            List<LoadCityFcDO> loadCityFcDOList = datas.stream()
                .filter(loadCityFcDO -> !loadCityFcDO.getCityId().equals(Constants.PROVINCE_ID)).collect(
                    Collectors.toList());
            List<List<BigDecimal>> loadFcList = new ArrayList<>();
            for (LoadCityFcDO loadCityFcDO : loadCityFcDOList) {
                List<BigDecimal> bigDecimals = BasePeriodUtils.toList(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM,
                    Constants.LOAD_CURVE_START_WITH_ZERO);
                loadFcList.add(bigDecimals);
            }
            List<BigDecimal> result = DataUtil.addList(loadFcList);
            return result;
        }
        return null;
    }

    @Override
    public void reportDayForecastResultFileToGuoDiao(Date date) throws Exception {
        // 上报D+4~D+10天的，福建的预测数据到国调服务器
        Date startDate = getStartDate(date);
        Date endDate = DateUtils.addDays(date, 15);
        SystemData systemSetting = settingSystemService.getSystemSetting();
        List<String> queryAlgorithmIds = new ArrayList<>();
        SettingAlgorithmMultiStrategyDTO effectStrategySetting = settingAlgorithmMultiStrategyService.getDailyStrategySetting(CityConstants.PROVINCE_ID, date);
        if (effectStrategySetting != null && CollectionUtil.isNotEmpty(effectStrategySetting.getAlgorithmIds())) {
            queryAlgorithmIds.addAll(effectStrategySetting.getAlgorithmIds());
        } else {
            queryAlgorithmIds.add(systemSetting.getGuodiaoUploadFileAlgorithm());
        }
        List<LoadCityFcDO> reportFcList = loadCityFcDAO.getLoadCityFcDOs(CityConstants.PROVINCE_ID, systemSetting.getGuodiaoUploadFileCaliber(),
                AlgorithmEnum.FORECAST_MODIFY.getId(),startDate, endDate);
        List<LoadCityFcDO> loadCityFcDOs = loadCityFcDAO.getLoadCityFcDOInAlgorithmId(CityConstants.PROVINCE_ID, systemSetting.getGuodiaoUploadFileCaliber(),
                queryAlgorithmIds, startDate, endDate);
        if (CollectionUtils.isEmpty(loadCityFcDOs) && CollectionUtils.isEmpty(reportFcList)) {
            log.error("国调服务器数据上传失败，没有找到对应D+4至D+15的数据");
            throw TsieExceptionUtils.newBusinessException("国调服务器数据上传失败，没有找到对应D+4至D+15的数据");
        }
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        List<LoadCityFcDO> fulledFcList = checkForecastDataList(loadCityFcDOs, dateList, queryAlgorithmIds, reportFcList);
        CityDO provinceInfo = cityService.findCityById(CityConstants.PROVINCE_ID);
        for (GuodiaoReportConfig guodiaoReportConfig : d5000GuoDiaoReportProperties.getReportConfig()) {
            StandardEvaluationContext context = new StandardEvaluationContext();
            ExpressionParser parser = new SpelExpressionParser();
            context.setVariable("cityName", provinceInfo.getCity());
            String rootPathTemplate = guodiaoReportConfig.getGuodiaoLocalPath();
            Expression rootTemplate = parser.parseExpression(rootPathTemplate, new TemplateParserContext());
            context.setVariable("date", DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR));
            String rootPath = rootTemplate.getValue(context, String.class);
            for (LoadCityFcDO loadCityFcDO : fulledFcList) {
                context.setVariable("date", DateUtils.date2String(loadCityFcDO.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR));
                List<BigDecimal> loadList = loadCityFcDO.getloadList();
                String loadForecastValueList = generateDataContent(loadList);
                context.setVariable("loadForecastValueList", loadForecastValueList);
                for (GuodiaoReportFileConfig guodiaoReportFileConfig : guodiaoReportConfig.getGuodiaoReportFileConfigs()) {
                    if ("aloneUpdate".equals(guodiaoReportFileConfig.getFileName()) && "dky".equals(guodiaoReportConfig.getConfigName())) {
                        continue;
                    }
                    String contentTemplate = readTemplate(guodiaoReportFileConfig.getTemplatePath(), guodiaoReportFileConfig.getResultFileEncode());
                    if (StringUtils.isEmpty(contentTemplate)) {
                        log.error("-----模板文件不存在------：{}", guodiaoReportFileConfig.getTemplatePath());
                        continue;
                    }
                    Expression expression = parser.parseExpression(contentTemplate, new TemplateParserContext());
                    String result = expression.getValue(context, String.class);
                    String resultFileNameTemplate = guodiaoReportFileConfig.getResultFileNameTemplate();
                    Expression fileNameParser = parser.parseExpression(resultFileNameTemplate, new TemplateParserContext());
                    String resultFileName = fileNameParser.getValue(context, String.class);
                    writeContentToFile(rootPath, resultFileName, result, guodiaoReportFileConfig.getResultFileEncode());
                    try {
                        FileUploadUtil.uploadFile(result.getBytes(guodiaoReportFileConfig.getResultFileEncode()),
                                guodiaoReportConfig.getGuodiaoRemotePath(), resultFileName,
                                guodiaoReportConfig.getGuodiaoPushIp(),
                                guodiaoReportConfig.getGuodiaoPassWord(),
                                guodiaoReportConfig.getGuodiaoUser(),
                                guodiaoReportConfig.getGuodiaoPort());
                        log.error("---------上传国调文件成功------------" + guodiaoReportConfig.getGuodiaoRemotePath() + resultFileName);
                    } catch (Exception e) {
                        log.error("----------上传国调文件失败----------" + resultFileName + "------" + e.getMessage(), e);
                    }
                }
            }
            String execCommand = guodiaoReportConfig.getExecCommand();
            if (StringUtils.isNotBlank(execCommand)) {
                try {
                    log.error("--------开始执行命令-----------" + execCommand);
                    FileUploadUtil.exeCommand(guodiaoReportConfig.getGuodiaoPushIp(), guodiaoReportConfig.getGuodiaoUser(),
                            guodiaoReportConfig.getGuodiaoPassWord(), guodiaoReportConfig.getGuodiaoPort(), execCommand) ;
                    log.error("--------执行命令成功-----------" + execCommand);
                } catch (Exception e) {
                    log.error("-----------执行命令失败-------------" + execCommand + "------" + e.getMessage(), e);
                }
            }
        }
    }

    @Override
    public void reportDayForecastResultFileToDky(Date date) throws Exception {
        // 上报D+4~D+10天的，福建的预测数据到国调服务器
        Date startDate = DateUtils.addDays(date, 1);
        Date endDate = DateUtils.addDays(date, 10);
        SystemData systemSetting = settingSystemService.getSystemSetting();
        SettingAlgorithmMultiStrategyDTO dailyStrategySetting = settingAlgorithmMultiStrategyService.getDailyStrategySetting(CityConstants.PROVINCE_ID, date);
        Map<String, String> guodiaoUploadFileAlgorithmList = systemSetting.getGuodiaoUploadFileAlgorithmList();
        List<String> queryAlgorithmIds = CollectionUtil.isNotEmpty(guodiaoUploadFileAlgorithmList.values()) ?
                new ArrayList<>(guodiaoUploadFileAlgorithmList.values()) : new ArrayList<>();
        if (dailyStrategySetting != null && CollectionUtil.isNotEmpty(dailyStrategySetting.getAlgorithmIds())) {
            queryAlgorithmIds = dailyStrategySetting.getAlgorithmIds();
        }
        List<LoadCityFcDO> reportFcList = loadCityFcDAO.getLoadCityFcDOs(CityConstants.PROVINCE_ID, systemSetting.getGuodiaoUploadFileCaliber(),
                AlgorithmEnum.FORECAST_MODIFY.getId(),startDate, endDate);
        List<LoadCityFcDO> loadCityFcDOs = loadCityFcDAO.getLoadCityFcDOInAlgorithmId(CityConstants.PROVINCE_ID, systemSetting.getGuodiaoUploadFileCaliber(),
                queryAlgorithmIds, startDate, endDate);
        if (CollectionUtils.isEmpty(loadCityFcDOs)) {
            log.error("电科院服务器数据上传失败，没有找到对应D+1至D+10的数据");
            throw TsieExceptionUtils.newBusinessException("电科院服务器数据上传失败，没有找到对应D+1至D+105的数据");
        }
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        List<LoadCityFcDO> fulledFcList = checkForecastDataList(loadCityFcDOs, dateList, queryAlgorithmIds, reportFcList);
        CityDO provinceInfo = cityService.findCityById(CityConstants.PROVINCE_ID);
        for (GuodiaoReportConfig guodiaoReportConfig : d5000GuoDiaoReportProperties.getReportConfig()) {
            StandardEvaluationContext context = new StandardEvaluationContext();
            ExpressionParser parser = new SpelExpressionParser();
            context.setVariable("cityName", provinceInfo.getCity());
            String rootPathTemplate = guodiaoReportConfig.getGuodiaoLocalPath();
            Expression rootTemplate = parser.parseExpression(rootPathTemplate, new TemplateParserContext());
            context.setVariable("date", DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR));
            String rootPath = rootTemplate.getValue(context, String.class);
            for (LoadCityFcDO loadCityFcDO : fulledFcList) {
                context.setVariable("date", DateUtils.date2String(loadCityFcDO.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR));
                List<BigDecimal> loadList = loadCityFcDO.getloadList();
                String loadForecastValueList = generateDataContent(loadList);
                context.setVariable("loadForecastValueList", loadForecastValueList);
                for (GuodiaoReportFileConfig guodiaoReportFileConfig : guodiaoReportConfig.getGuodiaoReportFileConfigs()) {
                    if ("aloneUpdate".equals(guodiaoReportFileConfig.getFileName()) && "dky".equals(guodiaoReportConfig.getConfigName())) {
                        String contentTemplate = readTemplate(guodiaoReportFileConfig.getTemplatePath(), guodiaoReportFileConfig.getResultFileEncode());
                        if (StringUtils.isEmpty(contentTemplate)) {
                            log.error("-----模板文件不存在------：{}", guodiaoReportFileConfig.getTemplatePath());
                            continue;
                        }
                        Expression expression = parser.parseExpression(contentTemplate, new TemplateParserContext());
                        String result = expression.getValue(context, String.class);
                        String resultFileNameTemplate = guodiaoReportFileConfig.getResultFileNameTemplate();
                        Expression fileNameParser = parser.parseExpression(resultFileNameTemplate, new TemplateParserContext());
                        String resultFileName = fileNameParser.getValue(context, String.class);
                        writeContentToFile(rootPath, resultFileName, result, guodiaoReportFileConfig.getResultFileEncode());
                        try {
                            FileUploadUtil.uploadFile(result.getBytes(guodiaoReportFileConfig.getResultFileEncode()),
                                    guodiaoReportConfig.getGuodiaoRemotePath(), resultFileName,
                                    guodiaoReportConfig.getGuodiaoPushIp(),
                                    guodiaoReportConfig.getGuodiaoPassWord(),
                                    guodiaoReportConfig.getGuodiaoUser(),
                                    guodiaoReportConfig.getGuodiaoPort());
                            log.error("---------上传电科院文件成功------------" + guodiaoReportConfig.getGuodiaoRemotePath() + resultFileName);
                        } catch (Exception e) {
                            log.error("----------上传电科院文件失败----------" + resultFileName + "------" + e.getMessage(), e);
                        }
                    }
                }
            }
            String execCommand = guodiaoReportConfig.getExecCommand();
            if (StringUtils.isNotBlank(execCommand)) {
                try {
                    log.error("--------开始执行命令-----------" + execCommand);
                    FileUploadUtil.exeCommand(guodiaoReportConfig.getGuodiaoPushIp(), guodiaoReportConfig.getGuodiaoUser(),
                            guodiaoReportConfig.getGuodiaoPassWord(), guodiaoReportConfig.getGuodiaoPort(), execCommand) ;
                    log.error("--------执行命令成功-----------" + execCommand);
                } catch (Exception e) {
                    log.error("-----------执行命令失败-------------" + execCommand + "------" + e.getMessage(), e);
                }
            }
        }
        for (LoadCityFcDO loadCityFcDO : fulledFcList) {
            LoadCityFcDO newBpCheckFcDO = new LoadCityFcDO();
            BeanUtils.copyProperties(loadCityFcDO, newBpCheckFcDO, "id");
            String[] normalAlgorithmId = settingSystemService.findByFieldId(SystemConstant.NORMAL_ALGORITHM).getValue().split(Constants.SEPARATOR_PUNCTUATION);
            LoadCityFcDO modifyFcDO = loadCityFcDAO.getLoadCityFcDO(loadCityFcDO.getCityId(), loadCityFcDO.getCaliberId(), AlgorithmEnum.FORECAST_MODIFY.getId(), loadCityFcDO.getDate());
            if (AlgorithmEnum.BP_CHECK_OPTIMIZE.getId().equals(normalAlgorithmId[0]) && modifyFcDO == null) {
                newBpCheckFcDO.setRecommend(true);
                newBpCheckFcDO.setReport(true);
            } else {
                newBpCheckFcDO.setRecommend(false);
                newBpCheckFcDO.setReport(false);
            }
            newBpCheckFcDO.setAlgorithmId(AlgorithmEnum.BP_CHECK_OPTIMIZE.getId());
            newBpCheckFcDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            newBpCheckFcDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            newBpCheckFcDO.setId(null);
            loadCityFcBatchService.doSave(newBpCheckFcDO);
            this.doSaveOrUpdateLoadCityFcDO96(newBpCheckFcDO);
        }
    }

    /**
     * 读取模板文件内容
     */
    private static String readTemplate(String templatePath, String encoding) {
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(new FileInputStream(templatePath), encoding))) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return content.toString();
    }

    private static String generateDataContent(List<BigDecimal> loadList) {
        List<String> valueList = new ArrayList<>();
        for (BigDecimal item : loadList) {
            BigDecimal number = item.setScale(BigDecimalUtils.SCALE, BigDecimalUtils.ROUNDING_MODE);
            String plainString = number.toPlainString();
            valueList.add(plainString);
        }
        String result = String.join(" ", valueList);
        // 输出结果
        return result;
    }

    private void writeContentToFile(String rootPath, String resultFileName, String result, String encoding) {
        try {
            FileUtils.forceMkdir(new File(rootPath));
            File file = new File(rootPath + resultFileName);
            if (!file.exists()) {
                file.createNewFile();
            }
            FileOutputStream fos = new FileOutputStream(file);
            OutputStreamWriter writer = new OutputStreamWriter(fos, encoding);
            writer.write(result);
            writer.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    private List<LoadCityFcDO> checkForecastDataList(List<LoadCityFcDO> loadCityFcDOs, List<Date> dateList,
                                                     List<String> guodiaoUploadFileAlgorithmList, List<LoadCityFcDO> reportFcList) {
        List<LoadCityFcDO> result = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(guodiaoUploadFileAlgorithmList)) {
            Map<String, List<LoadCityFcDO>> groupMap = loadCityFcDOs.stream().collect(Collectors.groupingBy(
                    x -> DateUtils.date2String(x.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR) + "-" + x.getAlgorithmId()));
            List<LoadCityFcDO> filterList = new ArrayList<>();
            for (int i = 0; i < dateList.size(); i++) {
                String dateStr = DateUtils.date2String(dateList.get(i), DateFormatType.SIMPLE_DATE_FORMAT_STR);
                String algorithmId = null;
                if (i < guodiaoUploadFileAlgorithmList.size()) {
                    guodiaoUploadFileAlgorithmList.get(i);
                } else {
                    algorithmId = guodiaoUploadFileAlgorithmList.get(guodiaoUploadFileAlgorithmList.size() - 1);
                }
                if (groupMap.containsKey(dateStr + "-" + algorithmId)) {
                    filterList.add(groupMap.get(dateStr + "-" + algorithmId).get(0));
                }
            }
            if (CollectionUtil.isNotEmpty(filterList)) {
                loadCityFcDOs = filterList;
            }
        }
        List<String> dataStrList = dateList.stream().map(x -> DateUtils.date2String(x, DateFormatType.SIMPLE_DATE_FORMAT_STR)).collect(Collectors.toList());
        Map<String, LoadCityFcDO> reportFcMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(reportFcList)) {
            reportFcMap = reportFcList.stream().collect(Collectors.toMap(x ->
                    DateUtils.date2String(x.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR), x -> x, (key1, key2) -> key2));
        }
        LoadCityFcDO lastDayForecastResult = loadCityFcDOs.stream().collect(Collectors.maxBy(Comparator.comparing(LoadCityFcDO::getDate))).get();
        Map<String, LoadCityFcDO> loadFcMap = loadCityFcDOs.stream().collect(Collectors.toMap(x -> DateUtils.date2String(x.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR),
                Function.identity(), (key1, key2) -> key2));
        for (String dateStr : dataStrList) {
            if (MapUtil.isNotEmpty(reportFcMap) && reportFcMap.containsKey(dateStr)) {
                result.add(reportFcMap.get(dateStr));
                continue;
            }
            if (loadFcMap.containsKey(dateStr)) {
                result.add(loadFcMap.get(dateStr));
            } else {
                LoadCityFcDO copy = new LoadCityFcDO();
                BeanUtils.copyProperties(lastDayForecastResult, copy);
                long time = DateUtils.string2Date(dateStr, DateFormatType.SIMPLE_DATE_FORMAT_STR).getTime();
                copy.setDate(new java.sql.Date(time));
                result.add(copy);
            }
        }
        return result;
    }

    private Date getStartDate(Date date) {
        int addDays = 2;
        int dayOfWeek = DateUtil.getDayOfWeek(date);
        if (dayOfWeek <= 5) {
            addDays = 2;
        } else if (dayOfWeek == 6) {
            addDays = 4;
        } else {
            addDays = 3;
        }
        return DateUtils.addDays(date, addDays);
    }
}
