/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.load.impl;

import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.AirConditionerLoadBaseManageService;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.FoundationLoadHisMonthService;
import com.tsintergy.lf.serviceapi.base.airconditioner.dto.BaseLoadCurveRespDTO;
import com.tsintergy.lf.serviceapi.base.airconditioner.dto.LoadSolutionManageDTO;
import com.tsintergy.lf.serviceapi.base.airconditioner.dto.LoadSolutionManageRespDTO;
import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.FoundationLoadHisMonthDO;
import com.tsintergy.lf.serviceapi.base.common.enumeration.DateType2;
import com.tsintergy.lf.serviceimpl.load.dao.FoundationLoadHisMonthDAO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Description:<br>
 *
 * @Author:<EMAIL>
 * @Date: 2022/5/24 10:43
 * @Version:1.0.0
 */
@Slf4j
@Service("foundationLoadHisMonthService")
public class FoundationLoadHisMonthServiceImpl implements FoundationLoadHisMonthService {

    @Autowired
    FoundationLoadHisMonthDAO foundationLoadHisMonthDAO;

    @Autowired
    FoundationLoadHisMonthService foundationLoadHisMonthService;

    @Autowired
    private AirConditionerLoadBaseManageService airConditionerLoadBaseManageService;

    @Override
    public void doSave(FoundationLoadHisMonthDO foundationLoadHisMonthDO) {
        foundationLoadHisMonthDAO.save(foundationLoadHisMonthDO);
    }

    @Override
    public void doUpdate(FoundationLoadHisMonthDO foundationLoadHisMonthDO) {
        foundationLoadHisMonthDAO.update(foundationLoadHisMonthDO);
    }

    @Override
    public List<FoundationLoadHisMonthDO> getFoundationLoadHisMonth(String cityId, String caliberId, String year,
                                                                    String month, Integer type) {
        return foundationLoadHisMonthDAO.getFoundationLoadHisMonth(cityId, caliberId, year, month, type);
    }

    @Override
    public void wrapperNowYearBasicLoad(String caliberId, String cityId, int nowMonthNum, int nowYear, int type) throws Exception {

        List<BigDecimal> basicLoad = getNowYearBasicLoad(caliberId, cityId, type, nowMonthNum, nowYear);

        // 保存空调负荷
        if (CollectionUtils.isEmpty(basicLoad)) {
            return;
        }
        String monthStr = nowMonthNum >= 10 ? nowMonthNum + "" : "0" + nowMonthNum;
        List<FoundationLoadHisMonthDO> foundationLoadHisMonth = foundationLoadHisMonthService.getFoundationLoadHisMonth(cityId, caliberId, nowYear + "", monthStr, type);
        if (CollectionUtils.isEmpty(foundationLoadHisMonth)) {
            FoundationLoadHisMonthDO foundationLoadHisMonthDO = new FoundationLoadHisMonthDO();
            Map<String, BigDecimal> toMap = ColumnUtil.listToMap(basicLoad, Constants.LOAD_CURVE_START_WITH_ZERO);
            BasePeriodUtils.setAllFiled(foundationLoadHisMonthDO, toMap);
            foundationLoadHisMonthDO.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
            foundationLoadHisMonthDO.setMonth(monthStr);
            foundationLoadHisMonthDO.setYear(nowYear + "");
            foundationLoadHisMonthDO.setCityId(cityId);
            foundationLoadHisMonthDO.setCaliberId(caliberId);
            foundationLoadHisMonthDO.setType(type);
            foundationLoadHisMonthDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            foundationLoadHisMonthDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            foundationLoadHisMonthService.doSave(foundationLoadHisMonthDO);
        } else {
            FoundationLoadHisMonthDO foundationLoadHisMonthDO = foundationLoadHisMonth.get(0);
            Map<String, BigDecimal> toMap = ColumnUtil.listToMap(basicLoad, Constants.LOAD_CURVE_START_WITH_ZERO);
            BasePeriodUtils.setAllFiled(foundationLoadHisMonthDO, toMap);
            foundationLoadHisMonthDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            foundationLoadHisMonthService.doUpdate(foundationLoadHisMonthDO);
        }

    }

    //查询当年的方案的基础负荷曲线
    private List<BigDecimal> getNowYearBasicLoad(String caliberId, String cityId, int type, int nowMonthNum, int nowYear) throws Exception {
        LoadSolutionManageDTO loadSolutionManageDTO = new LoadSolutionManageDTO();
        loadSolutionManageDTO.setCaliberId(caliberId);
        loadSolutionManageDTO.setCityId(cityId);
        switch (type) {
            case 1:
                loadSolutionManageDTO.setDateType(DateType2.WORKDAY);
                break;
            case 2:
                loadSolutionManageDTO.setDateType(DateType2.SATURDAY);
                break;
            case 3:
                loadSolutionManageDTO.setDateType(DateType2.WEEKEND);
                break;
            case 4:
                loadSolutionManageDTO.setDateType(DateType2.REST);
                break;
            default:
                ;
        }
        loadSolutionManageDTO.setStartYear(String.valueOf(nowYear));
        loadSolutionManageDTO.setEndYear(String.valueOf(nowYear));

        int season = DateUtil.getSeason(nowMonthNum);
        final String seasonStr = String.valueOf(season);
        if (seasonStr.equals("4") && nowMonthNum <= 3) {
            //如果是冬季，且月份在1、2、3月份，那么要查前一年冬季的方案
            loadSolutionManageDTO.setStartYear(String.valueOf(nowYear - 1));
            loadSolutionManageDTO.setEndYear(String.valueOf(nowYear - 1));
        }

        List<LoadSolutionManageRespDTO> loadSolutionManageRespDTOS = airConditionerLoadBaseManageService.queryLoadSolutionManage(loadSolutionManageDTO);
        if (CollectionUtils.isEmpty(loadSolutionManageRespDTOS)) {
            log.info("年：" + nowYear + ",城市id:" + cityId + ",口径id:" + caliberId + ",日类型:" + type + "基础方案为空");
            return null;
        } else {

            List<LoadSolutionManageRespDTO> enable = loadSolutionManageRespDTOS.stream().filter(t -> seasonStr.equals(t.getSeason()) && type == t.getDateType().getId()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(enable)) {
                log.info(nowYear + "," + seasonStr + "," + type + "," + cityId + "," + caliberId + "未制定基础负荷方案");
                return null;
            } else {
                LoadSolutionManageRespDTO loadSolutionManageRespDTO = new LoadSolutionManageRespDTO();
                if (enable.size() == 1) {
                    // 取默认的
                    loadSolutionManageRespDTO = enable.get(0);
                } else {
                    // 取启用的
                    List<LoadSolutionManageRespDTO> enableList = enable.stream().filter(t -> t.getEnableCurve()).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(enableList)) {
                        loadSolutionManageRespDTO = enable.get(0);
                    } else {
                        loadSolutionManageRespDTO = enableList.get(0);
                    }
                }
                //BaseLoadCurveRespDTO resp = airConditionerLoadBaseManageService.queryBaseLoadCurve(loadSolutionManageRespDTO.getId());
                BaseLoadCurveRespDTO resp = airConditionerLoadBaseManageService.queryBaseNewLoadCurve(loadSolutionManageRespDTO.getId());
                if (resp == null) {
                    log.info(nowYear + "," + seasonStr + "," + type + "," + cityId + "," + caliberId + "生成的基础负荷曲线为空");
                    return null;
                }
                List<BigDecimal> dataValue = resp.getDataValue();
                return dataValue;
            }
        }
    }
}