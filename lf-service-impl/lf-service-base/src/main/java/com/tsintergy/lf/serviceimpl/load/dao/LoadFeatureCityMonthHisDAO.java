package com.tsintergy.lf.serviceimpl.load.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityMonthHisDO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @version $Id: LoadFeatureCityMonthHisDAO.java, v 0.1 2018-01-31 10:51:34 tao Exp $$
 */

@Component
public class LoadFeatureCityMonthHisDAO extends BaseAbstractDAO<LoadFeatureCityMonthHisDO> {

    private static final Logger logger = LogManager.getLogger(LoadFeatureCityMonthHisDAO.class);
    /**
     * 统计月负荷特性
     * @param loadFeatureCityDayHisVOs 一个月的日负荷特性
     * @return
     * @throws Exception
     */
    public LoadFeatureCityMonthHisDO statisticsMonthFeature(List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisVOs) throws Exception {

        if(loadFeatureCityDayHisVOs.size() > 31) {
            logger.error("统计月负荷特性有误：日负荷特性数据的超过31天，无法统计");
            return null;
        }

        if(loadFeatureCityDayHisVOs != null && loadFeatureCityDayHisVOs.size() > 0) {

            String cityId = loadFeatureCityDayHisVOs.get(0).getCityId();
            String caliberId = loadFeatureCityDayHisVOs.get(0).getCaliberId();
            String ym = DateUtil.getMonthByDate(loadFeatureCityDayHisVOs.get(0).getDate()); // 年月

            BigDecimal maxLoad = null; // 最大负荷
            BigDecimal minLoad = null; // 最小负荷
            String maxTime = null;
            String minTime = null;
            Date maxDate = null;

            List<BigDecimal> avgLoads = new ArrayList<BigDecimal>();
            List<BigDecimal> peaks = new ArrayList<BigDecimal>();
            List<BigDecimal> troughs = new ArrayList<BigDecimal>();
            List<BigDecimal> maxLoads = new ArrayList<BigDecimal>();
            BigDecimal energy = new BigDecimal(0).setScale(4);

            for(LoadFeatureCityDayHisDO loadFeatureCityDayHisVO : loadFeatureCityDayHisVOs) {

                if(!ym.equals(DateUtil.getMonthByDate(loadFeatureCityDayHisVO.getDate()))) {
                    logger.error("统计月负荷特性有误：日负荷特性数据的日期不是同一个月，无法统计");
                    return null;
                }

                if(maxLoad == null || maxLoad.compareTo(loadFeatureCityDayHisVO.getMaxLoad()) < 0) {
                    maxLoad = loadFeatureCityDayHisVO.getMaxLoad();
                    maxTime = loadFeatureCityDayHisVO.getMaxTime();
                    maxDate = loadFeatureCityDayHisVO.getDate();
                }

                if(minLoad == null || minLoad.compareTo(loadFeatureCityDayHisVO.getMinLoad()) > 0) {
                    minLoad = loadFeatureCityDayHisVO.getMinLoad();
                    minTime = loadFeatureCityDayHisVO.getMinTime();
                }

                avgLoads.add(loadFeatureCityDayHisVO.getAveLoad());
                peaks.add(loadFeatureCityDayHisVO.getPeak());
                troughs.add(loadFeatureCityDayHisVO.getTrough());
                maxLoads.add(loadFeatureCityDayHisVO.getMaxLoad());
                energy = BigDecimalUtils.add(energy,loadFeatureCityDayHisVO.getEnergy());

            }

            LoadFeatureCityMonthHisDO loadFeatureCityMonthHisVO = new LoadFeatureCityMonthHisDO();
            loadFeatureCityMonthHisVO.setCityId(cityId);
            loadFeatureCityMonthHisVO.setCaliberId(caliberId);
            loadFeatureCityMonthHisVO.setYear(ym.substring(0,4));
            loadFeatureCityMonthHisVO.setMonth(ym.substring(5,7));
            loadFeatureCityMonthHisVO.setMaxDate(maxDate);
            loadFeatureCityMonthHisVO.setMaxTime(maxTime);
            loadFeatureCityMonthHisVO.setMinTime(minTime);
            loadFeatureCityMonthHisVO.setMaxLoad(maxLoad);
            loadFeatureCityMonthHisVO.setMinLoad(minLoad);
            loadFeatureCityMonthHisVO.setAveLoad(BigDecimalUtils.avgList(avgLoads,4,false));
            loadFeatureCityMonthHisVO.setDifferent(BigDecimalUtils.sub(loadFeatureCityMonthHisVO.getMaxLoad(),loadFeatureCityMonthHisVO.getMinLoad()));
            loadFeatureCityMonthHisVO.setGradient(BigDecimalUtils.divide(loadFeatureCityMonthHisVO.getDifferent(),loadFeatureCityMonthHisVO.getMaxLoad(),4));
            loadFeatureCityMonthHisVO.setLoadGradient(BigDecimalUtils.divide(loadFeatureCityMonthHisVO.getAveLoad(),loadFeatureCityMonthHisVO.getMaxLoad(),4));
            loadFeatureCityMonthHisVO.setPeak(BigDecimalUtils.avgList(peaks,4,true));
            loadFeatureCityMonthHisVO.setTrough(BigDecimalUtils.avgList(troughs,4,false));
            loadFeatureCityMonthHisVO.setEnergy(energy);
            // 日不均衡系数 = average(月内各日最大负荷)/月最大负荷
            loadFeatureCityMonthHisVO.setDayUnbalance(BigDecimalUtils.avgList(maxLoads,4,false));

            return loadFeatureCityMonthHisVO;

        }

        return null;

    }


    /**
     * 统计月负荷特性
     * @param loadFeatureCityDayHisVOs 日负荷特性列表
     * @return
     * @throws Exception
     */
    public List<LoadFeatureCityMonthHisDO> statisticsMonthFeatures(List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisVOs) throws Exception {

        List<LoadFeatureCityMonthHisDO> loadFeatureCityMonthHisVOs = new ArrayList<LoadFeatureCityMonthHisDO>();

        if(loadFeatureCityDayHisVOs != null) {

            Map<String,List<LoadFeatureCityDayHisDO>> loadFeatureMap = new HashMap<String, List<LoadFeatureCityDayHisDO>>(); // Map<cityId_ym,List<LoadFeatureCityDayHisDO>>
            for(LoadFeatureCityDayHisDO loadFeatureCityDayHisVO : loadFeatureCityDayHisVOs) {
                String key = loadFeatureCityDayHisVO.getCityId() + "_" + loadFeatureCityDayHisVO.getCaliberId() + "_" + DateUtil.getMonthByDate(loadFeatureCityDayHisVO.getDate());
                if(!loadFeatureMap.containsKey(key)) {
                    loadFeatureMap.put(key,new ArrayList<LoadFeatureCityDayHisDO>());
                }
                loadFeatureMap.get(key).add(loadFeatureCityDayHisVO);
            }

            for(String key : loadFeatureMap.keySet()) {
                try {
                    LoadFeatureCityMonthHisDO loadFeatureCityMonthHisVO = statisticsMonthFeature(loadFeatureMap.get(key));
                    loadFeatureCityMonthHisVOs.add(loadFeatureCityMonthHisVO);
                } catch (Exception e) {
                    logger.error("统计月负荷特性出错了",e);
                }
            }

        }

        return loadFeatureCityMonthHisVOs;
    }

    /**
     * 获取月负荷特性
     * @param cityId 城市ID
     * @param year 年(yyyy)
     * @param month 月(MM)
     * @return
     */
    public LoadFeatureCityMonthHisDO getLoadFeatureCityMonthHisVO(String cityId, String year, String month, String caliberId) throws Exception {

        if(cityId == null) {
            throw new BusinessException("","城市ID不可为空");
        }

        if(year == null) {
            throw new BusinessException("","年份不可为空");
        }

        if(month == null) {
            throw new BusinessException("","月份不可为空");
        }

        if (caliberId == null){
            throw new BusinessException("","口径ID不可为空");
        }

        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        param.getQueryConditions().put("_ne_year",year);
        param.getQueryConditions().put("_ne_month",month);
        param.getQueryConditions().put("_ne_cityId", cityId);
        param.getQueryConditions().put("_ne_caliberId", caliberId);
        List<LoadFeatureCityMonthHisDO> loadFeatureCityMonthHisVOs = this.query(param).getDatas();
        if(loadFeatureCityMonthHisVOs.size() > 0) {
            return loadFeatureCityMonthHisVOs.get(0);
        }
        return null;
    }

    public List<LoadFeatureCityMonthHisDO> getLoadFeatureCityMonthHisVOList(String cityId, String year, String month, String caliberId) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);

        if(cityId != null) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }

        if(year != null) {
            param.getQueryConditions().put("_ne_year",year);
        }

        if(month != null) {
            param.getQueryConditions().put("_ne_month",month);
        }

        if (caliberId != null){
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }

        List<LoadFeatureCityMonthHisDO> loadFeatureCityMonthHisVOs = this.query(param).getDatas();
        return loadFeatureCityMonthHisVOs;
    }


    /**
     * 获取月负荷特性
     * @param cityId 城市ID
     * @param year 年(yyyy)
     * @return
     */
    public List<LoadFeatureCityMonthHisDO>getLoadFeatureCityMonthHisVO(String cityId, String year, String caliberId) throws Exception {

        if(cityId == null) {
            throw new BusinessException("","城市ID不可为空");
        }
        if(year == null) {
            throw new BusinessException("","年份不可为空");
        }
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        param.getQueryConditions().put("_ne_year",year);
        param.getQueryConditions().put("_ne_cityId", cityId);
        if (caliberId != null){
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        List<LoadFeatureCityMonthHisDO> loadFeatureCityMonthHisVOs = this.query(param).getDatas();
        return loadFeatureCityMonthHisVOs;
    }


    /**
     * 保存或更新
     * @param loadFeatureCityMonthHisVO
     * @return
     */
    public LoadFeatureCityMonthHisDO doSaveOrUpdateLoadFeatureCityMonthHisVO(LoadFeatureCityMonthHisDO loadFeatureCityMonthHisVO) throws Exception {
        if(loadFeatureCityMonthHisVO == null) {
            return null;
        }
        LoadFeatureCityMonthHisDO oldVO = getLoadFeatureCityMonthHisVO(loadFeatureCityMonthHisVO.getCityId(), loadFeatureCityMonthHisVO.getYear(), loadFeatureCityMonthHisVO.getMonth(), loadFeatureCityMonthHisVO.getCaliberId());
        if(oldVO != null) {
            String id = oldVO.getId();
            BeanUtils.copyProperties(loadFeatureCityMonthHisVO,oldVO,"createtime");
            oldVO.setId(id);
            oldVO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            return (LoadFeatureCityMonthHisDO)this.updateAndFlush(oldVO);
        }
        else {
            loadFeatureCityMonthHisVO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            return (LoadFeatureCityMonthHisDO)this.createAndFlush(loadFeatureCityMonthHisVO);
        }
    }

    /**
     * 保存或更新
     * @param loadFeatureCityMonthHisVOs
     * @return
     */
    public List<LoadFeatureCityMonthHisDO> doSaveOrUpdateLoadFeatureCityMonthHisDOs(List<LoadFeatureCityMonthHisDO> loadFeatureCityMonthHisVOs) throws Exception {
        List<LoadFeatureCityMonthHisDO> vos = new ArrayList<LoadFeatureCityMonthHisDO>();
        for(LoadFeatureCityMonthHisDO loadFeatureCityMonthHisVO : loadFeatureCityMonthHisVOs) {
            try {
                vos.add(this.doSaveOrUpdateLoadFeatureCityMonthHisVO(loadFeatureCityMonthHisVO));
            }
            catch (Exception e) {
                logger.error("保存月负荷特性出错了",e);
            }
        }
        return vos;
    }

    /**
     * 获取月负荷特性
     * @param cityId 城市ID
     * @param startYM 开始月份 yyyy-MM
     * @param endYM 结束月份 yyyy-MM
     * @return
     */
    public List<LoadFeatureCityMonthHisDO> getLoadFeatureCityMonthHisVOs(String cityId, String startYM, String endYM, String caliberId) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if(null != startYM){
            param.getQueryConditions().put("_snl_year",startYM.substring(0,4));
        }
        if (null != endYM){
            param.getQueryConditions().put("_snm_year",endYM.substring(0,4));
        }
        if (null != cityId) {
            param.getQueryConditions().put("_se_cityId", cityId);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_se_caliberId", caliberId);
        }
        param.setOrderby("year,month");
        param.setDesc("0,0");
        List<LoadFeatureCityMonthHisDO> loadFeatureCityMonthHisVOs = query(param).getDatas();
        List<LoadFeatureCityMonthHisDO> loadFeatureCityMonthHisVOs2 = new ArrayList<LoadFeatureCityMonthHisDO>();
        if (startYM!=null&&endYM!=null){
            for(LoadFeatureCityMonthHisDO loadFeatureCityMonthHisVO : loadFeatureCityMonthHisVOs) {
                if((loadFeatureCityMonthHisVO.getYear()+"-"+loadFeatureCityMonthHisVO.getMonth()).compareTo(startYM) > -1 && (loadFeatureCityMonthHisVO.getYear()+"-"+loadFeatureCityMonthHisVO.getMonth()).compareTo(endYM) < 1) {
                    loadFeatureCityMonthHisVOs2.add(loadFeatureCityMonthHisVO);
                }
            }
        }else {
            for(LoadFeatureCityMonthHisDO loadFeatureCityMonthHisVO : loadFeatureCityMonthHisVOs) {
                    loadFeatureCityMonthHisVOs2.add(loadFeatureCityMonthHisVO);
            }
        }

        return loadFeatureCityMonthHisVOs2;
    }

}