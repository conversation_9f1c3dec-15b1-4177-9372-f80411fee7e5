/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  Lenovo
 * Date:  2019/4/16 17:06
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.load.impl;


import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.core.util.PeriodDataUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.common.enumeration.ErrorType;
import com.tsintergy.lf.serviceapi.base.datamanage.api.DataCheckInfoService;
import com.tsintergy.lf.serviceapi.base.datamanage.pojo.DataCheckInfoDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisClctService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCompareService;
import com.tsintergy.lf.serviceapi.base.load.dto.*;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisClctDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/4/16 
 * @since 1.0.0
 */
@Service("loadCompareService")
public class LoadCompareServiceImpl implements LoadCompareService {

    private final Logger logger = LoggerFactory.getLogger(LoadCompareServiceImpl.class);

    @Autowired
    LoadCityHisService loadCityHisService;

    @Autowired
    LoadCityHisClctService loadCityHisClctService;

    @Autowired
    DataCheckInfoService dataCheckInfoService;

    @Autowired
    CityService cityService;

    @Override
    public LoadCompareDTO findLoadCompareDTO(Date date, String cityId, String caliberId) throws Exception {
        try {
            List<LoadCityHisDO> loadCityHisVOList = loadCityHisService.getLoadCityHisDOS(cityId, caliberId, date, date);
            if (loadCityHisVOList == null || loadCityHisVOList.size() < 1) {
                throw new BusinessException("T706","");
            }
            List<LoadCityHisClctDO> loadCityHisClctVOList = loadCityHisClctService.findLoadCityHisVOS(cityId, date, date, caliberId);
            if (loadCityHisClctVOList == null || loadCityHisClctVOList.size() < 1) {
                throw new BusinessException("T706","");
            }
            LoadRepairDTO colcRepairDTO = generateColcLoadRepairDTO(loadCityHisClctVOList.get(0));
            LoadRepairDTO hisRepairDTO = generateHisLoadRepairDTO(loadCityHisVOList.get(0));
            LoadCompareDTO loadCompareDTO = new LoadCompareDTO();
            loadCompareDTO.setRepair(hisRepairDTO);
            loadCompareDTO.setCollect(colcRepairDTO);
            return loadCompareDTO;

        } catch (Exception e) {
            logger.error("查看修正前后的数据异常。。。", e);
            throw e;
        }
    }

    @Override
    public LoadErrorDTO findLoadErroDTO(Date startDate, Date endDate, String cityId, String caliberId) {
        int nullCount = 0;
        int zeroCount = 0;
        int constantCount = 0;
        int jumpCount = 0;
        int count = 0;
        try {
            List<DataCheckInfoDO> dataCheckInfoVOS = dataCheckInfoService.findLoadDataCheckInfoDO(cityId,caliberId,new java.sql.Date(startDate.getTime()),new java.sql.Date(endDate.getTime()));
            if (dataCheckInfoVOS != null && dataCheckInfoVOS.size() > 0) {
                for (DataCheckInfoDO dataCheckInfoVO : dataCheckInfoVOS) {
                    count++;
                    if (ErrorType.NULL_POINT.getType() == dataCheckInfoVO.getErrorType()) {
                        nullCount++;
                        continue;
                    }
                    if (ErrorType.ZERO_POINT.getType() == dataCheckInfoVO.getErrorType()) {
                        zeroCount++;
                        continue;
                    }
                    if (ErrorType.CONSTANT_POINT.getType() == dataCheckInfoVO.getErrorType()) {
                        constantCount++;
                        continue;
                    }
                    if (ErrorType.JUMP_POINT.getType() == dataCheckInfoVO.getErrorType()) {
                        jumpCount++;
                        continue;
                    }
                }
            }
            LoadErrorDTO loadErrorDTO = new LoadErrorDTO();
            loadErrorDTO.setConstantCount(constantCount);
            loadErrorDTO.setConstantPercent(BigDecimal.ZERO);
            if (constantCount != 0) {
                loadErrorDTO.setConstantPercent(new BigDecimal(constantCount).divide(new BigDecimal(count),4,BigDecimal.ROUND_HALF_UP));
            }
            loadErrorDTO.setJumpPercent(BigDecimal.ZERO);
            loadErrorDTO.setJumpCount(jumpCount);
            if (jumpCount != 0) {
                loadErrorDTO.setJumpPercent(new BigDecimal(jumpCount).divide(new BigDecimal(count),4,BigDecimal.ROUND_HALF_UP));
            }
            loadErrorDTO.setNullPercent(BigDecimal.ZERO);
            loadErrorDTO.setNullCount(nullCount);
            if (nullCount != 0) {
                loadErrorDTO.setNullPercent(new BigDecimal(nullCount).divide(new BigDecimal(count),4,BigDecimal.ROUND_HALF_UP));
            }
            loadErrorDTO.setZeroPercent(BigDecimal.ZERO);
            loadErrorDTO.setZeroCount(zeroCount);
            if (zeroCount != 0) {
                loadErrorDTO.setZeroPercent(new BigDecimal(zeroCount).divide(new BigDecimal(count),4,BigDecimal.ROUND_HALF_UP));
            }
            return loadErrorDTO;
        } catch (Exception e) {
            logger.error("查询异常数据错误...",e);
            throw new BusinessException("T706","",e);
        }
    }

    @Override
    public List<LoadRepairInfoDTO> findLoadRepairInfoDTOS(String cityId, String caliberId, Date startDate, Date endDate) throws Exception {

        try {
            List<DataCheckInfoDO> dataCheckInfoVOS = dataCheckInfoService.findLoadDataCheckInfoDO(cityId,caliberId,new java.sql.Date(startDate.getTime()),new java.sql.Date(endDate.getTime()));
            //根据日期和属性名划分
            Map<String,Map<String,DataCheckInfoDO>> dataCheckInfoVOMap = devideDataCheckInfoVOByDateAndField(dataCheckInfoVOS);
            List<LoadCityHisDO> loadCityHisVOS = loadCityHisService.getLoadCityHisDOS(cityId,caliberId,startDate,endDate);
            if (loadCityHisVOS == null || loadCityHisVOS.size() < 1) {
                throw new BusinessException("T706","");
            }
            List<LoadRepairInfoDTO> loadRepairInfoDTOS = new ArrayList<>();
            Map<String, String> cityNameMap = cityService.findAllCitys().stream().collect(Collectors.toMap(CityDO::getId, CityDO::getCity));
            for (LoadCityHisDO loadCityHisVO : loadCityHisVOS) {
                LoadRepairInfoDTO loadRepairInfoDTO = new LoadRepairInfoDTO();
                loadRepairInfoDTO.setCity(cityNameMap.get(loadCityHisVO.getCityId()));
                loadRepairInfoDTO.setCityId(loadCityHisVO.getCityId());
                loadRepairInfoDTO.setDate(loadCityHisVO.getDate());
                loadRepairInfoDTO.setWeek(DateUtil.getWeek(loadCityHisVO.getDate()));

                List<ErrorPoint> errorPoints = new ArrayList<>();
                Map<String,DataCheckInfoDO> dataMap = dataCheckInfoVOMap.get(DateUtils.date2String(loadCityHisVO.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR));
                String[] fieldNames = PeriodDataUtil.column96;
                for (String fieldName : fieldNames) {
                    String getMethodName = "get" + fieldName.substring(0,1).toUpperCase() + fieldName.substring(1);
                    Method getMethod = LoadCityHisDO.class.getMethod(getMethodName,null);
                    Object value = getMethod.invoke(loadCityHisVO,null);
                    ErrorPoint errorPoint = new ErrorPoint();
                    if (value != null) {
                        errorPoint.setRepairValue(new BigDecimal(String.valueOf(value)));
                        if (dataMap != null && dataMap.keySet().contains(fieldName)) {
                            errorPoint.setErrorType(dataMap.get(fieldName).getErrorType());
                        }
                    }
                    errorPoints.add(errorPoint);
                }

                loadRepairInfoDTO.setData(errorPoints);
                loadRepairInfoDTOS.add(loadRepairInfoDTO);
            }
            return loadRepairInfoDTOS;
        } catch (BusinessException b) {
            throw b;
        } catch (Exception e) {
            logger.error("查询修复数据异常信息错误。。。",e);
            throw e;
        }
    }

    private Map<String, Map<String, DataCheckInfoDO>> devideDataCheckInfoVOByDateAndField(List<DataCheckInfoDO> dataCheckInfoVOS) {
        Map<String,Map<String,DataCheckInfoDO>> mapMap = new HashMap<>();
        for (DataCheckInfoDO dataCheckInfoVO : dataCheckInfoVOS) {
            String date = DateUtils.date2String(dataCheckInfoVO.getDate(),DateFormatType.SIMPLE_DATE_FORMAT_STR);
            if (mapMap.get(date) == null) {
                Map<String, DataCheckInfoDO> dataCheckInfoVOMap = new HashMap<>();
                mapMap.put(date,dataCheckInfoVOMap);
            }
            mapMap.get(date).put(dataCheckInfoVO.getField(),dataCheckInfoVO);
        }
        return mapMap;
    }

    private LoadRepairDTO generateHisLoadRepairDTO(LoadCityHisDO loadCityHisVO) throws Exception {
        LoadRepairDTO hisRepairDTO = new LoadRepairDTO();
        hisRepairDTO.setWeek(DateUtil.getWeek(loadCityHisVO.getDate()));
        hisRepairDTO.setDate(loadCityHisVO.getDate());
        CityDO cityVO = cityService.findCityById(loadCityHisVO.getCityId());
        hisRepairDTO.setCity(cityVO.getCity());
        List<BigDecimal> list = BasePeriodUtils.toList(loadCityHisVO,96, Constants.LOAD_CURVE_START_WITH_ZERO);
        hisRepairDTO.setData(list);
        return hisRepairDTO;
    }

    private LoadRepairDTO generateColcLoadRepairDTO(LoadCityHisClctDO loadCityHisClctVO) throws Exception {
        LoadRepairDTO colcRepairDTO = new LoadRepairDTO();
        CityDO cityVO = cityService.findCityById(loadCityHisClctVO.getCityId());
        colcRepairDTO.setCity(cityVO.getCity());
        List<BigDecimal> list = BasePeriodUtils.toList(loadCityHisClctVO,96,Constants.LOAD_CURVE_START_WITH_ZERO);
        colcRepairDTO.setData(list);
        colcRepairDTO.setDate(loadCityHisClctVO.getDate());
        colcRepairDTO.setWeek(DateUtil.getWeek(loadCityHisClctVO.getDate()));
        return colcRepairDTO;
    }

    private Map<String, LoadCityHisClctDO> devideLoadCityHisCollectVOByDate(List<LoadCityHisClctDO> loadCityHisClctVOList) {
        Map<String,LoadCityHisClctDO> map = new HashMap();
        for (LoadCityHisClctDO loadCityHisClctVO : loadCityHisClctVOList) {
            String date = DateUtils.date2String(loadCityHisClctVO.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR);
            if (map.get(date) == null) {
                map.put(date,loadCityHisClctVO);
            }
        }
        return map;
    }

    private Map<String, LoadCityHisDO> devideLoadCityHisVOByDate(List<LoadCityHisDO> loadCityHisVOList) {
        Map<String,LoadCityHisDO> map = new HashMap();
        for (LoadCityHisDO loadCityHisVO : loadCityHisVOList) {
            String date = DateUtils.date2String(loadCityHisVO.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR);
            if (map.get(date) == null) {
                map.put(date,loadCityHisVO);
            }
        }
        return map;
    }


}
