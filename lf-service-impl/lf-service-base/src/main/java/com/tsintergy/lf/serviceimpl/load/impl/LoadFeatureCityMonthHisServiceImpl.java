
package com.tsintergy.lf.serviceimpl.load.impl;


import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.service.BaseServiceImpl;
import org.springframework.beans.BeanUtils;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityMonthHisService;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureExtendDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityMonthHisDO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadFeatureCityMonthHisDAO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $Id: LoadFeatureCityMonthHisServiceImpl.java, v 0.1 2018-01-31 10:51:45 tao Exp $$
 */
@Service("loadFeatureCityMonthHisService")
public class LoadFeatureCityMonthHisServiceImpl extends BaseServiceImpl implements LoadFeatureCityMonthHisService {

    private static final Logger logger = LogManager.getLogger( LoadFeatureCityMonthHisServiceImpl.class);

    @Autowired
    LoadFeatureCityMonthHisDAO loadFeatureCityMonthHisDAO;

    @Autowired
    CityService cityService;

    @Override
    public DataPackage queryLoadFeatureCityMonthHisDO(DBQueryParam param) throws Exception{
        try {
            return loadFeatureCityMonthHisDAO.query(param);
        }
        catch (BusinessException e) {
            throw e;
        }
        catch (Exception e) {
            throw e;
        }
    }
    
    @Override
    public LoadFeatureCityMonthHisDO doCreate(LoadFeatureCityMonthHisDO vo) throws Exception{
        try {
            return (LoadFeatureCityMonthHisDO)loadFeatureCityMonthHisDAO.createAndFlush(vo);
        }
        catch (BusinessException e) {
            throw e;
        }
        catch (Exception e) {
            throw e;
        }
    }

    @Override
    public void doRemoveLoadFeatureCityMonthHisDO(LoadFeatureCityMonthHisDO vo) throws Exception{
        try {
            loadFeatureCityMonthHisDAO.remove(vo);
        }
        catch (BusinessException e) {
            throw e;
        }
        catch (Exception e) {
            throw e;
        }
    }

    @Override
    public void doRemoveLoadFeatureCityMonthHisDOByPK(Serializable pk) throws Exception{
        try {
            loadFeatureCityMonthHisDAO.removeByPk(pk);
        }
        catch (BusinessException e) {
            throw e;
        }
        catch (Exception e) {
            throw e;
        }
    }

    @Override
    public LoadFeatureCityMonthHisDO doUpdateLoadFeatureCityMonthHisDO(LoadFeatureCityMonthHisDO vo) throws Exception{
        try {
            return (LoadFeatureCityMonthHisDO)loadFeatureCityMonthHisDAO.update(vo);
        }
        catch (BusinessException e) {
            throw e;
        }
        catch (Exception e) {
            throw e;
        }
    }

    @Override
    public LoadFeatureCityMonthHisDO findLoadFeatureCityMonthHisDOByPk(Serializable pk) throws Exception{
         try {
            return (LoadFeatureCityMonthHisDO)loadFeatureCityMonthHisDAO.findVOByPk(pk);
        }
        catch (BusinessException e) {
            throw e;
        }
        catch (Exception e) {
            throw e;
        }
    }


    @Override
    public LoadFeatureCityMonthHisDO getLoadFeatureCityMonthHisDO(String cityId, String year, String month, String caliberId) throws Exception {
        return loadFeatureCityMonthHisDAO.getLoadFeatureCityMonthHisVO(cityId,year,month,caliberId);
    }

    @Override
    public List<LoadFeatureCityMonthHisDO> getLoadFeatureCityMonthHisDOList(String cityId, String year, String month, String caliberId) throws Exception {
        return loadFeatureCityMonthHisDAO.getLoadFeatureCityMonthHisVOList(cityId,year,month,caliberId);
    }


    @Override
    public List<LoadFeatureCityMonthHisDO> getLoadFeatureCityMonthHisDOS(String cityId, String startYM, String endYM, String caliberId) throws Exception {
        return loadFeatureCityMonthHisDAO.getLoadFeatureCityMonthHisVOs(cityId, startYM, endYM,caliberId);
    }

    @Override
    public List<LoadFeatureCityMonthHisDO>getLoadFeatureCityMonthHisVOByYear(String cityId, String year, String caliberId) throws Exception {
        return loadFeatureCityMonthHisDAO.getLoadFeatureCityMonthHisVO(cityId,year, caliberId);
    }


    @Override
    public List<LoadFeatureDTO> findMonthLoadFeatureDTOS(String cityId, String startYM, String endYM, String caliberId) throws Exception {
        List<LoadFeatureCityMonthHisDO> loadFeatureCityMonthHisVOS = this.getLoadFeatureCityMonthHisDOS(cityId, startYM, endYM,caliberId);
        List<LoadFeatureDTO> loadFeatureDTOS = new ArrayList<LoadFeatureDTO>();
        if(loadFeatureCityMonthHisVOS != null) {
            for(LoadFeatureCityMonthHisDO loadFeatureCityMonthHisVO : loadFeatureCityMonthHisVOS) {
                LoadFeatureDTO loadFeatureDTO = new LoadFeatureDTO();
                loadFeatureDTO.setDate(loadFeatureCityMonthHisVO.getYear() + "-" + loadFeatureCityMonthHisVO.getMonth());
                loadFeatureDTO.setMin(loadFeatureCityMonthHisVO.getMinLoad());
                loadFeatureDTO.setMax(loadFeatureCityMonthHisVO.getMaxLoad());
                loadFeatureDTO.setAvg(loadFeatureCityMonthHisVO.getAveLoad());
                loadFeatureDTO.setCaliberId(loadFeatureCityMonthHisVO.getCaliberId());
                loadFeatureDTOS.add(loadFeatureDTO);
            }
        }
        return loadFeatureDTOS;
    }

    @Override
    public List<LoadFeatureExtendDTO> findLoadFeatureExtendDTOS(String cityId, Date startDate, Date endDate) throws Exception {
        return findLoadFeatureExtendDTOS(cityId, startDate, endDate, null);
    }

    @Override
    public List<LoadFeatureExtendDTO> findLoadFeatureExtendDTOS(String cityId, Date startDate, Date endDate, String caliberId) throws Exception {
        String startYM = DateUtils.date2String(startDate, DateFormatType.YEAR_MONTH_STR);
        String endYM = DateUtils.date2String(endDate, DateFormatType.YEAR_MONTH_STR);
        List<LoadFeatureCityMonthHisDO> loadFeatureCityMonthHisVOS = this.getLoadFeatureCityMonthHisDOS(cityId,startYM,endYM,caliberId);
        List<LoadFeatureExtendDTO> loadFeatureExtendDTOS = new ArrayList<LoadFeatureExtendDTO>(30);
        for (LoadFeatureCityMonthHisDO loadFeatureCityMonthHisVO : loadFeatureCityMonthHisVOS) {
            LoadFeatureExtendDTO loadFeatureExtendDTO = new LoadFeatureExtendDTO();
            BeanUtils.copyProperties(loadFeatureCityMonthHisVO,loadFeatureExtendDTO);
            loadFeatureExtendDTO.setDate(loadFeatureCityMonthHisVO.getYear() + "-" + loadFeatureCityMonthHisVO.getMonth());
            loadFeatureExtendDTO.setCity(cityService.findCityById(loadFeatureCityMonthHisVO.getCityId()).getCity());
            loadFeatureExtendDTO.setDataDate(DateUtils.string2Date(loadFeatureExtendDTO.getDate(),DateFormatType.YEAR_MONTH_STR));
            loadFeatureExtendDTO.setCaliberId(loadFeatureCityMonthHisVO.getCaliberId());
            loadFeatureExtendDTOS.add(loadFeatureExtendDTO);
        }
        return loadFeatureExtendDTOS;
    }
}
