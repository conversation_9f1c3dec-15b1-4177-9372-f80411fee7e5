package com.tsintergy.lf.serviceimpl.common.util;

import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.common.enumeration.ParamDate;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public  class ParamDateUtil {





    public static ParamDate parseNotNullParam(String param){
        ParamDate paramDate = null;
        String[] split = param.split(",");
        if(checkParam(split)){
            paramDate = parseSplitParams(split);
        }else {
            throw new RuntimeException("参数解析后个数不正确");
        }
        return  paramDate;
    }


    public static ParamDate parseSplitParams(String[] params){
        ParamDate paramDate = new ParamDate();
        String startDateStr = params[0];
        String endDateStr = params[1];
        Date startDate = com.tsieframework.core.base.format.datetime.DateUtils.string2Date(startDateStr, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
        Date endDate = com.tsieframework.core.base.format.datetime.DateUtils.string2Date(endDateStr, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
        paramDate.setStartDate(startDate);
        paramDate.setEndDate(endDate);
        return  paramDate;
    }



    public static boolean checkParam(String[] params){
        if(params == null || params.length<=1){
            return false;
        }else if(params.length == 2){
            return true;
        }
        return  false;
    }



    public static ParamDate getYearParamDate(ParamDate paramDate){
        ParamDate date = new ParamDate();
        Date startDate = paramDate.getStartDate();
        Calendar instance = Calendar.getInstance();
        instance.setTime(startDate);
        int year = instance.get(Calendar.YEAR);
        Date yearFirstDayByDate = DateUtil.getYearFirst(year);
        date.setStartDate(yearFirstDayByDate);

        Date endDate = paramDate.getEndDate();
        instance.setTime(endDate);
        year = instance.get(Calendar.YEAR);
        Date yearLastDayByDate = DateUtil.getYearLast(year);
        date.setEndDate(yearLastDayByDate);
        return date;
    }




    public static ParamDate getQuarterParamDate(ParamDate paramDate){
        ParamDate date = new ParamDate();
        Date startDate = paramDate.getStartDate();
        Date endDate = paramDate.getEndDate();
        Calendar instance = Calendar.getInstance();
        instance.setTime(startDate);
        int i = instance.get(Calendar.MONTH);
        int a = i/3;
        int month = a*3;
        instance.set(Calendar.MONTH,month);
        Date quarterFirstDayByDate = findMonthFirstDayByDate(instance.getTime());
        date.setStartDate(quarterFirstDayByDate);


        instance.setTime(endDate);
        i = instance.get(Calendar.MONTH);
        a = i/3;
        instance.set(Calendar.MONTH,(a+1)*3-1);
        Date quarterLastDayByDate = findMonthLastDayByDate(instance.getTime());
        date.setEndDate(quarterLastDayByDate);
        return date;
    }







    public static ParamDate getWeekParamDate(ParamDate paramDate){
        ParamDate date = new ParamDate();
        Date startDate = paramDate.getStartDate();
        Date weekFirstDayByDate = findWeekFirstDayByDate(startDate);
        date.setStartDate(weekFirstDayByDate);
        Date endDate = paramDate.getEndDate();
        Date weekLastDayByDate = findWeekLastDayByDate(endDate);
        date.setEndDate(weekLastDayByDate);
        return date;
    }



    public static ParamDate getMonthParamDate(ParamDate paramDate){
        ParamDate date = new ParamDate();
        Date startDate = paramDate.getStartDate();
        Date monthFirstDayByDate = findMonthFirstDayByDate(startDate);
        date.setStartDate(monthFirstDayByDate);
        Date endDate = paramDate.getEndDate();
        Date monthLastDayByDate = findMonthLastDayByDate(endDate);
        date.setEndDate(monthLastDayByDate);
        return date;
    }


    public static Date findWeekFirstDayByDate(Date date){

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        int dayWeek = instance.get(Calendar.DAY_OF_WEEK);
        if(dayWeek == 1){
            instance.add(Calendar.DAY_OF_MONTH, -1);
        }
        instance.setFirstDayOfWeek(Calendar.MONDAY);
        int day = instance.get(Calendar.DAY_OF_WEEK);
        instance.add(Calendar.DATE,instance.getFirstDayOfWeek()-day);
        System.out.println("所在周星期一的日期：" + sdf.format(instance.getTime()));
        return instance.getTime();
    }





    public static Date findWeekLastDayByDate(Date date){
        LocalDate inputDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endDayOfWeek = inputDate.with(DayOfWeek.SUNDAY);
        ZonedDateTime zonedDateTime = endDayOfWeek.atStartOfDay(ZoneId.systemDefault());

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        int dayWeek = instance.get(Calendar.DAY_OF_WEEK);
        if(dayWeek == 1){
            instance.add(Calendar.DAY_OF_MONTH, -1);
        }
        instance.setFirstDayOfWeek(Calendar.MONDAY);
        int day = instance.get(Calendar.DAY_OF_WEEK);
        instance.add(Calendar.DATE,8-day);
        System.out.println("所在周星期日的日期：" + sdf.format(instance.getTime()));
        return Date.from(zonedDateTime.toInstant());
       // return instance.getTime();
    }

    public static Date findMonthLastDayByDate(Date date){
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        //设置为1号,当前日期既为本月最后一天
        instance.set(Calendar.DAY_OF_MONTH, instance.getActualMaximum(Calendar.DAY_OF_MONTH));
        return instance.getTime();
    }


    public static Date findMonthFirstDayByDate(Date date){
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        //设置为1号,当前日期既为本月最后一天
        instance.set(Calendar.DAY_OF_MONTH, instance.getActualMinimum(Calendar.DAY_OF_MONTH));
        return instance.getTime();
    }


    /**
     * 结果格式 yyyy-mm
     * @return
     */
    public static List<String>  getYearMonthList(Date startDate,Date endDate){
        List<Date> dateList= com.tsintergy.lf.serviceimpl.common.util.DateUtils.getListBetweenDay(startDate,endDate);
        List<String> stringList = new ArrayList<>();
        for (Date date : dateList) {
            String ym = DateUtil.getMonthByDate(date);
            if (!stringList.contains(ym)) {
                stringList.add(ym);
            }
        }
        return  stringList;
    }

}