/**
 * Copyright (C), 2015‐2022, 北京清能互联科技有限公司 Author:  <EMAIL> Date:  2022/5/24 10:15 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.airconditioner.dao;

import com.tsieframework.core.base.dao.DomainDAO;
import com.tsieframework.core.base.dao.jpa.BaseJpaDAO;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.LoadSolutionManageEntityService;
import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.LoadSolutionManageDO;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2022/5/24
 * @since 1.0.0
 */
@DomainDAO(entityServiceClass = LoadSolutionManageEntityService.class)
public interface LoadSolutionManageDAO extends BaseJpaDAO<LoadSolutionManageDO, String> {

}  
