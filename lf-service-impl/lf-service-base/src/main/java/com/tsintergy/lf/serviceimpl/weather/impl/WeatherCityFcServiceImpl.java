
package com.tsintergy.lf.serviceimpl.weather.impl;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.enums.CityStationEnum;
import com.tsintergy.lf.core.enums.WeatherSourceEnum;
import com.tsintergy.lf.core.util.*;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.weather.annotation.FcWeatherDataSource;
import com.tsintergy.lf.serviceapi.base.weather.api.*;
import com.tsintergy.lf.serviceapi.base.weather.collect.WeatherFcCollectDTO;
import com.tsintergy.lf.serviceapi.base.weather.collect.WeatherFcCollectVO;
import com.tsintergy.lf.serviceapi.base.weather.dto.*;
import com.tsintergy.lf.serviceapi.base.weather.pojo.*;
import com.tsintergy.lf.serviceimpl.common.util.AlgorithmUtil;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcBatchDAO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcDAO;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.MATH_CONTEXT;

/**
 * <AUTHOR>
 * @version $Id: WeatherCityFcServiceImpl.java, v 0.1 2018-01-31 10:59:53 tao Exp $$
 */

@Service("weatherCityFcService")
@FcWeatherDataSource(source = "")
@Slf4j
public class WeatherCityFcServiceImpl extends AbstractBaesWeatherServiceImpl implements WeatherCityFcService {

    private static final Logger logger = LogManager.getLogger(WeatherCityHisServiceImpl.class);

    @Autowired
    WeatherCityFcModifyService weatherCityFcModifyService;

    @Autowired
    CityService cityService;

    @Autowired
    WeatherCityFcDAO weatherCityFcDAO;

    @Autowired
    private WeatherCityFcService weatherCityFcService;

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Autowired
    private WeatherStationFcClctWgService weatherStationFcClctWgService;

    @Autowired
    private BaseWeatherStationWgService baseWeatherStationWgService;

    @Autowired
    private WeatherStationFcBasicWgService weatherStationFcBasicWgService;

    @Autowired
    private StatisticsSynthesizeWeatherCityDayFcService statisticsSynthesizeWeatherCityDayFcService;

    @Autowired
    private SettingSystemService settingSystemService;

    @Autowired
    private WeatherCityFcBatchDAO weatherCityFcBatchDAO;

    @Autowired
    private WeatherCityFcAdapterService weatherCityFcAdapterService;

    @Autowired
    private WeatherFeatureCityDayFcService weatherFeatureCityDayFcService;

    @Autowired
    private WeatherCityFcMeteoService weatherCityFcMeteoService;

    @Override
    protected List<? extends BaseWeatherDO> findWeatherCityVO(String cityId, Integer type, Date startDate, Date endDate)
            throws Exception {
        return weatherCityFcDAO.findWeatherCityFcDO(cityId, type, startDate, endDate);
    }

    @Override
    protected List<? extends BaseWeatherDO> findStatisticsSynthesizeWeatherCityDayVO(String cityId, Integer type,
                                                                                     Date startDate, Date endDate) throws Exception {
        return statisticsSynthesizeWeatherCityDayFcService
                .findStatisticsSynthesizeWeatherCityDayFcDO(cityId, type, startDate, endDate);
    }


    @Override
    public DataPackage queryWeatherCityFcDO(DBQueryParam param) throws Exception {
        try {
            return weatherCityFcDAO.query(param);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    /**
     * 查找预测数据
     */
    @Override
    public List<WeatherDTO> findWeatherCityFcDTOs(String cityId, Integer type, Date startDate, Date endDate)
        throws Exception {
        try {
            List<WeatherCityFcDO> weatherCityFcVOS = weatherCityFcDAO
                .findWeatherCityFcDO(cityId, type, startDate, endDate);
            if (weatherCityFcVOS.size() < 1) {
                return new ArrayList<>();
            }
            List<WeatherDTO> weatherDTOS = new ArrayList<WeatherDTO>(10);
            for (WeatherCityFcDO weatherCityHisVO : weatherCityFcVOS) {
                WeatherDTO weatherDTO = new WeatherDTO();
                weatherDTO.setId(weatherCityHisVO.getId());
                weatherDTO.setDate(weatherCityHisVO.getDate());
                weatherDTO.setWeek(DateUtil.getWeek(weatherCityHisVO.getDate()));
                CityDO cityDO = this.cityService.findCityById(weatherCityHisVO.getCityId());
                weatherDTO.setCity(cityDO.getCity());
                weatherDTO.setCityId(cityDO.getId());
                weatherDTO.setData(BasePeriodUtils.toList(weatherCityHisVO, Constants.WEATHER_CURVE_POINT_NUM,
                        Constants.WEATHER_CURVE_START_WITH_ZERO));
                weatherDTOS.add(weatherDTO);
            }
            return weatherDTOS;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public WeatherCityFcDO doCreate(WeatherCityFcDO vo) throws Exception {
        try {
            return (WeatherCityFcDO) weatherCityFcDAO.create(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public void doRemoveWeatherCityFcDO(WeatherCityFcDO vo) throws Exception {
        try {
            weatherCityFcDAO.remove(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public void doRemoveWeatherCityFcDOByPK(Serializable pk) throws Exception {
        try {
            weatherCityFcDAO.removeByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public WeatherCityFcDO doUpdateWeatherCityFcDO(WeatherCityFcDO vo) throws Exception {
        try {
            return (WeatherCityFcDO) weatherCityFcDAO.update(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public WeatherCityFcDO findWeatherCityFcDOByPk(Serializable pk) throws Exception {
        try {
            return (WeatherCityFcDO) weatherCityFcDAO.findByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public List<WeatherCityFcDO> findWeatherCityFcDOs(String cityId, Integer type, Date startDate, Date endDate)
        throws Exception {
        try {
            cityId = cityService.findWeatherCityId(cityId);
            return weatherCityFcDAO.findWeatherCityFcDO(cityId, type, startDate, endDate);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public List<WeatherNameDTO> findAllByDateAndCityId(String cityId, Date targetDate) throws Exception {
        List<WeatherNameDTO> weatherNameDTOS = new ArrayList<WeatherNameDTO>(4);
        cityId = cityService.findWeatherCityId(cityId);
        List<WeatherCityFcDO> weatherCityFcVOS = weatherCityFcDAO
            .findWeatherCityFcDO(cityId, null, targetDate, targetDate);
        if (weatherCityFcVOS.size() < 1) {
            return null;
        }
        for (WeatherCityFcDO weatherCityFcVO : weatherCityFcVOS) {
            WeatherNameDTO weatherNameDTO = new WeatherNameDTO();
            weatherNameDTO.setName(WeatherEnum.getValueByName(weatherCityFcVO.getType()));
            weatherNameDTO.setWeather(BasePeriodUtils
                .toList(weatherCityFcVO, Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO));
            weatherNameDTOS.add(weatherNameDTO);
        }
        return weatherNameDTOS;
    }

    @Override
    public WeatherCityFcDO findWeatherCityFcDO(String cityId, Integer type, Date date) throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create()
            .where(QueryOp.DateEqualTo, "date", new java.sql.Date(date.getTime()))
            .where(QueryOp.StringEqualTo, "cityId", cityId);
        if(type != null){
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "type", type);
        }
        List<WeatherCityFcDO> datas = weatherCityFcDAO.query(dbQueryParamBuilder.build()).getDatas();
        if (datas!=null&&datas.size()!=0)
            return datas.get(0);
        return null;
    }


    @Override
    public void doInsertOrUpdate(WeatherCityFcDO weatherCityFcVO) throws Exception {
        if (weatherCityFcVO.getDate() == null || weatherCityFcVO.getCityId() == null
            || weatherCityFcVO.getType() == null) {
            throw new BusinessException("01C20180008", "");
        }
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly()
            .where(QueryOp.DateEqualTo, "date", weatherCityFcVO.getDate())
            .where(QueryOp.StringEqualTo, "cityId", weatherCityFcVO.getCityId())
            .where(QueryOp.NumberEqualTo, "type", weatherCityFcVO.getType());
        List<WeatherCityFcDO> weatherCityFcVOS = this.queryWeatherCityFcDO(dbQueryParamBuilder.build()).getDatas();
        if (weatherCityFcVOS == null || weatherCityFcVOS.size() < 1) {
            weatherCityFcVO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            this.doCreate(weatherCityFcVO);
            return;
        }
        weatherCityFcDAO.getSession().flush();
        weatherCityFcDAO.getSession().clear();
        WeatherCityFcDO old = weatherCityFcVOS.get(0);
        PeriodDataUtil.replaceValues(old, weatherCityFcVO);
        old.setUpdatetime(new Timestamp(System.currentTimeMillis()));
        this.doUpdateWeatherCityFcDO(old);
    }


    @Override
    public List<BigDecimal> find96WeatherCityFcValue(Date date, String cityId, Integer type) throws Exception {
        cityId = cityService.findWeatherCityId(cityId);
        List<WeatherCityFcDO> weatherCityFcVOs = this.findWeatherCityFcDOs(cityId, type, date, date);
        if (!CollectionUtils.isEmpty(weatherCityFcVOs)) {
            return BasePeriodUtils.toList(weatherCityFcVOs.get(0), Constants.WEATHER_CURVE_POINT_NUM,
                Constants.WEATHER_CURVE_START_WITH_ZERO);
        }
        return null;
    }

    @Override
    public List<BigDecimal> findListPoint(Date startDate, Date endDate, String cityId, Integer type) throws Exception {
        List<BigDecimal> points = new ArrayList<>();
        List<Date> dates = DateUtil.getListBetweenDay(startDate, endDate);
        for (Date date : dates) {
            List<BigDecimal> value = find96WeatherCityFcValue(date, cityId, type);
            if (value != null && value.size()>0){
                points.addAll(value);
            }else {
                points.addAll(DataUtil.getNullList(Constants.WEATHER_CURVE_POINT_NUM));
            }
        }
        return points;
    }


    @Override
    public WeatherDeatilDTO findWeatherDeatilDTO(Date date, String cityId) throws Exception {

        List<WeatherCityFcDO> weatherCityFcVOs = this.findWeatherCityFcDOs(cityId, null, date, date);
        Map<Integer, WeatherCityFcDO> weatherMap = weatherCityFcVOs.stream()
            .collect(Collectors.toMap(WeatherCityFcDO::getType, Function.identity()));
        String weatherCityId = cityService.findWeatherCityId(cityId);

        List<WeatherCityFcModifyDO> modifyWeatherByDateAndType = weatherCityFcModifyService
            .findModifyWeatherByDateAndType(new SimpleDateFormat("yyyy-MM-dd").format(date), null, weatherCityId);
        Map<Integer, List<WeatherCityFcModifyDO>> modifyWeather = null;
        if (!CollectionUtils.isEmpty(modifyWeatherByDateAndType)) {
            modifyWeather = modifyWeatherByDateAndType.stream()
                .collect(Collectors.groupingBy(WeatherCityFcModifyDO::getType));
        }

        List<WeatherCurveDTOList> weatherCurveDTOListList = new ArrayList<>();
        List<WeatherFeatureDTO> weatherFeatureDTOS = new ArrayList<WeatherFeatureDTO>() {
            {
                add(new WeatherFeatureDTO("气象源"));
                add(new WeatherFeatureDTO("人工调整"));
            }
        };
        for (WeatherEnum weatherEnum : WeatherEnum.values()) {
            if (weatherEnum.getType() > 4) {
                continue;
            }
            WeatherCityFcDO fcVO = weatherMap.get(weatherEnum.getType());
            if (fcVO != null) {
                List<BigDecimal> weatherFcModifyValues = null;
                List<BigDecimal> weatherFcValues = BasePeriodUtils
                    .toList(fcVO, Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO);
                if (modifyWeather == null || CollectionUtils.isEmpty(modifyWeather.get(weatherEnum.getType()))) {
                    weatherFcModifyValues = weatherFcValues;
                } else {
                    List<WeatherCityFcModifyDO> WeatherCityFcModifyDOS = modifyWeather.get(weatherEnum.getType());
                    if(WeatherCityFcModifyDOS.get(0) == null){
                        weatherFcModifyValues = weatherFcValues;
                    } else {
                        weatherFcModifyValues = BasePeriodUtils
                            .toList(WeatherCityFcModifyDOS.get(0), Constants.WEATHER_CURVE_POINT_NUM,
                                Constants.WEATHER_CURVE_START_WITH_ZERO);
                    }
                }

                List<WeatherCurveDTO> weatherCurveDTOS = new ArrayList<>();
                WeatherCurveDTO weatherFcDTO = new WeatherCurveDTO("101", weatherFcValues);
                weatherCurveDTOS.add(weatherFcDTO);
                WeatherCurveDTO weatherModifyDTO = new WeatherCurveDTO("102", weatherFcModifyValues);
                weatherCurveDTOS.add(weatherModifyDTO);
                WeatherCurveDTOList weatherCurveDTOList = new WeatherCurveDTOList(String.valueOf(weatherEnum.getType()),
                    weatherCurveDTOS);
                weatherCurveDTOListList.add(weatherCurveDTOList);

                if (weatherEnum.equals(WeatherEnum.TEMPERATURE)) {
                    weatherFeatureDTOS.get(0).setHighestTemperature(LoadCalUtil.max(weatherFcValues));
                    weatherFeatureDTOS.get(0).setLowestTemperature(LoadCalUtil.min(weatherFcValues));

                    weatherFeatureDTOS.get(1).setHighestTemperature(LoadCalUtil.max(weatherFcModifyValues));
                    weatherFeatureDTOS.get(1).setLowestTemperature(LoadCalUtil.min(weatherFcModifyValues));
                }
                if (weatherEnum.equals(WeatherEnum.RAINFALL)) {
                    weatherFeatureDTOS.get(0).setRainfall(BigDecimalUtils.addAllValue(weatherFcValues));
                    weatherFeatureDTOS.get(1).setRainfall(BigDecimalUtils.addAllValue(weatherFcModifyValues));
                }
                if (weatherEnum.equals(WeatherEnum.HUMIDITY)) {
                    weatherFeatureDTOS.get(0)
                        .setAveHumidity(BasePeriodUtils.getMaxMinAvg(weatherFcValues, 4).get(BasePeriodUtils.LOAD_AVG));
                    weatherFeatureDTOS.get(1).setAveHumidity(
                        BasePeriodUtils.getMaxMinAvg(weatherFcModifyValues, 4).get(BasePeriodUtils.LOAD_AVG));
                }
            }
        }

        return new WeatherDeatilDTO(weatherCurveDTOListList, weatherFeatureDTOS);
    }

    @Override
    public List<WeatherCityFcDO> findWeatherCityFcDOs(String cityId, Date date, Date date1) throws Exception {
        return weatherCityFcDAO.findWeatherCityFcDO(cityId, date, date);
    }

    @Override
    public List<WeatherCityFcDO> findWeatherCityHisDOs(List<String> cityIds, Integer type, Date hisStartDate, Date endDate) {
        return weatherCityFcDAO.findWeatherCityHisDOs(cityIds, type, hisStartDate, endDate);
    }


    private Map<String, List<WeatherFcCollectDTO>> weatherFcCollectVOListToDTOMap(
        List<WeatherFcCollectVO> weatherFcCollectVOS) {
        Map<String, List<WeatherFcCollectDTO>> map = new HashMap<String, List<WeatherFcCollectDTO>>();
        for (WeatherFcCollectVO weatherFcCollectVO : weatherFcCollectVOS) {
            Date date = DateUtils.addHours(weatherFcCollectVO.getForecastStartTime(), weatherFcCollectVO.getTimes());
            String time = DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_STR);
            if (map.get(time) == null) {
                map.put(time, new ArrayList<WeatherFcCollectDTO>());
            }
            WeatherFcCollectDTO weatherFcCollectDTO = new WeatherFcCollectDTO();
            weatherFcCollectDTO.setDate(date);
            weatherFcCollectDTO.setHumidity(weatherFcCollectVO.getHumidity());
            weatherFcCollectDTO.setRainfall(weatherFcCollectVO.getRainfall());
            weatherFcCollectDTO.setStationNo(weatherFcCollectVO.getStationNo());
            weatherFcCollectDTO.setTemperature(weatherFcCollectVO.getTemperature());
            weatherFcCollectDTO.setWindSpeed(weatherFcCollectVO.getWindSpeed());
            map.get(time).add(weatherFcCollectDTO);
        }
        return map;
    }

    @Override
    public List<WeatherCityFcDO> findFcWeatherData(String cityId, Integer type, Date startDate, Date endDate)
            throws Exception {
        return null;
    }

    @Override
    public void wgStationFcWeatherToCity(String cityId, Integer type, Date startDate, Date endDate) throws Exception {
        //将格点气象站24点数据转化为96点
        List<String> stationWgIdList;
        LambdaQueryWrapper<BaseWeatherStationWgDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BaseWeatherStationWgDO::getCityId, cityId);
        List<BaseWeatherStationWgDO> baseWeatherStationWgDOS = baseWeatherStationWgService.list(queryWrapper);
        stationWgIdList = baseWeatherStationWgDOS.stream().map(BaseWeatherStationWgDO::getId).collect(Collectors.toList());
        List<Date[]> dates = DateUtil.splitDateRange(startDate, endDate, 10);
        for (Date[] dateArr : dates) {
            weatherStationFcClctWgService.clctToBasic(dateArr[0], dateArr[1], stationWgIdList);
        }

        //查询格点气象站96点数据
        List<WeatherStationFcBasicWgDO> weatherStationFcBasicWgDOS = weatherStationFcBasicWgService.findByCondition(stationWgIdList, type, startDate, endDate);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(weatherStationFcBasicWgDOS)) {
            logger.error("统计格点预测气象失败，格点气象无数据");
            return;
        }
        Map<String, List<WeatherStationFcBasicWgDO>> weatherStationFcBasicWgDOSMap =
                weatherStationFcBasicWgDOS.stream().collect(
                        Collectors.groupingBy(e ->
                                e.getType() + "-" + e.getDate().getTime()
                        ));

        //通过格点气象站96点数据计算城市气象
        List<WeatherCityFcDO> resultList = new ArrayList<>();
        for (Map.Entry<String, List<WeatherStationFcBasicWgDO>> entry : weatherStationFcBasicWgDOSMap.entrySet()) {
            List<BigDecimal> valueList = new ArrayList<>();
            for (int i = 0; i <= 95; i++) {
                List<BigDecimal> dataList = new ArrayList<>();
                for (WeatherStationFcBasicWgDO basicWgDO : entry.getValue()) {
                    if (basicWgDO.getloadList().get(i) != null) {
                        dataList.add(basicWgDO.getloadList().get(i));
                    }
                }
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(dataList)) {
                    BigDecimal bigDecimal = BigDecimalFunctions.listAvg(dataList);
                    valueList.add(bigDecimal);
                } else {
                    valueList.add(null);
                }
            }

            WeatherStationFcBasicWgDO weatherStationFcBasicWgDO = entry.getValue().get(0);
            WeatherCityFcDO weatherCityFcDO = new WeatherCityFcDO();
            weatherCityFcDO.setCityId(cityId);
            weatherCityFcDO.setType(weatherStationFcBasicWgDO.getType());
            weatherCityFcDO.setDate(weatherStationFcBasicWgDO.getDate());
            //通过两侧不为null的数据，计算差值补点
            weatherCityHisService.extracted(valueList);
            Map<String, BigDecimal> stringBigDecimalMap = ColumnUtil.listToMap(valueList, Constants.LOAD_CURVE_START_WITH_ZERO);
            BasePeriodUtils.setAllFiled(weatherCityFcDO, stringBigDecimalMap);
            resultList.add(weatherCityFcDO);
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(resultList)) {
            try {
                for (WeatherCityFcDO weatherCityFcDO : resultList) {
                    weatherCityFcService.doInsertOrUpdate(weatherCityFcDO);
                }
            } catch (Exception e) {
                logger.error("保存气象站历史数据出错了", e);
            }
        }
    }

    @Override
    public void doFcCityWeatherToProvince(Date startDate, Date endDate) throws Exception {
        String str = settingSystemService.getValue("province_weather_express");
        Map map = (Map) JSON.parse(str);
        Set set = map.keySet();

        while (startDate.before(DateUtils.addDays(endDate, 1))) {
            List<WeatherCityFcDO> weatherCityFcDOs = weatherCityFcService.findWeatherCityFcDOs(null, null, startDate, startDate);
            if (CollectionUtils.isEmpty(weatherCityFcDOs)) {
                startDate = DateUtils.addDays(startDate, 1);
                continue;
            }
            List<BigDecimal> tem = zero96List();
            List<BigDecimal> hum = zero96List();
            List<BigDecimal> rain = zero96List();
            List<BigDecimal> wind = zero96List();
            for (WeatherCityFcDO weatherCityFcDO : weatherCityFcDOs) {
                if (set.contains(weatherCityFcDO.getCityId())) {
                    Integer type = weatherCityFcDO.getType();
                    List<BigDecimal> bigDecimals = BasePeriodUtils.toList(weatherCityFcDO, 96, Constants.WEATHER_CURVE_START_WITH_ZERO);
                    String ratioStr = map.get(weatherCityFcDO.getCityId()).toString();
                    BigDecimal ratio = new BigDecimal(ratioStr);
                    for (int i = 0; i < bigDecimals.size(); i++) {
                        BigDecimal bigDecimal = bigDecimals.get(i);
                        if (bigDecimal != null) {
                            BigDecimal multiply = bigDecimal.multiply(ratio);
                            bigDecimals.set(i, multiply);
                        } else {
                            //如果有地市数据为空，省调的数据直接置为null,继续下一个点
                            switch (type) {
                                case 1:
                                    hum.set(i, null);
                                    break;
                                case 2:
                                    tem.set(i, null);
                                    break;
                                case 4:
                                    wind.set(i, null);
                                    break;
                                case 3:
                                    rain.set(i, null);
                                    break;
                            }
                            continue;
                        }
                    }
                    switch (type) {
                        case 1:
                            hum = countList(hum, bigDecimals);
                            break;
                        case 2:
                            tem = countList(tem, bigDecimals);
                            break;
                        case 4:
                            wind = countList(wind, bigDecimals);
                            break;
                        case 3:
                            rain = countList(rain, bigDecimals);
                            break;
                    }
                }
            }
            WeatherCityFcDO cityFcDO = new WeatherCityFcDO();
            Map<String, BigDecimal> decimalMap = ColumnUtil.listToMap(tem, Constants.LOAD_CURVE_START_WITH_ZERO);
            BasePeriodUtils.setAllFiled(cityFcDO, decimalMap);
            cityFcDO.setType(WeatherEnum.TEMPERATURE.getType());
            cityFcDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            cityFcDO.setCityId("1");
            cityFcDO.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
            cityFcDO.setDate(new java.sql.Date(startDate.getTime()));
            weatherCityFcService.doInsertOrUpdate(cityFcDO);


            WeatherCityFcDO cityFcDO1 = new WeatherCityFcDO();
            Map<String, BigDecimal> decimalMap1 = ColumnUtil.listToMap(hum, Constants.LOAD_CURVE_START_WITH_ZERO);
            BasePeriodUtils.setAllFiled(cityFcDO1, decimalMap1);
            cityFcDO1.setType(WeatherEnum.HUMIDITY.getType());
            cityFcDO1.setCreatetime(new Timestamp(System.currentTimeMillis()));
            cityFcDO1.setCityId("1");
            cityFcDO1.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
            cityFcDO1.setDate(new java.sql.Date(startDate.getTime()));
            weatherCityFcService.doInsertOrUpdate(cityFcDO1);


            WeatherCityFcDO cityFcDO2 = new WeatherCityFcDO();
            Map<String, BigDecimal> decimalMap2 = ColumnUtil.listToMap(rain, Constants.LOAD_CURVE_START_WITH_ZERO);
            BasePeriodUtils.setAllFiled(cityFcDO2, decimalMap2);
            cityFcDO2.setType(WeatherEnum.RAINFALL.getType());
            cityFcDO2.setCreatetime(new Timestamp(System.currentTimeMillis()));
            cityFcDO2.setCityId("1");
            cityFcDO2.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
            cityFcDO2.setDate(new java.sql.Date(startDate.getTime()));
            weatherCityFcService.doInsertOrUpdate(cityFcDO2);


            WeatherCityFcDO cityFcDO4 = new WeatherCityFcDO();
            Map<String, BigDecimal> decimalMap4 = ColumnUtil.listToMap(wind, Constants.LOAD_CURVE_START_WITH_ZERO);
            BasePeriodUtils.setAllFiled(cityFcDO4, decimalMap4);
            cityFcDO4.setType(WeatherEnum.WINDSPEED.getType());
            cityFcDO4.setCreatetime(new Timestamp(System.currentTimeMillis()));
            cityFcDO4.setCityId("1");
            cityFcDO4.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
            cityFcDO4.setDate(new java.sql.Date(startDate.getTime()));
            weatherCityFcService.doInsertOrUpdate(cityFcDO4);


            List<WeatherCityFcDO> windDirects = weatherCityFcService.findWeatherCityFcDOs("2", WeatherEnum.WINDDIRECTION.getType(), startDate, startDate);
            if (!CollectionUtils.isEmpty(windDirects)) {
                WeatherCityFcDO weatherCityFcDO = windDirects.get(0);
                weatherCityFcDO.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
                weatherCityFcDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                weatherCityFcDO.setCityId("1");
                weatherCityFcDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
                weatherCityFcService.doInsertOrUpdate(weatherCityFcDO);
            }
            startDate = DateUtils.addDays(startDate, 1);
        }
    }

    public List<BigDecimal> zero96List() {
        List<BigDecimal> list = new ArrayList<>(96);
        for (int i = 0; i < 96; i++) {
            list.add(null);
        }
        return list;
    }

    public List<BigDecimal> countList(List<BigDecimal> list1, List<BigDecimal> list2) {

        for (int j = 0; j < list2.size(); j++) {
            BigDecimal count = null;
            BigDecimal var1 = list1.get(j);
            BigDecimal var2 = list2.get(j);
            if (var1 == null && var2 == null) {
                count = null;
            } else if (var1 == null && var2 != null) {
                count = var2;
            } else if (var1 != null && var2 == null) {
                count = var1;
            } else {
                count = var1.add(var2);
            }
            list1.set(j, count);
        }
        return list1;
    }

    @Override
    public List<WeatherCityFcDO> getProvinceWeightAvgFcWeatherData(Integer type, Date startDate, Date endDate) throws Exception {
        List<WeatherCityFcDO> fcWeatherDOS = new ArrayList<>();
        String str = settingSystemService.getValue("province_weather_express");
        Map map = (Map) JSON.parse(str);

        List<WeatherStationFcBasicWgDO> weatherStationFcBasicWgDOS = weatherStationFcBasicWgService.getStandardStationFcWeatherBasic(null, type, startDate, endDate);
        if (CollectionUtils.isEmpty(weatherStationFcBasicWgDOS)) {
            return Collections.emptyList();
        }

        Map<String, List<WeatherStationFcBasicWgDO>> weatherMap = weatherStationFcBasicWgDOS.stream()
                .collect(Collectors.groupingBy(weatherStationFcBasicWgDO -> weatherStationFcBasicWgDO.getDate() + "_" + weatherStationFcBasicWgDO.getType()));
        for (Map.Entry<String, List<WeatherStationFcBasicWgDO>> entry : weatherMap.entrySet()) {
            String[] split = entry.getKey().split("_");
            String date = split[0];
            String weatherType = split[1];
            List<WeatherStationFcBasicWgDO> value = entry.getValue();
            List<List<BigDecimal>> lists = new ArrayList<>();
            // 用于记录每个地市的比率
            List<BigDecimal> ratioList = new ArrayList<>();
            for (WeatherStationFcBasicWgDO weatherStationFcBasicWgDO : value) {
                List<BigDecimal> bigDecimals = BasePeriodUtils.toList(weatherStationFcBasicWgDO, 96, Constants.WEATHER_CURVE_START_WITH_ZERO);
                String ratioStr = map.get(CityStationEnum.getCityIdByStation(weatherStationFcBasicWgDO.getStationWgId())).toString();
                BigDecimal ratio = new BigDecimal(ratioStr);
                ratioList.add(ratio);
                List<BigDecimal> weather96RatioList = zero96List();
                for (int i = 0; i < bigDecimals.size(); i++) {
                    BigDecimal bigDecimal = bigDecimals.get(i);
                    if (com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.isValidValue(bigDecimal)) {
                        BigDecimal scaledValue = bigDecimal.multiply(ratio);
                        weather96RatioList.set(i, scaledValue);
                    }
                }
                lists.add(weather96RatioList);
            }

            List<BigDecimal> decimalList = zero96List();
            for (int i = 0; i < 96; i++) {
                BigDecimal sum = BigDecimal.ZERO;
                BigDecimal totalRatio = BigDecimal.ZERO;
                for (int j = 0; j < lists.size(); j++) {
                    List<BigDecimal> cityLoads = lists.get(j);
                    BigDecimal decimal = cityLoads.get(i);
                    if (decimal != null) {
                        sum = sum.add(decimal);
                        totalRatio = totalRatio.add(ratioList.get(j)); // 使用已知的比率
                    }
                }
                BigDecimal curValue = totalRatio.equals(BigDecimal.ZERO) ? null : sum.divide(totalRatio, MATH_CONTEXT);
                decimalList.set(i, curValue);
            }

            WeatherCityFcDO cityFcDO = new WeatherCityFcDO();
            Map<String, BigDecimal> decimalMap = ColumnUtil.listToMap(decimalList, Constants.LOAD_CURVE_START_WITH_ZERO);
            BasePeriodUtils.setAllFiled(cityFcDO, decimalMap);
            cityFcDO.setType(Integer.valueOf(weatherType));
            cityFcDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            cityFcDO.setCityId("1");
            cityFcDO.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
            cityFcDO.setDate(new java.sql.Date(DateUtils.string2Date(date, DateFormatType.SIMPLE_DATE_FORMAT_STR).getTime()));
            fcWeatherDOS.add(cityFcDO);
        }
        return fcWeatherDOS;
    }

    @Override
    public void doAutoStatFcDaysTemperature(String cityId, Date startDate, Date endDate) throws Exception {
        Date featureEndDate = DateUtil.getMoveDay(startDate, 10);

//        List<WeatherCityFcDO> weatherCityFcDOs = this.
//                findWeatherCityFcDOs(cityId, WeatherEnum.TEMPERATURE.getType(), startDate, endDate);
        List<WeatherCityFcDO> weatherCityFcDOs = weatherCityFcAdapterService
                .findWeatherCityFcDOS(WeatherSourceEnum.METEO.name(),cityId,WeatherEnum.TEMPERATURE.getType(), startDate, endDate);

        if (CollectionUtils.isEmpty(weatherCityFcDOs)){
            return;
        }
//        Map<java.sql.Date, WeatherCityFcDO> weatherCityFcDOMap =
//            weatherCityFcDOs.stream().collect(Collectors.toMap(t -> t.getDate(), t -> t));
        List<WeatherFeatureCityDayFcDO> weatherFeatureCityDayFcDOList = weatherFeatureCityDayFcService
                .getWeatherFeatureCityDayFcDOList(cityId, startDate, featureEndDate);
        Map<java.sql.Date, WeatherFeatureCityDayFcDO> featureCityDayFcDOMap = weatherFeatureCityDayFcDOList.stream()
                .collect(Collectors.toMap(t -> t.getDate(), t -> t));
        //归一化后的数据集合
        List<BigDecimal> sumValues = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.createList(BigDecimal.ZERO,Constants.WEATHER_CURVE_POINT_NUM);
        //单独计算T0000时刻点数据,因为气象getWeatherList中是否从0开始为false,顾需要补T0000
        List<BigDecimal> t0000sumValues = new ArrayList<>();
        t0000sumValues.add(BigDecimal.ZERO);
        int count96 = 0;
        int countT0000 = 0;
        for (WeatherCityFcDO weatherCityFcDO : weatherCityFcDOs) {
            WeatherFeatureCityDayFcDO weatherFeatureCityDayFcDO = featureCityDayFcDOMap.get(weatherCityFcDO.getDate());
            if(weatherFeatureCityDayFcDO == null)continue;
            BigDecimal highestTemperature = weatherFeatureCityDayFcDO.getHighestTemperature();
            BigDecimal lowestTemperature = weatherFeatureCityDayFcDO.getLowestTemperature();
            List<BigDecimal> weatherList = weatherCityFcDO.getloadList();
            List<BigDecimal> t0000Weather = Arrays.asList(weatherCityFcDO.getT0000());
            if(weatherList.stream().filter(t->t!= null).collect(Collectors.toList()).size() != 96){
                log.error("城市id:"+weatherCityFcDO.getCityId()+"在日期"+DateUtils.date2String(weatherCityFcDO.getDate(),DateFormatType.SIMPLE_DATE_FORMAT_STR)+"温度数据不足96点");
                continue;
            }
            List<BigDecimal> normWeather = getNormWeather(weatherList, highestTemperature, lowestTemperature);
            List<BigDecimal> t0000NormWeather = getNormWeather(t0000Weather, highestTemperature, lowestTemperature);

            sumValues = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAdd(sumValues,normWeather);
            //t0000时刻有值才计算
            if(t0000NormWeather.size() != 0 ) {
                t0000sumValues = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAdd(t0000sumValues, t0000NormWeather);
                countT0000++;
            }
            count96++;
        }
        //取平均
        List<BigDecimal> result = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listDivideValue(sumValues,new BigDecimal(count96));
        List<BigDecimal> t0000Result = new ArrayList<>();
        if(countT0000 != 0) {
            t0000Result = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listDivideValue(
                    t0000sumValues, new BigDecimal(countT0000));
        }
        //根据10日特性获取每一天的曲线
        for (WeatherFeatureCityDayFcDO weatherFeatureCityDayFcDO : weatherFeatureCityDayFcDOList) {
            java.sql.Date date = weatherFeatureCityDayFcDO.getDate();
            //当天不修正
//            if (date.equals(startDate)){
//                continue;
//            }
            //若当天存在曲线则不需要生成
//            if (weatherCityFcDOMap.get(date)!=null){
//                continue;
//            }

            BigDecimal lowestTemperature = weatherFeatureCityDayFcDO.getLowestTemperature();
            BigDecimal highestTemperature = weatherFeatureCityDayFcDO.getHighestTemperature();
            //获取最终值
            List<BigDecimal> normWeather = getWeatherValueByNorm(result, highestTemperature, lowestTemperature);
            List<BigDecimal> t0000NormWeather = new ArrayList<>();
            if(countT0000 != 0) {
                t0000NormWeather = getWeatherValueByNorm(t0000Result, highestTemperature,
                        lowestTemperature);
            }

            if (CollectionUtils.isEmpty(normWeather)){
                continue;
            }
            //进行极值拉伸
            if(countT0000 != 0) {
                normWeather.add(t0000NormWeather.get(0));
            }
            normWeather = AlgorithmUtil.recorrectLoad(normWeather, highestTemperature, lowestTemperature);
            WeatherCityFcMeteoDO weatherCityFcDO = new WeatherCityFcMeteoDO();
            weatherCityFcDO.setCityId(weatherFeatureCityDayFcDO.getCityId());
            weatherCityFcDO.setDate(weatherFeatureCityDayFcDO.getDate());
            weatherCityFcDO.setType(WeatherEnum.TEMPERATURE.getType());
            BasePeriodUtils.setAllFiled(weatherCityFcDO, ColumnUtil.listToMap(normWeather.subList(0,96),Constants.WEATHER_CURVE_START_WITH_ZERO));
            if(countT0000 != 0) {
                weatherCityFcDO.setT0000(normWeather.get(normWeather.size() - 1));
            }
            weatherCityFcMeteoService.doSaveOrUpdate(weatherCityFcDO);
        }
    }

    private List<BigDecimal> getWeatherValueByNorm(List<BigDecimal> values, BigDecimal highestTemperature,
                                                   BigDecimal lowestTemperature) {
        if (highestTemperature == null || lowestTemperature == null){
            return null;
        }
        List<BigDecimal> result = new ArrayList<>();
        for (BigDecimal bigDecimal : values) {
            if (bigDecimal!=null){
                BigDecimal value = (bigDecimal.multiply(highestTemperature.subtract(lowestTemperature))).add(lowestTemperature);
                result.add(value);
            }
        }
        return result;
    }

    private List<BigDecimal> getNormWeather(List<BigDecimal> weatherList, BigDecimal highestTemperature,
                                            BigDecimal lowestTemperature) {
        if (highestTemperature == null || lowestTemperature == null){
            return null;
        }
        List<BigDecimal> result = new ArrayList<>();
        for (BigDecimal bigDecimal : weatherList) {
            if (bigDecimal!=null){
                BigDecimal value = (bigDecimal.subtract(lowestTemperature))
                        .divide(highestTemperature.subtract(lowestTemperature), 2, RoundingMode.HALF_UP);
                result.add(value);
            }
        }
        return result;
    }

    @Override
    public void doSaveOuUpdatePreLoadWeather(WeatherCityFcBatchDO weatherCityFcBatchDOS) {
        weatherCityFcBatchDAO.saveOrUpdateByTemplate(weatherCityFcBatchDOS);
    }
}
