package com.tsintergy.lf.serviceimpl.forecast.impl;


import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DataUtil;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.AlgorithmService;
import com.tsintergy.lf.serviceapi.base.forecast.api.ForecastService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.api.ManualForecastService;
import com.tsintergy.lf.serviceapi.base.forecast.dto.ManualAlgorithmDTO;
import com.tsintergy.lf.serviceapi.base.forecast.dto.ManualContrastDTO;
import com.tsintergy.lf.serviceapi.base.forecast.dto.ManualForecastCurveDTO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 手动预测服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service(value = "manualForecastService")
public class ManualForecastServiceImpl extends BaseServiceImpl implements ManualForecastService {


    @Autowired
    private ForecastService forecastService;

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private LoadCityFcService loadCityFcService;

    @Autowired
    private SettingSystemService settingSystemService;

    @Override
    public List<ManualForecastCurveDTO> forecastCurve(Date date, String cityId, String caliberId,
        List<String> algorithmIds) throws Exception {
        //获取手动预测算法
        Map<String, ManualAlgorithmDTO> manualAlgorithmDTOMap = getStringManualAlgorithmDTOMap();
        if (CollectionUtils.isEmpty(algorithmIds)) {
            algorithmIds = new ArrayList<>(manualAlgorithmDTOMap.keySet());
        }

        //算法推荐id
        String recommendId = generateRecommendId(cityId);
        //是否有人工修正
        boolean hasModify = false;
        List<BigDecimal> modifyValue = null;
        List<ManualForecastCurveDTO> manualForecastCurveDTOS = new ArrayList<>();
        for (String algorithmId : algorithmIds) {
            try {
                List<BigDecimal> loadCityFcVOS = loadCityFcService
                    .findLoadCityFcDO(date, cityId, caliberId, algorithmId);
                if (!CollectionUtils.isEmpty(loadCityFcVOS)) {
                    //如果算法id是算法id，将hasModify修改为ture
                    if (algorithmId.equals(AlgorithmEnum.FORECAST_MODIFY.getId())) {
                        hasModify = true;
                    }
                    //如果算法id为推荐的id，将人工修正曲线预设为推荐的曲线
                    if (algorithmId.equals(recommendId)) {
                        modifyValue = loadCityFcVOS;
                    }
                    ManualForecastCurveDTO manualForecastCurveDTO = wrapManualForecastCurveDTO(manualAlgorithmDTOMap,
                        algorithmId, loadCityFcVOS);
                    manualForecastCurveDTOS.add(manualForecastCurveDTO);
                }
            } catch (Exception e) {
                log.error("查询预测负荷异常", e);
            }
        }

        //设置人工修正曲线
        generateModifyCurve(manualAlgorithmDTOMap, hasModify, modifyValue, manualForecastCurveDTOS);
        //获取基准日负荷
//        generateBaseDayCurve(date, cityId, caliberId, manualAlgorithmDTOMap, manualForecastCurveDTOS);
        //获取实际负荷
//        generateHisCurve(date, cityId, caliberId, manualAlgorithmDTOMap, manualForecastCurveDTOS);

        return manualForecastCurveDTOS;
    }

    private Map<String, ManualAlgorithmDTO> getStringManualAlgorithmDTOMap() {
        Map<String, ManualAlgorithmDTO> manualAlgorithmDTOMap = new HashMap<>();
        for (ManualAlgorithmDTO manualAlgorithmDTO : findForecastAlgorithm()) {
            manualAlgorithmDTOMap.put(manualAlgorithmDTO.getId(), manualAlgorithmDTO);
        }
        manualAlgorithmDTOMap.put(AlgorithmEnum.FORECAST_MODIFY.getId(),
            new ManualAlgorithmDTO(AlgorithmEnum.FORECAST_MODIFY.getId(), AlgorithmEnum.FORECAST_MODIFY.getType(),
                AlgorithmEnum.FORECAST_MODIFY.getDescription()));
        return manualAlgorithmDTOMap;
    }

//    private void generateBaseDayCurve(Date date, String cityId, String caliberId,
//        Map<String, ManualAlgorithmDTO> manualAlgorithmDTOMap, List<ManualForecastCurveDTO> manualForecastCurveDTOS)
//        throws Exception {
//        LoadCityHisDO loadCityHisVO = loadCityHisService.findLoadOfBaseDay(date, caliberId, cityId);
//        List<BigDecimal> baseDayValues = BasePeriodUtils.toList(loadCityHisVO, Constants.WEATHER_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
//        manualAlgorithmDTOMap.put("201", new ManualAlgorithmDTO("201", "201", "基准日"));
//        ManualForecastCurveDTO manualForecastCurveDTO = wrapManualForecastCurveDTO(manualAlgorithmDTOMap, "201",
//            baseDayValues);
//        manualForecastCurveDTOS.add(manualForecastCurveDTO);
//    }

    private void generateHisCurve(Date date, String cityId, String caliberId,
        Map<String, ManualAlgorithmDTO> manualAlgorithmDTOMap, List<ManualForecastCurveDTO> manualForecastCurveDTOS)
        throws Exception {
        List<BigDecimal> loadCityHisVO = loadCityHisService.findLoadCityHisDO(date, caliberId, cityId);
        manualAlgorithmDTOMap.put("203", new ManualAlgorithmDTO("203", "203", "实际值"));
        ManualForecastCurveDTO manualForecastCurveDTO = wrapManualForecastCurveDTO(manualAlgorithmDTOMap, "203",
            loadCityHisVO);
        manualForecastCurveDTOS.add(manualForecastCurveDTO);
    }

    private String generateRecommendId(String cityId) throws Exception {
        SystemData systemSetting = settingSystemService.getSystemSetting();
        if (CityConstants.PROVINCE_ID.equals(cityId)) {
            return systemSetting.getProvinceNormalAlgorithm();
        } else {
            return systemSetting.getCityNormalAlgorithm();
        }
    }

    private void generateModifyCurve(Map<String, ManualAlgorithmDTO> manualAlgorithmDTOMap, boolean hasModify,
        List<BigDecimal> modifyValue, List<ManualForecastCurveDTO> manualForecastCurveDTOS) {
        //如果没有人工修正曲线，将推荐曲线设为人工修正曲线；如果没有推荐曲线，随机选择一个预测曲线为人工修正曲线；如果都没有，将人工修正曲线设为96个0点
        if (!hasModify) {
            if (CollectionUtils.isEmpty(modifyValue)) {
                if (!CollectionUtils.isEmpty(manualForecastCurveDTOS)) {
                    modifyValue = manualForecastCurveDTOS.get(0).getCurve();
                } else {
                    modifyValue = DataUtil.generate96Zero();
                }
            }
            ManualForecastCurveDTO manualForecastCurveDTO = wrapManualForecastCurveDTO(manualAlgorithmDTOMap,
                AlgorithmEnum.FORECAST_MODIFY.getId(), modifyValue);
            manualForecastCurveDTOS.add(manualForecastCurveDTO);
        }
    }

    private ManualForecastCurveDTO wrapManualForecastCurveDTO(Map<String, ManualAlgorithmDTO> manualAlgorithmDTOMap,
        String algorithmId, List<BigDecimal> loadCityFcVOS) {
        ManualForecastCurveDTO manualForecastCurveDTO = new ManualForecastCurveDTO();
        manualForecastCurveDTO.setAlgorithmId(algorithmId);
        manualForecastCurveDTO.setAlgorithmName(manualAlgorithmDTOMap.get(algorithmId).getName());
        manualForecastCurveDTO.setCode(manualAlgorithmDTOMap.get(algorithmId).getCode());
        manualForecastCurveDTO.setCurve(loadCityFcVOS);
        manualForecastCurveDTO.setMaxLoad(LoadCalUtil.max(loadCityFcVOS));
        manualForecastCurveDTO.setMinLoad(LoadCalUtil.min(loadCityFcVOS));
        manualForecastCurveDTO.setGradient(
            LoadCalUtil.calDifferent(manualForecastCurveDTO.getMaxLoad(), manualForecastCurveDTO.getMinLoad()));
        manualForecastCurveDTO.setEnergy(LoadCalUtil.calcEnergy(loadCityFcVOS));
        return manualForecastCurveDTO;
    }

    @Override
    public List<ManualAlgorithmDTO> findForecastAlgorithm() {
        List<ManualAlgorithmDTO> manualAlgorithmDTO = new ArrayList<ManualAlgorithmDTO>() {
            {
                add(new ManualAlgorithmDTO(AlgorithmEnum.FORECAST_XGBOOST.getId(),
                    AlgorithmEnum.FORECAST_XGBOOST.getType(), AlgorithmEnum.FORECAST_XGBOOST.getDescription()));
                add(new ManualAlgorithmDTO(AlgorithmEnum.FORECAST_INNOVATION.getId(),
                    AlgorithmEnum.FORECAST_INNOVATION.getType(), AlgorithmEnum.FORECAST_INNOVATION.getDescription()));
                add(new ManualAlgorithmDTO(AlgorithmEnum.REPLENISH_LGB.getId(), AlgorithmEnum.REPLENISH_LGB.getType(),
                    AlgorithmEnum.REPLENISH_LGB.getDescription()));
                add(new ManualAlgorithmDTO(AlgorithmEnum.COMPREHENSIVE_MODEL.getId(),
                    AlgorithmEnum.COMPREHENSIVE_MODEL.getType(), AlgorithmEnum.COMPREHENSIVE_MODEL.getDescription()));
                add(new ManualAlgorithmDTO(AlgorithmEnum.FORECAST_SVM.getId(), AlgorithmEnum.FORECAST_SVM.getType(),
                    AlgorithmEnum.FORECAST_SVM.getDescription()));
                add(new ManualAlgorithmDTO(AlgorithmEnum.FORECAST_SVM_LARGE.getId(),
                    AlgorithmEnum.FORECAST_SVM_LARGE.getType(), AlgorithmEnum.FORECAST_SVM_LARGE.getDescription()));
            }
        };
        return manualAlgorithmDTO;
    }
    @Autowired
    private AlgorithmService algorithmService;

    @Autowired
    private CityService cityService;

    @Override
    public List<ManualAlgorithmDTO> findForecastAlgorithm(String cityId) throws Exception{
        List<ManualAlgorithmDTO> manualAlgorithmDTO = new ArrayList<ManualAlgorithmDTO>();
        List<AlgorithmDO> allAlgorithmsNotCache = algorithmService.getAllAlgorithmsNotCache();
        List<AlgorithmDO> pageAlgorithms = allAlgorithmsNotCache.stream().filter(
            t -> AlgorithmConstants.COMMON_ALGORITHM_TYPE.equals(t.getType()) || AlgorithmConstants.NORMAL_ALGORITHM_TYPE
                .equals(t.getType()))
            .collect(Collectors.toList());
        List<AlgorithmDO> viewAlgorithms = null;
        CityDO cityDO = cityService.findCityById(cityId);
        if(!CollectionUtils.isEmpty(pageAlgorithms)){
            if(CityConstants.PROVINCE_ID.equals(cityDO.getType())){
                viewAlgorithms = pageAlgorithms.stream().filter(t->t.getProvinceView() == true).collect(Collectors.toList());
            }else if(CityConstants.CITY_TYPE.equals(cityDO.getType())){
                viewAlgorithms = pageAlgorithms.stream().filter(t->t.getCityView() == true).collect(Collectors.toList());
            }
        }

        for(AlgorithmDO algorithmDO:viewAlgorithms){
            manualAlgorithmDTO.add(new ManualAlgorithmDTO(algorithmDO.getId(),
                algorithmDO.getCode(), algorithmDO.getAlgorithmCn()));
        }
        return manualAlgorithmDTO;
    }

    /**
     * 算法预测结果对比接口
     *
     * @param dataDTOS 算法预测结果list
     * @return 结果集
     * @throws Exception 异常
     * <AUTHOR>
     */
    private List<ManualContrastDTO> findContrastData(List<LoadCityFcDO> dataDTOS) throws Exception {
        List<ManualContrastDTO> dtoList = new ArrayList<>();
        for (LoadCityFcDO dto : dataDTOS) {
            ManualContrastDTO contrastDTO = new ManualContrastDTO();
            List<LoadCityHisDO> hisLoad = this.loadCityHisService
                .getLoadCityHisDOS(dto.getCityId(), null, dto.getDate(), dto.getDate());
            BigDecimal dayAccuracy;
            if (hisLoad.size() > 0) {
                dayAccuracy = LoadCalUtil.getDayAccuracy(hisLoad.get(0), dto, Constants.LOAD_CURVE_POINT_NUM,null);
                contrastDTO.setAccuracy(dayAccuracy);
            }
            contrastDTO.setAlgorithmId(dto.getAlgorithmId());
            contrastDTO.setAlgorithmName(AlgorithmEnum.findById(dto.getAlgorithmId()).getDescription());
            contrastDTO.setDate(dto.getDate());
            BigDecimal maxFcLoad = BasePeriodUtils.getMaxMinAvg(BasePeriodUtils
                    .toList(dto, Constants.LOAD_CURVE_POINT_NUM,
                        Constants.LOAD_CURVE_START_WITH_ZERO),
                4).get("max");
            BigDecimal minFcLoad = BasePeriodUtils.getMaxMinAvg(BasePeriodUtils
                    .toList(dto, Constants.LOAD_CURVE_POINT_NUM,
                        Constants.LOAD_CURVE_START_WITH_ZERO),
                4).get("min");
            contrastDTO.setForecastMax(maxFcLoad);
            contrastDTO.setForecastMin(minFcLoad);
            dtoList.add(contrastDTO);
        }
        return dtoList;
    }
}
