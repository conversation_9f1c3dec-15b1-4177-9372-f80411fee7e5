/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.recall.impl;

import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.evalucation.api.AccuracyLoadCityFcService;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyLoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcBatchService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcBatchDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.recall.api.AccuracyLoadCityFcRecallService;
import com.tsintergy.lf.serviceapi.base.recall.api.LoadCityFcRecallService;
import com.tsintergy.lf.serviceapi.base.recall.dto.LoadFcQueryDTO;
import com.tsintergy.lf.serviceapi.base.recall.pojo.AccuracyLoadCityFcServiceRecallDO;
import com.tsintergy.lf.serviceapi.base.recall.pojo.LoadCityFcRecallDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingBatchInitService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingBatchInitDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcLoadForecastService;
import com.tsintergy.lf.serviceimpl.recall.dao.LoadCityFcRecallDAO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/1 12:16
 * @Version: 1.0.0
 */

@Service("loadCityFcRecallService")
public class LoadCityFcRecallServiceImpl extends BaseServiceImpl implements LoadCityFcRecallService {

    @Autowired
    LoadCityFcRecallDAO loadCityFcRecallDAO;

    @Autowired
    LoadCityFcService loadCityFcService;

    @Autowired
    LoadCityHisService loadCityHisService;

    @Autowired
    LoadCityFcRecallService loadCityFcRecallService;

    @Autowired
    AccuracyLoadCityFcService accuracyLoadCityFcService;

    @Autowired
    AccuracyLoadCityFcRecallService accuracyLoadCityFcRecallService;

    @Autowired
    LoadCityFcBatchService loadCityFcBatchService;

    @Autowired
    WeatherCityFcLoadForecastService weatherCityFcLoadForecastService;

    @Override
    public LoadCityFcRecallDO getLoadCityFcRecallDO(String cityId, String caliberId, Date date, String algorithmId) throws Exception {
        return loadCityFcRecallDAO.getLoadCityFcDO(cityId,caliberId,algorithmId,date);
    }

    @Override
    public List<LoadCityFcRecallDO> getLoadCityFc(String cityId, String caliberId, Date startDate, Date endDate,
        String algorithmId) throws Exception {
        return loadCityFcRecallDAO
            .getLoadCityFcDOs(cityId, caliberId, algorithmId, startDate, endDate);
    }

    @Override
    public LoadCityFcRecallDO doSaveOrUpdateLoadCityFcDO96(LoadCityFcRecallDO loadCityFcRecallDO) throws Exception {
        weatherCityFcLoadForecastService.insertOrUpdateWeatherHisInfoRecall(loadCityFcRecallDO.getCityId(), loadCityFcRecallDO.getDate(),
            loadCityFcRecallDO.getAlgorithmId(), loadCityFcRecallDO.getCaliberId());
        return loadCityFcRecallDAO.doSaveOrUpdateLoadCityFcDO(loadCityFcRecallDO);
    }


    @Override
    public LoadFcQueryDTO getLoadFcQueryDTO(String cityId, String caliberId, Date date, String algorithmId) {
        try {
            LoadCityFcDO loadCityFcDO = loadCityFcService.getLoadFc(date, cityId, caliberId, algorithmId);
            List<BigDecimal> loadCityFcDOList = new ArrayList<>();
            if (loadCityFcDO != null) {
                loadCityFcDOList = BasePeriodUtils
                    .toList(loadCityFcDO, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
            }

            LoadCityHisDO loadCityHisDO = loadCityHisService.getLoadCityHisDO(cityId, caliberId, date);
            List<BigDecimal> loadCityHisDOList = new ArrayList<>();
            if (loadCityHisDO != null) {
                loadCityHisDOList = BasePeriodUtils
                    .toList(loadCityHisDO, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
            }

            LoadCityFcRecallDO loadCityFcRecallDO = loadCityFcRecallService.getLoadCityFcRecallDO(cityId, caliberId, date, algorithmId);
            List<BigDecimal> loadCityFcRecallDOList = new ArrayList<>();
            if (loadCityFcDO != null) {
                loadCityFcRecallDOList = BasePeriodUtils
                    .toList(loadCityFcRecallDO, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
            }

            List<AccuracyLoadCityFcDO> cityFcDOS = accuracyLoadCityFcService.getAccuracyLoadCityFcDOList(cityId, caliberId, date, algorithmId);
            List<BigDecimal> accuracyLoadCityFcDOList = new ArrayList<>();
            if (cityFcDOS != null && cityFcDOS.size() > 0) {
                accuracyLoadCityFcDOList = BasePeriodUtils
                    .toList(cityFcDOS.get(0), 96, Constants.LOAD_CURVE_START_WITH_ZERO);
            }

            List<AccuracyLoadCityFcServiceRecallDO> cityFcServiceRecallDOS = accuracyLoadCityFcRecallService.getAccuracyLoadCityFcRecallDO(cityId, caliberId, algorithmId, date);
            List<BigDecimal> accuracyLoadCityFcServiceRecallDOList = new ArrayList<>();
            if (cityFcServiceRecallDOS != null && cityFcServiceRecallDOS.size() > 0) {
                accuracyLoadCityFcServiceRecallDOList = BasePeriodUtils
                    .toList(cityFcServiceRecallDOS.get(0), 96, Constants.LOAD_CURVE_START_WITH_ZERO);
            }

            LoadFcQueryDTO dto = new LoadFcQueryDTO();
            if (loadCityFcRecallDOList != null) {
                dto.setRecallFc(loadCityFcRecallDOList);
            }
            if (loadCityHisDOList != null) {
                dto.setLoadHis(loadCityHisDOList);
            }
            if (accuracyLoadCityFcDOList != null) {
                dto.setAlgorithmFc(loadCityFcDOList);

            }
            if (accuracyLoadCityFcServiceRecallDOList != null) {
                dto.setAccuracyRecall(accuracyLoadCityFcServiceRecallDOList);
            }
            if (loadCityFcDOList != null) {
                dto.setAccuracyFc(accuracyLoadCityFcDOList);
            }

            return dto;

        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public LoadFcQueryDTO getLoadBatchFcQueryDTO(String cityId, String caliberId, Date date, String algorithmId
            , String batchId, Integer day) {
        try {
            LoadCityFcBatchDO loadCityFcBatchDO = loadCityFcBatchService.findOneByConditionByBatchId(cityId, date, caliberId
                    , algorithmId, batchId, day);
            List<BigDecimal> loadCityFcBatchDOList = new ArrayList<>();
            if (loadCityFcBatchDO != null) {
                loadCityFcBatchDOList = BasePeriodUtils
                        .toList(loadCityFcBatchDO, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
            }

            LoadCityHisDO loadCityHisDO = loadCityHisService.getLoadCityHisDO(cityId, caliberId, date);
            List<BigDecimal> loadCityHisDOList = new ArrayList<>();
            if (loadCityHisDO != null) {
                loadCityHisDOList = BasePeriodUtils
                        .toList(loadCityHisDO, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
            }

            LoadCityFcRecallDO loadCityFcRecallDO = loadCityFcRecallService.getLoadCityFcRecallDO(cityId, caliberId, date, algorithmId);
            List<BigDecimal> loadCityFcRecallDOList = new ArrayList<>();
            if (loadCityFcBatchDOList != null) {
                loadCityFcRecallDOList = BasePeriodUtils
                        .toList(loadCityFcRecallDO, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
            }

            List<AccuracyLoadCityFcDO> cityFcDOS = accuracyLoadCityFcService.getAccuracyLoadCityFcDOList(cityId, caliberId, date, algorithmId);
            List<BigDecimal> accuracyLoadCityFcDOList = new ArrayList<>();
            if (cityFcDOS != null && cityFcDOS.size() > 0) {
                accuracyLoadCityFcDOList = BasePeriodUtils
                        .toList(cityFcDOS.get(0), 96, Constants.LOAD_CURVE_START_WITH_ZERO);
            }

            List<AccuracyLoadCityFcServiceRecallDO> cityFcServiceRecallDOS = accuracyLoadCityFcRecallService.getAccuracyLoadCityFcRecallDO(cityId, caliberId, algorithmId, date);
            List<BigDecimal> accuracyLoadCityFcServiceRecallDOList = new ArrayList<>();
            if (cityFcServiceRecallDOS != null && cityFcServiceRecallDOS.size() > 0) {
                accuracyLoadCityFcServiceRecallDOList = BasePeriodUtils
                        .toList(cityFcServiceRecallDOS.get(0), 96, Constants.LOAD_CURVE_START_WITH_ZERO);
            }

            LoadFcQueryDTO dto = new LoadFcQueryDTO();
            if (loadCityFcRecallDOList != null) {
                dto.setRecallFc(loadCityFcRecallDOList);
            }
            if (loadCityHisDOList != null) {
                dto.setLoadHis(loadCityHisDOList);
            }
            if (accuracyLoadCityFcDOList != null) {
                dto.setAlgorithmFc(loadCityFcBatchDOList);

            }
            if (accuracyLoadCityFcServiceRecallDOList != null) {
                dto.setAccuracyRecall(accuracyLoadCityFcServiceRecallDOList);
            }
            if (loadCityFcBatchDOList != null) {
                dto.setAccuracyFc(accuracyLoadCityFcDOList);
            }

            return dto;

        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }
}