
package com.tsintergy.lf.serviceimpl.load.impl;


import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.enums.LoadFeatureEnum;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.load.dto.AreaLoadFeatureDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.AreaLoadFeatureDTOS;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureExtendDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadFeatureCityDayHisDAO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $Id: LoadFeatureCityDayHisServiceImpl.java, v 0.1 2018-01-31 10:51:27 tao Exp $$
 */

@Service("loadFeatureCityDayHisService")
public class LoadFeatureCityDayHisServiceImpl extends BaseServiceImpl implements LoadFeatureCityDayHisService {

    @Autowired
    CityService cityService;

    @Autowired
    LoadFeatureCityDayHisDAO loadFeatureCityDayHisDAO;

    @Override
    public DataPackage queryLoadFeatureCityDayHisDO(DBQueryParam param) throws Exception {
        try {
            return loadFeatureCityDayHisDAO.query(param);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public LoadFeatureCityDayHisDO doCreate(LoadFeatureCityDayHisDO vo) throws Exception {
        try {
            vo.setCreatetime(new Timestamp(System.currentTimeMillis()));
            return (LoadFeatureCityDayHisDO) loadFeatureCityDayHisDAO
                   .createAndFlush(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public void doRemoveLoadFeatureCityDayHisDO(LoadFeatureCityDayHisDO vo) throws Exception {
        try {
            loadFeatureCityDayHisDAO.remove(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw e;
        }
    }


    @Override
    public LoadFeatureCityDayHisDO doUpdateLoadFeatureCityDayHisDO(LoadFeatureCityDayHisDO vo) throws Exception {
        try {
            vo.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            return (LoadFeatureCityDayHisDO) loadFeatureCityDayHisDAO
                    .update(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public LoadFeatureCityDayHisDO findLoadFeatureCityDayHisDOByPk(Serializable pk) throws Exception {
        try {
            return (LoadFeatureCityDayHisDO) loadFeatureCityDayHisDAO
                    .findVOByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public List<LoadFeatureCityDayHisDO> findLoadFeatureCityDayHisDOS(String cityId, Date startDate, Date endDate,
                                                                      String caliberId) throws Exception {
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = loadFeatureCityDayHisDAO
                .getLoadFeatureCityDayHisDOs(cityId, startDate, endDate, caliberId);
        if (loadFeatureCityDayHisDOS.size() < 1) {
            return null;
        }
        return loadFeatureCityDayHisDOS;
    }

    @Override
    public List<LoadFeatureExtendDTO> findLoadFeatureExtendDTOS(String cityId, Date startDate, Date endDate,
                                                                String caliberId) throws Exception {
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = this
                .findLoadFeatureCityDayHisDOS(cityId, startDate, endDate, caliberId);
        List<LoadFeatureExtendDTO> loadFeatureExtendDTOS = new ArrayList<LoadFeatureExtendDTO>(30);
        if (CollectionUtils.isEmpty(loadFeatureCityDayHisDOS)) {
            return new ArrayList<>();
        }
        Map<String, String> cityNameMap = cityService.findAllCitys().stream().collect(Collectors.toMap(CityDO::getId, CityDO::getCity));

        for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS) {
            LoadFeatureExtendDTO loadFeatureExtendDTO = new LoadFeatureExtendDTO();
            BeanUtils.copyProperties(loadFeatureCityDayHisDO, loadFeatureExtendDTO);
            loadFeatureExtendDTO.setDate(
                    DateUtils.date2String(loadFeatureCityDayHisDO.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR));
            loadFeatureExtendDTO.setWeek(DateUtil.getWeek(loadFeatureCityDayHisDO.getDate()));
            loadFeatureExtendDTO.setCity(cityNameMap.get(loadFeatureCityDayHisDO.getCityId()));
            loadFeatureExtendDTOS.add(loadFeatureExtendDTO);
        }
        return loadFeatureExtendDTOS;
    }


    @Override
    public List<LoadFeatureDTO> findDayLoadFeatureDTOS(String cityId, Date startDate, Date endDate, String caliberId)
            throws Exception {
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = this
                .findLoadFeatureCityDayHisDOS(cityId, startDate, endDate, caliberId);
        List<LoadFeatureDTO> loadFeatureDTOS = new ArrayList<LoadFeatureDTO>();
        if (loadFeatureCityDayHisDOS != null) {
            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS) {
                LoadFeatureDTO loadFeatureDTO = new LoadFeatureDTO();
                loadFeatureDTO.setDate(DateUtils.date2String(loadFeatureCityDayHisDO.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR));
                loadFeatureDTO.setMin(loadFeatureCityDayHisDO.getMinLoad());
                loadFeatureDTO.setMax(loadFeatureCityDayHisDO.getMaxLoad());
                loadFeatureDTO.setAvg(loadFeatureCityDayHisDO.getAveLoad());
                loadFeatureDTOS.add(loadFeatureDTO);
            }
        }
        return loadFeatureDTOS;
    }


    @Override
    public List<LoadFeatureCityDayHisDO> listLoadFeature(String cityId, String caliberId, List<Date> dateList) throws Exception {
        return loadFeatureCityDayHisDAO.listLoadFeature(cityId, caliberId, dateList);
    }


    @Override
    public AreaLoadFeatureDTOS findAreaLoadFeatureDTO(String caliberId, Date date, Integer loadType) throws Exception {

        List<LoadFeatureCityDayHisDO> LoadFeatureCityDayHisDOS = this.findLoadFeatureCityDayHisDOS(null, date, date, caliberId);
        if (!CollectionUtils.isEmpty(LoadFeatureCityDayHisDOS)) {
            Map<String, BigDecimal> loadFeatureMap = null;
            if (loadType.equals(LoadFeatureEnum.Max.getType())) {
                loadFeatureMap = LoadFeatureCityDayHisDOS.stream().collect(Collectors.toMap(LoadFeatureCityDayHisDO::getCityId, LoadFeatureCityDayHisDO::getMaxLoad));
            } else {
                loadFeatureMap = LoadFeatureCityDayHisDOS.stream().collect(Collectors.toMap(LoadFeatureCityDayHisDO::getCityId, LoadFeatureCityDayHisDO::getMinLoad));
            }

            Map<String, String> cityMap =cityService.findAllCitys().stream().filter(CityDO -> StringUtils.isNotBlank(CityDO.getArea()))
                    .collect(Collectors.toMap(CityDO::getId, CityDO::getArea));
            Map<String,BigDecimal> areaLoadAddMap = new HashMap<>();
            loadFeatureMap.forEach((cityId,load) -> {
                String areaName = cityMap.get(cityId);
                if (StringUtils.isNotBlank(areaName)) {
                    if (areaLoadAddMap.get(areaName) == null) {
                        areaLoadAddMap.put(areaName, load);
                    } else {
                        areaLoadAddMap.put(areaName, load.add(areaLoadAddMap.get(areaName)));
                    }
                }
            });

            AreaLoadFeatureDTOS areaLoadFeatureDTOS = new AreaLoadFeatureDTOS();
            areaLoadFeatureDTOS.setProvinceFeatureLoad(loadFeatureMap.get(CityConstants.PROVINCE_ID));
            List<AreaLoadFeatureDTO> areaLoadFeatureDTOList = new ArrayList<>();
            areaLoadAddMap.forEach((area,load) -> {
                AreaLoadFeatureDTO areaLoadFeatureDTO = new AreaLoadFeatureDTO(area,load,
                        load.divide(areaLoadFeatureDTOS.getProvinceFeatureLoad(),4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)));
                areaLoadFeatureDTOList.add(areaLoadFeatureDTO);
            });
            areaLoadFeatureDTOS.setData(areaLoadFeatureDTOList);

            return areaLoadFeatureDTOS;
        }

        return null;
    }

    @Override
    public List<LoadFeatureCityDayHisDO> findLoadFeatureCityDayHisVOS(String cityId,Date startDate, Date endDate, String caliberId) throws Exception {
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisVOS = loadFeatureCityDayHisDAO.getLoadFeatureCityDayHisDOs(cityId, startDate, endDate, caliberId);
        if (loadFeatureCityDayHisVOS.size() < 1) {
            return null;
        }
        return loadFeatureCityDayHisVOS;
    }
}
