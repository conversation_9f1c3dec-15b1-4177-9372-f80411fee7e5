package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DataUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcMeteoService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcMeteoDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcMeteoDAO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2024-12-04
 * @since 1.0.0
 */
@Slf4j
@Service("weatherCityFcMeteoService")
public class WeatherCityFcMeteoServiceImpl implements WeatherCityFcMeteoService {

    @Autowired
    private WeatherCityFcMeteoDAO weatherCityFcMeteoDAO;

    @Autowired
    private CityService cityService;

    @Override
    public List<WeatherCityFcMeteoDO> getListByCondition(String cityId, Integer type, Date startDate, Date endDate) {
        return weatherCityFcMeteoDAO.findAll(
                JpaWrappers.<WeatherCityFcMeteoDO>lambdaQuery()
                        .eq(cityId != null, WeatherCityFcMeteoDO::getCityId, cityId)
                        .eq(type != null, WeatherCityFcMeteoDO::getType, type)
                        .ge(WeatherCityFcMeteoDO::getDate, new java.sql.Date(startDate.getTime()))
                        .le(WeatherCityFcMeteoDO::getDate, new java.sql.Date(endDate.getTime())));
    }

    @Override
    public void doSaveOrUpdate(WeatherCityFcMeteoDO weatherCityFcMeteoDO) {
        List<WeatherCityFcMeteoDO> all = weatherCityFcMeteoDAO.findAll(
                JpaWrappers.<WeatherCityFcMeteoDO>lambdaQuery()
                        .eq(WeatherCityFcMeteoDO::getCityId, weatherCityFcMeteoDO.getCityId())
                        .eq(WeatherCityFcMeteoDO::getType, weatherCityFcMeteoDO.getType())
                        .eq(WeatherCityFcMeteoDO::getDate, weatherCityFcMeteoDO.getDate()));
        if (CollectionUtils.isEmpty(all)) {
            weatherCityFcMeteoDAO.save(weatherCityFcMeteoDO);
        } else {
            weatherCityFcMeteoDO.setId(all.get(0).getId());
            weatherCityFcMeteoDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            weatherCityFcMeteoDAO.saveOrUpdateByTemplate(weatherCityFcMeteoDO);
        }
    }

    @SneakyThrows
    @Override
    public void insertYesterday24HourData(WeatherCityFcMeteoDO weatherCityFcMeteoDO) {
        Date yesterday = DateUtils.addDays(weatherCityFcMeteoDO.getDate(), -1);
        WeatherCityFcMeteoDO yesterdayDO = weatherCityFcMeteoDAO.findOne(
                JpaWrappers.<WeatherCityFcMeteoDO>lambdaQuery()
                        .eq(WeatherCityFcMeteoDO::getCityId, weatherCityFcMeteoDO.getCityId())
                        .eq(WeatherCityFcMeteoDO::getType, weatherCityFcMeteoDO.getType())
                        .eq(WeatherCityFcMeteoDO::getDate, yesterday));
        if (yesterdayDO == null) {
            return;
        }
        yesterdayDO.setT2400(weatherCityFcMeteoDO.getT0000());

        BigDecimal t2300 = yesterdayDO.getT2300();
        BigDecimal t2400 = weatherCityFcMeteoDO.getT0000();
        if (t2400 != null) {
            BigDecimal difference = t2400.subtract(t2300);
            BigDecimal t2315 = t2300.add(difference.multiply(new BigDecimal("0.25")));
            BigDecimal t2330 = t2300.add(difference.multiply(new BigDecimal("0.5")));
            BigDecimal t2345 = t2300.add(difference.multiply(new BigDecimal("0.75")));
            yesterdayDO.setT2315(t2315);
            yesterdayDO.setT2330(t2330);
            yesterdayDO.setT2345(t2345);
        }
        List<BigDecimal> weatherList = yesterdayDO.getWeatherList();
        ColumnUtil.supplimentPoit(weatherList);
        Map<String, BigDecimal> decimalMap = ColumnUtil.listToMap(weatherList, Constants.LOAD_CURVE_START_WITH_ZERO);
        BasePeriodUtils.setAllFiled(yesterdayDO, decimalMap);
        yesterdayDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
        weatherCityFcMeteoDAO.saveOrUpdateByTemplate(yesterdayDO);
    }

    @SneakyThrows
    @Override
    public void statMeteoProvinceFcWeather(Date startDate, Date endDate) {
        List<WeatherCityFcMeteoDO> weatherCityFcMeteoDOS = this.getListByCondition(null, null, startDate, startDate);
        if (!org.springframework.util.CollectionUtils.isEmpty(weatherCityFcMeteoDOS)) {
            List<WeatherCityFcMeteoDO> meteoDOS = new ArrayList<>();
            Map<String, List<WeatherCityFcMeteoDO>> dateWeatherMap = weatherCityFcMeteoDOS.stream()
                    .collect(Collectors.groupingBy(t -> DateUtil.formateDate(t.getDate()) + Constants.SEPARATOR_PUNCTUATION + t.getType()));
            Map<String, String> cityMap = cityService.findAllCitys().stream()
                    .collect(Collectors.toMap(CityDO::getCity, CityDO::getId));
            dateWeatherMap.forEach((key, weatherDOS) -> {
                if (!org.springframework.util.CollectionUtils.isEmpty(weatherDOS)) {
                    try {
                        List<BigDecimal> resultValues = new ArrayList<>();
                        for (WeatherCityFcMeteoDO weatherCityHisDO : weatherDOS) {
                            if (weatherCityHisDO.getCityId().equals(cityMap.get("武汉"))) {
                                List<BigDecimal> weatherValues = BasePeriodUtils
                                        .toList(weatherCityHisDO, Constants.WEATHER_CURVE_POINT_NUM, true);
                                weatherValues.add(weatherCityHisDO.getT2400());
                                //log.info("武汉原始数据：" + weatherValues.toString());
                                List<BigDecimal> values = DataUtil.multiply(weatherValues, new BigDecimal(0.33333333));
                                //log.info("武汉*0.33333333后的结果：" + values.toString());
                                resultValues = DataUtil.listAdd(resultValues, values);
                            } else if (weatherCityHisDO.getCityId().equals(cityMap.get("黄冈"))) {
                                List<BigDecimal> weatherValues = BasePeriodUtils.toList(weatherCityHisDO, Constants.WEATHER_CURVE_POINT_NUM, true);
                                weatherValues.add(weatherCityHisDO.getT2400());
                                //log.info("黄冈原始数据：" + weatherValues.toString());
                                List<BigDecimal> values = DataUtil.multiply(weatherValues, new BigDecimal(0.15896188));
                                //log.info("黄冈*0.15896188后的结果：" + values.toString());
                                resultValues = DataUtil.listAdd(resultValues, values);
                            } else if (weatherCityHisDO.getCityId().equals(cityMap.get("襄阳"))) {
                                List<BigDecimal> weatherValues = BasePeriodUtils.toList(weatherCityHisDO, Constants.WEATHER_CURVE_POINT_NUM, true);
                                weatherValues.add(weatherCityHisDO.getT2400());
                                //log.info("襄阳原始数据：" + weatherValues.toString());
                                List<BigDecimal> values = DataUtil.multiply(weatherValues, new BigDecimal(0.14220059));
                                //log.info("襄阳*0.14220059后的结果：" + values.toString());
                                resultValues = DataUtil.listAdd(resultValues, values);
                            } else if (weatherCityHisDO.getCityId().equals(cityMap.get("荆州"))) {
                                List<BigDecimal> weatherValues = BasePeriodUtils.toList(weatherCityHisDO, Constants.WEATHER_CURVE_POINT_NUM, true);
                                weatherValues.add(weatherCityHisDO.getT2400());
                                //log.info("荆州原始数据：" + weatherValues.toString());
                                List<BigDecimal> values = DataUtil.multiply(weatherValues, new BigDecimal(0.14138956));
                                //log.info("荆州*0.14138956后的结果：" + values.toString());
                                resultValues = DataUtil.listAdd(resultValues, values);
                            } else if (weatherCityHisDO.getCityId().equals(cityMap.get("孝感"))) {
                                List<BigDecimal> weatherValues = BasePeriodUtils.toList(weatherCityHisDO, Constants.WEATHER_CURVE_POINT_NUM, true);
                                weatherValues.add(weatherCityHisDO.getT2400());
                                //log.info("孝感原始数据：" + weatherValues.toString());
                                List<BigDecimal> values = DataUtil.multiply(weatherValues, new BigDecimal(0.1154366));
                                //log.info("孝感*0.1154366后的结果：" + values.toString());
                                resultValues = DataUtil.listAdd(resultValues, values);
                            } else if (weatherCityHisDO.getCityId().equals(cityMap.get("宜昌"))) {
                                List<BigDecimal> weatherValues = BasePeriodUtils.toList(weatherCityHisDO, Constants.WEATHER_CURVE_POINT_NUM, true);
                                weatherValues.add(weatherCityHisDO.getT2400());
                                //log.info("宜昌原始数据：" + weatherValues.toString());
                                List<BigDecimal> values = DataUtil.multiply(weatherValues, new BigDecimal(0.10867802));
                                //log.info("宜昌*0.10867802后的结果：" + values.toString());
                                resultValues = DataUtil.listAdd(resultValues, values);
                            }
                        }

                        if (!org.springframework.util.CollectionUtils.isEmpty(resultValues)) {
                            //log.info("湖北最后的结果：" + resultValues.toString());
                            WeatherCityFcMeteoDO WeatherCityFcMeteoDO = new WeatherCityFcMeteoDO();
                            WeatherCityFcMeteoDO.setType(WeatherEnum.TEMPERATURE.getType());
                            String[] split = key.split(Constants.SEPARATOR_PUNCTUATION);
                            WeatherCityFcMeteoDO.setType(Integer.valueOf(split[1]));
                            WeatherCityFcMeteoDO.setDate(new java.sql.Date(DateUtil.getDate(split[0], "yyyy-MM-dd").getTime()));
                            WeatherCityFcMeteoDO.setCityId(CityConstants.PROVINCE_ID);
                            Map<String, BigDecimal> decimalMap = ColumnUtil
                                    .listToMap(resultValues.subList(0, 96), true);
                            BasePeriodUtils.setAllFiled(WeatherCityFcMeteoDO, decimalMap);
                            WeatherCityFcMeteoDO.setT2400(resultValues.get(96));
                            meteoDOS.add(WeatherCityFcMeteoDO);

                        }
                    } catch (Exception e) {
                        log.error("装配省调气象异常", e);
                    }
                }
            });

            if (CollectionUtils.isNotEmpty(meteoDOS)) {
                for (WeatherCityFcMeteoDO weatherCityFcMeteoDO : meteoDOS) {
                    this.doSaveOrUpdate(weatherCityFcMeteoDO);
                    this.insertYesterday24HourData(weatherCityFcMeteoDO);
                }
            }
        }
    }

    @Override
    public List<WeatherCityFcMeteoDO> getWeatherCityFcDOS(List<String> cityIds, Integer type, Date start, Date end) {
        return weatherCityFcMeteoDAO.findAll(
                JpaWrappers.<WeatherCityFcMeteoDO>lambdaQuery()
                        .in(cityIds != null, WeatherCityFcMeteoDO::getCityId, cityIds)
                        .eq(type != null, WeatherCityFcMeteoDO::getType, type)
                        .ge(WeatherCityFcMeteoDO::getDate, new java.sql.Date(start.getTime()))
                        .le(WeatherCityFcMeteoDO::getDate, new java.sql.Date(end.getTime())));
    }
}
