package com.tsintergy.lf.serviceimpl.load.impl;

import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.vo.BasePeriod96VO;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFc288DO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayFcShortService;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureShortFcDayDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadShortFcAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHis288DO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayShortFcDO;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.serviceimpl.forecast.dao.LoadCityFc288DAO;
import com.tsintergy.lf.serviceimpl.forecast.dao.LoadCityFcDAO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadCityHis288DAO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadCityHisDAO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadFeatureCityDayShortFcDAO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Service("loadFeatureCityDayFcShortService")
@Slf4j
public class LoadFeatureCityDayFcShortServiceImpl implements LoadFeatureCityDayFcShortService {

    @Autowired
    private LoadFeatureCityDayShortFcDAO loadFeatureCityDayShortFcDAO;

    @Autowired
    private LoadCityFc288DAO loadCityFc288DAO;

    @Autowired
    private LoadCityHis288DAO loadCityHis288DAO;

    @Autowired
    private LoadCityFcDAO loadCityFcDAO;

    @Autowired
    private LoadCityHisDAO loadCityHisDAO;



    @Override
    public List<LoadFeatureShortFcDayDTO> findFeatureStatisDTOS(Date startDate, Date endDate, String caliberId, String cityId, Integer type) throws Exception {
        List<LoadFeatureCityDayShortFcDO> loadFeatureCityDayShortFcDOS = findLoadFeatureCityDayShortFcDOs(cityId , startDate , endDate , caliberId ,  null,type);
        if (CollectionUtils.isEmpty(loadFeatureCityDayShortFcDOS)){
            return new ArrayList<>();
        }
        Map<String , LoadFeatureCityDayShortFcDO> map = loadFeatureCityDayShortFcDOS.stream().collect(
                Collectors.toMap(e->e.getDate().getTime()+e.getCityId() + e.getCaliberId() + e.getType() , e->e , (oldv ,curv)->curv)
        );
        List<LoadFeatureShortFcDayDTO> result = new ArrayList<>();
        for(LoadFeatureCityDayShortFcDO loadFeatureCityDayShortFcDO : map.values()){
            LoadFeatureShortFcDayDTO dayDTO = new LoadFeatureShortFcDayDTO();
            dayDTO.setDate(DateUtil.getStrDate(loadFeatureCityDayShortFcDO.getDate() , "yyyy-MM-dd"));
            BeanUtils.copyProperties(loadFeatureCityDayShortFcDO , dayDTO);
            result.add(dayDTO);
        }
        result.stream().sorted(Comparator.comparing(LoadFeatureShortFcDayDTO::getDate)).collect(Collectors.toList());
        //计算最后一行 平均行数据
        BigDecimal maxLoadAccuracyAvg = result.stream().map(LoadFeatureShortFcDayDTO::getMaxLoadAccuracy).reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(result.size()), 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal minLoadAccuracyAvg = result.stream().map(LoadFeatureShortFcDayDTO::getMinLoadAccuracy).reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(result.size()), 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal maxAccuracyAvg = result.stream().map(LoadFeatureShortFcDayDTO::getMaxAccuracy).reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(result.size()), 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal minAccuracyAvg = result.stream().map(LoadFeatureShortFcDayDTO::getMinAccuracy).reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(result.size()), 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal avgAccuracyAvg = result.stream().map(LoadFeatureShortFcDayDTO::getAvgAccuracy).reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(result.size()), 4, BigDecimal.ROUND_HALF_UP);
        LoadFeatureShortFcDayDTO dayDTO = new LoadFeatureShortFcDayDTO();
        dayDTO.setDate("平均");
        dayDTO.setMaxLoadAccuracy(maxLoadAccuracyAvg);
        dayDTO.setMinLoadAccuracy(minLoadAccuracyAvg);
        dayDTO.setMaxAccuracy(maxAccuracyAvg);
        dayDTO.setMinAccuracy(minAccuracyAvg);
        dayDTO.setAvgAccuracy(avgAccuracyAvg);
        result.add(dayDTO);
        return result;
    }

    @Override
    public void doSaveOrUpdateLoadFeatureCityDayFcDOS(List<LoadFeatureCityDayShortFcDO> loadFeatureCityDayShortFcDOS) {
        for (LoadFeatureCityDayShortFcDO loadFeatureCityDayShortFcDO : loadFeatureCityDayShortFcDOS) {
            try {
                this.doSaveOrUpdateLoadFeatureCityDayShortFcDO(loadFeatureCityDayShortFcDO);
            } catch (Exception e) {
                log.error("存储超短期预测负荷特性异常...", e);
            }
        }
    }

    @Override
    public List<LoadShortFcAccuracyDTO> findDayTimeAccuracy(Date date, String cityId, String caliberId, Integer type) throws Exception{
        if(type == 1){
            //五分钟间隔  288点预测
            LoadCityFc288DO fc288DOS = loadCityFc288DAO.getLoadCityFcDO(cityId,caliberId , null , date);
            LoadCityHis288DO his288DOS = loadCityHis288DAO.getLoadCityHisDO(cityId ,date ,caliberId);
            return getLoadShortFcAccuracyDTOList(fc288DOS , his288DOS , type);
        }else{
            //十五分钟间隔 96点预测
            LoadCityFcDO loadCityFcDO = loadCityFcDAO.getLoadCityFcDO(cityId , caliberId , AlgorithmEnum.SHORT_FORECAST.getId() , date);
            LoadCityHisDO loadCityHisDO = loadCityHisDAO.getLoadCityHisDOByOneDate(cityId , date , caliberId);
            return getLoadShortFcAccuracyDTOList(loadCityFcDO , loadCityHisDO , type);
        }
    }

    private List<LoadShortFcAccuracyDTO> getLoadShortFcAccuracyDTOList(BasePeriod96VO hisDO, BasePeriod96VO fcDO , Integer type) throws Exception{
        List<LoadShortFcAccuracyDTO> resultList = new ArrayList<>();
        List<BigDecimal> fcBigDecimals = BasePeriodUtils.toList(fcDO , type == 1 ? 288 : 96, Constants.LOAD_CURVE_START_WITH_ZERO );
        List<BigDecimal> hisBigDecimals = BasePeriodUtils.toList(hisDO , type == 1 ? 288 : 96, Constants.LOAD_CURVE_START_WITH_ZERO );
        if(CollectionUtils.isEmpty(fcBigDecimals) || CollectionUtils.isEmpty(hisBigDecimals)){
            TsieExceptionUtils.newBusinessException("检查历史或预测数据");
        }
        List<String> columns = ColumnUtil.getColumns(type.intValue() == 1 ? 288 : 96, Constants.LOAD_CURVE_START_WITH_ZERO, false);
        for(int i = 0 ; i < fcBigDecimals.size() ; i++){
            LoadShortFcAccuracyDTO dto = new LoadShortFcAccuracyDTO();
            dto.setMomentTime(columns.get(i).substring(0,2) + ":" + columns.get(i).substring(2,4));
            dto.setHisLoad(hisBigDecimals.get(i));
            dto.setFcLoad(fcBigDecimals.get(i));
            dto.setAccuracy(LoadCalUtil.calcMaxMinAccuracy(hisBigDecimals.get(i) , fcBigDecimals.get(i)));
            resultList.add(dto);
        }
        return resultList;
    }

    private void doSaveOrUpdateLoadFeatureCityDayShortFcDO(LoadFeatureCityDayShortFcDO loadFeatureCityDayShortFcDO) throws Exception{
        LoadFeatureCityDayShortFcDO oldVO = findLoadFeatureCityDayShortFcDO(loadFeatureCityDayShortFcDO.getCityId(), loadFeatureCityDayShortFcDO.getDate(), loadFeatureCityDayShortFcDO.getCaliberId(), loadFeatureCityDayShortFcDO.getAlgorithmId() , loadFeatureCityDayShortFcDO.getType());
        if (oldVO != null) {
            String id = oldVO.getId();
            BeanUtils.copyProperties(loadFeatureCityDayShortFcDO, oldVO);
            oldVO.setId(id);
            oldVO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            loadFeatureCityDayShortFcDAO.updateAndFlush(oldVO);
        } else {
            loadFeatureCityDayShortFcDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            loadFeatureCityDayShortFcDAO.createAndFlush(loadFeatureCityDayShortFcDO);
        }
    }

    private LoadFeatureCityDayShortFcDO findLoadFeatureCityDayShortFcDO(String cityId, Date date, String caliberId, String algorithmId , Integer type) throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (StringUtils.isNotBlank(cityId)) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "cityId", cityId);
        }
        if (date != null) {
            dbQueryParamBuilder.where(QueryOp.DateEqualTo, "date", new java.sql.Date(date.getTime()));
        }
        if (StringUtils.isNotBlank(caliberId)) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (StringUtils.isNotBlank(algorithmId)) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "algorithmId", algorithmId);
        }
        if (type != null) {
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "type", type);
        }
        List<LoadFeatureCityDayShortFcDO> datas = loadFeatureCityDayShortFcDAO.query(dbQueryParamBuilder.build()).getDatas();
        if (!CollectionUtils.isEmpty(datas)) {
            return datas.get(0);
        }
        return null;
    }

    private List<LoadFeatureCityDayShortFcDO> findLoadFeatureCityDayShortFcDOs(String cityId, Date startDate, Date endDate, String caliberId, String algorithmId , Integer type) throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (StringUtils.isNotBlank(cityId)) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "cityId", cityId);
        }
        if (startDate != null) {
            dbQueryParamBuilder.where(QueryOp.DateNoLessThan, "date", new java.sql.Date(startDate.getTime()));
        }
        if (endDate != null) {
            dbQueryParamBuilder.where(QueryOp.DateNoMoreThan, "date", new java.sql.Date(endDate.getTime()));
        }
        if (StringUtils.isNotBlank(caliberId)) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (StringUtils.isNotBlank(algorithmId)) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "algorithmId", algorithmId);
        }
        if (type != null) {
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "type", type);
        }
        return loadFeatureCityDayShortFcDAO.query(dbQueryParamBuilder.build()).getDatas();
    }
}
