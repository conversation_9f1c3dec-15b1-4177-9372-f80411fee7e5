package com.tsintergy.lf.serviceimpl.cache.configure;

import com.tsieframework.cloud.security.core.base.properties.SecurityProperties;
import com.tsieframework.cloud.security.serviceapi.system.api.SecurityService;
import com.tsieframework.cloud.security.serviceimpl.system.impl.SecurityServiceImpl;
import com.tsieframework.core.component.cache.business.CacheService;
import com.tsieframework.core.component.cache.business.CacheServiceFactory;
import com.tsieframework.core.component.cache.business.CacheServiceImpl;
import com.tsieframework.core.component.cache.service.DataCacheService;
import com.tsintergy.lf.serviceimpl.cache.impl.AlgorithmCacheServiceImpl;
import com.tsintergy.lf.serviceimpl.cache.impl.CaliberCacheServiceImpl;
import com.tsintergy.lf.serviceimpl.cache.impl.CityCacheServiceImpl;
import com.tsintergy.lf.serviceimpl.cache.impl.SettingSystemCacheServiceImpl;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <p>
 * Service模块配置，通常这里负责注入非DAO、Service的其他类型的Bean定义，方便统一查找
 * </p>
 *
 * <AUTHOR>
 * @date 2020-02-04 14:05:40
 */
@Configuration
public class ServiceCacheConfig {

    private Logger logger = LoggerFactory.getLogger(ServiceCacheConfig.class);

    public ServiceCacheConfig() {
        logger.debug("初始化" + getClass());
    }

    @Bean
    public CacheService CacheService(CacheServiceFactory cacheServiceFactory, SecurityProperties securityProperties) {

        CacheServiceImpl cacheService = new CacheServiceImpl();
        cacheService.setCacheExpiredTime(String.valueOf(securityProperties.getCacheExpiredSeconds()));
        cacheService.setCacheServiceFactory(cacheServiceFactory);
        return cacheService;
    }


    @Bean
    public CacheServiceFactory CacheServiceFactory(CityCacheServiceImpl cityCacheService,
        AlgorithmCacheServiceImpl algorithmCacheService,
        SettingSystemCacheServiceImpl settingSystemCacheService, CaliberCacheServiceImpl caliberCacheService) {
        Map<String, DataCacheService> serviceMap = new HashMap<>();
        serviceMap.put("city", cityCacheService);
        serviceMap.put("algorithm", algorithmCacheService);
        serviceMap.put("settingSystem", settingSystemCacheService);
        serviceMap.put("caliber", caliberCacheService);

        CacheServiceFactory cacheServiceFactory = new CacheServiceFactory();
        cacheServiceFactory.setServiceMap(serviceMap);
        return cacheServiceFactory;
    }


}
