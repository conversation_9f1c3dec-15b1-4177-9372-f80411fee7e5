/**
 *     Copyright (C), 2015‐2020, 北京清能互联科技有限公司    Author:  EDZ    Date:  2020/10/12 14:06    History:  
 *  <author> <time> <version> <desc> 
 */
package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.util.ColumnUtil;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.base.api.AlgorithmParamService;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.system.api.SettingBatchInitService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingBatchInitDO;
import com.tsintergy.lf.serviceapi.base.weather.api.*;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherFeatureDTO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.*;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcDAO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcLoadForecastDAO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityHisLoadForecastDAO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityHisLoadForecastRecallDAO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *     Description:  <br>     <AUTHOR>    @create 2020/10/12    @since 1.0.0  
 */
@Slf4j
@Service("weatherCityFcLoadForecastService")
public class WeatherCityFcLoadForecastServiceImpl implements WeatherCityFcLoadForecastService {

    @Autowired
    private WeatherCityFcLoadForecastDAO weatherCityFcLoadForecastDAO;

    @Autowired
    WeatherCityFcDAO weatherCityFcDAO;

    @Autowired
    WeatherCityFcService weatherCityFcService;

    @Autowired
    private WeatherCityFcAdapterService weatherCityFcAdapterService;

    @Autowired
    WeatherCityFcLoadForecastService weatherCityFcLoadForecastService;

    @Autowired
    private CityService cityService;

    @Autowired
    private SettingBatchInitService settingBatchInitService;

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Autowired
    private WeatherCityHisLoadForecastDAO weatherCityHisLoadForecastDAO;

    @Autowired
    private WeatherCityHisLoadForecastRecallDAO weatherCityHisLoadForecastRecallDAO;


    @Autowired
    private AlgorithmParamService algorithmParamService;
    private static final Integer FIRST_INDEX = 0;

    /**
     * 查询预测使用气象时间
     */
    @Override
    public Date findWeatherFcTime(String cityId, Integer type, List<String> algorithmId, Date date, String caliberId)
        throws Exception {

        Date weatherFcTime = null;
        List<WeatherCityFcLoadForecastDO> weatherCityFcLoadForecastDOList = weatherCityFcLoadForecastDAO
            .findWeatherInfoFcLoadForecast(cityId, type, algorithmId, date, caliberId);
        List<Date> list = new ArrayList<>();
        for (WeatherCityFcLoadForecastDO weatherCityFcLoadForecastDO : weatherCityFcLoadForecastDOList) {
            Date dateTime = null;
            if (weatherCityFcLoadForecastDO.getUpdatetime() != null) {
                dateTime = weatherCityFcLoadForecastDO.getUpdatetime();
            } else {
                dateTime = weatherCityFcLoadForecastDO.getCreatetime();
            }

            if (dateTime != null) {
                list.add(dateTime);
            }
        }
        if (!CollectionUtils.isEmpty(list)) {
            weatherFcTime = Collections.min(list);
        }

        return weatherFcTime;
    }

    @Override
    public List<WeatherDTO> findWeatherFc(String cityId, Integer type, List<String> algorithmId, Date date,
        String caliberId) throws Exception {
        List<WeatherDTO> list = new ArrayList<>();
        List<WeatherCityFcLoadForecastDO> weatherInfoFcLoadForecast = weatherCityFcLoadForecastDAO
            .findWeatherInfoFcLoadForecast(cityId, type, algorithmId, date, caliberId);
        for (WeatherCityFcLoadForecastDO weatherCityFcLoadForecastDO : weatherInfoFcLoadForecast) {
            WeatherDTO weatherDTO = new WeatherDTO();
            weatherDTO.setCity(weatherCityFcLoadForecastDO.getCityId());
            weatherDTO.setType(weatherCityFcLoadForecastDO.getType());
            weatherDTO.setDate(weatherCityFcLoadForecastDO.getDate());
            weatherDTO
                .setAlgorithm(AlgorithmEnum.findById(weatherCityFcLoadForecastDO.getAlgorithmId()).getDescription());
            weatherDTO.setBatch(weatherCityFcLoadForecastDO.getBatchId());
            weatherDTO.setData(BasePeriodUtils
                .toList(weatherCityFcLoadForecastDO, Constants.LOAD_CURVE_POINT_NUM,
                    Constants.LOAD_CURVE_START_WITH_ZERO));
            list.add(weatherDTO);
        }
        return list;
    }

    @Override
    public List<WeatherFeatureDTO> findWeatherFeature(String cityId, Integer type, List<String> algorithmIds, Date date,
        List<WeatherFeatureDTO> weatherFeatureList, String caliberId) throws Exception {

        if (weatherFeatureList == null) {
            throw new BusinessException("T100", "气象特性列表为空");
        }
        for (String algorithmId : algorithmIds) {
            List<WeatherCityFcLoadForecastDO> weatherCityFcLoadForecastDOList = weatherCityFcLoadForecastDAO
                .findWeatherInfoFcLoadForecast(cityId, type, algorithmId, date, caliberId);
            if (!CollectionUtils.isEmpty(weatherCityFcLoadForecastDOList)) {
                WeatherFeatureDTO weatherFeatureDTO = new WeatherFeatureDTO("");
                for (WeatherCityFcLoadForecastDO weatherCityFcLoadForecastDO : weatherCityFcLoadForecastDOList) {
                    List<BigDecimal> weatherList = BasePeriodUtils
                        .toList(weatherCityFcLoadForecastDO, Constants.WEATHER_CURVE_POINT_NUM,
                            Constants.WEATHER_CURVE_START_WITH_ZERO);
                    Map<String, BigDecimal> maxMixAvg = BasePeriodUtils.getMaxMinAvg(weatherList, 4);


                    if (WeatherEnum.HUMIDITY.value().equals(weatherCityFcLoadForecastDO.getType())) {
                        weatherFeatureDTO.setAveHumidity(maxMixAvg.get("avg"));
                    } else if (WeatherEnum.TEMPERATURE.value().equals(weatherCityFcLoadForecastDO.getType())) {
                        weatherFeatureDTO.setHighestTemperature(maxMixAvg.get("max"));
                        weatherFeatureDTO.setLowestTemperature(maxMixAvg.get("min"));
                    } else if (WeatherEnum.RAINFALL.value().equals(weatherCityFcLoadForecastDO.getType())) {
                        List<BigDecimal> bigDecimals = BasePeriodUtils
                            .toList(weatherCityFcLoadForecastDO, Constants.WEATHER_CURVE_POINT_NUM_24,
                                Constants.WEATHER_CURVE_START_WITH_ZERO);
                        bigDecimals.set(FIRST_INDEX, BigDecimal.ZERO);
                        if (weatherCityFcLoadForecastDO.getT2400() != null) {
                            bigDecimals.add(weatherCityFcLoadForecastDO.getT2400());
                            weatherFeatureDTO.setRainfall(BigDecimalUtils.addAllValue(bigDecimals));
                        } else {
                            java.sql.Date srcDate = weatherCityFcLoadForecastDO.getDate();
                            java.sql.Date tomorrowDate = new java.sql.Date(
                                DateUtils.addDays(new Date(srcDate.getTime()), 1).getTime());
                            WeatherCityFcDO tomorrowData = weatherCityFcService
                                .findWeatherCityFcDO(weatherCityFcLoadForecastDO.getCityId(),
                                    WeatherEnum.RAINFALL.getType(), tomorrowDate);
                            BigDecimal T0000 = null;
                            if (tomorrowData != null) {
                                T0000 = tomorrowData.getT0000();
                            }
                            bigDecimals.add(T0000);
                            weatherFeatureDTO.setRainfall(BigDecimalUtils.addAllValue(bigDecimals));
                        }
                    }

                }
                weatherFeatureDTO.setWeatherName(
                    AlgorithmEnum.findById(weatherCityFcLoadForecastDOList.get(0).getAlgorithmId()).getDescription());
                weatherFeatureList.add(weatherFeatureDTO);
            }
        }

        return weatherFeatureList;
    }


    /**
     * 插入或者更新预测时气象数据信息
     */
    @Override
    public void insertOrUpdateWeatherFcInfo(String cityId, Date date, String systemAlgorithmId, String caliberId,
        Integer batchId, Timestamp createtime) {
        try {
            String weatherDataCityId = cityService.findWeatherCityId(cityId);
            //判断（预测目标日)和触发算法时刻，哪个时间靠前；（存在job手动给参数跑历史数据情况,这种情况没有使用的目标日的预测气象，跳过存储）
            //截止到算法执行时刻，判断使用的预测气象是否为4四小时内更新的；
            Date creatTime = DateUtil.getFormatDate(new Date(createtime.getTime()));
//            if (creatTime.after(date)) {
//                return;
//            }
            //正常每日预测为T+1，一次预测多天，如果T+1日有4小时以内更新的预测气象，判定这一次触发预测的多天都为正常；
            List<WeatherCityFcDO> stationFcBasicWgDOS;
            if (AlgorithmEnum.Day_96_FORECAST_EC.getId().equals(systemAlgorithmId) || AlgorithmEnum.Day_96_FORECAST_FC.getId().equals(systemAlgorithmId)) {
                //查询多地市
                String useCity = algorithmParamService.getListByAlgorithmId(AlgorithmEnum.DAY_MAX_LOAD_FORECAST.getId()).stream().filter(t -> t.getParamEn().equals("useCity")).collect(Collectors.toList()).get(0).getDefaultValue();

                stationFcBasicWgDOS = weatherCityFcAdapterService.findFcWeather(Arrays.asList(useCity.split(",")),systemAlgorithmId,WeatherEnum.TEMPERATURE.getType(),date,date);
            } else {
                stationFcBasicWgDOS = weatherCityFcService.findWeatherCityFcDOs(weatherDataCityId, date, date);
            }
            for (WeatherCityFcDO stationFcBasicWgDO : stationFcBasicWgDOS) {
                WeatherCityFcLoadForecastDO weatherCityFcLoadForecastDO = new WeatherCityFcLoadForecastDO(
                        cityId, systemAlgorithmId, stationFcBasicWgDO.getType(), date, caliberId);
                BasePeriodUtils.setAllFiled(weatherCityFcLoadForecastDO,
                        BasePeriodUtils.toMap(stationFcBasicWgDO, 96, Constants.LOAD_CURVE_START_WITH_ZERO));
                weatherCityFcLoadForecastDO.setBatchId(batchId);
                Timestamp dateTime = stationFcBasicWgDO.getUpdatetime() == null ? stationFcBasicWgDO.getCreatetime()
                        : stationFcBasicWgDO.getUpdatetime();
                //校验预测气象是否为4小时内更新的数据；
                boolean inTimeFrame = DateUtil.isInTimeFrame(creatTime, new Date(dateTime.getTime()), 4);
                //是否不缺失
                List<BigDecimal> bigDecimals = stationFcBasicWgDO.getloadList();
                boolean validList = isValidList(bigDecimals);
                //使用的预测气象为4小时内更新并且数据不缺失;记录为正常；
                weatherCityFcLoadForecastDO.setNormal(inTimeFrame && validList);
                weatherCityFcLoadForecastDO.setWeatherCityId(stationFcBasicWgDO.getCityId());
                weatherCityFcLoadForecastDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
                weatherCityFcLoadForecastDO.setWeatherCreateTime(dateTime);
                weatherCityFcLoadForecastDO.setWeatherDate(stationFcBasicWgDO.getDate());
                weatherCityFcLoadForecastService.doSaveOrUpdateFc(weatherCityFcLoadForecastDO);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static boolean isValidList(List<BigDecimal> bigDecimals) {
        if (bigDecimals == null || bigDecimals.size() != 96) {
            return false;
        }
        for (BigDecimal value : bigDecimals) {
            if (value == null) {
                return false;
            }
        }
        return true;
    }

    @Override
    public List<WeatherCityFcLoadForecastDO> findWeatherInfoFcLoadForecast(String cityId, Integer type,
        String algorithmId, Date date, String caliberId) throws Exception {
        return weatherCityFcLoadForecastDAO.findWeatherInfoFcLoadForecast(cityId, type, algorithmId, date, caliberId);
    }

    @Override
    public List<WeatherCityFcLoadForecastDO> findWeatherInfoFcLoadForecastByBatchId(String cityId, Integer type,
        String algorithmId, Date date, String caliberId, Integer batchId) throws Exception {
        return weatherCityFcLoadForecastDAO
            .findWeatherInfoFcLoadForecast(cityId, type, algorithmId, date, date, caliberId, batchId);
    }

    @Override
    public List<WeatherCityFcLoadForecastDO> findWeatherInfoFcLoadForecast(String cityId, Integer type,
        String algorithmId, Date startDate, Date endDate, String caliberId) throws Exception {
        return weatherCityFcLoadForecastDAO
            .findWeatherInfoFcLoadForecast(cityId, type, algorithmId, startDate, endDate, caliberId, null);
    }

    @Override
    public List<WeatherCityFcLoadForecastDO> findWeatherInfoFcLoadForecastBatch(String cityId, Integer type,
        List<String> algorithmIds, List<Integer> batchIds, Date startDate, Date endDate, String caliberId)
        throws Exception {
        return weatherCityFcLoadForecastDAO
            .findWeatherInfoFcLoadForecastBatch(cityId, type, algorithmIds, batchIds, startDate, endDate, caliberId);
    }

    @Override
    public void doCreateAndFlush(WeatherCityFcLoadForecastDO weatherCityFcLoadForecastDO) {
        weatherCityFcLoadForecastDAO.createAndFlush(weatherCityFcLoadForecastDO);
    }

    @Override
    public void doSaveOrUpdate(WeatherCityFcLoadForecastDO weatherCityFcLoadForecastDO) {
        weatherCityFcLoadForecastDAO.saveOrUpdateEntityByTemplate(weatherCityFcLoadForecastDO);
    }

    @Override
    public void doDelete(String id) throws Exception {
        weatherCityFcLoadForecastDAO.deleteByPk(id);
    }

    @Override
    public void insertOrUpdateWeatherHisInfo(String cityId, Date date, String systemAlgorithmId, String caliberId,
        Integer batchId, Timestamp createtime) {
        try {
            String weatherDataCityId = cityService.findWeatherCityId(cityId);
            //判断（预测目标日)和触发算法时刻，哪个时间靠前；（存在job手动给参数跑历史数据情况,这种情况使用的目标日气象；如果目标日没数据判定为不正常；）
            //截止到算法执行时刻，有没有当日的气象数据；当日预测多天时的情况，判定为气象使用正常；
            //ex1:t日预测t+1~t+7，t日有历史气象；判定为气象使用正常；
            //ex2:t日预测t+1~t+7，t日没有历史气象；使用的t-1 or t-n，判定为该批次预测结果气象使用不正常；
            Date creatate = new Date(createtime.getTime());
            Date srcDate;
            int maxDayNum = 10;
            if (creatate.before(date)) {
                srcDate = new Date(createtime.getTime());
            } else {
                srcDate = date;
            }
            List<String> cityIds = Arrays.asList(weatherDataCityId);
            List<WeatherCityHisDO> weatherCityHisDOS;
            //gru神经网络使用的地区地市气象数据；这里查询所有城市的保存
            if (AlgorithmEnum.GRU_FACTORS.getId().equals(systemAlgorithmId) || AlgorithmEnum.GRU_FACTORS_CHECK.getId()
                .equals(systemAlgorithmId)) {
                //查询九地市
                List<CityDO> cityDOS = cityService.findCitysByBelongId(Constants.PROVINCE_ID);
                cityIds = cityDOS.stream().map(x -> x.getId()).collect(Collectors.toList());
            } weatherCityHisDOS = weatherCityHisService
                .getWeatherCityHisDOs(cityIds, null,srcDate, srcDate);
            boolean flag = true;
            //如果目标预测日当天没有可用的历史气象，代表当天的历史气象入库延迟严重，标记为异常；
            if (CollectionUtils.isEmpty(weatherCityHisDOS)) {
                flag = false;
                for (int i = 1; i < maxDayNum; i++) {
                    srcDate = DateUtils.addDays(srcDate, -1);
                    List<WeatherCityHisDO> tarList = weatherCityHisService
                        .getWeatherCityHisDOs(cityIds, null, srcDate, srcDate);
                    if (!CollectionUtils.isEmpty(tarList)) {
                        weatherCityHisDOS = tarList;
                        break;
                    }
                }
            }
            if (CollectionUtils.isEmpty(weatherCityHisDOS)) {
                return;
            }
            List<WeatherCityHisDO> tarHisList = mergeHisAndFcWeather(weatherCityHisDOS);
            for (WeatherCityHisDO weatherCityHis : tarHisList) {
                WeatherCityHisLoadForecastDO weatherCityHisLoadForecastDO = new WeatherCityHisLoadForecastDO(
                    cityId, systemAlgorithmId, weatherCityHis.getType(), date, caliberId);
                BasePeriodUtils.setAllFiled(weatherCityHisLoadForecastDO,
                    BasePeriodUtils.toMap(weatherCityHis, 96, Constants.LOAD_CURVE_START_WITH_ZERO));
                weatherCityHisLoadForecastDO.setBatchId(batchId);
                Timestamp dateTime = weatherCityHis.getUpdatetime() == null ? weatherCityHis.getCreatetime()
                    : weatherCityHis.getUpdatetime();
                weatherCityHisLoadForecastDO.setWeatherCreateTime(dateTime);
                weatherCityHisLoadForecastDO.setWeatherCityId(weatherCityHis.getCityId());
                weatherCityHisLoadForecastDO.setNormal(flag);
                weatherCityHisLoadForecastDO.setDate(new java.sql.Date(date.getTime()));
                weatherCityHisLoadForecastDO.setWeatherDate(new java.sql.Date(weatherCityHis.getDate().getTime()));
                weatherCityFcLoadForecastService.doSaveOrUpdateHis(weatherCityHisLoadForecastDO);

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 如果目标日期的实际气象缺点，使用预测气象补点
     * @param weatherCityHisDOS
     * @return
     * @throws Exception
     */
    private List<WeatherCityHisDO> mergeHisAndFcWeather(List<WeatherCityHisDO> weatherCityHisDOS) throws Exception {
        List<WeatherCityHisDO> result = new ArrayList<>();
        WeatherCityHisDO hisDO = weatherCityHisDOS.get(0);
        List<String> collect = weatherCityHisDOS.stream().map(WeatherCityHisDO::getCityId)
            .collect(Collectors.toList());
        List<WeatherCityFcDO> weatherCityFcDO = this.weatherCityFcDAO.findWeatherCityHisDOs(collect, null, hisDO.getDate(), hisDO.getDate());
        Map<String, WeatherCityHisDO> lastDayWeatherMap = weatherCityHisDOS.stream()
            .collect(Collectors.toMap(src->src.getCityId()+"-"+src.getType(), Function
                .identity(), (key1, key2) -> key2));
        List<WeatherCityFcDO> lastFcWeather = weatherCityFcDO.stream().collect(Collectors.toList());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(lastFcWeather)) {
            Map<String, List<BigDecimal>> fcWeatherMap = lastFcWeather.stream()
                .collect(Collectors.toMap(src->src.getCityId()+"-"+src.getType(), WeatherCityFcDO::getloadList));
            //清理最后一天实际气象
            lastDayWeatherMap.forEach((type, data) -> {
                List<BigDecimal> value = data.getloadList();
                List<BigDecimal> fcWeatherList = fcWeatherMap.get(type);
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(fcWeatherList)) {
                    //使用预测气象替换最后一天实际气象的缺点
                    for (int l = 0; l < value.size(); l++) {
                        if (Objects.isNull(value.get(l))) {
                            value.set(l, fcWeatherList.get(l));
                        }
                    }
                }
                WeatherCityHisDO lastDayDo = new WeatherCityHisDO();
                lastDayDo.setType(data.getType());
                lastDayDo.setCityId(data.getCityId());
                lastDayDo.setCreatetime(data.getCreatetime());
                lastDayDo.setUpdatetime(data.getUpdatetime());
                lastDayDo.setDate(new java.sql.Date(hisDO.getDate().getTime()));
                try {
                    BasePeriodUtils
                        .setAllFiled(lastDayDo, ColumnUtil.listToMap(value, Constants.LOAD_CURVE_START_WITH_ZERO));
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                result.add(lastDayDo);
            });
        }
        return result;
    }

    @Override
    public void doSaveOrUpdateFc(WeatherCityFcLoadForecastDO weatherCityFcLoadForecastDO) {
        weatherCityFcLoadForecastDAO.saveOrUpdateEntityByTemplate(weatherCityFcLoadForecastDO);
    }

    @Override
    public void doSaveOrUpdateHis(WeatherCityHisLoadForecastDO weatherCityHisLoadForecastDO) {
        weatherCityHisLoadForecastDAO.saveOrUpdateEntityByTemplate(weatherCityHisLoadForecastDO);
    }

    @Override
    public void doSaveOrUpdateHisRecall(WeatherCityHisLoadForecastRecallDO weatherCityHisLoadForecastDO) {
        weatherCityHisLoadForecastRecallDAO.saveOrUpdateEntityByTemplate(weatherCityHisLoadForecastDO);
    }


    @Override
    public void insertOrUpdateWeatherHisInfoRecall(String cityId, Date date, String systemAlgorithmId, String caliberId) {
        try {
            String weatherDataCityId = cityService.findWeatherCityId(cityId);
            //判断预测目标日，有没有当日的气象数据；
            List<WeatherCityHisDO> weatherCityHisDOS = weatherCityHisService
                .findWeatherCityHisDOs(weatherDataCityId, null, date,  date);
            Date srcDate = date;
            int maxDayNum = 10;
            boolean flag = true;
            //如果目标预测日当天没有可用的历史气象，代表当天的历史气象入库延迟严重，标记为异常；
            if (CollectionUtils.isEmpty(weatherCityHisDOS)) {
                flag = false;
                for (int i = 1; i < maxDayNum; i++) {
                    date = DateUtils.addDays(date, -1);
                    List<WeatherCityHisDO> tarList = weatherCityHisService
                        .findWeatherCityHisDOs(weatherDataCityId, null, date, date);
                    if (!CollectionUtils.isEmpty(tarList)) {
                        weatherCityHisDOS = tarList;
                        break;
                    }
                }
            }
            for (WeatherCityHisDO weatherCityHis : weatherCityHisDOS) {
                WeatherCityHisLoadForecastRecallDO weatherCityHisLoadForecastDO = new WeatherCityHisLoadForecastRecallDO(
                    weatherDataCityId, systemAlgorithmId, weatherCityHis.getType(), date, caliberId);
                BasePeriodUtils.setAllFiled(weatherCityHisLoadForecastDO,
                    BasePeriodUtils.toMap(weatherCityHis, 96, Constants.LOAD_CURVE_START_WITH_ZERO));
                Timestamp dateTime = weatherCityHis.getUpdatetime() == null ? weatherCityHis.getCreatetime()
                    : weatherCityHis.getUpdatetime();
                weatherCityHisLoadForecastDO.setWeatherCreateTime(dateTime);
                weatherCityHisLoadForecastDO.setNormal(flag);
                weatherCityHisLoadForecastDO.setCityId(cityId);
                weatherCityHisLoadForecastDO.setWeatherCityId(weatherDataCityId);
                weatherCityHisLoadForecastDO.setDate(new java.sql.Date(srcDate.getTime()));
                weatherCityHisLoadForecastDO.setWeatherDate(new java.sql.Date(date.getTime()));
                weatherCityFcLoadForecastService.doSaveOrUpdateHisRecall(weatherCityHisLoadForecastDO);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Override
    public List<WeatherCityFcLoadForecastDO> findWeatherFcLoadForecastBatch(String cityId, String algorithmId,
        Date date, String caliberId, String batchId) throws Exception {
        List<WeatherCityFcLoadForecastDO> tarList = new ArrayList<>();
        List<WeatherCityFcLoadForecastDO> resultList = new ArrayList<>();
        SettingBatchInitDO batchById = settingBatchInitService.getBatchById(batchId);
        List<WeatherCityFcLoadForecastDO> weather = weatherCityFcLoadForecastDAO
            .findWeatherInfoFcLoadForecast(cityId, null, algorithmId, date, caliberId);
        for (WeatherCityFcLoadForecastDO one : weather) {
            if (DateUtil
                .isWithinTimeRange(one.getCreatetime(), batchById.getStartTime(), batchById.getEndTime())) {
                resultList.add(one);
            }
        }
        Map<Integer, List<WeatherCityFcLoadForecastDO>> map = resultList.stream().collect(Collectors.groupingBy(
            WeatherCityFcLoadForecastDO::getType, TreeMap::new, Collectors.toList())
        );
        //获取批次时间段内最新的一条数据
        map.forEach((key, list)->{
            list.sort(Comparator.comparing(WeatherCityFcLoadForecastDO::getCreatetime).reversed());
            tarList.add(list.get(0));
        });
        return tarList;
    }

    @Override
    public List<WeatherCityHisLoadForecastDO> findWeatherHisLoadForecastBatch(String cityId, String algorithmId,
        Date date, String caliberId, String batchId) throws Exception {
        List<WeatherCityHisLoadForecastDO> tarList = new ArrayList<>();
        List<WeatherCityHisLoadForecastDO> resultList = new ArrayList<>();
        SettingBatchInitDO batchById = settingBatchInitService.getBatchById(batchId);
        List<WeatherCityHisLoadForecastDO> weather = weatherCityHisLoadForecastDAO
            .findWeatherInfoFcLoadForecast(cityId, null, algorithmId, date, caliberId);
        for (WeatherCityHisLoadForecastDO one : weather) {
            if (DateUtil
                .isWithinTimeRange(one.getCreatetime(), batchById.getStartTime(), batchById.getEndTime())) {
                resultList.add(one);
            }
        }
        Map<Integer, List<WeatherCityHisLoadForecastDO>> map = resultList.stream().collect(Collectors.groupingBy(
            WeatherCityHisLoadForecastDO::getType, TreeMap::new, Collectors.toList())
        );
        //获取批次时间段内最新的一条数据
        map.forEach((key, list)->{
            list.sort(Comparator.comparing(WeatherCityHisLoadForecastDO::getCreatetime).reversed());
            tarList.add(list.get(0));
        });
        return tarList;
    }

    @Override
    public List<WeatherCityFcLoadForecastDO> findWeatherFcLoadForecastBatchByType(String cityId, String algorithmId,
        String caliberId, String batchId, Integer type, Date startDate, Date endDate) throws Exception {
        List<WeatherCityFcLoadForecastDO> tarList = new ArrayList<>();
        List<WeatherCityFcLoadForecastDO> resultList = new ArrayList<>();
        SettingBatchInitDO batchById = settingBatchInitService.getBatchById(batchId);
        List<WeatherCityFcLoadForecastDO> weather = weatherCityFcLoadForecastDAO
            .findWeatherInfoFcLoadForecastBatch(cityId, type, Arrays.asList(algorithmId), null,startDate, endDate, caliberId);
        for (WeatherCityFcLoadForecastDO one : weather) {
            if (DateUtil
                .isWithinTimeRange(one.getCreatetime(), batchById.getStartTime(), batchById.getEndTime())) {
                resultList.add(one);
            }
        }
        Map<String, List<WeatherCityFcLoadForecastDO>> map = resultList.stream().collect(Collectors.groupingBy(
            src->src.getDate()+"-"+src.getWeatherCityId(), TreeMap::new, Collectors.toList())
        );
        //获取批次时间段内最新的一条数据
        map.forEach((key, list)->{
            list.sort(Comparator.comparing(WeatherCityFcLoadForecastDO::getCreatetime).reversed());
            tarList.add(list.get(0));
        });
        return tarList;
    }

}
