/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.load.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.LoadAcHisBasicDO;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

/**
 * Description:<br>
 *
 * @Author:<EMAIL>
 * @Date: 2022/5/23 18:47
 * @Version:1.0.0
 */
public class LoadAcHisBasicDAO extends BaseAbstractDAO<LoadAcHisBasicDO> {

    public List<LoadAcHisBasicDO> getLoadFeatureCityDayHisDO(String cityId, Date startDate, Date endDate,
        String caliberId) {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (StringUtils.isNotBlank(cityId)) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        param.setOrderby("date");
        param.setDesc("0");
        return this.query(param).getDatas();
    }
}