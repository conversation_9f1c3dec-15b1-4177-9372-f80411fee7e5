
package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityMonthHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityMonthHisDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherFeatureCityMonthHisDAO;
import java.io.Serializable;
import java.util.List;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version $Id: WeatherFeatureCityMonthHisServiceImpl.java, v 0.1 2018-04-04 09:53:59 tao Exp $$
 */
@Service("weatherFeatureCityMonthHisService")
public class WeatherFeatureCityMonthHisServiceImpl extends BaseServiceImpl implements
    WeatherFeatureCityMonthHisService {

    private static final Logger logger = LogManager.getLogger(WeatherFeatureCityMonthHisServiceImpl.class);

    @Autowired
    WeatherFeatureCityMonthHisDAO weatherFeatureCityMonthHisDAO;

    @Autowired
    private CityService cityService;

    @Override
    public DataPackage queryWeatherFeatureCityMonthHisDO(DBQueryParam param) throws BusinessException {
        try {
            return weatherFeatureCityMonthHisDAO.query(param);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public WeatherFeatureCityMonthHisDO doCreate(WeatherFeatureCityMonthHisDO vo) throws BusinessException {
        try {
            return (WeatherFeatureCityMonthHisDO) weatherFeatureCityMonthHisDAO.create(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public void doRemoveWeatherFeatureCityMonthHisDO(WeatherFeatureCityMonthHisDO vo) throws BusinessException {
        try {
            weatherFeatureCityMonthHisDAO.remove(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public void doRemoveWeatherFeatureCityMonthHisDOByPK(Serializable pk) throws BusinessException {
        try {
            weatherFeatureCityMonthHisDAO.removeByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public WeatherFeatureCityMonthHisDO doUpdateWeatherFeatureCityMonthHisDO(WeatherFeatureCityMonthHisDO vo)
        throws BusinessException {
        try {
            return (WeatherFeatureCityMonthHisDO) weatherFeatureCityMonthHisDAO.update(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public WeatherFeatureCityMonthHisDO findWeatherFeatureCityMonthHisDOByPk(Serializable pk) throws BusinessException {
        try {
            return (WeatherFeatureCityMonthHisDO) weatherFeatureCityMonthHisDAO.findByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public WeatherFeatureCityMonthHisDO getWeatherFeatureCityMonthHisDO(String cityId, String year, String month)
        throws Exception {
        return weatherFeatureCityMonthHisDAO.getWeatherFeatureCityMonthHisDO(cityId, year, month);
    }

    @Override
    public List<WeatherFeatureCityMonthHisDO>getWeatherFeatureCityYearHisDO(String cityId, String year) throws Exception {
        cityId = cityService.findWeatherCityId(cityId);
        return weatherFeatureCityMonthHisDAO.getWeatherFeatureCityYearHisDO(cityId, year);
    }

    @Override
    public List<WeatherFeatureCityMonthHisDO> findWeatherFeatureCityMonthHisStat(String cityId, String startYM,
                                                                                 String endYM) throws Exception {
        return weatherFeatureCityMonthHisDAO.getLoadFeatureCityMonthHisDOs(cityId, startYM, endYM);
    }

}
