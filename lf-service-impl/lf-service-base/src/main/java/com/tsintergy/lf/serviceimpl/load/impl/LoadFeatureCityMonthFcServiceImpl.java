/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  wangchen Date:  2020/4/19 17:04 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.load.impl;

import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityMonthFcService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityMonthFcDO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadFeatureCityMonthFcDAO;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/4/19
 * @since 1.0.0
 */
@Service(value = "loadFeatureCityMonthFcService")
public class LoadFeatureCityMonthFcServiceImpl extends BaseServiceImpl implements LoadFeatureCityMonthFcService {

    private static final Logger logger = LoggerFactory.getLogger(LoadFeatureCityMonthFcServiceImpl.class);

    @Autowired
    private LoadFeatureCityMonthFcDAO loadFeatureCityMonthFcDAO;

    @Override
    public void doSaveOrUpdateLoadFeatureCityMonthFcDOS(List<LoadFeatureCityMonthFcDO> monthFcVOS) {
        for (LoadFeatureCityMonthFcDO monthVO : monthFcVOS) {
            try {
                saveOrUpdate(monthVO);
            } catch (Exception e) {
                logger.error("保存预测-周-负荷特性出错了", e);
            }
        }
    }

    @Override
    public List<LoadFeatureCityMonthFcDO> findLoadFeatureMonth(String cityId, String algorithmId,
        String startYm,
        String endYm, String caliberId) throws Exception {
        List<LoadFeatureCityMonthFcDO> reportAccuracyMonthVOS = new ArrayList<>();
        String startYear = startYm.substring(0, 4);
        String endYear = endYm.substring(0, 4);
        List<LoadFeatureCityMonthFcDO> monthVOList = loadFeatureCityMonthFcDAO
            .findByYear(cityId, algorithmId, startYear, endYear, caliberId);
        for (LoadFeatureCityMonthFcDO monthVO : monthVOList) {
            if ((monthVO.getYear() + "-" + monthVO.getMonth()).compareTo(startYm) > -1
                && (monthVO.getYear() + "-" + monthVO.getMonth()).compareTo(endYm) < 1) {
                reportAccuracyMonthVOS.add(monthVO);
            }
        }
        return reportAccuracyMonthVOS;
    }

    private void saveOrUpdate(LoadFeatureCityMonthFcDO fcVO) throws Exception {
        if (fcVO == null) {
            return;
        }
        List<LoadFeatureCityMonthFcDO> oldVoList = loadFeatureCityMonthFcDAO
            .find(fcVO.getCityId(), fcVO.getAlgorithmId(), fcVO.getYear(), fcVO.getMonth(), fcVO.getCaliberId());
        if (CollectionUtils.isEmpty(oldVoList)) {
            this.loadFeatureCityMonthFcDAO
                .createAndFlush(fcVO);
            return;
        }
        LoadFeatureCityMonthFcDO oldVO = oldVoList.get(0);
        String id = oldVO.getId();
        BeanUtils.copyProperties(fcVO, oldVO);
        oldVO.setId(id);
        loadFeatureCityMonthFcDAO.updateAndFlush(oldVO);
    }

    @Override
    public List<LoadFeatureCityMonthFcDO> statisticsMonthFeatures(List<LoadFeatureCityDayFcDO> FcVOS)
        throws Exception {
        List<LoadFeatureCityMonthFcDO> monthFcList = new ArrayList<>();
        if (FcVOS != null) {
            Map<String, List<LoadFeatureCityDayFcDO>> loadFeatureMap = new HashMap<>(16);
            for (LoadFeatureCityDayFcDO loadFeatureCityDayHisVO : FcVOS) {
                String key = loadFeatureCityDayHisVO.getCityId() + "_" + loadFeatureCityDayHisVO.getAlgorithmId() + "_"
                    + loadFeatureCityDayHisVO.getCaliberId() + "_" + DateUtil
                    .getMonthByDate(loadFeatureCityDayHisVO.getDate());
                if (!loadFeatureMap.containsKey(key)) {
                    loadFeatureMap.put(key, new ArrayList<>());
                }
                loadFeatureMap.get(key).add(loadFeatureCityDayHisVO);
            }

            for (String key : loadFeatureMap.keySet()) {
                try {
                    LoadFeatureCityMonthFcDO loadFeatureCityMonthHisVO = statisticsMonthFeature(
                        loadFeatureMap.get(key));
                    monthFcList.add(loadFeatureCityMonthHisVO);
                } catch (Exception e) {
                    logger.error("统计月负荷特性出错了", e);
                }
            }

        }
        return monthFcList;
    }

    /**
     * 统计月负荷特性
     *
     * @param voList 一个月的日负荷特性
     */
    public LoadFeatureCityMonthFcDO statisticsMonthFeature(List<LoadFeatureCityDayFcDO> voList) throws Exception {
        if (voList.size() > 31) {
            logger.error("统计月负荷特性有误：日负荷特性数据的超过31天，无法统计");
            return null;
        }
        if (voList != null && voList.size() > 0) {
            String cityId = voList.get(0).getCityId();
            String caliberId = voList.get(0).getCaliberId();
            String algorithmId = voList.get(0).getAlgorithmId();
            String ym = DateUtil.getMonthByDate(voList.get(0).getDate()); // 年月
            BigDecimal maxLoad = null; // 最大负荷
            BigDecimal minLoad = null; // 最小负荷
            String maxTime = null;
            String minTime = null;
            Date maxDate = null;
            List<BigDecimal> avgLoads = new ArrayList<BigDecimal>();
            List<BigDecimal> peaks = new ArrayList<BigDecimal>();
            List<BigDecimal> troughs = new ArrayList<BigDecimal>();
            List<BigDecimal> maxLoads = new ArrayList<BigDecimal>();
            BigDecimal energy = new BigDecimal(0).setScale(4);
            for (LoadFeatureCityDayFcDO dayVO : voList) {
                if (!ym.equals(DateUtil.getMonthByDate(dayVO.getDate()))) {
                    logger.error("统计月负荷特性有误：日负荷特性数据的日期不是同一个月，无法统计");
                    return null;
                }
                if (maxLoad == null || maxLoad.compareTo(dayVO.getMaxLoad()) < 0) {
                    maxLoad = dayVO.getMaxLoad();
                    maxTime = dayVO.getMaxTime();
                    maxDate = dayVO.getDate();
                }

                if (minLoad == null || minLoad.compareTo(dayVO.getMinLoad()) > 0) {
                    minLoad = dayVO.getMinLoad();
                    minTime = dayVO.getMinTime();
                }

                avgLoads.add(dayVO.getAveLoad());
                peaks.add(dayVO.getPeak());
                troughs.add(dayVO.getTrough());
                maxLoads.add(dayVO.getMaxLoad());
                energy = BigDecimalUtils.add(energy, dayVO.getEnergy());

            }

            LoadFeatureCityMonthFcDO loadFeatureCityMonthHisVO = new LoadFeatureCityMonthFcDO();
            loadFeatureCityMonthHisVO.setCityId(cityId);
            loadFeatureCityMonthHisVO.setCaliberId(caliberId);
            loadFeatureCityMonthHisVO.setYear(ym.substring(0, 4));
            loadFeatureCityMonthHisVO.setMonth(ym.substring(5, 7));
            loadFeatureCityMonthHisVO.setMaxDate(maxDate);
            loadFeatureCityMonthHisVO.setMaxTime(maxTime);
            loadFeatureCityMonthHisVO.setMinTime(minTime);
            loadFeatureCityMonthHisVO.setAlgorithmId(algorithmId);
            loadFeatureCityMonthHisVO.setMaxLoad(maxLoad);
            loadFeatureCityMonthHisVO.setMinLoad(minLoad);
            loadFeatureCityMonthHisVO.setAveLoad(BigDecimalUtils.avgList(avgLoads, 4, false));
            loadFeatureCityMonthHisVO.setDifferent(
                BigDecimalUtils.sub(loadFeatureCityMonthHisVO.getMaxLoad(), loadFeatureCityMonthHisVO.getMinLoad()));
            loadFeatureCityMonthHisVO.setGradient(BigDecimalUtils
                .divide(loadFeatureCityMonthHisVO.getDifferent(), loadFeatureCityMonthHisVO.getMaxLoad(), 4));
            loadFeatureCityMonthHisVO.setLoadGradient(BigDecimalUtils
                .divide(loadFeatureCityMonthHisVO.getAveLoad(), loadFeatureCityMonthHisVO.getMaxLoad(), 4));
            loadFeatureCityMonthHisVO.setPeak(BigDecimalUtils.avgList(peaks, 4, true));
            loadFeatureCityMonthHisVO.setTrough(BigDecimalUtils.avgList(troughs, 4, false));
            loadFeatureCityMonthHisVO.setEnergy(energy);
            // 日不均衡系数 = average(月内各日最大负荷)/月最大负荷
            loadFeatureCityMonthHisVO.setDayUnbalance(BigDecimalUtils.avgList(maxLoads, 4, false));

            return loadFeatureCityMonthHisVO;

        }

        return null;

    }


}
