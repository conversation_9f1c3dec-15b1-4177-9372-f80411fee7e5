package com.tsintergy.lf.serviceimpl.evalucation.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyAssessDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/12 14:18
 */
@Component
public class AccuracyAssessDAO extends BaseAbstractDAO<AccuracyAssessDO> {


    public List<AccuracyAssessDO> selectList(String cityId, String caliberId, String algorithmId, Date startDate,
        Date endDate,String batchId) {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (null != cityId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "cityId", cityId);
        }
        if (null != caliberId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (null != algorithmId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "algorithmId", algorithmId);
        }
        if (null != batchId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "batchId", batchId);
        }
        dbQueryParamBuilder.where(QueryOp.DateNoLessThan, "date", new java.sql.Date(startDate.getTime()));
        dbQueryParamBuilder.where(QueryOp.DateNoMoreThan, "date", new java.sql.Date(endDate.getTime()));
        List<AccuracyAssessDO> datas = this.query(dbQueryParamBuilder.build()).getDatas();
        return datas;
    }

    public List<AccuracyAssessDO> selectListByName(String cityId, String caliberId, String accuracyName, Date startDate,
        Date endDate) {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (null != cityId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "cityId", cityId);
        }
        if (null != caliberId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (null != accuracyName) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "assessName", accuracyName);
        }
        dbQueryParamBuilder.where(QueryOp.DateNoLessThan, "date", new java.sql.Date(startDate.getTime()));
        dbQueryParamBuilder.where(QueryOp.DateNoMoreThan, "date", new java.sql.Date(endDate.getTime()));
        dbQueryParamBuilder.addOrderByAsc("createtime");
        List<AccuracyAssessDO> datas = this.query(dbQueryParamBuilder.build()).getDatas();
        return datas;
    }

    public List<AccuracyAssessDO> selectListByAlgorithmIds(String cityId, String caliberId, List<String> algorithmIds, Date startDate, Date endDate) {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (null != cityId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "cityId", cityId);
        }
        if (null != caliberId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (CollectionUtils.isNotEmpty(algorithmIds)) {
            dbQueryParamBuilder.where(QueryOp.StringIsIn, "algorithmId", algorithmIds);
        }
        dbQueryParamBuilder.where(QueryOp.DateNoLessThan, "date", new java.sql.Date(startDate.getTime()));
        dbQueryParamBuilder.where(QueryOp.DateNoMoreThan, "date", new java.sql.Date(endDate.getTime()));
        dbQueryParamBuilder.addOrderByAsc("createtime");
        List<AccuracyAssessDO> datas = this.query(dbQueryParamBuilder.build()).getDatas();
        return datas;
    }

    public List<AccuracyAssessDO> selectList(String cityId, String caliberId,String accuracyName, List<String> algorithmIds, Date startDate,
                                             Date endDate) {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (null != cityId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "cityId", cityId);
        }
        if (null != caliberId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (CollectionUtils.isNotEmpty(algorithmIds)) {
            dbQueryParamBuilder.where(QueryOp.StringIsIn, "algorithmId", algorithmIds);
        }
        if (null != accuracyName) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "assessName", accuracyName);
        }
        dbQueryParamBuilder.where(QueryOp.DateNoLessThan, "date", new java.sql.Date(startDate.getTime()));
        dbQueryParamBuilder.where(QueryOp.DateNoMoreThan, "date", new java.sql.Date(endDate.getTime()));
        List<AccuracyAssessDO> datas = this.query(dbQueryParamBuilder.build()).getDatas();
        return datas;
    }
}
