package com.tsintergy.lf.serviceimpl.area.impl;

import com.tsintergy.lf.serviceapi.base.area.api.BaseAreaService;
import com.tsintergy.lf.serviceapi.base.area.pojo.BaseAreaDO;
import com.tsintergy.lf.serviceimpl.area.dao.BaseAreaDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/** @Description
 * <AUTHOR>
 * @Date 2025/8/27 10:10
 **/
@Service("baseAreaService")
public class BaseAreaServiceImpl implements BaseAreaService {

    @Autowired
    private BaseAreaDAO baseAreaDAO;
    @Override
    public List<BaseAreaDO> getAllAreas() {
        return baseAreaDAO.findAll();
    }

    @Override
    public List<String> getCityByArea(String areaId) {
        Optional<BaseAreaDO> baseAreaDO = baseAreaDAO.findById(areaId);
        return baseAreaDO.map(areaDO -> Arrays.asList(areaDO.getCityIds().split(","))).orElse(Collections.emptyList());
    }
}
