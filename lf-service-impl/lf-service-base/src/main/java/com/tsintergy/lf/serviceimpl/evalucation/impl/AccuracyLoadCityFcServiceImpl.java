
package com.tsintergy.lf.serviceimpl.evalucation.impl;

import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsintergy.lf.serviceapi.base.evalucation.api.AccuracyLoadCityFcService;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyLoadCityFcDO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.AccuracyLoadCityFcDAO;
import java.util.Date;
import java.util.List;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @version $Id: AccuracyLoadCityFcServiceImpl.java, v 0.1 2018-01-31 10:15:17 tao Exp $$
 */
@Service("accuracyLoadCityFcService")
public class AccuracyLoadCityFcServiceImpl extends BaseServiceImpl implements AccuracyLoadCityFcService {
    private static final Logger logger = LogManager.getLogger( AccuracyLoadCityFcServiceImpl.class);

    @Autowired
    private AccuracyLoadCityFcDAO accuracyLoadCityFcDAO;

    @Override
    public DataPackage queryAccuracyLoadCityFcDO(DBQueryParam param) throws Exception{
        try {
            return accuracyLoadCityFcDAO.query(param);
        }
        catch (BusinessException e) {
            throw e;
        }
        catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }
    
    @Override
    public AccuracyLoadCityFcDO doCreate(AccuracyLoadCityFcDO vo) throws Exception{
        try {
            return accuracyLoadCityFcDAO.create(vo);
        }
        catch (BusinessException e) {
            throw e;
        }
        catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }
    
    @Override
    public void doRemoveAccuracyLoadCityFcDO(AccuracyLoadCityFcDO vo) throws Exception{
         try {
             accuracyLoadCityFcDAO.remove(vo);
        }
        catch (BusinessException e) {
            throw e;
        }
        catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }
    
    @Override
    public void doRemoveAccuracyLoadCityFcDOByPK(Serializable pk) throws Exception{
        try {
            accuracyLoadCityFcDAO.removeByPk(pk);
        }
        catch (BusinessException e) {
            throw e;
        }
        catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public AccuracyLoadCityFcDO doUpdateAccuracyLoadCityFcDO(AccuracyLoadCityFcDO vo) throws Exception{
         try {
             return accuracyLoadCityFcDAO.update(vo);
        }
        catch (BusinessException e) {
            throw e;
        }
        catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public AccuracyLoadCityFcDO findAccuracyLoadCityFcDOByPk(Serializable pk) throws Exception{
         try {
             return accuracyLoadCityFcDAO.findByPk(pk);
        }
        catch (BusinessException e) {
            throw e;
        }
        catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public List<AccuracyLoadCityFcDO> getAccuracyLoadCityFcDOList(String cityId, String caliberId, String algorithmId, Date startDate, Date endDate, Boolean isReport) throws Exception {
        return accuracyLoadCityFcDAO.getAccuracyLoadCityFcDOs(cityId,caliberId,algorithmId,startDate,endDate,null);
    }

    @Override
    public List<AccuracyLoadCityFcDO> getAccuracyLoadCityFcDOList(String cityId, String caliberId, Date date, String algorithmId) throws Exception {
        return accuracyLoadCityFcDAO.getAccuracyLoadCityFcDO(cityId, caliberId, algorithmId, date);
    }

    @Override
    public List<AccuracyLoadCityFcDO> getAccuracyLoadCityFcDOList(List<String> cityIds,String algorithmId,Date startDate,Date endDate,String caliberId, Boolean isReport) throws Exception {
        return accuracyLoadCityFcDAO.getAccuracyLoadCityFcDOs(cityIds,caliberId,algorithmId,startDate,endDate,isReport);
    }

}
