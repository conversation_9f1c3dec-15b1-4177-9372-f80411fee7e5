
package com.tsintergy.lf.serviceimpl.weather.impl;


import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.weather.annotation.FcWeatherFeatureDataSource;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayFcService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.BaseWeatherFeatureCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayFcDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherFeatureCityDayFcDAO;
import java.util.Date;
import java.util.List;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @version $Id: WeatherFeatureCityDayFcServiceImpl.java, v 0.1 2018-01-31 11:00:24 tao Exp $$
 */
@Service("weatherFeatureCityDayFcService")
@FcWeatherFeatureDataSource(source = "")
public class WeatherFeatureCityDayFcServiceImpl extends BaseServiceImpl implements WeatherFeatureCityDayFcService {

    private static final Logger logger = LogManager.getLogger(WeatherFeatureCityDayFcServiceImpl.class);

    @Autowired
    WeatherFeatureCityDayFcDAO weatherFeatureCityDayFcDAO;

    @Autowired
    private CityService cityService;



    @Override
    public WeatherFeatureCityDayFcDO doCreate(WeatherFeatureCityDayFcDO vo) throws Exception {
        try {
            return  weatherFeatureCityDayFcDAO
                    .createAndFlush(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("",e.getMessage(), e);
        }
    }


    @Override
    public WeatherFeatureCityDayFcDO findWeatherFeatureCityFcVOByDate(String cityId, Date date) throws Exception {
        List<WeatherFeatureCityDayFcDO> weatherFeatureCityDayFcVOS = weatherFeatureCityDayFcDAO
                .findWeatherFeatureCityFcVOs(cityId, date);
        if (weatherFeatureCityDayFcVOS.size() < 1) {
            return null;
        }
        return weatherFeatureCityDayFcVOS.get(0);
    }



    @Override
    public DataPackage listWeatherFeatureCityDayByPage(String cityId, Date startDate, Date endDate) throws Exception {
        DBQueryParamBuilder builder = DBQueryParamBuilder.create();
        if (!StringUtils.isEmpty(cityId)) {
            builder.where(QueryOp.StringEqualTo, "cityId", cityId);
        }
        if (startDate != null) {
            builder.where(QueryOp.DateNoLessThan, "date", new java.sql.Date(startDate.getTime()));
        }
        if (endDate != null) {
            builder.where(QueryOp.DateNoMoreThan, "date", new java.sql.Date(endDate.getTime()));
        }
        builder.addOrderByDesc("date");

        return weatherFeatureCityDayFcDAO
                .query(builder.build());
    }


    @Override
    public WeatherFeatureCityDayFcDO weatherFeatureCityDayFcVO(Date date, String cityId) throws Exception {
        cityId = cityService.findWeatherCityId(cityId);
        DBQueryParamBuilder builder = DBQueryParamBuilder.create().queryDataOnly()
                .where(QueryOp.StringEqualTo, "cityId", cityId)
                .where(QueryOp.DateEqualTo, "date", new java.sql.Date(date.getTime()));
        List<WeatherFeatureCityDayFcDO> weatherFeatureCityDayFcVOS = weatherFeatureCityDayFcDAO.query(builder.build()).getDatas();
        if (!CollectionUtils.isEmpty(weatherFeatureCityDayFcVOS)) {
            return weatherFeatureCityDayFcVOS.get(0);
        }
        return null;
    }

    @Override
    public WeatherFeatureCityDayFcDO findWeatherFeatureCityDayFcVO(Date date, String cityId) throws Exception {
        cityId = cityService.findWeatherCityId(cityId);
        DBQueryParamBuilder builder = DBQueryParamBuilder.create().queryDataOnly()
            .where(QueryOp.StringEqualTo, "cityId", cityId)
            .where(QueryOp.DateEqualTo, "date", new java.sql.Date(date.getTime()));
        List<WeatherFeatureCityDayFcDO> weatherFeatureCityDayFcVOS = weatherFeatureCityDayFcDAO.query(builder.build()).getDatas();
        if (!CollectionUtils.isEmpty(weatherFeatureCityDayFcVOS)) {
            return weatherFeatureCityDayFcVOS.get(0);
        }
        return null;
    }

    @Override
    public void doSaveOrUpdateWeatherFeatureCityDayFcDOs(List<WeatherFeatureCityDayFcDO> weatherFeatureCityDayFcDOS) {
        try {
            weatherFeatureCityDayFcDAO.doSaveOrUpdateWeatherFeatureCityDayFcDOs(weatherFeatureCityDayFcDOS);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<WeatherFeatureCityDayFcDO> findByDate(Date date) {
        DBQueryParamBuilder builder = DBQueryParamBuilder.create().queryDataOnly()
            .where(QueryOp.DateEqualTo, "date", new java.sql.Date(date.getTime()));
        List<WeatherFeatureCityDayFcDO> weatherFeatureCityDayFcVOS = weatherFeatureCityDayFcDAO.query(builder.build()).getDatas();
        return weatherFeatureCityDayFcVOS;
    }

    @Override
    public List<? extends BaseWeatherFeatureCityDayFcDO> findFcWeatherFeatureData(String cityId, Date startDate,
        Date endDate) throws Exception {
        return null;
    }

    @Override
    public void saveOrUpdateWeatherFeature(BaseWeatherFeatureCityDayFcDO baseWeatherFeatureCityDayFcDO)
        throws Exception {
    }

    @Override
    public List<WeatherFeatureCityDayFcDO> getWeatherFeatureCityDayFcDOList(String cityId, Date startDate, Date endDate) throws Exception {
        return weatherFeatureCityDayFcDAO.getWeatherFeatureCityDayFcDOs(cityId,startDate,endDate);
    }

}
