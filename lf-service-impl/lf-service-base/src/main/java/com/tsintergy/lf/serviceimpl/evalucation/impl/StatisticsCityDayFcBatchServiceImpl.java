
package com.tsintergy.lf.serviceimpl.evalucation.impl;

import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsCityDayFcBatchService;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.StatisticsDayBatchDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcBatchDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingBatchInitService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingBatchInitDO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.StatisticsCityDayFcBatchDAO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $Id: StatisticsCityDayFcServiceImpl.java, v 0.1 2018-01-31 10:20:10 tao Exp $$
 */

@Service("statisticsCityDayFcBatchServiceImpl")
public class StatisticsCityDayFcBatchServiceImpl extends BaseServiceImpl implements StatisticsCityDayFcBatchService {

    @Autowired
    private StatisticsCityDayFcBatchDAO statisticsCityDayFcDAO;

    @Autowired
    private SettingBatchInitService settingBatchInitService;

    @Override
    public List<StatisticsDayBatchDTO> getDayAccuracy(String cityId, String caliberId, List<String> algorithmIds, List<Integer> batchIds, Date startDate,
        Date endDate) throws Exception {
        List<StatisticsDayBatchDTO> statisticsDayDTOS = new ArrayList<>();
        List<StatisticsCityDayFcBatchDO> algorithmList = statisticsCityDayFcDAO
            .getStatisticsCityDayFcBatchDOs(cityId, caliberId, algorithmIds,batchIds, startDate, endDate);

        for (StatisticsCityDayFcBatchDO data : algorithmList) {
            StatisticsDayBatchDTO result = new StatisticsDayBatchDTO();
            result.setDate(data.getDate());
            result.setBatch(String.valueOf(data.getBatchId()));
            AlgorithmEnum byId = AlgorithmEnum.findById(data.getAlgorithmId());
            result.setAlgorithm(byId ==null? null: byId.getDescription());
            result.setAlgorithmId(data.getAlgorithmId());
            result.setAccuracy(data.getAccuracy());
            result.setAlgoForeTime(data.getAlgoForeTime());
            statisticsDayDTOS.add(result);
        }

        // 结果排序
        List<StatisticsDayBatchDTO> sortedResult = statisticsDayDTOS.stream()
            .sorted(Comparator.comparing(StatisticsDayBatchDTO::getDate)
                .thenComparing((one, two) -> {
                    Integer oneInt = Integer.parseInt(one.getBatch());
                    Integer twoInt = Integer.parseInt(two.getBatch());
                    return twoInt.compareTo(oneInt);
                })).collect(Collectors.toList());
        return sortedResult;
    }

    @Override
    public List<StatisticsCityDayFcBatchDO> doSaveOrUpdateStatisticsCityDayFcDOs(
        List<StatisticsCityDayFcBatchDO> statisticsCityDayFcVOS) throws Exception {
        return statisticsCityDayFcDAO.doSaveOrUpdateStatisticsCityDayFcBatchDOs(statisticsCityDayFcVOS);
    }


    @Override
    public List<StatisticsCityDayFcBatchDO> getDayAccuracyInBatch(String cityId, String caliberId, String algorithmId,
                                                                  String batchId, Date startDate, Date endDate) throws Exception {
        List<String> algorithmIds = StringUtils.isNotBlank(algorithmId) ? Collections.singletonList(algorithmId) : Collections.emptyList();
        List<StatisticsCityDayFcBatchDO> srcList = statisticsCityDayFcDAO
                .getStatisticsCityDayFcBatchDOs(cityId, caliberId, algorithmIds, null, startDate, endDate);

        List<StatisticsCityDayFcBatchDO> tarList = new ArrayList<>();
        SettingBatchInitDO batchById = settingBatchInitService.getBatchById(batchId);
        List<StatisticsCityDayFcBatchDO> collect = srcList.stream().filter(one -> DateUtil
            .isWithinTimeRange(one.getCreatetime(), batchById.getStartTime(), batchById.getEndTime()))
            .collect(Collectors.toList());
        Map<Date, List<StatisticsCityDayFcBatchDO>> map = collect.stream().collect(Collectors.groupingBy(
                StatisticsCityDayFcBatchDO::getDate, TreeMap::new, Collectors.toList())
        );
        //获取批次时间段内最新的一条数据
        map.forEach((key, list) -> {
            list.sort(Comparator.comparing(StatisticsCityDayFcBatchDO::getCreatetime).reversed());
            tarList.add(list.get(0));
        });
        return tarList;
    }
}
