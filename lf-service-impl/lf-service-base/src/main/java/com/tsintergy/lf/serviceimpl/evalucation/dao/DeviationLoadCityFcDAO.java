package com.tsintergy.lf.serviceimpl.evalucation.dao;


import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.DeviationLoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @version $Id: DeviationLoadCityFcDAO.java, v 0.1 2018-01-31 10:15:33 tao Exp $$
 */
@Slf4j
@Component
public class DeviationLoadCityFcDAO extends BaseAbstractDAO<DeviationLoadCityFcDO> {


    /**
     * 计算预测准确率
     *
     * @param loadCityHisVOs 历史数据
     * @param loadCityFcDOS 预测数据
     */
    public List<DeviationLoadCityFcDO> calculateDeviation(List<LoadCityHisDO> loadCityHisVOs,
        List<LoadCityFcDO> loadCityFcDOS) throws Exception {

        Map<String, LoadCityHisDO> loadCityHisVOMap = new HashMap<String, LoadCityHisDO>();
        for (LoadCityHisDO loadCityHisVO : loadCityHisVOs) {
            String key = loadCityHisVO.getCityId() + "-" + loadCityHisVO.getCaliberId() + "-" + loadCityHisVO.getDate()
                .getTime();
            loadCityHisVOMap.put(key, loadCityHisVO);
        }

        Map<String, List<LoadCityFcDO>> loadCityFcVOMap = new HashMap<String, List<LoadCityFcDO>>();
        for (LoadCityFcDO loadCityFcDO : loadCityFcDOS) {
            String key =
                loadCityFcDO.getCityId() + "-" + loadCityFcDO.getCaliberId() + "-" + loadCityFcDO.getDate().getTime();
            if (loadCityFcVOMap.get(key) == null) {
                loadCityFcVOMap.put(key, new ArrayList<LoadCityFcDO>());
            }
            loadCityFcVOMap.get(key).add(loadCityFcDO);
        }

        List<DeviationLoadCityFcDO> deviationLoadCityFcVOs = new ArrayList<DeviationLoadCityFcDO>();
        for (String key : loadCityHisVOMap.keySet()) {
            if (loadCityFcVOMap.get(key) != null) {
                for (LoadCityFcDO loadCityFcDO : loadCityFcVOMap.get(key)) {
                    try {
                        deviationLoadCityFcVOs.add(this.calculateDeviation(loadCityHisVOMap.get(key), loadCityFcDO));
                    } catch (Exception e) {
                        log.error("计算偏差值出错了", e);
                    }
                }
            }
        }

        return deviationLoadCityFcVOs;
    }

    /**
     *      功能描述: <br>  获取地区负荷预测偏差率
     *
     * @param cityId 地区ID
     * @param caliberId 口径ID
     * @param algorithmId 算法
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 地区负荷预测准确率列表  @since: 1.0.0     @Author:zhaoml     @Date: 2018/4/24 11:15   
     */
    public List<DeviationLoadCityFcDO> getDeviationLoadCityFcDO(String cityId, String caliberId, String algorithmId,
        Date startDate, Date endDate, Boolean isReport) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (null != algorithmId) {
            param.getQueryConditions().put("_ne_algorithmId", algorithmId);
        }
        if (StringUtils.isNotBlank(cityId)) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        if (null != isReport) {
            param.getQueryConditions().put("_ne_report", isReport);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        param.setDesc("date");
        return query(param).getDatas();
    }

    /**
     * 计算指定时段内的平均偏差率
     *
     * @param deviationLoadCityFcVOS 偏差率列表
     * @param startPeriod 开始时段
     * @param endPeriod 结束时段
     */
    public BigDecimal calculateAvgDeviation(List<DeviationLoadCityFcDO> deviationLoadCityFcVOS, String startPeriod,
        String endPeriod) {
        List<BigDecimal> datas = new ArrayList<BigDecimal>();

        if (deviationLoadCityFcVOS != null) {
            for (DeviationLoadCityFcDO deviationLoadCityFcVO : deviationLoadCityFcVOS) {
                Map<String, BigDecimal> map = BasePeriodUtils
                    .toMap(deviationLoadCityFcVO, Constants.LOAD_CURVE_POINT_NUM,
                        Constants.LOAD_CURVE_START_WITH_ZERO);
                for (String column : map.keySet()) {
                    if (column.replace("t", "").compareTo(startPeriod) > -1
                        && column.replace("t", "").compareTo(endPeriod) < 1) {
                        datas.add(map.get(column));
                    }
                }
            }
        }

        BigDecimal avg = BigDecimalUtils.avgList(datas, 4, false);
        return avg;
    }

    /**
     *      功能描述: <br>  获取偏差值
     *
     * @param cityId 城市ID
     * @param caliberId 口径ID
     * @param algorithmId 算法ID
     * @param date 日期
     * @return  @since: 1.0.0     @Author:zhaoml     @Date: 2018/4/24 11:18   
     */
    public DeviationLoadCityFcDO getDeviationLoadCityFcDO(String cityId, String caliberId, String algorithmId,
        Date date) throws Exception {
        if (cityId == null) {
            throw TsieExceptionUtils.newBusinessException("城市ID不可为空");
        }
        if (caliberId == null) {
            throw TsieExceptionUtils.newBusinessException("口径ID不可为空");
        }
        if (algorithmId == null) {
            throw TsieExceptionUtils.newBusinessException("算法ID不可为空");
        }

        if (date == null) {
            throw TsieExceptionUtils.newBusinessException("日期不可为空");
        }

        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        param.getQueryConditions().put("_de_date", new java.sql.Date(date.getTime()));
        param.getQueryConditions().put("_ne_cityId", cityId);
        param.getQueryConditions().put("_ne_algorithmId", algorithmId);
        param.getQueryConditions().put("_ne_caliberId", caliberId);
        List<DeviationLoadCityFcDO> deviationLoadCityFcVOS = query(param)
            .getDatas();
        if (deviationLoadCityFcVOS.size() > 0) {
            return deviationLoadCityFcVOS.get(0);
        }
        return null;
    }


    /**
     *      功能描述: <br>  get entities by ids and type in a period
     *
     * @return   @since: 1.0.0     @Author:zhaoml     @Date: 2018/4/24 11:21   
     */
    public List<DeviationLoadCityFcDO> getDeviationLoadCityFcDOS(List<String> cityIds, String caliberId, Date startDate,
        Date endDate, Boolean isReport) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();

        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (null != cityIds && cityIds.size() > 0) {
            param.getQueryConditions().put("_sin_cityId", cityIds);
        }
        if (StringUtils.isNotBlank(caliberId)) {
            param.getQueryConditions().put("_se_caliberId", caliberId);
        }
        param.setDesc("cityId,date");
        return query(param).getDatas();
    }

    /**
     *      功能描述: <br>  获取地区负荷预测偏差率
     *
     * @param cityIds 地区ID
     * @param caliberId 口径ID
     * @param algorithmId 算法
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 地区负荷预测准确率列表  @since: 1.0.0     @Author:zhaoml     @Date: 2018/4/24 11:23   
     */
    public List<DeviationLoadCityFcDO> getDeviationLoadCityFcDO(List<String> cityIds, String caliberId,
        String algorithmId, Date startDate, Date endDate, Boolean isReport) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();

        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (StringUtils.isNotBlank(algorithmId)) {
            param.getQueryConditions().put("_ne_algorithmId", algorithmId);
        }
        if (cityIds != null && !cityIds.isEmpty()) {
            param.getQueryConditions().put("_sin_cityId", cityIds);
        }
        if (StringUtils.isNotBlank(algorithmId)) {
            param.getQueryConditions().put("_ne_report", isReport);
        }
        param.setDesc("cityId,date");
        return query(param).getDatas();
    }


    public List<DeviationLoadCityFcDO> getDeviationLoadCityFcDO(String cityId, String caliberId, String algorithmId,
        Date startDate, Date endDate) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
               param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (StringUtils.isNotBlank(cityId)) {
            param.getQueryConditions().put("_se_cityId", cityId);
        }
        if (StringUtils.isNotBlank(caliberId)) {
            param.getQueryConditions().put("_se_caliberId", caliberId);
        }
        if (StringUtils.isNotBlank(caliberId)) {
            param.getQueryConditions().put("_se_algorithmId", algorithmId);
        }
        param.setDesc("date");
        return query(param).getDatas();
    }


    /**
     * 计算预测偏差值
     *
     * @param loadCityHisDO 历史数据
     * @param loadCityFcDO 预测数据
     */
    public DeviationLoadCityFcDO calculateDeviation(LoadCityHisDO loadCityHisDO, LoadCityFcDO loadCityFcDO)
        throws Exception {

        if (!loadCityHisDO.getCityId().equals(loadCityFcDO.getCityId())) {
            log.error("历史数据和预测数据的城市不同，不可以计算");
            return null;
        }

        if (!loadCityHisDO.getDate().equals(loadCityFcDO.getDate())) {
            log.error("历史数据和预测数据的日期不同，不可以计算");
            return null;
        }

        Map<String, BigDecimal> hisDataMap = BasePeriodUtils
            .toMap(loadCityHisDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        Map<String, BigDecimal> fcDataMap = BasePeriodUtils
            .toMap(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        Map<String, BigDecimal> deviationMap = new HashMap<String, BigDecimal>();

        for (String column : hisDataMap.keySet()) {
            deviationMap.put(column, LoadCalUtil.getDevication(hisDataMap.get(column), fcDataMap.get(column)));
        }

        DeviationLoadCityFcDO deviationLoadCityFcVO = new DeviationLoadCityFcDO();
        deviationLoadCityFcVO.setAlgorithmId(loadCityFcDO.getAlgorithmId());
        deviationLoadCityFcVO.setCityId(loadCityFcDO.getCityId());
        deviationLoadCityFcVO.setDate(loadCityFcDO.getDate());
        deviationLoadCityFcVO.setCaliberId(loadCityFcDO.getCaliberId());
        BasePeriodUtils.setAllFiled(deviationLoadCityFcVO, deviationMap);
        deviationLoadCityFcVO.setReport(loadCityFcDO.getReport());

        return deviationLoadCityFcVO;
    }


    /**
     * 保存或更新
     */
    public DeviationLoadCityFcDO doSaveOrUpdateDeviationLoadCityFcDO(DeviationLoadCityFcDO deviationLoadCityFcVO)
        throws Exception {
        DeviationLoadCityFcDO oldVO = getDeviationLoadCityFcDO(deviationLoadCityFcVO.getCityId(),
            deviationLoadCityFcVO.getCaliberId(), deviationLoadCityFcVO.getAlgorithmId(),
            deviationLoadCityFcVO.getDate());
        if (oldVO != null) {
            String id = oldVO.getId();
            BeanUtils.copyProperties(deviationLoadCityFcVO, oldVO);
            oldVO.setId(id);
            return this.updateAndFlush(oldVO);
        } else {
            return this
                .createAndFlush(deviationLoadCityFcVO);
        }
    }

    /**
     * 保存或更新
     */
    public List<DeviationLoadCityFcDO> doSaveOrUpdateDeviationLoadCityFcDOs(
        List<DeviationLoadCityFcDO> accuracyLoadCityFcVOS) throws Exception {
        List<DeviationLoadCityFcDO> vos = new ArrayList<DeviationLoadCityFcDO>();
        for (DeviationLoadCityFcDO deviationLoadCityFcVO : accuracyLoadCityFcVOS) {
            try {
                vos.add(this.doSaveOrUpdateDeviationLoadCityFcDO(deviationLoadCityFcVO));
            } catch (Exception e) {
                log.error("保存偏差值出错了", e);
            }
        }
        return vos;
    }


    /**
     * 计算偏差率
     */
    public Map<String, BigDecimal> calculateDeviationRatio(LoadCityHisDO loadCityHisDO, LoadCityFcDO loadCityFcDO)
        throws Exception {
        Map<String, BigDecimal> hisDataMap = BasePeriodUtils
            .toMap(loadCityHisDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        Map<String, BigDecimal> fcDataMap = BasePeriodUtils
            .toMap(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        Map<String, BigDecimal> deviationMap = new HashMap<String, BigDecimal>();
        for (String column : hisDataMap.keySet()) {
            deviationMap.put(column, LoadCalUtil.getPointDeviationRatio(hisDataMap.get(column), fcDataMap.get(column),hisDataMap.get(column)));
        }
        return deviationMap;
    }
}