package com.tsintergy.lf.serviceimpl.forecast.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 *
 * <AUTHOR>
 * @version $Id: LoadCityFcDAO.java, v 0.1 2018-01-31 10:23:36 tao Exp $$
 */

@Component
public class LoadCityFcDAO extends BaseAbstractDAO<LoadCityFcDO> {


    /**
     * 获取某个口径预测负荷
     *
     * @param cityId      城市ID
     * @param caliberId   口径ID
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param algorithmId 算法ID
     * @return
     */
    public List<LoadCityFcDO> getLoadCityFcDOs(String cityId, String caliberId, String algorithmId, Date startDate,
                                               Date endDate) throws Exception {

        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (null != algorithmId) {
            param.getQueryConditions().put("_se_algorithmId", algorithmId);
        }
        if (null != cityId) {
            param.getQueryConditions().put("_se_cityId", cityId);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_se_caliberId", caliberId);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<LoadCityFcDO> loadCityFcDOS = this.query(param).getDatas();
        return loadCityFcDOS;
    }


    public void deleteById(LoadCityFcDO loadCityFc) throws Exception {
        this.remove(loadCityFc);
    }

    /**
     * 查询预测负荷数据
     *
     * @param cityId      城市ID
     * @param caliberId   口径ID
     * @param algorithmId 算法ID
     * @param date        日期
     * @return
     */
    public LoadCityFcDO getLoadCityFcDO(String cityId, String caliberId, String algorithmId, Date date) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        param.getQueryConditions().put("_ne_date", new java.sql.Date(date.getTime()));
        param.getQueryConditions().put("_ne_algorithmId", algorithmId);
        param.getQueryConditions().put("_ne_cityId", cityId);
        param.getQueryConditions().put("_ne_caliberId", caliberId);
        List<LoadCityFcDO> loadCityFcDOS =this.query(param).getDatas();
        if (loadCityFcDOS != null && loadCityFcDOS.size() > 0) {
            return loadCityFcDOS.get(0);
        }
        return null;
    }

    /**
     * 查询上报预测负荷数据
     *
     * @param cityId    城市ID
     * @param caliberId 口径ID
     * @param date      日期
     * @return
     */
    public LoadCityFcDO getReportLoadCityFcDO(String cityId, String caliberId, Date date) throws Exception {
        if (cityId == null) {
            throw new BusinessException("T706","城市ID不可为空");
        }
        if (caliberId == null) {
            throw new BusinessException("T706","口径ID不可为空");
        }
        if (date == null) {
            throw new BusinessException("T706","日期不可为空");
        }
        DBQueryParam param = DBQueryParamBuilder.create().build();

        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        param.getQueryConditions().put("_ne_date", new java.sql.Date(date.getTime()));
        param.getQueryConditions().put("_ne_report", true);
        param.getQueryConditions().put("_se_cityId", cityId);
        param.getQueryConditions().put("_se_caliberId", caliberId);
        List<LoadCityFcDO> loadCityFcDOS = this.query(param).getDatas();
        if (loadCityFcDOS != null && loadCityFcDOS.size() > 0) {
            return loadCityFcDOS.get(0);
        }
        return null;
    }

    /**
     * 获取预测上报负荷
     *
     * @param cityId    城市ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return
     */
    public List<LoadCityFcDO> getReportLoadCityFcDOs(String cityId, Date startDate, Date endDate, String caliberId,
                                                     String algorithmId) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();

        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != algorithmId) {
            param.getQueryConditions().put("_se_algorithmId", algorithmId);
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (null != cityId) {
            param.getQueryConditions().put("_se_cityId", cityId);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        param.getQueryConditions().put("_ne_report", true);
        param.setOrderby("date");
        param.setDesc("0");
        List<LoadCityFcDO> loadCityFcDOS = this.query(param).getDatas();
        return loadCityFcDOS;
    }

    /**
     * 保存或更新
     *
     * @param loadCityFcDO
     * @return
     */
    public LoadCityFcDO doSaveOrUpdateLoadCityFcDO(LoadCityFcDO loadCityFcDO) throws Exception {
        LoadCityFcDO oldVO = this.getLoadCityFcDO(loadCityFcDO.getCityId(), loadCityFcDO.getCaliberId(),
                loadCityFcDO.getAlgorithmId(), loadCityFcDO.getDate());
        if (oldVO != null) {
            String id = oldVO.getId();
            ColumnUtil.copyPropertiesIgnoreNull(loadCityFcDO, oldVO);
            oldVO.setId(id);
            oldVO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            this.getSession().flush();
            this.getSession().clear();
            return (LoadCityFcDO) this.updateAndFlush(oldVO);
        } else {
            return (LoadCityFcDO) this.createAndFlush(loadCityFcDO);
        }
    }


    /**
     * 查询推荐的算法
     * @param cityId
     * @param caliberId
     * @param startDate
     * @param endDate
     * @return
     * @throws Exception
     */
    public List<LoadCityFcDO> findRecommendLoadCityFcDOs(String cityId, String caliberId, Date startDate, Date endDate) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != cityId) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        param.getQueryConditions().put("_ne_recommend", true);
        param.setOrderby("date");
        param.setDesc("0");
        return this.query(param).getDatas();
    }

    public LoadCityFcDO getRecommendLoadCityFcDO(String cityId, String caliberId, Date date) throws Exception {
        List<LoadCityFcDO> loadCityFcDOS = findRecommendLoadCityFcDOs(cityId,caliberId,date,date);
        if (!CollectionUtils.isEmpty(loadCityFcDOS)) {
            return loadCityFcDOS.get(0);
        }
        return null;
    }


    public LoadCityFcDO getReportLoadCityFcDO(Date date, String cityId, String caliberId, String algorithmId, Boolean report) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();

        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (date != null) {
            param.getQueryConditions().put("_ne_date", new java.sql.Date(date.getTime()));
        }
        if (StringUtils.isNotBlank(algorithmId)) {
            param.getQueryConditions().put("_ne_algorithmId", algorithmId);
        }
        if (StringUtils.isNotBlank(cityId)) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        if (StringUtils.isNotBlank(caliberId)) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        if (report != null) {
            param.getQueryConditions().put("_ne_report", report);
        }
        List<LoadCityFcDO> loadCityFcDOS = this.query(param).getDatas();
        if (loadCityFcDOS != null && loadCityFcDOS.size() > 0) {
            return loadCityFcDOS.get(0);
        }
        return null;
    }


    public List<LoadCityFcDO> getReportFc(Date startDate, Date endDate, String cityId, String caliberId) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();

        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (StringUtils.isNotBlank(cityId)) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        if (StringUtils.isNotBlank(caliberId)) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        param.getQueryConditions().put("_ne_report", true);
        List<LoadCityFcDO> loadCityFcDOS = this.query(param).getDatas();
        return loadCityFcDOS;
    }

    /**
     * 获取上报预测数据，如果没有上报则返回推荐的预测数据
     * @param startDate
     * @param endDate
     * @param cityId
     * @param caliberId
     * @return
     * @throws Exception
     */
    public List<LoadCityFcDO> findReportFcIfNullUseRecommend(Date startDate, Date endDate, String cityId, String caliberId) throws Exception {
        List<LoadCityFcDO> reportFc = getReportFc(startDate, endDate, cityId, caliberId);
        List<LoadCityFcDO> recommendLoadCityFcDOs = findRecommendLoadCityFcDOs(cityId, caliberId, startDate, endDate);
        if (CollectionUtils.isEmpty(reportFc)) {
            return recommendLoadCityFcDOs;
        } else {
            // 找出推荐中有而上报中没有的数据
            List<LoadCityFcDO> recommendLoadList = new ArrayList<>();
            Map<String, LoadCityFcDO> reportMap = reportFc.stream()
                .collect(Collectors.toMap(loadCityFcDO -> generateMapKey(loadCityFcDO), Function.identity(), (o, n) -> n));
            for (LoadCityFcDO recommendLoadCityFcDO : recommendLoadCityFcDOs) {
                LoadCityFcDO reportFcDO = reportMap.get(generateMapKey(recommendLoadCityFcDO));
                if (reportFcDO == null) {
                    recommendLoadList.add(recommendLoadCityFcDO);
                }
            }

            // 将推荐中有而上报中没有的数据合并到上报的集合中
            reportFc.addAll(recommendLoadList);
            return reportFc;
        }
    }

    private static String generateMapKey(LoadCityFcDO loadCityFcDO) {
        return loadCityFcDO.getCityId() + loadCityFcDO.getCaliberId() + DateUtils.date2String(loadCityFcDO.getDate(),
            DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
    }


    public List<LoadCityFcDO> getLoadCityFcDOInAlgorithmId(String cityId, String caliberId, List<String> algorithmIds, Date startDate,
                                                           Date endDate) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();

        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (null != algorithmIds) {
            param.getQueryConditions().put("_sin_algorithmId", algorithmIds);
        }
        if (null != cityId) {
            param.getQueryConditions().put("_se_cityId", cityId);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_se_caliberId", caliberId);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<LoadCityFcDO> loadCityFcDOS = this.query(param).getDatas();
        return loadCityFcDOS;
    }

    public List<LoadCityFcDO> findLoadCityFcReportByUserId(String cityId, String caliberId, String userId, Date startDate, Date endDate) {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (null != userId) {
            param.getQueryConditions().put("_se_userId", userId);
        }
        if (null != cityId) {
            param.getQueryConditions().put("_se_cityId", cityId);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_se_caliberId", caliberId);
        }
        param.getQueryConditions().put("_ne_report", true);
        param.setOrderby("date");
        param.setDesc("0");
        param.setOrderby("date");
        param.setDesc("0");
        List<LoadCityFcDO> loadCityFcDOS = this.query(param).getDatas();
        return loadCityFcDOS;
    }
}