package com.tsintergy.lf.serviceimpl.load.impl;


import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.SystemConstant;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DataUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CaliberDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.evalucation.api.BatchDataFilterService;
import com.tsintergy.lf.serviceapi.base.forecast.api.AlgorithmService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcBatchService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcBatchDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.industry.api.IndustryCityLoadDayFcAlgoService;
import com.tsintergy.lf.serviceapi.base.industry.api.IndustryCityLoadDayHisClctNewService;
import com.tsintergy.lf.serviceapi.base.industry.pojo.IndustryCityLoadDayFcAlgoDO;
import com.tsintergy.lf.serviceapi.base.industry.pojo.IndustryCityLoadDayHisClctNewDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisClctService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.dto.*;
import com.tsintergy.lf.serviceapi.base.load.pojo.BaseLoadIntervalDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHis288DO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherStationFcBasicWgService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherStationHisBasicWgService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationFcBasicWgDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationHisBasicWgDO;
import com.tsintergy.lf.serviceimpl.forecast.dao.LoadCityFcDAO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadCityHis288DAO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadCityHisDAO;
import lombok.SneakyThrows;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.tsintergy.lf.core.constants.Constants.TRADE_ID_All;
import static com.tsintergy.lf.serviceimpl.industry.impl.IndustryCityLoadDayHisClctServiceImpl.xnTradeCode;
import static com.tsintergy.lf.serviceimpl.industry.impl.IndustryServiceImpl.K;

/**
 * <AUTHOR>
 * @version $Id: LoadCityHisServiceImpl.java, v 0.1 2018-01-31 10:50:38 tao Exp $$
 */

@Service("loadCityHisService")
public class LoadCityHisServiceImpl extends BaseServiceImpl implements LoadCityHisService {

    private static final Logger logger = LogManager.getLogger(LoadCityHisServiceImpl.class);

    // 解析excel 固定从0000点开始到2400
    private static final Boolean EXCEL_LOAD_CURVE_START_WITH_ZERO = true;
    @Autowired
    LoadCityHisClctService loadCityHisClctService;
    @Autowired
    LoadCityFcService loadCityFcService;
    @Autowired
    AlgorithmService algorithmService;
    @Autowired
    CaliberService caliberService;
    @Autowired
    LoadCityHisDAO loadCityHisDAO;
    @Autowired
    LoadCityFcBatchService loadCityFcBatchService;
    @Autowired
    private CityService cityService;
    @Autowired
    private LoadCityHis288DAO loadCityHis288DAO;
    @Autowired
    private LoadCityFcDAO loadCityFcDAO;
    @Autowired
    private WeatherCityHisService weatherCityHisService;
    @Autowired
    private WeatherCityFcService weatherCityFcService;
    @Autowired
    private WeatherStationHisBasicWgService weatherStationHisBasicWgService;

    @Autowired
    private WeatherStationFcBasicWgService weatherStationFcBasicWgService;

    @Autowired
    SettingSystemService settingSystemService;

    @Autowired
    BatchDataFilterService batchDataFilterService;

    @Autowired
    IndustryCityLoadDayHisClctNewService industryCityLoadDayHisClctNewService;

    @Autowired
    IndustryCityLoadDayFcAlgoService industryCityLoadDayFcAlgoService;


    @Override
    public List<LoadCityHisDO> findLoadCityHisDOS(String cityId, Date startDate, Date endDate, String caliberId)
            throws Exception {
        return loadCityHisDAO.getLoadCityHisDO(cityId, startDate, endDate, caliberId);
    }

    @Override
    public DataPackage queryLoadCityHisDO(DBQueryParam param) throws Exception {
        try {
            return loadCityHisDAO.query(param);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("T706", e.getMessage());
        }
    }

    @Override
    public LoadCityHisDO doCreate(LoadCityHisDO vo) throws Exception {
        try {
            return (LoadCityHisDO) loadCityHisDAO.create(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("T706", e.getMessage());
        }
    }

    @Override
    public void doRemoveLoadCityHisDO(LoadCityHisDO vo) throws Exception {
        try {
            loadCityHisDAO.remove(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("T706", e.getMessage());
        }
    }

    @Override
    public void doRemoveLoadCityHisDOByPK(Serializable pk) throws Exception {
        try {
            loadCityHisDAO.removeByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("T706", e.getMessage());
        }
    }

    @Override
    public LoadCityHisDO doUpdateLoadCityHisDO(LoadCityHisDO vo) throws Exception {
        try {
            return (LoadCityHisDO) loadCityHisDAO.update(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("T706", e.getMessage());
        }
    }

    @Override
    public LoadCityHisDO findLoadCityHisDOByPk(Serializable pk) throws Exception {
        try {
            return (LoadCityHisDO) loadCityHisDAO.findByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("T706", e.getMessage());
        }
    }

    @Override
    public List<CityValueDTO> find24LoadCityByDateAndCityIds(Date date, List<String> cityIds, String caliberId)
            throws Exception {
        List<CityValueDTO> cityValueDTOS = new ArrayList<CityValueDTO>();
        try {
            List<LoadCityHisDO> LoadCityHisDOS = loadCityHisDAO.getLoadCityHisDOSByCityIds(cityIds, date, date, caliberId);
            if (LoadCityHisDOS == null || LoadCityHisDOS.size() < 1) {
                throw TsieExceptionUtils.newBusinessException("T706");
            } else {
                for (LoadCityHisDO LoadCityHisDO : LoadCityHisDOS) {
                    CityValueDTO cityValueDTO = new CityValueDTO();
                    cityValueDTO.setCityId(LoadCityHisDO.getCityId());
                    cityValueDTO.setCity(cityService.findCityById(LoadCityHisDO.getCityId()).getCity());
                    cityValueDTO.setValue(
                            BasePeriodUtils.toList(LoadCityHisDO, 24, Constants.LOAD_CURVE_START_WITH_ZERO)); // 转24点负荷数据
                    cityValueDTOS.add(cityValueDTO);
                }
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("T706", e);
        }
        return cityValueDTOS;

    }

    @Override
    public List<BigDecimal> findLoadCityHisDO(Date date, String cityId, String caliberId) throws Exception {
        List<LoadCityHisDO> LoadCityHisDOS = loadCityHisDAO.getLoadCityHisDO(cityId, date, date, caliberId);
        if (LoadCityHisDOS.size() < 1) {
            return new ArrayList<>();
        }
        return BasePeriodUtils
                .toList(LoadCityHisDOS.get(0), Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
    }

    @Override
    public List<BigDecimal> findLoadCityHis288DO(Date date, String cityId, String caliberId) throws Exception {
        List<LoadCityHis288DO> LoadCityHisDOS = loadCityHis288DAO.getLoadCityHisDO(cityId, date, date, caliberId);
        if (LoadCityHisDOS.size() < 1) {
            return new ArrayList<>();
        }
        return BasePeriodUtils.toList(LoadCityHisDOS.get(0), 288, Constants.LOAD_CURVE_START_WITH_ZERO);

    }

    @Override
    public List<BigDecimal> findLoadCityHisDO(String cityId, Date startDate, Date endDate, String caliberId)
            throws Exception {
        List<LoadCityHisDO> powerLoadHisCityClctDOS = null;
        try {
            powerLoadHisCityClctDOS = this.getLoadCityHisDOS(cityId, caliberId, startDate, endDate);
        } catch (Exception e) {
            logger.error("历史出力查询异常...", e);
            throw TsieExceptionUtils.newBusinessException("01C20180003");
        }
        List<BigDecimal> result = new ArrayList<>();
        List nullList = new ArrayList() {
            {
                for (int i = 0; i < 96; i++) {
                    add(null);
                }
            }
        };
        if (!CollectionUtils.isEmpty(powerLoadHisCityClctDOS) && powerLoadHisCityClctDOS.size() > 0) {
            Map<Date, LoadCityHisDO> mapData = powerLoadHisCityClctDOS.stream()
                    .collect(Collectors.toMap(LoadCityHisDO::getDate, Function.identity(), (o, n) -> n));
            List<Date> listDate = DateUtil.getListBetweenDay(startDate, endDate);
            for (Date date : listDate) {
                LoadCityHisDO hisCityClctDO = mapData.get(date);
                if (hisCityClctDO == null) {
                    result.addAll(nullList);
                } else {
                    result.addAll(BasePeriodUtils
                            .toList(hisCityClctDO, Constants.LOAD_CURVE_POINT_NUM,
                                    Constants.LOAD_CURVE_START_WITH_ZERO));
                }
            }
        }
        return result;
    }

    @Override
    public List<LoadCityHisDO> find24LoadCityHisDO(Date date, String cityId, String caliberId) throws Exception {
        List<LoadCityHisDO> LoadCityHisDOS = loadCityHisDAO.getLoadCityHisDO(cityId, date, date, caliberId);
        return LoadCityHisDOS;
    }


    @Override
    public List<LoadHisDataDTO> findLoadCityVOsByCityId(Date startDate, Date endDate, String cityId, String caliberId)
            throws Exception {
        List<LoadCityHisDO> LoadCityHisDOS = loadCityHisDAO.getLoadCityHisDO(cityId, startDate, endDate, caliberId);
        if (LoadCityHisDOS.size() < 1) {
            return new ArrayList<>();
        }
        Map<String, String> cityNameMap = cityService.findAllCitys().stream().collect(Collectors.toMap(CityDO::getId, CityDO::getCity));
        List<LoadHisDataDTO> loadCommonDTOS = new ArrayList<LoadHisDataDTO>(10);
        for (LoadCityHisDO LoadCityHisDO : LoadCityHisDOS) {
            LoadHisDataDTO loadCommonDTO = new LoadHisDataDTO();
            loadCommonDTO.setId(LoadCityHisDO.getId());
            loadCommonDTO.setDate(LoadCityHisDO.getDate());
            loadCommonDTO.setData(BasePeriodUtils
                    .toList(LoadCityHisDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            loadCommonDTO.setWeek(DateUtil.getWeek(LoadCityHisDO.getDate()));
            loadCommonDTO.setCity(cityNameMap.get(LoadCityHisDO.getCityId()));
            loadCommonDTOS.add(loadCommonDTO);
        }
        return loadCommonDTOS;
    }


    @Override
    public List<LoadCommonDTO<Date>> findLoadCityVOsByCityIdAndDates(String cityId, List<Date> dates, String caliberId)
            throws Exception {
        List<LoadCityHisDO> LoadCityHisDOS = loadCityHisDAO.getLoadCityHisDOSByDates(cityId, dates, caliberId);
        if (LoadCityHisDOS.size() < 1) {
            throw TsieExceptionUtils.newBusinessException("T706", "");
        }
        List<LoadCommonDTO<Date>> loadCommonDTOS = new ArrayList<LoadCommonDTO<Date>>(10);
        for (LoadCityHisDO LoadCityHisDO : LoadCityHisDOS) {
            LoadCommonDTO<Date> loadCommonDTO = new LoadCommonDTO<Date>();
            loadCommonDTO.setName(LoadCityHisDO.getDate());
            loadCommonDTO.setData(BasePeriodUtils
                    .toList(LoadCityHisDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            loadCommonDTOS.add(loadCommonDTO);
        }
        return loadCommonDTOS;
    }

    @Override
    public List<LoadCityHisDO> findLoadCityDOsByCityIdInDates(String cityId, List<Date> dates, String caliberId)
            throws Exception {
        List<LoadCityHisDO> LoadCityHisDOS = loadCityHisDAO.getLoadCityHisDOSByDates(cityId, dates, caliberId);
        if (LoadCityHisDOS.size() < 1) {
            throw TsieExceptionUtils.newBusinessException("01C20180002");
        } else {
            return LoadCityHisDOS;
        }
    }


    @Override
    public CityValueDTO find24LoadCityBetweenDate(String cityId, Date startDate, Date endDate, String caliberId)
            throws Exception {
        try {
            List<LoadCityHisDO> LoadCityHisDOS = loadCityHisDAO.getLoadCityHisDO(cityId, startDate, endDate, caliberId);
            if (LoadCityHisDOS.size() < 1) {
                throw TsieExceptionUtils.newBusinessException("T706", "");
            } else {
                CityValueDTO cityValueDTO = new CityValueDTO();
                cityValueDTO.setCityId(LoadCityHisDOS.get(0).getCityId());
                cityValueDTO.setCity(cityService.findCityById(cityValueDTO.getCityId()).getCity());
                List<BigDecimal> values = new ArrayList<BigDecimal>();
                for (LoadCityHisDO LoadCityHisDO : LoadCityHisDOS) {
                    values.addAll(BasePeriodUtils.toList(LoadCityHisDO, 24, Constants.LOAD_CURVE_START_WITH_ZERO));
                }
                cityValueDTO.setValue(values);
                return cityValueDTO;
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("01C20180003", e);
        }
    }

    @Override
    public List<LoadCityHisDO> doUpdateLoadCityHisDO(List<LoadHisDTO> loadHisDTOS, String caliberId) throws Exception {
        List<LoadCityHisDO> LoadCityHisDOS = new ArrayList<LoadCityHisDO>();
        for (LoadHisDTO loadHisDTO : loadHisDTOS) {
            LoadCityHisDO LoadCityHisDO = new LoadCityHisDO();
            LoadCityHisDO.setId(loadHisDTO.getId());
            LoadCityHisDO.setCityId(loadHisDTO.getCityId());
            LoadCityHisDO.setCaliberId(caliberId);
            LoadCityHisDO.setDate(new java.sql.Date(loadHisDTO.getDate().getTime()));
//            LoadCityHisDO.setAllFied(loadHisDTO.getData());
            BasePeriodUtils.setAllFiled(LoadCityHisDO,
                    ColumnUtil.listToMap(loadHisDTO.getData(), Constants.LOAD_CURVE_START_WITH_ZERO));
            LoadCityHisDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            LoadCityHisDOS.add(this.doUpdateLoadCityHisDO(LoadCityHisDO));
        }
        return LoadCityHisDOS;
    }

    @Override
    public List<LoadCityHisDO> doUpdateLoadCityHisDOFromExcel(List<List<List<String>>> dataList)
            throws Exception {
        List<LoadCityHisDO> LoadCityHisDOS = new ArrayList<LoadCityHisDO>();
        List<List<String>> rows = dataList.get(0);
        for (int i = 1; i < rows.size(); i++) {
            LoadCityHisDO excelLoadCityHisDO = new LoadCityHisDO();
            excelLoadCityHisDO.setCityId(cityService.findCityByName(rows.get(i).get(0)).getId());
            excelLoadCityHisDO.setDate(java.sql.Date.valueOf(rows.get(i).get(1)));
            CaliberDO caliberVO = caliberService.findCaliberDOByName(rows.get(i).get(2));
            if (null != caliberVO) {
                excelLoadCityHisDO.setCaliberId(caliberVO.getId());
            } else {
                throw TsieExceptionUtils.newBusinessException("01C20180008");
            }
            LoadCityHisDO LoadCityHisDO = loadCityHisDAO
                    .getLoadCityHisDOByOneDate(excelLoadCityHisDO.getCityId(), excelLoadCityHisDO.getDate(),
                            excelLoadCityHisDO.getCaliberId());
            if (LoadCityHisDO == null) {
                LoadCityHisDO = new LoadCityHisDO();
                LoadCityHisDO.setCityId(excelLoadCityHisDO.getCityId());
                LoadCityHisDO.setCaliberId(excelLoadCityHisDO.getCaliberId());
                LoadCityHisDO.setDate(excelLoadCityHisDO.getDate());
            }

            List<BigDecimal> list = new ArrayList<BigDecimal>();
            for (String data : rows.get(i).subList(4, rows.get(i).size())) {
                list.add(new BigDecimal(data));
            }
            BasePeriodUtils
                    .setAllFiled(LoadCityHisDO, ColumnUtil.listToMap(list.subList(0, list.size() - 1), EXCEL_LOAD_CURVE_START_WITH_ZERO));
            LoadCityHisDO.setT2400(list.get(list.size() - 1));

            if (StringUtils.isEmpty(LoadCityHisDO.getId())) {// 插入
                LoadCityHisDO.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
                LoadCityHisDOS.add(this.doCreate(LoadCityHisDO));
            } else {
                LoadCityHisDOS.add(this.doUpdateLoadCityHisDO(LoadCityHisDO));
            }

        }
        return LoadCityHisDOS;
    }


    public Workbook getWorkbok(InputStream in, File file) throws IOException {
        Workbook wb = null;
        if (file.getName().endsWith("xlsx")) {
            wb = new XSSFWorkbook(in);
        }
        return wb;
    }

    private Object getValue(Cell cell) {
        Object obj = null;
        switch (cell.getCellTypeEnum()) {
            case BOOLEAN:
                obj = cell.getBooleanCellValue();
                break;
            case ERROR:
                obj = cell.getErrorCellValue();
                break;
            case NUMERIC:
                obj = cell.getNumericCellValue();
                break;
            case STRING:
                obj = cell.getStringCellValue();
                break;
            default:
                break;
        }
        return obj;
    }

    /**
     * 功能描述: <br>
     * <p>
     * 获取文件夹下所有的文件名
     *
     * @return:
     * @since: 1.0.0
     * @Author:wangchen
     * @Date: 2018/9/18 9:03
     */
    public ArrayList<String> getFiles(String path) {
        ArrayList<String> files = new ArrayList<String>();
        File file = new File(path);
        File[] tempList = file.listFiles();

        for (int i = 0; i < tempList.length; i++) {
            if (tempList[i].isFile()) {
                files.add(tempList[i].toString());
                System.out.println(tempList[i].toString());
            }
        }
        return files;
    }


    @Override
    public List<LoadCityHisDO> getLoadCityHisDOS(String cityId, String caliberId, Date startDate, Date endDate)
            throws Exception {
        return loadCityHisDAO.getLoadCityHisDO(cityId, startDate, endDate, caliberId);
    }

    @Override
    public void doInsertOrUpdate(LoadCityHisDO LoadCityHisDO) throws Exception {
        LoadCityHisDO LoadCityHisDOTemp = loadCityHisDAO
                .getLoadCityHisDOByOneDate(LoadCityHisDO.getCityId(), LoadCityHisDO.getDate(), LoadCityHisDO.getCaliberId());
        if (LoadCityHisDOTemp == null) {
            loadCityHisDAO.createAndFlush(LoadCityHisDO);
            return;
        }
        loadCityHisDAO.getSession().flush();
        loadCityHisDAO.getSession().clear();
        LoadCityHisDO.setId(LoadCityHisDOTemp.getId());
        LoadCityHisDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
        loadCityHisDAO.updateAndFlush(LoadCityHisDO);
    }


    /**
     * 预测查询--负荷预测
     */
    @Override
    public LoadQueryDTO findLoad(String cityId, String caliberId, Date startDate, Date endDate, String algorithmId, String batchId)
            throws Exception {
        //真实数据
        List<LoadCityHisDO> LoadCityHisDOS = loadCityHisDAO.getLoadCityHisDO(cityId, startDate, endDate, caliberId);
        //查询上报数据
        List<LoadCityFcDO> LoadCityFcDOS = loadCityFcService.findReportLoadFc(cityId, caliberId, startDate, endDate);
        //查询算法的数据
//        List<LoadCityFcDO> fcVOS = loadCityFcService
//                .findFcByAlgorithmId(cityId, caliberId, algorithmId, startDate, endDate);
        List<LoadCityFcBatchDO> fcVOS = loadCityFcBatchService
                .findOneByBatchTime(cityId, startDate, endDate, caliberId, algorithmId, batchId);
        CityDO cityById = cityService.findCityById(cityId);
        // 营销实际负荷
        List<IndustryCityLoadDayHisClctNewDO> industryCityLoadDayHisClctDOS = industryCityLoadDayHisClctNewService.findByDate(cityById.getOrgNo(),
                TRADE_ID_All, DateUtils.addDays(startDate, -1), endDate);
        // 抽水蓄能实际负荷
        List<IndustryCityLoadDayHisClctNewDO> xnByDate = industryCityLoadDayHisClctNewService.findByDate(cityById.getOrgNo(),
                xnTradeCode, DateUtils.addDays(startDate, -1), endDate);
        // 营销预测
        List<IndustryCityLoadDayFcAlgoDO> byDateCodes = industryCityLoadDayFcAlgoService.findByDateCodes(
                cityById.getOrgNo(), Arrays.asList(TRADE_ID_All), startDate, endDate);
        if (LoadCityHisDOS.size() < 1 && LoadCityFcDOS.size() < 1 && fcVOS.size() < 1) {
            return null;
        }
        //分别转map
        Map<Date, LoadCityHisDO> realMap = LoadCityHisDOS.stream()
                .collect(Collectors.toMap(LoadCityHisDO::getDate, Function.identity(), (key1, key2) -> key2));
        Map<Date, LoadCityFcDO> reportMap = LoadCityFcDOS.stream()
                .collect(Collectors.toMap(LoadCityFcDO::getDate, Function.identity(), (key1, key2) -> key2));
        Map<Date, IndustryCityLoadDayHisClctNewDO> industryCityLoadDayHisClctDOMap = industryCityLoadDayHisClctDOS.stream()
                .collect(Collectors.toMap(IndustryCityLoadDayHisClctNewDO::getDate, Function.identity(), (key1, key2) -> key2));
        Map<Date, IndustryCityLoadDayHisClctNewDO> xnIndustryMap = xnByDate.stream()
                .collect(Collectors.toMap(IndustryCityLoadDayHisClctNewDO::getDate, Function.identity(), (key1, key2) -> key2));
        Map<Date, LoadCityFcBatchDO> fcMap = fcVOS.stream()
                .collect(Collectors.toMap(LoadCityFcBatchDO::getDate, Function.identity(), (key1, key2) -> key2));
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        List<BigDecimal> real = new ArrayList<>();
        List<BigDecimal> fc = new ArrayList<>();
        List<BigDecimal> report = new ArrayList<>();
        List<BigDecimal> todayYx = new ArrayList<>();
        List<BigDecimal> yesterdayYx = new ArrayList<>();
        LoadQueryDTO queryDTO = new LoadQueryDTO();
        for (Date date : dateList) {
            LoadCityHisDO hisVO = realMap.get(date);
            if (hisVO != null) {
                real.addAll(BasePeriodUtils
                        .toList(hisVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            } else {
                real.addAll(LoadCalUtil.getNullList(Constants.LOAD_CURVE_POINT_NUM));
            }
            LoadCityFcDO reportVo = reportMap.get(date);
            report.addAll(getList(reportVo));
            LoadCityFcBatchDO fcVO = fcMap.get(date);
            fc.addAll(fcVO.getloadList());

            //今日实际营销负荷
            IndustryCityLoadDayHisClctNewDO industryCityLoadDayHisClctDO = industryCityLoadDayHisClctDOMap.get(date);
            if (industryCityLoadDayHisClctDO != null) {
                List<BigDecimal> bigDecimals = industryCityLoadDayHisClctDO.getloadList();
                List<BigDecimal> xnLoad = xnIndustryMap.get(date).getloadList();
                List<BigDecimal> hisLoad = BigDecimalFunctions.listSubtract(bigDecimals, xnLoad);
                hisLoad = BigDecimalFunctions.listDivideValue(hisLoad, K);
                todayYx.addAll(hisLoad);
            }

            //昨日实际营销负荷
            IndustryCityLoadDayHisClctNewDO yesterdayDO = industryCityLoadDayHisClctDOMap.get(DateUtils.addDays(date, -1));
            if (yesterdayDO != null) {
                List<BigDecimal> bigDecimals = yesterdayDO.getloadList();
                List<BigDecimal> xnLoad = xnIndustryMap.get(DateUtils.addDays(date, -1)).getloadList();
                List<BigDecimal> hisLoad = BigDecimalFunctions.listSubtract(bigDecimals, xnLoad);
                hisLoad = BigDecimalFunctions.listDivideValue(hisLoad, K);
                yesterdayYx.addAll(hisLoad);
            }
        }
        queryDTO.setFc(fc);
        queryDTO.setReal(real);
        queryDTO.setReport(report);
        queryDTO.setTodayYx(todayYx);
        queryDTO.setYesterdayYx(yesterdayYx);
        queryDTO.setYesterday(this.getYesterdayDataWhenSingleDayQuery(cityId, startDate, endDate, caliberId));
        queryDTO.setAlgorithmName(algorithmService.findAlgorithmVOByPk(algorithmId).getAlgorithmCn());
        if (!CollectionUtils.isEmpty(byDateCodes)) {
            List<BigDecimal> bigDecimalsAll = new ArrayList<>();
            for (IndustryCityLoadDayFcAlgoDO byDateCode : byDateCodes) {
                List<BigDecimal> bigDecimals = BigDecimalFunctions.listDivideValue(byDateCode.getloadList(), K);
                bigDecimalsAll.addAll(bigDecimals);
            }
            queryDTO.setFcYx(bigDecimalsAll);
        }
        return queryDTO;
    }

    @SneakyThrows
    private List<BigDecimal> getYesterdayDataWhenSingleDayQuery(String cityId, Date startDate, Date endDate, String caliberId) {
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        //只有查询一天数据时才展示昨日实际气象,查询多天时不展示
        if (dateList.size() != 1) {
            return Collections.emptyList();
        }
        Date yesterday = DateUtils.addDays(startDate, -1);
        LoadCityHisDO loadCityHisDO = loadCityHisDAO.getLoadCityHisDOByOneDate(cityId, yesterday, caliberId);
        List<BigDecimal> yesterdayLoads = new ArrayList<>();
        if (loadCityHisDO == null) {
            yesterdayLoads.addAll(LoadCalUtil.getNullList(Constants.LOAD_CURVE_POINT_NUM));
        } else {
            yesterdayLoads.addAll(BasePeriodUtils.toList(loadCityHisDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
        }
        return yesterdayLoads;
    }

    @Override
    public List<CityLoadDTO> find24CityLoadDTO(Date today, List<String> cityIds, String caliberId) throws Exception {
        Map<String, LoadCityHisDO> todayHisLoadsMap = getLoadCityHisDOMap(today, cityIds, caliberId);
        Map<String, LoadCityHisDO> yesterdayLoadsMap = getLoadCityHisDOMap(DateUtils.addDays(today, -1), cityIds,
                caliberId);
        Map<String, LoadCityFcDO> todayFcMap = getLoadCityFcDOMap(today, cityIds, caliberId);
        Map<String, WeatherCityHisDO> todayTemperaturesHisVOMap = getWeatherCityVOMap(today, cityIds);
        Map<String, String> cityMap = cityService.findAllCitys().stream()
                .collect(Collectors.toMap(CityDO::getId, CityDO::getCity));
        List<CityLoadDTO> cityLoadDTOS = new ArrayList<>();
        for (String cityId : cityIds) {
            CityLoadDTO cityLoadDTO = new CityLoadDTO();
            cityLoadDTO.setCityName(cityMap.get(cityId));
            setTodayHisLoads(todayHisLoadsMap, cityId, cityLoadDTO);
            setYesterdayHisLoads(yesterdayLoadsMap, cityId, cityLoadDTO);
            setTodayFcLoads(todayFcMap, cityId, cityLoadDTO, today, caliberId);
            setTodayTemperatures(todayTemperaturesHisVOMap, cityId, cityLoadDTO);
            cityLoadDTOS.add(cityLoadDTO);
        }
        return cityLoadDTOS;
    }

    @Override
    public List<AreaLoadRateDTO> find24AreaLoadRateDTO(Date date, String caliberId) throws Exception {
        Map<String, String> cityMap = cityService.findAllCitys().stream()
                .filter(cityVO -> StringUtils.isNotBlank(cityVO.getArea()))
                .collect(Collectors.toMap(CityDO::getId, CityDO::getArea));
        List<LoadCityHisDO> LoadCityHisDOS = this.findLoadCityHisDO(date, caliberId);
        if (!CollectionUtils.isEmpty(LoadCityHisDOS)) {
            Map<String, List<BigDecimal>> areaLoadsMap = new HashMap<>();
            List<BigDecimal> provinceLoads = null;
            for (LoadCityHisDO LoadCityHisDO : LoadCityHisDOS) {
                if (LoadCityHisDO.getCityId().equals(CityConstants.PROVINCE_ID)) {
                    provinceLoads = BasePeriodUtils
                            .toList(LoadCityHisDO, Constants.LOAD_CURVE_POINT_NUM_24, Constants.LOAD_CURVE_START_WITH_ZERO);
                    continue;
                }
                CalcAreaLoads(cityMap, areaLoadsMap, LoadCityHisDO);
            }

            if (!areaLoadsMap.isEmpty()) {
                List<AreaLoadRateDTO> areaLoadRateDTOS = new ArrayList<>();
                Set<String> areaNames = areaLoadsMap.keySet();
                Iterator<String> iterator = areaNames.iterator();
                while (iterator.hasNext()) {
                    String areaName = iterator.next();
                    List<BigDecimal> areaLoads = areaLoadsMap.get(areaName);
                    AreaLoadRateDTO areaLoadRateDTO = new AreaLoadRateDTO();
                    areaLoadRateDTO.setAreaName(areaName);
                    List<BigDecimal> areaLoadsRate = DataUtil
                            .listDivide(areaLoads, provinceLoads);
                    areaLoadRateDTO.setLoadRates(areaLoadsRate);
                    areaLoadRateDTOS.add(areaLoadRateDTO);
                }
                return areaLoadRateDTOS;
            }
        }

        return null;
    }

    @Override
    public Date24LoadDTO findDate24LoadDTOS(Date date, String cityId, String caliberId) throws Exception {
        LoadCityHisDO LoadCityHisDO = null;
        List<LoadCityHisDO> LoadCityHisDOs = loadCityHisDAO.getLoadCityHisDO(cityId, date, date, caliberId);
        if (!CollectionUtils.isEmpty(LoadCityHisDOs)) {
            LoadCityHisDO = LoadCityHisDOs.get(0);
        }
        LoadCityFcDO LoadCityFcDOS = loadCityFcDAO.getReportLoadCityFcDO(cityId, caliberId, date);
        Date24LoadDTO date24LoadDTO = new Date24LoadDTO();
        date24LoadDTO.setCityId(cityId);
        if (LoadCityFcDOS != null) {
            date24LoadDTO.setFcLoads(BasePeriodUtils
                    .toList(LoadCityFcDOS, Constants.LOAD_CURVE_POINT_NUM_24, Constants.LOAD_CURVE_START_WITH_ZERO));
        }
        if (LoadCityHisDO != null) {
            date24LoadDTO.setHisLoads(BasePeriodUtils
                    .toList(LoadCityHisDO, Constants.LOAD_CURVE_POINT_NUM_24, Constants.LOAD_CURVE_START_WITH_ZERO));
        }
        return date24LoadDTO;
    }

    @Override
    public LoadCityHisDO getOne(Date date, String caliberId, String cityId) throws Exception {

        return loadCityHisDAO.getLoadCityHisDOByOneDate(cityId, date, caliberId);
    }

    @Override
    public List<LoadCityHisDO> findLoadCityList(Date date, String caliberId) {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create()
                .where(QueryOp.DateEqualTo, "date", new java.sql.Date(date.getTime()))
                .where(QueryOp.StringEqualTo, "caliberId", caliberId);
        return loadCityHisDAO.query(dbQueryParamBuilder.build()).getDatas();
    }

    @Override
    public List<LoadCityHisDO> getListByCityDates(Date startDate, Date endDate, String cityId, String caliberId) {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create()
                .where(QueryOp.StringEqualTo, "caliberId", caliberId)
                .where(QueryOp.StringEqualTo, "cityId", cityId)
                .where(QueryOp.DateNoLessThan, "date", new java.sql.Date(startDate.getTime()))
                .where(QueryOp.DateNoMoreThan, "date", new java.sql.Date(endDate.getTime()));
        return loadCityHisDAO.query(dbQueryParamBuilder.build()).getDatas();
    }


    @Override
    public LoadCityHisDO getLoadCityHisDO(String cityId, String caliberId, Date date) throws Exception {
        return loadCityHisDAO.getLoadCityHisDOByOneDate(cityId, date, caliberId);
    }

    @Override
    public List<LoadCityHisDO> getListByDates(Date startDate, Date endDate, String caliberId) {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create()
                .where(QueryOp.DateNoLessThan, "date", new java.sql.Date(startDate.getTime()))
                .where(QueryOp.DateNoMoreThan, "date", new java.sql.Date(endDate.getTime()))
                .where(QueryOp.StringEqualTo, "caliberId", caliberId);
        return loadCityHisDAO.query(dbQueryParamBuilder.build()).getDatas();
    }

    @Override
    @Transactional
    public List<BigDecimal> loadCityHisAccumulation(Date date, String caliberId) throws Exception {
        List<BigDecimal> result = new ArrayList<>();
        // 地市累加
        List<LoadCityHisDO> loadHisList = this.findLoadCityList(date, caliberId);
        if (!CollectionUtils.isEmpty(loadHisList)) {
            //筛选九地市的数据
            loadHisList = loadHisList.stream().filter(item -> !Constants.PROVINCE_ID.equals(item.getCityId())).collect(Collectors.toList());
            Map<String, BigDecimal> valueAddMap = LoadCalUtil.addListObject(loadHisList,
                    Constants.LOAD_CURVE_POINT_NUM,
                    Constants.LOAD_CURVE_START_WITH_ZERO);
            LoadCityFcDO loadCityHisDO = new LoadCityFcDO();
            BasePeriodUtils.setAllFiled(loadCityHisDO, valueAddMap);
            result = loadCityHisDO.getloadList();
        }
        return result;
    }

    @Override
    public List<BigDecimal> getNetworkLoss(Date date) {
        // 地市累加
        List<LoadCityHisDO> loadHisList = this.findLoadCityList(date, Constants.CALIBER_QUAN);
        List<BigDecimal> provinceLoadList = new ArrayList<>();
        List<BigDecimal> cityLoadList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(loadHisList)) {
            provinceLoadList = loadHisList.stream().filter(item -> Constants.PROVINCE_ID.equals(item.getCityId()))
                    .map(x -> x.getloadList()).reduce((k1, k2) -> BigDecimalFunctions.listAdd(k1, k2)).get();
            cityLoadList = loadHisList.stream().filter(item -> !Constants.PROVINCE_ID.equals(item.getCityId()))
                    .map(x -> x.getloadList()).reduce((k1, k2) -> BigDecimalFunctions.listAdd(k1, k2)).get();

        }
        return BigDecimalFunctions.listSubtract(provinceLoadList, cityLoadList);
    }

    @Override
    public List<BigDecimal> getNetworkLossByDateList(List<Date> dates) {
        List<BigDecimal> result = new ArrayList<>();
        List<List<BigDecimal>> lists = new ArrayList<>();
        Collections.sort(dates);
        List<String> dateStrList = new ArrayList<>();
        dates.forEach(
                item -> {
                    dateStrList.add(DateUtil.getDateToStrFORMAT(item, "yyyyMMdd"));
                }
        );
        // 地市累加
        Date startDate = dates.get(0);
        Date endDate = dates.get(dates.size() - 1);
        List<LoadCityHisDO> loadHisList = this.getListByDates(startDate, endDate, Constants.CALIBER_QUAN);
        if (!CollectionUtils.isEmpty(loadHisList)) {
            Map<String, List<LoadCityHisDO>> dateStr = loadHisList.stream().collect(Collectors.groupingBy(t -> DateUtil.getDateToStrFORMAT(t.getDate(), "yyyyMMdd")));
            for (Map.Entry<String, List<LoadCityHisDO>> stringListEntry : dateStr.entrySet()) {
                String key = stringListEntry.getKey();
                if (dateStrList.contains(key)) {
                    List<LoadCityHisDO> value = stringListEntry.getValue();
                    List<BigDecimal> provinceLoadList = new ArrayList<>();
                    List<BigDecimal> cityLoadList = new ArrayList<>();
                    if (!CollectionUtils.isEmpty(value)) {
                        provinceLoadList = value.stream().filter(item -> Constants.PROVINCE_ID.equals(item.getCityId()))
                                .map(x -> x.getloadList()).reduce((k1, k2) -> BigDecimalFunctions.listAdd(k1, k2)).get();
                        cityLoadList = value.stream().filter(item -> !Constants.PROVINCE_ID.equals(item.getCityId()))
                                .map(x -> x.getloadList()).reduce((k1, k2) -> BigDecimalFunctions.listAdd(k1, k2)).get();
                        lists.add(BigDecimalFunctions.listSubtract(provinceLoadList, cityLoadList));
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(lists)) {
            List<BigDecimal> bigDecimals = lists.stream().map(x -> x).reduce((k1, k2) -> BigDecimalFunctions.listAdd(k1, k2)).get();
            result = BigDecimalFunctions.listDivideValue(bigDecimals, new BigDecimal(lists.size()));
        }
        return result;
    }


    @Override
    public List<CurveViewDTO> findCurveViewDTOS(Date date, String caliberId, String cityId, Integer type, String dataType) throws Exception {
        List<CurveViewDTO> result = new ArrayList<>();

        //今日预测负荷
        String[] normalAlgorithmId = settingSystemService.findByFieldId(SystemConstant.NORMAL_ALGORITHM).getValue().split(Constants.SEPARATOR_PUNCTUATION);
        List<LoadCityFcBatchDO> loadCityFcBatchDOS = loadCityFcBatchService.findOneByBatchTime(cityId, date, date, caliberId, normalAlgorithmId[0], "1");
        List<BigDecimal> todayFcLoads = BasePeriodUtils.toList(loadCityFcBatchDOS.get(0), Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        List<BigDecimal> todayFc = getBigDecimals(todayFcLoads);
        result.add(new CurveViewDTO(CurveViewDTO.TODAY_FC, todayFc));

        //今日历史负荷
        List<BigDecimal> todayHisLoads = this.find96LoadCityHisValue(type.equals(2) ? DateUtils.addDays(date, -1) : date, caliberId, cityId);
        List<BigDecimal> todayHis = getBigDecimals(todayHisLoads);
        result.add(new CurveViewDTO(CurveViewDTO.TODAY_HIS, todayHis));

        //昨日历史负荷
        List<BigDecimal> yesterHisLoads = this
                .find96LoadCityHisValue(DateUtils.addDays(date, -1), caliberId, cityId);
        List<BigDecimal> yesterHis = getBigDecimals(yesterHisLoads);
        result.add(new CurveViewDTO(CurveViewDTO.YESTARDAY_HIS, yesterHis));

        //预测温度
        List<BigDecimal> temperatureFcs = weatherCityFcService
                .find96WeatherCityFcValue(date, cityId, WeatherEnum.TEMPERATURE.getType());

        if (caliberId.equals("10")) {
            WeatherCityFcDO weatherCityFcDO = weatherCityFcService
                    .findWeatherCityFcDO(cityId, WeatherEnum.TEMPERATURE.getType(), date);
            if (weatherCityFcDO != null) {
                temperatureFcs = BasePeriodUtils.toList(weatherCityFcDO, Constants.WEATHER_CURVE_POINT_NUM,
                        Constants.WEATHER_CURVE_START_WITH_ZERO);
            } else {
                temperatureFcs = null;
            }
        }

        //1:查询标准站点预测温度   2:查询地市标准站点加权平均预测温度(仅福建)  3:福州站点平均(仅福建)
        if ("1".equals(dataType)) {
            List<WeatherStationFcBasicWgDO> hisWeatherBasic =
                    weatherStationFcBasicWgService.getStandardStationFcWeatherBasic(cityId, WeatherEnum.TEMPERATURE.getType(), date, date);
            temperatureFcs = CollectionUtils.isEmpty(hisWeatherBasic) ?
                    null : BasePeriodUtils.toList(hisWeatherBasic.get(0), Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO);
        } else if ("2".equals(dataType) && Constants.PROVINCE_ID.equals(cityId)) {
            List<WeatherCityFcDO> hisWeatherBasic =
                    weatherCityFcService.getProvinceWeightAvgFcWeatherData(WeatherEnum.TEMPERATURE.getType(), date, date);
            temperatureFcs = CollectionUtils.isEmpty(hisWeatherBasic) ?
                    null : BasePeriodUtils.toList(hisWeatherBasic.get(0), Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO);
        } else if ("3".equals(dataType) && Constants.PROVINCE_ID.equals(cityId)) {
            temperatureFcs = weatherCityFcService.find96WeatherCityFcValue(date, "2", WeatherEnum.TEMPERATURE.getType());
        }

        if (CollectionUtils.isEmpty(temperatureFcs)) {
            temperatureFcs = ColumnUtil
                    .getZeroOrNullList(Constants.LOAD_CURVE_POINT_NUM, null);
        }
        result.add(new CurveViewDTO(CurveViewDTO.TEMPERATURE_FC, temperatureFcs));

        //历史温度
        List<BigDecimal> temperatureHis = weatherCityHisService
                .find96WeatherCityHisValue(date, cityId, WeatherEnum.TEMPERATURE.getType());

        if (caliberId.equals("10")) {
            List<WeatherCityHisDO> weatherCityHisDOs = weatherCityHisService
                    .findWeatherCityHisDOSBySQLDate(cityId, WeatherEnum.TEMPERATURE.getType(), new java.sql.Date(date.getTime()), new java.sql.Date(date.getTime()));
            if (!CollectionUtils.isEmpty(weatherCityHisDOs)) {
                temperatureHis = BasePeriodUtils.toList(weatherCityHisDOs.get(0), Constants.WEATHER_CURVE_POINT_NUM,
                        Constants.WEATHER_CURVE_START_WITH_ZERO);
            } else {
                temperatureHis = null;
            }
        }

        //1:查询标准站点历史温度   2:查询地市标准站点加权平均历史温度(仅福建)  3:福州站点平均(仅福建)
        if ("1".equals(dataType)) {
            List<WeatherStationHisBasicWgDO> hisWeatherBasic =
                    weatherStationHisBasicWgService.getStandardStationHisWeatherBasic(cityId, WeatherEnum.TEMPERATURE.getType(), date, date);
            temperatureHis = CollectionUtils.isEmpty(hisWeatherBasic) ?
                    null : BasePeriodUtils.toList(hisWeatherBasic.get(0), Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO);
        } else if ("2".equals(dataType) && Constants.PROVINCE_ID.equals(cityId)) {
            List<WeatherCityHisDO> hisWeatherBasic =
                    weatherCityHisService.getProvinceWeightAvgHisWeatherData(WeatherEnum.TEMPERATURE.getType(), date, date);
            temperatureHis = CollectionUtils.isEmpty(hisWeatherBasic) ?
                    null : BasePeriodUtils.toList(hisWeatherBasic.get(0), Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO);
        } else if ("3".equals(dataType) && Constants.PROVINCE_ID.equals(cityId)) {
            temperatureHis = weatherCityHisService.find96WeatherCityHisValue(date, "2", WeatherEnum.TEMPERATURE.getType());
        }

        if (CollectionUtils.isEmpty(temperatureHis)) {
            temperatureHis = ColumnUtil
                    .getZeroOrNullList(Constants.LOAD_CURVE_POINT_NUM, null);
        }
        result.add(new CurveViewDTO(CurveViewDTO.TEMPERATURE_HIS, temperatureHis));
        CityDO cityById = cityService.findCityById(cityId);

        // 营销昨日实际
        List<BigDecimal> industryYesterHisLoads = industryCityLoadDayHisClctNewService.findByCondition(cityById.getOrgNo(), TRADE_ID_All, DateUtils.addDays(date, -1));
        result.add(new CurveViewDTO(CurveViewDTO.YESTERDAY_HIS_LOAD, industryYesterHisLoads));

        // 营销今日实际
        List<BigDecimal> todayIndustryLoad = industryCityLoadDayHisClctNewService.findByCondition(cityById.getOrgNo(), TRADE_ID_All, date);
        result.add(new CurveViewDTO(CurveViewDTO.TODAY_HIS_LOAD, todayIndustryLoad));

        // 营销预测
        List<IndustryCityLoadDayFcAlgoDO> byDateCodes = industryCityLoadDayFcAlgoService.findByDateOneCodes(
                cityById.getOrgNo(), TRADE_ID_All, date);
        List<BigDecimal> industryTodayFcLoads = ColumnUtil
                .getZeroOrNullList(Constants.LOAD_CURVE_POINT_NUM, null);
        if (!CollectionUtils.isEmpty(byDateCodes)) {
            IndustryCityLoadDayFcAlgoDO industryCityLoadDayFcAlgoDO = byDateCodes.get(0);
            industryTodayFcLoads = BigDecimalFunctions.listDivideValue(industryCityLoadDayFcAlgoDO.getloadList(), K);
        }
        result.add(new CurveViewDTO(CurveViewDTO.TODAY_FC_LOAD, industryTodayFcLoads));

        return result;
    }

    private List<BigDecimal> getBigDecimals(List<BigDecimal> yesterHisLoads) {
        List<BigDecimal> yesterHis = null;
        if (CollectionUtils.isEmpty(yesterHisLoads)) {
            yesterHis = ColumnUtil
                    .getZeroOrNullList(Constants.LOAD_CURVE_POINT_NUM, null);
        } else {
            yesterHis = new ArrayList<>();
            for (BigDecimal yesterHisLoad : yesterHisLoads) {
                yesterHis.add(yesterHisLoad);
            }
        }
        return yesterHis;
    }

    private List<BigDecimal> find96LoadCityHisValue(Date date, String caliberId, String cityId) throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create()
                .where(QueryOp.DateEqualTo, "date", new java.sql.Date(date.getTime()))
                .where(QueryOp.StringEqualTo, "caliberId", caliberId)
                .where(QueryOp.StringEqualTo, "cityId", cityId);
        List<LoadCityHisDO> LoadCityHisDOS = loadCityHisDAO.query(dbQueryParamBuilder.build()).getDatas();
        if (!CollectionUtils.isEmpty(LoadCityHisDOS)) {
            return BasePeriodUtils
                    .toList(LoadCityHisDOS.get(0), Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        }
        return null;
    }

    private void CalcAreaLoads(Map<String, String> cityMap, Map<String, List<BigDecimal>> areaLoadsMap,
                               LoadCityHisDO LoadCityHisDO) {
        String areaName = cityMap.get(LoadCityHisDO.getCityId());
        if (StringUtils.isNotBlank(areaName)) {
            List<BigDecimal> loads = areaLoadsMap.get(areaName);
            if (CollectionUtils.isEmpty(loads)) {
                loads = BasePeriodUtils
                        .toList(LoadCityHisDO, Constants.LOAD_CURVE_POINT_NUM_24, Constants.LOAD_CURVE_START_WITH_ZERO);
            } else {
                loads = DataUtil.listAdd(loads, BasePeriodUtils
                        .toList(LoadCityHisDO, Constants.LOAD_CURVE_POINT_NUM_24, Constants.LOAD_CURVE_START_WITH_ZERO));
            }
            areaLoadsMap.put(areaName, loads);
        }
    }

    private void setTodayHisLoads(Map<String, LoadCityHisDO> todayHisLoadsMap, String cityId, CityLoadDTO cityLoadDTO) {
        if (todayHisLoadsMap != null && !todayHisLoadsMap.isEmpty()) {
            LoadCityHisDO todayHisLoad = todayHisLoadsMap.get(cityId);
            if (todayHisLoad != null) {
                List<BigDecimal> todayHisLoads = BasePeriodUtils
                        .toList(todayHisLoad, Constants.LOAD_CURVE_POINT_NUM_24, Constants.LOAD_CURVE_START_WITH_ZERO);
                cityLoadDTO.setTodayHisLoads(todayHisLoads);
            }
        }
    }

    private void setYesterdayHisLoads(Map<String, LoadCityHisDO> yesterdayHisLoadsMap, String cityId,
                                      CityLoadDTO cityLoadDTO) {
        if (yesterdayHisLoadsMap != null && !yesterdayHisLoadsMap.isEmpty()) {
            LoadCityHisDO yesterdayHisLoad = yesterdayHisLoadsMap.get(cityId);
            if (yesterdayHisLoad != null) {
                List<BigDecimal> todayHisLoads = BasePeriodUtils
                        .toList(yesterdayHisLoad, Constants.LOAD_CURVE_POINT_NUM_24, Constants.LOAD_CURVE_START_WITH_ZERO);
                cityLoadDTO.setYesterdayHisLoads(todayHisLoads);
            }
        }
    }

    private void setTodayFcLoads(Map<String, LoadCityFcDO> todayFcMap, String cityId, CityLoadDTO cityLoadDTO,
                                 Date today, String caliberId)
            throws Exception {
        if (todayFcMap != null && !todayFcMap.isEmpty()) {
            LoadCityFcDO todayFcLoad = todayFcMap.get(cityId);
            if (todayFcLoad != null) {
                List<BigDecimal> todayFcLoads = BasePeriodUtils
                        .toList(todayFcLoad, Constants.LOAD_CURVE_POINT_NUM_24, Constants.LOAD_CURVE_START_WITH_ZERO);
                cityLoadDTO.setTodayFcLoads(todayFcLoads);
            }
            //如果上报的数据没有
            else {
                List<LoadCityFcDO> loadCityFcDO = this.loadCityFcService
                        .listLoadCityFc(cityId, caliberId, today, today, null);
                if (!CollectionUtils.isEmpty(loadCityFcDO)) {
                    //step1：返回系统推荐算法的数据
                    List<LoadCityFcDO> collect = loadCityFcDO.stream().filter(LoadCityFcDO::getRecommend)
                            .collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(collect)) {
                        List<BigDecimal> todayFcLoads = BasePeriodUtils
                                .toList(collect.get(0), Constants.LOAD_CURVE_POINT_NUM_24,
                                        Constants.LOAD_CURVE_START_WITH_ZERO);
                        cityLoadDTO.setTodayFcLoads(todayFcLoads);
                    }
                    //step2:随便返回一个预测的数据
                    else {
                        List<BigDecimal> todayFcLoads = BasePeriodUtils
                                .toList(loadCityFcDO.get(0), Constants.LOAD_CURVE_POINT_NUM_24,
                                        Constants.LOAD_CURVE_START_WITH_ZERO);
                        cityLoadDTO.setTodayFcLoads(todayFcLoads);
                    }
                }
            }
        }
    }

    private void setTodayTemperatures(Map<String, WeatherCityHisDO> todayTemperaturesHisVOMap, String cityId,
                                      CityLoadDTO cityLoadDTO) {
        if (todayTemperaturesHisVOMap != null && !todayTemperaturesHisVOMap.isEmpty()) {
            WeatherCityHisDO todayTemperaturesHisVO = todayTemperaturesHisVOMap.get(cityId);
            if (todayTemperaturesHisVO != null) {
                List<BigDecimal> todayTemperatures = BasePeriodUtils
                        .toList(todayTemperaturesHisVO, Constants.LOAD_CURVE_POINT_NUM_24,
                                Constants.LOAD_CURVE_START_WITH_ZERO);
                cityLoadDTO.setTemperatures(todayTemperatures);
            }
        }
    }

    private Map<String, WeatherCityHisDO> getWeatherCityVOMap(Date date, List<String> cityIds) {
        Map<String, WeatherCityHisDO> todayWeatherMap = null;
        try {
            List<WeatherCityHisDO> todayWeathers = weatherCityHisService
                    .findWeatherCityHisDOSByCityIds(cityIds, WeatherEnum.TEMPERATURE.getType(), date);
            if (!CollectionUtils.isEmpty(todayWeathers)) {
                todayWeatherMap = todayWeathers.stream()
                        .collect(Collectors.toMap(WeatherCityHisDO::getCityId, Function.identity()));
            }
        } catch (Exception e) {
            logger.error("查询历史气象异常...", e);
        }
        return todayWeatherMap;
    }

    private Map<String, LoadCityFcDO> getLoadCityFcDOMap(Date today, List<String> cityIds, String caliberId)
            throws Exception {
        Map<String, LoadCityFcDO> loadFcMap = new HashMap<>(16);
        for (String cityId : cityIds) {
            LoadCityFcDO loadCityFcValue = this.loadCityFcService.find96LoadCityFcValue(today, caliberId, cityId);
            if (loadCityFcValue != null) {
                loadFcMap.put(cityId, loadCityFcValue);
            }
        }
        return loadFcMap;
    }

    private Map<String, LoadCityHisDO> getLoadCityHisDOMap(Date today, List<String> cityIds, String caliberId) {
        Map<String, LoadCityHisDO> hisLoadMap = null;
        try {
            List<LoadCityHisDO> todayHisLoads = findLoadCityHisDO(today, cityIds, caliberId);
            if (!CollectionUtils.isEmpty(todayHisLoads)) {
                hisLoadMap = todayHisLoads.stream()
                        .collect(Collectors.toMap(LoadCityHisDO::getCityId, Function.identity()));
            }
        } catch (Exception e) {
            logger.error("查询历史负荷异常...", e);
        }
        return hisLoadMap;
    }

    public List<LoadCityHisDO> findLoadCityHisDO(Date date, List<String> cityIds, String caliberId) throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create()
                .where(QueryOp.DateEqualTo, "date", new java.sql.Date(date.getTime()))
                .where(QueryOp.StringIsIn, "cityId", cityIds)
                .where(QueryOp.StringEqualTo, "caliberId", caliberId);
        return loadCityHisDAO.query(dbQueryParamBuilder.build()).getDatas();

    }

    public List<LoadCityHisDO> findLoadCityHisDO(Date date, String caliberId) throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create()
                .where(QueryOp.DateEqualTo, "date", new java.sql.Date(date.getTime()))
                .where(QueryOp.StringEqualTo, "caliberId", caliberId);
        return loadCityHisDAO.query(dbQueryParamBuilder.build()).getDatas();

    }


    private List<BigDecimal> getList(LoadCityFcDO LoadCityFcDO) throws Exception {
        if (LoadCityFcDO != null) {
            return BasePeriodUtils
                    .toList(LoadCityFcDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        } else {
            return LoadCalUtil.getNullList(Constants.LOAD_CURVE_POINT_NUM);
        }
    }

    @Override
    public List<BaseLoadIntervalDO> selectListData(String cityId, String caliberId, Date startDate,
                                                   Date endDate) throws Exception {
        List<BaseLoadIntervalDO> resultList = new ArrayList<>();
        List<LoadCityHisDO> hisDOS = findLoadCityHisDOS(cityId, startDate, endDate, caliberId);
        for (LoadCityHisDO his : hisDOS) {
            BaseLoadIntervalDO interval = new BaseLoadIntervalDO();
            BeanUtils.copyProperties(his, interval);
            interval.setAllTimeList(
                    DateUtil.getAllTimeListByInterval(Constants.MINUTE, 15, Constants.LOAD_CURVE_START_WITH_ZERO));
            interval.setHisLoadList(BasePeriodUtils
                    .toList(his, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            resultList.add(interval);
        }
        return resultList;
    }

    @Override
    public void doInsertOrUpdateNotNull(LoadCityHisDO LoadCityHisDO) throws Exception {
        LoadCityHisDO LoadCityHisDOTemp = loadCityHisDAO
                .getLoadCityHisDOByOneDate(LoadCityHisDO.getCityId(), LoadCityHisDO.getDate(), LoadCityHisDO.getCaliberId());
        if (LoadCityHisDOTemp == null) {
            loadCityHisDAO.createAndFlush(LoadCityHisDO);
            return;
        }
        loadCityHisDAO.getSession().flush();
        loadCityHisDAO.getSession().clear();
        String id = LoadCityHisDOTemp.getId();
        Map<String, String> describe = org.apache.commons.beanutils.BeanUtils.describe(LoadCityHisDO);
        // 移除null值
        describe.values().removeIf(Objects::isNull);
        // 复制非null属性
        org.apache.commons.beanutils.BeanUtils.populate(LoadCityHisDOTemp, describe);
        LoadCityHisDOTemp.setUpdatetime(new Timestamp(System.currentTimeMillis()));
        LoadCityHisDOTemp.setId(id);
        loadCityHisDAO.updateAndFlush(LoadCityHisDOTemp);
    }
}
