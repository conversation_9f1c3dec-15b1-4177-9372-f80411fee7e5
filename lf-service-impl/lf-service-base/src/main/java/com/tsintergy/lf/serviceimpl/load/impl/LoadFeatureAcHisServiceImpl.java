/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.load.impl;

import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.AcLoadReportD5000Service;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.FoundationLoadHisMonthService;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.LoadAcHisBasicService;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.LoadFeatureAcHisService;
import com.tsintergy.lf.serviceapi.base.airconditioner.dto.AcFeatureAnalyseDTO;
import com.tsintergy.lf.serviceapi.base.airconditioner.dto.AcFeatureStatisticsDTO;
import com.tsintergy.lf.serviceapi.base.airconditioner.dto.AcLoadCurveDTO;
import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.AcLoadReportD5000DO;
import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.FoundationLoadHisMonthDO;
import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.LoadAcHisBasicDO;
import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.LoadFeatureAcHisDO;
import com.tsintergy.lf.serviceapi.base.assess.api.SettingAssessService;
import com.tsintergy.lf.serviceapi.base.assess.pojo.SettingAssessDO;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.forecast.api.ForecastDataService;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadFeatureAcHisDAO;
import com.tsintergy.lf.serviceimpl.system.dao.SettingSystemDAO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Description:<br>
 *
 * @Author:<EMAIL>
 * @Date: 2022/5/23 15:13
 * @Version:1.0.0
 */
@Service("loadFeatureAcHisService")
@Slf4j
public class LoadFeatureAcHisServiceImpl implements LoadFeatureAcHisService {

    @Autowired
    LoadFeatureAcHisDAO loadFeatureAcHisDAO;

    @Autowired
    WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    LoadAcHisBasicService loadAcHisBasicService;

    @Autowired
    LoadFeatureAcHisService loadFeatureAcHisService;

    @Autowired
    FoundationLoadHisMonthService foundationLoadHisMonthService;

    @Autowired
    WeatherCityHisService weatherCityHisService;

    @Autowired
    CityService cityService;

    @Autowired
    private SettingSystemDAO settingSystemDAO;

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private SettingAssessService settingAssessService;

    @Autowired
    private ForecastDataService forecastDataService;

    // 前端传递体感温度类型
    private static final List<String> TYPE_LIST = Arrays.asList("7", "8", "9");

    private static final List<String> MAX_LIST = Arrays.asList("1", "4", "7");
    private static final List<String> AVE_LIST = Arrays.asList("2", "5", "8");
    private static final List<String> MIN_LIST = Arrays.asList("3", "6", "9");

    @Override
    public List<AcFeatureAnalyseDTO> getLoadFeatureAcHis(String cityId, String caliberId, Date startDate,
        Date endDate, String loadType, String weatherType, String weatherId) throws Exception {
        //日特性
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisStatDOList = new ArrayList<>();
        String targetCityId = "1".equals(weatherId) ? cityId : "2";
        Integer targetType = TYPE_LIST.contains(weatherType) ? 6 : 2;
        // 查询气象数据（类型固定为2）
        List<WeatherCityHisDO> hisWeather = forecastDataService.findHisWeather(targetCityId, targetType, startDate, endDate);
        if (CollectionUtils.isNotEmpty(hisWeather)) {
            weatherFeatureCityDayHisStatDOList = listWeatherFeatureCityDayHisDO(hisWeather);
        }
        List<LoadFeatureAcHisDO> loadFeatureAcHisDOList = loadFeatureAcHisDAO.getLoadFeatureCityDayHisDO(cityId, startDate, endDate, caliberId);
        //将气象的日期和最高温放到map中
        Map<Date, BigDecimal> highestTemperatureMap = new HashMap<>();
        Map<Date, BigDecimal> aveTemperatureMap = new HashMap<>();
        Map<Date, BigDecimal> lowestTemperatureMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(weatherFeatureCityDayHisStatDOList)) {
            Map<Date, BigDecimal> highestTemperatureDateMap = weatherFeatureCityDayHisStatDOList.stream().collect(
                Collectors.toMap(WeatherFeatureCityDayHisDO::getDate,
                    weatherFeatureCityDayHisDO -> weatherFeatureCityDayHisDO.getHighestTemperature() == null
                        ? new BigDecimal(0) : weatherFeatureCityDayHisDO.getHighestTemperature()));
            highestTemperatureDateMap.forEach((k, v) ->
                highestTemperatureMap.put(k, v)
            );
            Map<Date, BigDecimal> aveTemperatureDateMap = weatherFeatureCityDayHisStatDOList.stream().collect(
                    Collectors.toMap(WeatherFeatureCityDayHisDO::getDate,
                            weatherFeatureCityDayHisDO -> weatherFeatureCityDayHisDO.getAveTemperature() == null
                                    ? new BigDecimal(0) : weatherFeatureCityDayHisDO.getAveTemperature()));
            aveTemperatureDateMap.forEach((k, v) ->
                    aveTemperatureMap.put(k, v)
            );
            Map<Date, BigDecimal> lowestTemperatureDateMap = weatherFeatureCityDayHisStatDOList.stream().collect(
                    Collectors.toMap(WeatherFeatureCityDayHisDO::getDate,
                            weatherFeatureCityDayHisDO -> weatherFeatureCityDayHisDO.getLowestTemperature() == null
                                    ? new BigDecimal(0) : weatherFeatureCityDayHisDO.getLowestTemperature()));
            lowestTemperatureDateMap.forEach((k, v) ->
                    lowestTemperatureMap.put(k, v)
            );
        }

        List<AcFeatureAnalyseDTO> acFeatureAnalyseDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(loadFeatureAcHisDOList)) {
            loadFeatureAcHisDOList.forEach(powerFeatureCityDayHisStatDO -> {
                AcFeatureAnalyseDTO acFeatureAnalyseDTO = new AcFeatureAnalyseDTO();
                BeanUtils.copyProperties(powerFeatureCityDayHisStatDO, acFeatureAnalyseDTO);
                acFeatureAnalyseDTO
                        .setHighestTemperature(highestTemperatureMap.get(powerFeatureCityDayHisStatDO.getDate()));
                if (MAX_LIST.contains(weatherType)) {
                    acFeatureAnalyseDTO
                            .setHighestTemperature(highestTemperatureMap.get(powerFeatureCityDayHisStatDO.getDate()));
                } else if (AVE_LIST.contains(weatherType)) {
                    acFeatureAnalyseDTO
                            .setHighestTemperature(aveTemperatureMap.get(powerFeatureCityDayHisStatDO.getDate()));
                } else if (MIN_LIST.contains(weatherType)) {
                    acFeatureAnalyseDTO
                            .setHighestTemperature(lowestTemperatureMap.get(powerFeatureCityDayHisStatDO.getDate()));
                }
                if ("2".equals(loadType)) {
                    // 午间最小负荷
                    acFeatureAnalyseDTO.setMaxLoad(powerFeatureCityDayHisStatDO.getMinimumNoonLoad());
                } else if ("3".equals(loadType)) {
                    // 夜间最小负荷
                    acFeatureAnalyseDTO.setMaxLoad(powerFeatureCityDayHisStatDO.getMinimumNightLoad());
                } else if ("4".equals(loadType)) {
                    // 保供时段平均负荷
                    acFeatureAnalyseDTO.setMaxLoad(powerFeatureCityDayHisStatDO.getBgAveLoad());
                }
                acFeatureAnalyseDTOList.add(acFeatureAnalyseDTO);
            });
        }
        return acFeatureAnalyseDTOList;
    }


    @Override
    public AcFeatureStatisticsDTO doStatisticsAcFeature(String cityId, String caliberId, Date date, String loadType,
                                                        String weatherType, String weatherId) throws Exception {
        //去年今日
        Date lastYear = DateUtils.addYears(date, -1);
        //日特性
        List<LoadFeatureAcHisDO> loadFeatureAcHisDOList = loadFeatureAcHisDAO
            .getLoadFeatureCityDayHisDO(cityId, date, date, caliberId);

        List<LoadFeatureAcHisDO> lastYearLoadFeatureAcHisDOList = loadFeatureAcHisDAO
            .getLoadFeatureCityDayHisDO(cityId, lastYear, lastYear, caliberId);

        cityId = cityService.findWeatherCityId(cityId);
        WeatherFeatureCityDayHisDO featureCityHisVOByDate = new WeatherFeatureCityDayHisDO();
        WeatherFeatureCityDayHisDO lastYearFeatureCityHisVOByDate = new WeatherFeatureCityDayHisDO();


        String targetCityId = "1".equals(weatherId) ? cityId : "2";
        Integer targetType = TYPE_LIST.contains(weatherType) ? 6 : 2;
        // 查询气象数据（类型固定为2）
        List<WeatherCityHisDO> hisWeather = forecastDataService.findHisWeather(targetCityId, targetType, date, date);
        if (CollectionUtils.isNotEmpty(hisWeather)) {
            featureCityHisVOByDate = listWeatherFeatureCityDayHisDO(hisWeather).get(0);
        }
        List<WeatherCityHisDO> hisWeather1 = forecastDataService.findHisWeather(targetCityId, targetType, lastYear, lastYear);
        if (CollectionUtils.isNotEmpty(hisWeather1)) {
            lastYearFeatureCityHisVOByDate = listWeatherFeatureCityDayHisDO(hisWeather1).get(0);
        }

        AcFeatureStatisticsDTO acFeatureStatisticsDTO = new AcFeatureStatisticsDTO();
        if (CollectionUtils.isNotEmpty(loadFeatureAcHisDOList)) {
            LoadFeatureAcHisDO loadFeatureAcHisDO = loadFeatureAcHisDOList.get(0);
            BeanUtils.copyProperties(loadFeatureAcHisDO, acFeatureStatisticsDTO);
            if ("1".equals(loadType)) {
                acFeatureStatisticsDTO.setLastYearMaxLoadProportion(loadFeatureAcHisDO.getMaxLoadProportion());
            } else if ("2".equals(loadType)) {
                // 午间最小负荷
                if (loadFeatureAcHisDO.getMinimumNoonLoad() != null) {
                    acFeatureStatisticsDTO.setMaxLoadProportion(loadFeatureAcHisDO.getMinimumNoonLoad().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP));
                }
            } else if ("3".equals(loadType)) {
                // 夜间最小负荷
                if (loadFeatureAcHisDO.getMinimumNightLoad() != null) {
                    acFeatureStatisticsDTO.setMaxLoadProportion(loadFeatureAcHisDO.getMinimumNightLoad().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP));
                }
            } else if ("4".equals(loadType)) {
                // 保供时段平均负荷
                if (loadFeatureAcHisDO.getBgAveLoad() != null) {
                    acFeatureStatisticsDTO.setMaxLoadProportion(loadFeatureAcHisDO.getBgAveLoad().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP));
                }
            }
        }
        if (CollectionUtils.isNotEmpty(lastYearLoadFeatureAcHisDOList)) {
            LoadFeatureAcHisDO lastYearLoadFeatureAcHisDO = lastYearLoadFeatureAcHisDOList.get(0);
            acFeatureStatisticsDTO.setLastYearMaxLoad(lastYearLoadFeatureAcHisDO.getMaxLoad());
            acFeatureStatisticsDTO.setLastYearMinLoad(lastYearLoadFeatureAcHisDO.getMinLoad());
            acFeatureStatisticsDTO.setLastYearAvgLoad(lastYearLoadFeatureAcHisDO.getAvgLoad());
            if ("1".equals(loadType)) {
                acFeatureStatisticsDTO.setLastYearMaxLoadProportion(lastYearLoadFeatureAcHisDO.getMaxLoadProportion());
            } else if ("2".equals(loadType)) {
                // 午间最小负荷
                if (lastYearLoadFeatureAcHisDO.getMinimumNoonLoad() != null) {
                    acFeatureStatisticsDTO.setLastYearMaxLoadProportion(lastYearLoadFeatureAcHisDO.getMinimumNoonLoad().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP));
                }
            } else if ("3".equals(loadType)) {
                // 夜间最小负荷
                if (lastYearLoadFeatureAcHisDO.getMinimumNightLoad() != null) {
                    acFeatureStatisticsDTO.setLastYearMaxLoadProportion(lastYearLoadFeatureAcHisDO.getMinimumNightLoad().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP));
                }
            } else if ("4".equals(loadType)) {
                // 保供时段平均负荷
                if (lastYearLoadFeatureAcHisDO.getBgAveLoad() != null) {
                    acFeatureStatisticsDTO.setLastYearMaxLoadProportion(lastYearLoadFeatureAcHisDO.getBgAveLoad().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP));
                }
            }
            acFeatureStatisticsDTO.setLastYearEnergy(lastYearLoadFeatureAcHisDO.getEnergy());
        }
        if (MAX_LIST.contains(weatherType)) {
            acFeatureStatisticsDTO.setHighestTemperature(
                    Optional.ofNullable(featureCityHisVOByDate).orElse(new WeatherFeatureCityDayHisDO())
                            .getHighestTemperature());
            acFeatureStatisticsDTO.setLastYearHighestTemperature(
                    Optional.ofNullable(lastYearFeatureCityHisVOByDate).orElse(new WeatherFeatureCityDayHisDO())
                            .getHighestTemperature());
        } else if (AVE_LIST.contains(weatherType)) {
            acFeatureStatisticsDTO.setHighestTemperature(
                    Optional.ofNullable(featureCityHisVOByDate).orElse(new WeatherFeatureCityDayHisDO())
                            .getAveTemperature());
            acFeatureStatisticsDTO.setLastYearHighestTemperature(
                    Optional.ofNullable(lastYearFeatureCityHisVOByDate).orElse(new WeatherFeatureCityDayHisDO())
                            .getAveTemperature());
        } else if (MIN_LIST.contains(weatherType)) {
            acFeatureStatisticsDTO.setHighestTemperature(
                    Optional.ofNullable(featureCityHisVOByDate).orElse(new WeatherFeatureCityDayHisDO())
                            .getLowestTemperature());
            acFeatureStatisticsDTO.setLastYearHighestTemperature(
                    Optional.ofNullable(lastYearFeatureCityHisVOByDate).orElse(new WeatherFeatureCityDayHisDO())
                            .getLowestTemperature());
        }

        return acFeatureStatisticsDTO;
    }

    @Autowired
    AcLoadReportD5000Service acLoadReportD5000Service;

    @Override
    public AcLoadCurveDTO getLoadCurve(String cityId, String caliberId, Date startDate, Date endDate, String weatherId) {
        AcLoadCurveDTO acLoadCurveDTO = new AcLoadCurveDTO();
        //初始化一个长度96，值为NULL的list
        final ArrayList<BigDecimal> emptyList = new ArrayList<>(96);
        IntStream.range(0, 96).forEach(i -> emptyList.add(null));
        //查询空调负荷特性
        List<LoadAcHisBasicDO> loadAcHisBasicDOList = loadAcHisBasicService
            .getloadAcHisBasicDOList(cityId, caliberId, startDate, endDate);
        List<BigDecimal> acLoad = new ArrayList<>();
        Map<Date, LoadAcHisBasicDO> mapData = loadAcHisBasicDOList.stream()
            .collect(Collectors.toMap(LoadAcHisBasicDO::getDate, Function.identity(), (o, n) -> n));
        List<Date> listDate = DateUtil.getListBetweenDay(startDate, endDate);
        for (Date date : listDate) {
            LoadAcHisBasicDO loadAcHisBasicDO = mapData.get(date);
            if (loadAcHisBasicDO == null) {
                acLoad.addAll(emptyList);
            } else {
                acLoad.addAll(BasePeriodUtils
                    .toList(loadAcHisBasicDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            }
        }

        List<BigDecimal> baseLoad = new ArrayList<>();
        List<BigDecimal> temperature = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        listDate.forEach(date -> {
            calendar.setTime(date);
            String month = String.valueOf(calendar.get(Calendar.MONTH) + 1);
            String monthStr = month.length() == 1 ? "0" + month : month;
            String year = String.valueOf(calendar.get(Calendar.YEAR));

            //上报的基础负荷
            List<AcLoadReportD5000DO> acLoadReportD5000DOS = acLoadReportD5000Service
                .getAcLoadReportD5000DO(cityId, caliberId, year, monthStr, !DateUtil.isWeekend(date) ? 1 : 2,null);

            //基础负荷
            List<FoundationLoadHisMonthDO>  foundationLoadHisMonthDOList = null;
            if(CollectionUtils.isEmpty(acLoadReportD5000DOS)){
                foundationLoadHisMonthDOList = foundationLoadHisMonthService
                    .getFoundationLoadHisMonth(cityId, caliberId, year, monthStr, !DateUtil.isWeekend(date) ? 1 : 2);
            }else {
                foundationLoadHisMonthDOList = new ArrayList<>();
                for(AcLoadReportD5000DO acLoadReportD5000DO:acLoadReportD5000DOS){
                    FoundationLoadHisMonthDO foundationLoadHisMonthDO = new FoundationLoadHisMonthDO();
                    BeanUtils.copyProperties(acLoadReportD5000DO,foundationLoadHisMonthDO);
                    foundationLoadHisMonthDOList.add(foundationLoadHisMonthDO);
                }
            }

            if (CollectionUtils.isNotEmpty(foundationLoadHisMonthDOList)) {
                baseLoad
                    .addAll(BasePeriodUtils.toList(foundationLoadHisMonthDOList.get(0), Constants.LOAD_CURVE_POINT_NUM,
                        Constants.LOAD_CURVE_START_WITH_ZERO));
            } else {
                baseLoad.addAll(emptyList);
            }


            //温度曲线
            try {
                /*List<BigDecimal> tempList = weatherCityHisService
                    .find96WeatherCityHisValue(date, cityId, WeatherEnum.TEMPERATURE.getType());*/
                List<BigDecimal> tempList = new ArrayList<>();
                String targetCityId = "1".equals(weatherId) ? cityId : "2";
                List<WeatherCityHisDO> hisWeather = forecastDataService.findHisWeather(targetCityId, 2, date, date);
                if (CollectionUtils.isNotEmpty(hisWeather)) {
                    tempList = hisWeather.get(0).getloadList();
                }
                temperature.addAll(CollectionUtils.isNotEmpty(tempList) ? tempList : emptyList);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        acLoadCurveDTO.setTemperature(temperature);
        acLoadCurveDTO.setAcLoad(acLoad);
        acLoadCurveDTO.setBaseLoad(baseLoad);
        return acLoadCurveDTO;
    }

    @Override
    public void doHisLoadFeatureCityDay(Date startDate, Date endDate) throws Exception {
        // 考核点配置
        Map<String, List<SettingAssessDO>> assessSettingByData = settingAssessService.findAssessSettingByData(startDate, endDate, Constants.CALIBER_CITY_ID);
        //空调历史负荷信息
        List<LoadAcHisBasicDO> loadAcHisBasicDOS = loadAcHisBasicService.getloadAcHisBasicDOList(null, null, startDate,
            endDate);
        if (CollectionUtils.isNotEmpty(loadAcHisBasicDOS)) {
            List<LoadCityHisDO> loadCityHisDOS = loadCityHisService.findLoadCityHisDOS(null, startDate, endDate, null);

            // 预测负荷list to map
            Map<String, LoadCityHisDO> loadCityHisVOMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(loadCityHisDOS)) {
                for (LoadCityHisDO loadCityHisDO : loadCityHisDOS) {
                    String key =
                        loadCityHisDO.getCityId() + "-" + loadCityHisDO.getCaliberId() + "-" + loadCityHisDO.getDate()
                            .getTime();
                    loadCityHisVOMap.put(key, loadCityHisDO);
                }
            }
            for (LoadAcHisBasicDO loadAcHisBasicDO : loadAcHisBasicDOS) {
                String cityId = loadAcHisBasicDO.getCityId();
                String caliberId = loadAcHisBasicDO.getCaliberId();
                java.sql.Date date = loadAcHisBasicDO.getDate();

                String key = cityId + "-" + caliberId + "-" + date.getTime();
                LoadCityHisDO loadCityHisDO = loadCityHisVOMap.get(key);
                // 年月
                String yearMonth = DateUtil.getDateToStrFORMAT(date, "yyyy-MM-dd").substring(0, 7);
                List<SettingAssessDO> settingAssessDOS = new ArrayList<>();
                if (MapUtils.isNotEmpty(assessSettingByData)) {
                    settingAssessDOS = assessSettingByData.get(yearMonth);
                }
                List<String> noonList = new ArrayList<>();
                List<String> nightList = new ArrayList<>();
                List<String> bgList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(settingAssessDOS)) {
                    Map<String, List<SettingAssessDO>> collect = settingAssessDOS.stream().collect(Collectors.groupingBy(t -> t.getAssessName()));
                    List<SettingAssessDO> settingAssessDOS1 = collect.get("午间最小负荷");
                    if (CollectionUtils.isNotEmpty(settingAssessDOS1)) {
                        String timeStr = settingAssessDOS1.get(0).getStartTime() + "~" + settingAssessDOS1.get(0).getEndTime();
                        noonList = getPeakList(timeStr);
                    }
                    List<SettingAssessDO> settingAssessDOS2 = collect.get("夜间最小负荷");
                    if (CollectionUtils.isNotEmpty(settingAssessDOS2)) {
                        String timeStr = settingAssessDOS2.get(0).getStartTime() + "~" + settingAssessDOS2.get(0).getEndTime();
                        nightList = getPeakList(timeStr);
                    }
                    List<SettingAssessDO> settingAssessDOS3 = collect.get("保供关键时段");
                    if (CollectionUtils.isNotEmpty(settingAssessDOS3)) {
                        String timeStr = settingAssessDOS3.get(0).getStartTime() + "~" + settingAssessDOS3.get(0).getEndTime();
                        bgList = getPeakList(timeStr);
                    }
                }
                LoadFeatureAcHisDO loadFeatureAcHisDO = this.findStatisticsDayFeature(loadAcHisBasicDO, loadCityHisDO, noonList, nightList, bgList);
                List<LoadFeatureAcHisDO> loadFeatureCityDayHisDO = loadFeatureAcHisDAO.getLoadFeatureCityDayHisDO(
                    cityId, date, date, caliberId);
                if (CollectionUtils.isNotEmpty(loadFeatureCityDayHisDO)) {
                    LoadFeatureAcHisDO loadFeatureAcHisDO1 = loadFeatureCityDayHisDO.get(0);
                    if (loadFeatureAcHisDO1 != null && StringUtils.isNotEmpty(loadFeatureAcHisDO1.getId())) {
                        loadFeatureAcHisDAO.delete(loadFeatureAcHisDO1);
                    }
                }
                loadFeatureAcHisDAO.saveOrUpdate(loadFeatureAcHisDO);
            }
        }
    }

    private List<String> getPeakList(String timeStr) throws Exception {
        List<String> times = new ArrayList<String>();
        if (StringUtils.isNotEmpty(timeStr)) {
            for (String time : timeStr.split(",")) {
                String[] startAndEndcolumns = time.split("~");
                if (startAndEndcolumns.length == 2) {
                    List<String> columns = ColumnUtil.getColumnsBetween(startAndEndcolumns[0].replace(":", ""),
                            startAndEndcolumns[1].replace(":", ""), Constants.LOAD_CURVE_POINT_NUM,
                            Constants.LOAD_CURVE_START_WITH_ZERO, false);
                    times.addAll(columns);
                }
            }
        }
        return times;
    }

    private LoadFeatureAcHisDO findStatisticsDayFeature(LoadAcHisBasicDO loadAcHisBasicDO, LoadCityHisDO loadCityHisDO,
                                                         List<String> noonList, List<String> nightList, List<String> bgList)
        throws Exception {
        if (loadAcHisBasicDO == null) {
            return null;
        }
        List<BigDecimal> peaks = new ArrayList<BigDecimal>();
        List<String> peakSectionTimes = settingSystemDAO.getPeakSectionTime();
        List<BigDecimal> noonPeak = new ArrayList<BigDecimal>();
        List<BigDecimal> nightPeak = new ArrayList<BigDecimal>();
        List<BigDecimal> bgPeak = new ArrayList<BigDecimal>();
        // 最小负荷发生时刻
        String minTime = null;
        // 最大负荷发生时刻
        String maxTime = null;
        Map<String, BigDecimal> loadMap = BasePeriodUtils.toMap(loadAcHisBasicDO, Constants.LOAD_CURVE_POINT_NUM,
            Constants.LOAD_CURVE_START_WITH_ZERO);
        List<BigDecimal> loadList = BasePeriodUtils.toList(loadAcHisBasicDO, Constants.LOAD_CURVE_POINT_NUM,
            Constants.LOAD_CURVE_START_WITH_ZERO);
        Map<String, BigDecimal> maxMixAvg = BasePeriodUtils.getMaxMinAvg(loadList, 4);
        for (String column : loadMap.keySet()) {
            BigDecimal load = loadMap.get(column);
            if (null != load) {
                column = column.substring(1);
                if (load.compareTo(maxMixAvg.get("max")) == 0) {
                    maxTime = column;
                }
                if (peakSectionTimes != null && peakSectionTimes.contains(column)) {
                    peaks.add(load);
                }
                if (load.compareTo(maxMixAvg.get("min")) == 0) {
                    minTime = column;
                }
                if (CollectionUtils.isNotEmpty(noonList) && noonList.contains(column)) {
                    noonPeak.add(load);
                }
                if (CollectionUtils.isNotEmpty(nightList) && nightList.contains(column)) {
                    nightPeak.add(load);
                }
                if (CollectionUtils.isNotEmpty(bgList) && bgList.contains(column)) {
                    bgPeak.add(load);
                }
            }
        }
        LoadFeatureAcHisDO result = new LoadFeatureAcHisDO();
        result.setDate(loadAcHisBasicDO.getDate());
        result.setCityId(loadAcHisBasicDO.getCityId());
        result.setCaliberId(loadAcHisBasicDO.getCaliberId());
//        result.setAlgorithmId(null);
//        result.setAlgorithmName(null);
        result.setMaxLoad(maxMixAvg.get("max"));
        result.setMinLoad(maxMixAvg.get("min"));
        result.setAvgLoad(maxMixAvg.get("avg"));
        if (maxTime != null) {
            result.setMaxTime(new StringBuffer(maxTime).insert(2, ":").toString());
        }
        if (minTime != null) {
            result.setMinTime(new StringBuffer(minTime).insert(2, ":").toString());
        }
//        // 峰谷差 = 日最大负荷 – 日最小负荷
//        result.setDifferent(BigDecimalUtils.sub(result.getMaxLoad(), result.getMinLoad()));
//        // 峰谷差率 = （日最大负荷 – 日最小负荷）/日最大负荷
//        if (result.getMaxLoad().compareTo(BigDecimal.ZERO) != 0) {
//            result.setGradient(BigDecimalUtils.divide(result.getDifferent(), result.getMaxLoad(), 4));
//        }
        // 积分电量 = 96点负荷之和/4
//        result.setIntegralLoad(BigDecimalUtils.divide(BigDecimalUtils.addAllValue(loadList), new BigDecimal(4), 4));
//        // 段峰电量 = 尖峰时段在数据库setting_system_init中，默认值为08：00~22:00
//        result.setPeakSectionLoad(BigDecimalUtils.divide(BigDecimalUtils.addAllValue(peaks), new BigDecimal(4), 4));
        String time = result.getMaxTime() == null ? "" : result.getMaxTime().replace(":", "");
        BigDecimal value = null;
        try {
            Method m = loadCityHisDO.getClass().getMethod("getT" + time);
            value = (BigDecimal) m.invoke(loadCityHisDO);
        } catch (NoSuchMethodException e) {
            log.error("空调负荷特性定时：无法获取反射属性：【{}】", e);
        } catch (NullPointerException e) {
            log.error("空调负荷特性定时：无法获取反射属性：【{}】", e);
        }

        if (value != null && value.compareTo(BigDecimal.ZERO) != 0) {
            // 最大空调负荷占比=（空调负荷最大值/空调负荷最大值对应时刻的总负荷）*100%
            result.setMaxLoadProportion(result.getMaxLoad().divide(value, 4, RoundingMode.HALF_UP));
        }
        // 累积电量=平均负荷×24
        result.setEnergy(result.getAvgLoad() == null ? null : result.getAvgLoad().multiply(new BigDecimal(24)));
        // 新增午间最小负荷、夜间最小负荷、保供时段平均负荷
        result.setMinimumNoonLoad(BigDecimalUtils.getMin(noonPeak));
        result.setMinimumNightLoad(BigDecimalUtils.getMin(nightPeak));
        BigDecimal bigDecimal = BigDecimalUtils.addAllValue(bgPeak);
        if (CollectionUtils.isNotEmpty(bgPeak)) {
            result.setBgAveLoad(bigDecimal.divide(BigDecimal.valueOf(bgPeak.size()), 4, RoundingMode.HALF_UP));
        } else {
            // 根据业务需求设置默认值，这里设置为 null
            result.setBgAveLoad(null);
        }
        return result;
    }

    private List<WeatherFeatureCityDayHisDO> listWeatherFeatureCityDayHisDO(List<WeatherCityHisDO> weatherCityHisDOS) {
        List<WeatherFeatureCityDayHisDO> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(weatherCityHisDOS)) {
            weatherCityHisDOS.forEach(weatherCityHisDO -> {
                WeatherFeatureCityDayHisDO weatherFeatureCityDayHisDO = new WeatherFeatureCityDayHisDO();
                BeanUtils.copyProperties(weatherCityHisDO, weatherFeatureCityDayHisDO);
                weatherFeatureCityDayHisDO.setHighestTemperature(BigDecimalUtils.getMax(weatherCityHisDO.getloadList()));
                weatherFeatureCityDayHisDO.setLowestTemperature(BigDecimalUtils.getMin(weatherCityHisDO.getloadList()));
                BigDecimal bigDecimal = BigDecimalUtils.addAllValue(weatherCityHisDO.getloadList());
                if (CollectionUtils.isNotEmpty(weatherCityHisDO.getloadList()) || bigDecimal != null) {
                    bigDecimal = bigDecimal.divide(BigDecimal.valueOf(weatherCityHisDO.getloadList().size()), 4, RoundingMode.HALF_UP);
                }
                weatherFeatureCityDayHisDO.setAveTemperature(bigDecimal);
                result.add(weatherFeatureCityDayHisDO);
            });
        }
        return result;
    }

}