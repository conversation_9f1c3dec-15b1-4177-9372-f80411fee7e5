package com.tsintergy.lf.serviceimpl.system.dao;


import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.core.constants.SystemConstant;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingSystemDO;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version $Id: SettingSystemDAO.java, v 0.1 2018-01-31 10:57:07 tao Exp $$
 */
@Component
public class SettingSystemDAO extends BaseAbstractDAO<SettingSystemDO> {

    /**
     * 获取字段值
     */
    public String getValue(String field) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != field) {
            param.getQueryConditions().put("_ne_field", field);
        }
        List<SettingSystemDO> settingSystemVOs = this.query(param).getDatas();
        if (settingSystemVOs != null & settingSystemVOs.size() > 0) {
            return settingSystemVOs.get(0).getValue();
        }
        return null;
    }

    /**
     * 根据字段获取VO
     */
    public SettingSystemDO getSettingSystemVO(String field) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        param.getQueryConditions().put("_se_field", field);
        List<SettingSystemDO> settingSystemVOs = this.query(param).getDatas();
        if (settingSystemVOs != null & settingSystemVOs.size() > 0) {
            return settingSystemVOs.get(0);
        }
        return null;
    }

    /**
     * 根据字段获取VO
     */
    public List<SettingSystemDO> getSettingSystemVO(List<String> fields) throws Exception {
        DBQueryParamBuilder builder = DBQueryParamBuilder.create().queryDataOnly();
        builder.where(QueryOp.StringIsIn, "field", fields);
        return this.query(builder.build()).getDatas();
    }


    /**
     * 获取高峰时段
     */
    public List<String> getPeakTimes() throws Exception {
        List<String> times = new ArrayList<String>();
        String peakTimeStr = this.getValue(SystemConstant.PEAK_TIME);
        if (StringUtils.isNotEmpty(peakTimeStr)) {
            for (String time : peakTimeStr.split(",")) {
                String[] startAndEndcolumns = time.split("~");
                if (startAndEndcolumns.length == 2) {
                    List<String> columns = ColumnUtil.getColumnsBetween(startAndEndcolumns[0].replace(":", ""),
                        startAndEndcolumns[1].replace(",", ""), Constants.LOAD_CURVE_POINT_NUM,
                        Constants.LOAD_CURVE_START_WITH_ZERO, false);
                    times.addAll(columns);
                }
            }
        }
        return times;
    }


    /**
     * 获取峰段电量时段
     */
    public List<String> getPeakSectionTime() throws Exception {
        List<String> times = new ArrayList<String>();
        String peakTimeStr = this.getValue(SystemConstant.PEAK_SECTION_TIME);
        if (StringUtils.isNotEmpty(peakTimeStr)) {
            for (String time : peakTimeStr.split(",")) {
                String[] startAndEndcolumns = time.split("~");
                if (startAndEndcolumns.length == 2) {
                    List<String> columns = ColumnUtil.getColumnsBetween(startAndEndcolumns[0].replace(":", ""),
                        startAndEndcolumns[1].replace(":", ""), Constants.LOAD_CURVE_POINT_NUM,
                        Constants.LOAD_CURVE_START_WITH_ZERO, false);
                    times.addAll(columns);
                }
            }
        }
        return times;
    }

    /**
     * 获取低谷时段
     */
    public List<String> getTroughTimes() throws Exception {
        List<String> times = new ArrayList<String>();
        String peakTimeStr = this.getValue(SystemConstant.TROUGH_TIME);
        if (StringUtils.isNotEmpty(peakTimeStr)) {
            for (String time : peakTimeStr.split(",")) {
                String[] startAndEndcolumns = time.split("~");
                if (startAndEndcolumns.length == 2) {
                    List<String> columns = ColumnUtil.getColumnsBetween(startAndEndcolumns[0].replace(":", ""),
                        startAndEndcolumns[1].replace(":", ""), Constants.LOAD_CURVE_POINT_NUM,
                        Constants.LOAD_CURVE_START_WITH_ZERO, false);
                    times.addAll(columns);
                }
            }
        }
        return times;
    }

    /**
     * 保存系统设置
     */
    public SettingSystemDO doSaveSettingSystemVO(SettingSystemDO settingSystemVO) throws Exception {
        SettingSystemDO oldVO = getSettingSystemVO(settingSystemVO.getField());
        if (oldVO != null) {
            String id = oldVO.getId();
            BeanUtils.copyProperties(settingSystemVO, oldVO);
            oldVO.setId(id);
            return (SettingSystemDO) this.updateAndFlush(oldVO);
        } else {
            return (SettingSystemDO) this.createAndFlush(settingSystemVO);
        }
    }

    /**
     * 保存系统设置
     */
    public SettingSystemDO doSaveSettingSystemVO(String field, String value) throws Exception {
        SettingSystemDO oldVO = getSettingSystemVO(field);
        if (oldVO != null) {
            oldVO.setValue(value);
            return (SettingSystemDO) this.updateAndFlush(oldVO);
        } else {
            SettingSystemDO settingSystemVO = new SettingSystemDO();
            settingSystemVO.setField(field);
            settingSystemVO.setValue(value);
            return (SettingSystemDO) this.createAndFlush(settingSystemVO);
        }
    }


    public List<SettingSystemDO> selectList(String key) {
        DBQueryParamBuilder builder = DBQueryParamBuilder.create().queryDataOnly();
        if (null != key) {
            builder.where(QueryOp.StringEqualTo, "field", key);
        }
        return this.query(builder.build()).getDatas();
    }

    /**
     * 获取高峰时段 时间所在下标 每个String用逗号分隔 开始下标,结束下标；
     * @return
     * @throws Exception
     */
    public List<List<Integer>> getPeakTimesBackMinuteSecond(List<String> allTimeListBySecond) throws Exception {
        List<List<Integer>> times = new ArrayList<>();
        String peakTimeStr = this.getValue("peak_time");
        if(StringUtils.isNotEmpty(peakTimeStr)) {
            for(String time : peakTimeStr.split(",")) {
                String[] startAndEndcolumns = time.split("~");
                List<Integer> data = new ArrayList<>();
                data.add(allTimeListBySecond.indexOf(startAndEndcolumns[0]+":00"));
                data.add(allTimeListBySecond
                    .indexOf(startAndEndcolumns[1]+":00") + 1);
                times.add(data);
            }
        }
        return times;
    }

    /**
     * 获取低谷时段 时间所在下标 每个String用逗号分隔 开始下标,结束下标；
     * @return
     * @throws Exception
     */
    public List<List<Integer>> getTroughTimesBackMinuteSecond(List<String> allTimeListBySecond) throws Exception {
        List<List<Integer>> times = new ArrayList<List<Integer>>();
        String peakTimeStr = this.getValue("trough_time");
        if(StringUtils.isNotEmpty(peakTimeStr)) {
            for(String time : peakTimeStr.split(",")) {
                String[] startAndEndcolumns = time.split("~");
                List<Integer> data = new ArrayList<>();
                data.add(allTimeListBySecond.indexOf(startAndEndcolumns[0]+":00"));
                data.add(allTimeListBySecond
                    .indexOf(startAndEndcolumns[1]+":00") + 1);
                times.add(data);
            }
        }
        return times;
    }

}