/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.load.impl;

import com.tsintergy.lf.serviceapi.base.airconditioner.api.LoadAcHisBasicService;
import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.LoadAcHisBasicDO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadAcHisBasicDAO;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:<br>
 *
 * @Author:<EMAIL>
 * @Date: 2022/5/23 18:51
 * @Version:1.0.0
 */
@Service("loadAcHisBasicService")
public class LoadAcHisBasicServiceimpl implements LoadAcHisBasicService {

    @Autowired
    LoadAcHisBasicDAO loadAcHisBasicDAO;

    @Override
    public List<LoadAcHisBasicDO> getloadAcHisBasicDOList(String cityId, String caliberId, Date startDate,
        Date endDate) {
        return loadAcHisBasicDAO.getLoadFeatureCityDayHisDO(cityId, startDate, endDate, caliberId);
    }

    @Override
    public void doSave(LoadAcHisBasicDO loadAcHisBasicDO) {
        loadAcHisBasicDAO.save(loadAcHisBasicDO);
    }

    @Override
    public void doUpdate(LoadAcHisBasicDO loadAcHisBasicDO) {
        loadAcHisBasicDAO.update(loadAcHisBasicDO);
    }
}