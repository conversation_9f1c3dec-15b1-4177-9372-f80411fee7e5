/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  wangchen Date:  2020/4/19 17:04 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.load.impl;

import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.enums.LoadFeatureEnum2;
import com.tsintergy.lf.core.util.DataUtil;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayFcService;
import com.tsintergy.lf.serviceapi.base.load.dto.FeatureStatisDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemData;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadFeatureCityDayFcDAO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadFeatureCityDayHisDAO;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/4/19
 * @since 1.0.0
 */
@Service(value = "loadFeatureCityDayFcService")
public class LoadFeatureCityDayFcServiceImpl extends BaseServiceImpl implements LoadFeatureCityDayFcService {

    private static final Logger logger = LoggerFactory.getLogger(LoadFeatureCityDayFcServiceImpl.class);

    @Autowired
    private WeatherFeatureCityDayFcService weatherFeatureCityDayFcService;

    @Autowired
    private WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    private LoadFeatureCityDayFcDAO loadFeatureCityDayFcDAO;

    @Autowired
    private LoadFeatureCityDayHisDAO loadFeatureCityDayHisDAO;

    @Autowired
    SettingSystemService settingSystemService;

    @Autowired
    HolidayService holidayService;

    @Override
    public List<FeatureStatisDTO> findFeatureStatisDTOS(Date date, String caliberId, String cityId,String dataType) throws Exception {

        LoadFeatureCityDayHisDO yestardayHisVO = this
            .findReportLoadFeatureCityDayHisDO(DateUtils.addDays(date, -1), caliberId, cityId);
        LoadFeatureCityDayHisDO lastYearHisVO = this
            .findReportLoadFeatureCityDayHisDO(DateUtils.addYears(date, -1), caliberId, cityId);
        WeatherFeatureCityDayHisDO yestardayWeatherHisV0 = weatherFeatureCityDayHisService
            .findWeatherFeatureCityHisVOByDate(cityId, DateUtils.addDays(date, -1));
        yestardayWeatherHisV0 =
            yestardayWeatherHisV0 == null ? new WeatherFeatureCityDayHisDO() : yestardayWeatherHisV0;
        WeatherFeatureCityDayHisDO lastYearWeatherHisV0 = weatherFeatureCityDayHisService
            .findWeatherFeatureCityHisVOByDate(cityId, DateUtils.addYears(date, -1));
        lastYearWeatherHisV0 = lastYearWeatherHisV0 == null ? new WeatherFeatureCityDayHisDO() : lastYearWeatherHisV0;

        LoadFeatureCityDayFcDO todayFcVO = this.findReportLoadFeatureCityDayFcDO(date, caliberId, cityId);
        todayFcVO = todayFcVO == null ? new LoadFeatureCityDayFcDO() : todayFcVO;
        LoadFeatureCityDayFcDO yestardayFcVO = this
            .findReportLoadFeatureCityDayFcDO(DateUtils.addDays(date, -1), caliberId, cityId);
        yestardayFcVO = yestardayFcVO == null ? new LoadFeatureCityDayFcDO() : yestardayFcVO;
        LoadFeatureCityDayFcDO lastYearFcVO = this
            .findReportLoadFeatureCityDayFcDO(DateUtils.addYears(date, -1), caliberId, cityId);
        lastYearFcVO = lastYearFcVO == null ? new LoadFeatureCityDayFcDO() : lastYearFcVO;
        WeatherFeatureCityDayFcDO todayWeatherFcV0 = weatherFeatureCityDayFcService
            .weatherFeatureCityDayFcVO(date, cityId);
        todayWeatherFcV0 = todayWeatherFcV0 == null ? new WeatherFeatureCityDayFcDO() : todayWeatherFcV0;
        WeatherFeatureCityDayFcDO yestardayWeatherFcV0 = weatherFeatureCityDayFcService
            .weatherFeatureCityDayFcVO(DateUtils.addDays(date, -1), cityId);
        yestardayWeatherFcV0 = yestardayWeatherFcV0 == null ? new WeatherFeatureCityDayFcDO() : yestardayWeatherFcV0;
        WeatherFeatureCityDayFcDO lastYearWeatherFcV0 = weatherFeatureCityDayFcService
            .weatherFeatureCityDayFcVO(DateUtils.addYears(date, -1), cityId);
        lastYearWeatherFcV0 = lastYearWeatherFcV0 == null ? new WeatherFeatureCityDayFcDO() : lastYearWeatherFcV0;

        List<FeatureStatisDTO> result = new ArrayList<>();

        for (LoadFeatureEnum2 loadFeature : LoadFeatureEnum2.values()) {
            FeatureStatisDTO featureStatisDTO = null;
            //循环各个特性，先判断是否有实际数据有则用实际数据对比，没有则用预测负荷对比，都没有则传null
            if (loadFeature.equals(LoadFeatureEnum2.MAX_LOAD)) {
                featureStatisDTO = new FeatureStatisDTO(loadFeature.getCode(), loadFeature.getName(),
                    toString(rounding(todayFcVO.getMaxLoad()))
                    , toString(rounding(DataUtil
                    .subtract(todayFcVO.getMaxLoad(), yestardayHisVO.getMaxLoad() == null ? yestardayFcVO.getMaxLoad()
                        : yestardayHisVO.getMaxLoad()))),
                    toString(rounding(DataUtil.subtract(todayFcVO.getMaxLoad(),
                        lastYearHisVO.getMaxLoad() == null ? lastYearFcVO.getMaxLoad() : lastYearHisVO.getMaxLoad()))));
            } else if (loadFeature.equals(LoadFeatureEnum2.MAX_LOAD_TIME)) {
                featureStatisDTO = new FeatureStatisDTO(loadFeature.getCode(), loadFeature.getName(),
                    todayFcVO.getMaxTime()
                    , yestardayFcVO.getMaxTime(), lastYearFcVO.getMaxTime());
            } else if (loadFeature.equals(LoadFeatureEnum2.MIN_LOAD)) {
                featureStatisDTO = new FeatureStatisDTO(loadFeature.getCode(), loadFeature.getName(),
                    toString(rounding(todayFcVO.getMinLoad()))
                    , toString(rounding(DataUtil.subtract(todayFcVO.getMinLoad(),
                    yestardayHisVO.getMinLoad() == null ? yestardayFcVO.getMinLoad() : yestardayHisVO.getMinLoad()))),
                    toString(rounding(DataUtil.subtract(todayFcVO.getMinLoad(),
                        lastYearHisVO.getMinLoad() == null ? lastYearFcVO.getMinLoad() : lastYearHisVO.getMinLoad()))));
            } else if (loadFeature.equals(LoadFeatureEnum2.MIN_LOAD_TIME)) {
                featureStatisDTO = new FeatureStatisDTO(loadFeature.getCode(), loadFeature.getName(),
                    todayFcVO.getMinTime()
                    , yestardayFcVO.getMinTime(), lastYearFcVO.getMinTime());
            } else if (loadFeature.equals(LoadFeatureEnum2.DIFFERENCE)) {
                featureStatisDTO = new FeatureStatisDTO(loadFeature.getCode(), loadFeature.getName(),
                    toString(rounding(todayFcVO.getDifferent()))
                    , toString(rounding(DataUtil.subtract(todayFcVO.getDifferent(),
                    yestardayHisVO.getDifferent() == null ? yestardayFcVO.getDifferent()
                        : yestardayHisVO.getDifferent()))),
                    toString(rounding(DataUtil.subtract(todayFcVO.getDifferent(),
                        lastYearHisVO.getDifferent() == null ? lastYearFcVO.getDifferent()
                            : lastYearHisVO.getDifferent()))));
            } else if (loadFeature.equals(LoadFeatureEnum2.DIFFERENCE_RATE)) {

                BigDecimal subtract1 = DataUtil.subtract(todayFcVO.getGradient(),
                    yestardayHisVO.getGradient() == null ? yestardayFcVO.getGradient() : yestardayHisVO.getGradient());
                BigDecimal subtract2 = DataUtil.subtract(todayFcVO.getGradient(),
                    lastYearHisVO.getGradient() == null ? lastYearFcVO.getGradient() : lastYearHisVO.getGradient());
                featureStatisDTO = new FeatureStatisDTO(loadFeature.getCode(), loadFeature.getName(), toString(
                    todayFcVO.getGradient() == null ? null : todayFcVO.getGradient().movePointRight(2).setScale(2))
                    , toString(subtract1 == null ? null : subtract1.movePointRight(2).setScale(2)),
                    toString(subtract2 == null ? null : subtract2.movePointRight(2).setScale(2)));
            } else if (loadFeature.equals(LoadFeatureEnum2.ENERGY)) {
                featureStatisDTO = new FeatureStatisDTO(loadFeature.getCode(), loadFeature.getName(),
                    toString(rounding(todayFcVO.getEnergy()))
                    , toString(rounding(DataUtil.subtract(todayFcVO.getEnergy(),
                    yestardayHisVO.getEnergy() == null ? yestardayFcVO.getEnergy() : yestardayHisVO.getEnergy()))),
                    toString(rounding(DataUtil.subtract(todayFcVO.getEnergy(),
                        lastYearHisVO.getEnergy() == null ? lastYearFcVO.getEnergy() : lastYearHisVO.getEnergy()))));
            } else if (loadFeature.equals(LoadFeatureEnum2.MAX_TEMPERATURE_FC)) {
                featureStatisDTO = new FeatureStatisDTO(loadFeature.getCode(), loadFeature.getName(),
                    toString(todayWeatherFcV0.getHighestTemperature())
                    , toString(DataUtil.subtract(todayWeatherFcV0.getHighestTemperature(),
                    yestardayWeatherHisV0.getHighestTemperature() == null ? yestardayWeatherFcV0.getHighestTemperature()
                        : yestardayWeatherHisV0.getHighestTemperature())),
                    toString(DataUtil.subtract(todayWeatherFcV0.getHighestTemperature(),
                        lastYearWeatherHisV0.getHighestTemperature() == null ? lastYearWeatherFcV0
                            .getHighestTemperature() : lastYearWeatherHisV0.getHighestTemperature())));
            } else if (loadFeature.equals(LoadFeatureEnum2.MIN_TEMPERATURE_FC)) {
                featureStatisDTO = new FeatureStatisDTO(loadFeature.getCode(), loadFeature.getName(),
                    toString(todayWeatherFcV0.getLowestTemperature())
                    , toString(DataUtil.subtract(todayWeatherFcV0.getLowestTemperature(),
                    yestardayWeatherHisV0.getLowestTemperature() == null ? yestardayWeatherFcV0.getLowestTemperature()
                        : yestardayWeatherHisV0.getLowestTemperature())),
                    toString(DataUtil.subtract(todayWeatherFcV0.getLowestTemperature(),
                        lastYearWeatherHisV0.getLowestTemperature() == null ? lastYearWeatherFcV0.getLowestTemperature()
                            : lastYearWeatherHisV0.getLowestTemperature())));
            }
            result.add(featureStatisDTO);
        }
        return result;
    }


    @Override
    public void doSaveOrUpdateLoadFeatureCityDayFcDOS(List<LoadFeatureCityDayFcDO> loadFeatureCityDayHisVOs) {
        for (LoadFeatureCityDayFcDO LoadFeatureCityDayFcDO : loadFeatureCityDayHisVOs) {
            try {
                this.doSaveOrUpdateLoadFeatureCityDayFcDO(LoadFeatureCityDayFcDO);
            } catch (Exception e) {
                logger.error("存储预测负荷特性异常...", e);
            }
        }
    }

    private void doSaveOrUpdateLoadFeatureCityDayFcDO(LoadFeatureCityDayFcDO LoadFeatureCityDayFcDO) throws Exception {

        LoadFeatureCityDayFcDO oldVO = findLoadFeatureCityDayFcDO(LoadFeatureCityDayFcDO.getCityId(),
            LoadFeatureCityDayFcDO.getDate(), LoadFeatureCityDayFcDO.getCaliberId(),
            LoadFeatureCityDayFcDO.getAlgorithmId());
        if (oldVO != null) {
            String id = oldVO.getId();
            BeanUtils.copyProperties(LoadFeatureCityDayFcDO, oldVO);
            oldVO.setId(id);
            oldVO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            loadFeatureCityDayFcDAO.updateAndFlush(oldVO);
        } else {
            LoadFeatureCityDayFcDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            loadFeatureCityDayFcDAO.createAndFlush(LoadFeatureCityDayFcDO);
        }

    }

    /**
     * 取人工修正的预测特性统计
     */
    @Override
    public List<LoadFeatureCityDayFcDO> findReportLoadFeatureCityDayFcList(List<String> cityIds, java.sql.Date startDate,
        java.sql.Date endDate, String caliberId) throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (!CollectionUtils.isEmpty(cityIds)) {
            dbQueryParamBuilder.where(QueryOp.StringIsIn, "cityId", cityIds);
        }
        if (startDate != null) {
            dbQueryParamBuilder.where(QueryOp.DateNoLessThan, "date", startDate);
        }
        if (endDate != null) {
            dbQueryParamBuilder.where(QueryOp.DateNoMoreThan, "date", endDate);
        }
        if (StringUtils.isNotBlank(caliberId)) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "report", true);
        List<LoadFeatureCityDayFcDO> datas = loadFeatureCityDayFcDAO.query(dbQueryParamBuilder.build())
            .getDatas();
        return datas;
    }

    @Override
    public List<LoadFeatureCityDayFcDO> findLoadFeatureCityDayFcList(List<String> cityIds, java.sql.Date startDate,
        java.sql.Date endDate, String caliberId, String algorithmId, Boolean report) throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (!CollectionUtils.isEmpty(cityIds)) {
            dbQueryParamBuilder.where(QueryOp.StringIsIn, "cityId", cityIds);
        }
        if (startDate != null) {
            dbQueryParamBuilder.where(QueryOp.DateNoLessThan, "date", startDate);
        }
        if (endDate != null) {
            dbQueryParamBuilder.where(QueryOp.DateNoMoreThan, "date", endDate);
        }
        if (StringUtils.isNotBlank(caliberId)) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (StringUtils.isNotBlank(algorithmId)) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "algorithmId", algorithmId);
        }
        if (report != null) {
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "report", report);
        }
        return loadFeatureCityDayFcDAO.query(dbQueryParamBuilder.build()).getDatas();
    }

    @Override
    public List<LoadFeatureCityDayFcDO> findReportIfNullUseRecommend(List<String> cityIds, java.sql.Date startDate,
        java.sql.Date endDate, String caliberId) throws Exception {
        List<LoadFeatureCityDayFcDO> reportList = findReportLoadFeatureCityDayFcList(cityIds, startDate,
            endDate, caliberId);

        // 获取推荐算法数据
        List<LoadFeatureCityDayFcDO> recommendList = new ArrayList<>();
        SystemData systemSetting = this.settingSystemService.getSystemSetting();
        List<LoadFeatureCityDayFcDO> allList = findLoadFeatureCityDayFcList(cityIds, startDate, endDate, caliberId,null,null);
        List<Date> holidayList = holidayService.findHoliday(startDate, endDate);
        for (LoadFeatureCityDayFcDO featureFcDO : allList) {
            if (Constants.PROVINCE_ID.equals(featureFcDO.getCityId())) {
                if (!CollectionUtils.isEmpty(holidayList) && holidayList.contains(featureFcDO.getDate())) {
                    if (systemSetting.getProvinceHolidayAlgorithm().equals(featureFcDO.getAlgorithmId())) {
                        recommendList.add(featureFcDO);
                    }
                } else {
                    if (systemSetting.getProvinceNormalAlgorithm().equals(featureFcDO.getAlgorithmId())) {
                        recommendList.add(featureFcDO);
                    }
                }
            } else {
                if (!CollectionUtils.isEmpty(holidayList) && holidayList.contains(featureFcDO.getDate())) {
                    if (systemSetting.getCityHolidayAlgorithm().equals(featureFcDO.getAlgorithmId())) {
                        recommendList.add(featureFcDO);
                    }
                } else {
                    if (systemSetting.getCityNormalAlgorithm().equals(featureFcDO.getAlgorithmId())) {
                        recommendList.add(featureFcDO);
                    }
                }
            }
        }


        if (CollectionUtils.isEmpty(reportList)) {
            return recommendList;
        } else {
            // 找出推荐中有而上报中没有的数据
            List<LoadFeatureCityDayFcDO> recommendLoadList = new ArrayList<>();
            Map<String, LoadFeatureCityDayFcDO> reportMap = reportList.stream()
                .collect(Collectors.toMap(featureFcDO -> generateMapKey(featureFcDO), Function.identity(), (o, n) -> n));
            for (LoadFeatureCityDayFcDO recommendDO : recommendList) {
                LoadFeatureCityDayFcDO reportDO = reportMap.get(generateMapKey(recommendDO));
                if (reportDO == null) {
                    recommendLoadList.add(recommendDO);
                }
            }

            // 将推荐中有而上报中没有的数据合并到上报的集合中
            reportList.addAll(recommendLoadList);
            return reportList;
        }
    }

    private static String generateMapKey(LoadFeatureCityDayFcDO statisticsCityDayFcDO) {
        return statisticsCityDayFcDO.getCityId() + statisticsCityDayFcDO.getCaliberId() + DateUtils.date2String(statisticsCityDayFcDO.getDate(),
            DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
    }


    private LoadFeatureCityDayFcDO findLoadFeatureCityDayFcDO(String cityId, java.sql.Date date, String caliberId,
        String algorithmId) throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (StringUtils.isNotBlank(cityId)) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "cityId", cityId);
        }
        if (date != null) {
            dbQueryParamBuilder.where(QueryOp.DateEqualTo, "date", date);
        }
        if (StringUtils.isNotBlank(caliberId)) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (StringUtils.isNotBlank(algorithmId)) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "algorithmId", algorithmId);
        }
        List<LoadFeatureCityDayFcDO> datas = loadFeatureCityDayFcDAO.query(dbQueryParamBuilder.build())
            .getDatas();
        if (!CollectionUtils.isEmpty(datas)) {
            return datas.get(0);
        }
        return null;
    }

    private BigDecimal rounding(BigDecimal value) {
        if (value != null) {
            return value.setScale(0, BigDecimal.ROUND_HALF_UP);
        }
        return null;
    }

    private String toString(Object obj) {
        if (obj != null) {
            return obj.toString();
        }
        return null;
    }

    private LoadFeatureCityDayFcDO findReportLoadFeatureCityDayFcDO(Date date, String caliberId, String cityId)
        throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly()
            .where(QueryOp.StringEqualTo, "cityId", cityId)
            .where(QueryOp.StringEqualTo, "caliberId", caliberId)
            .where(QueryOp.DateEqualTo, "date", new java.sql.Date(date.getTime()))
            .where(QueryOp.NumberEqualTo, "report", true);
        List<LoadFeatureCityDayFcDO> LoadFeatureCityDayFcDOS = loadFeatureCityDayFcDAO
            .query(dbQueryParamBuilder.build()).getDatas();
        if (!CollectionUtils.isEmpty(LoadFeatureCityDayFcDOS)) {
            return LoadFeatureCityDayFcDOS.get(0);
        }
        return null;
    }

    private LoadFeatureCityDayHisDO findReportLoadFeatureCityDayHisDO(Date date, String caliberId, String cityId)
        throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly()
            .where(QueryOp.StringEqualTo, "cityId", cityId)
            .where(QueryOp.StringEqualTo, "caliberId", caliberId)
            .where(QueryOp.DateEqualTo, "date", new java.sql.Date(date.getTime()));
        List<LoadFeatureCityDayHisDO> LoadFeatureCityDayHisDOS = loadFeatureCityDayHisDAO
            .query(dbQueryParamBuilder.build()).getDatas();
        if (!CollectionUtils.isEmpty(LoadFeatureCityDayHisDOS)) {
            return LoadFeatureCityDayHisDOS.get(0);
        }
        return new LoadFeatureCityDayHisDO();
    }

    @Override
    public LoadFeatureCityDayFcDO findLoadFeatureCityDayFcReport(String cityId, String caliberId, java.sql.Date date)
        throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (StringUtils.isNotBlank(cityId)) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "cityId", cityId);
        }
        if (date != null) {
            dbQueryParamBuilder.where(QueryOp.DateEqualTo, "date", date);
        }
        if (StringUtils.isNotBlank(caliberId)) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "report", true);
        List<LoadFeatureCityDayFcDO> datas = loadFeatureCityDayFcDAO.query(dbQueryParamBuilder.build())
            .getDatas();

        if (CollectionUtils.isEmpty(datas)) {
            return null;
        }
        return datas.get(0);
    }

    @Override
    public List<LoadFeatureCityDayFcDO> findFcFeatureReports(String cityId, String caliberId, java.sql.Date date) {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (StringUtils.isNotBlank(cityId)) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "cityId", cityId);
        }
        if (date != null) {
            dbQueryParamBuilder.where(QueryOp.DateEqualTo, "date", date);
        }
        if (StringUtils.isNotBlank(caliberId)) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "report", true);
        List<LoadFeatureCityDayFcDO> datas = loadFeatureCityDayFcDAO.query(dbQueryParamBuilder.build())
            .getDatas();

        return datas;
    }
}
