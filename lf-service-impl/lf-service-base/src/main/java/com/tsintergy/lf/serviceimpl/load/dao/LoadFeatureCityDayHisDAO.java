package com.tsintergy.lf.serviceimpl.load.dao;

import com.tsieframework.core.base.dao.hibernate.*;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.core.constants.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $Id: LoadFeatureCityDayHisDAO.java, v 0.1 2018-01-31 10:51:15 tao Exp $$
 */
@Component
@Slf4j
public class LoadFeatureCityDayHisDAO extends BaseAbstractDAO<LoadFeatureCityDayHisDO> {

    /**
     * 统计日负荷特性
     *
     * @param loadCityHisDO 历史负荷
     * @param peakTimes 尖峰时段
     * @param troughTimes 低谷时段
     */
    public LoadFeatureCityDayHisDO statisticsDayFeature(LoadCityHisDO loadCityHisDO, List<String> peakTimes,
        List<String> troughTimes) throws Exception {

        List<BigDecimal> peaks = new ArrayList<BigDecimal>();
        List<BigDecimal> troughs = new ArrayList<BigDecimal>();

        List<BigDecimal> loadList = BasePeriodUtils
            .toList(loadCityHisDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        Map<String, BigDecimal> loadMap = BasePeriodUtils
            .toMap(loadCityHisDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        Map<String, BigDecimal> maxMixAvg = BasePeriodUtils.getMaxMinAvg(loadList, 4);

        String maxTime = null; // 最大负荷发生时刻
        String minTime = null; // 最小负荷发生时刻

        for (String column : loadMap.keySet()) {
            BigDecimal load = loadMap.get(column);
            if (null != load) {
                column = column.substring(1);
                if (load.compareTo(maxMixAvg.get("max")) == 0) {
                    maxTime = column;
                }
                if (load.compareTo(maxMixAvg.get("min")) == 0) {
                    minTime = column;
                }
                if (peakTimes != null && peakTimes.contains(column)) {
                    peaks.add(load);
                }
                if (troughTimes != null && troughTimes.contains(column)) {
                    troughs.add(load);
                }
            }
        }

        LoadFeatureCityDayHisDO featureCityDayHisVO = new LoadFeatureCityDayHisDO();
        featureCityDayHisVO.setCityId(loadCityHisDO.getCityId());
        featureCityDayHisVO.setDate(loadCityHisDO.getDate());
        featureCityDayHisVO.setCaliberId(loadCityHisDO.getCaliberId());
        if (maxTime != null) {
            featureCityDayHisVO.setMaxTime(new StringBuffer(maxTime).insert(2, ":").toString());
        }
        if (minTime != null) {
            featureCityDayHisVO.setMinTime(new StringBuffer(minTime).insert(2, ":").toString());
        }

        // 最大负荷
        featureCityDayHisVO.setMaxLoad(maxMixAvg.get("max"));
        // 最小负荷
        featureCityDayHisVO.setMinLoad(maxMixAvg.get("min"));
        // 平均负荷
        featureCityDayHisVO.setAveLoad(maxMixAvg.get("avg"));
        // 峰谷差 = 日最大负荷 – 日最小负荷
        featureCityDayHisVO
            .setDifferent(BigDecimalUtils.sub(featureCityDayHisVO.getMaxLoad(), featureCityDayHisVO.getMinLoad()));
        // 峰谷差率 = （日最大负荷 – 日最小负荷）/日最大负荷
        featureCityDayHisVO.setGradient(
            BigDecimalUtils.divide(featureCityDayHisVO.getDifferent(), featureCityDayHisVO.getMaxLoad(), 4));
        // 负荷率 = 日平均负荷/日最大负荷
        featureCityDayHisVO.setLoadGradient(
            BigDecimalUtils.divide(featureCityDayHisVO.getAveLoad(), featureCityDayHisVO.getMaxLoad(), 4));
        // 尖峰平均负荷 = average(尖峰时段负荷）。尖峰时段后台可配，默认值为5:30~7:00，10:00~12:00，18：00~20:00
        featureCityDayHisVO.setPeak(BigDecimalUtils.avgList(peaks, 4, true));
        // 低谷平均负荷 = average（低谷时段负荷）。低谷时段可配，默认值为2:00~5:00，12:00~14:00，22:00~0:00
        featureCityDayHisVO.setTrough(BigDecimalUtils.avgList(troughs, 4, false));
        // 日电量 = 96点负荷之和/4
        featureCityDayHisVO
            .setEnergy(BigDecimalUtils.divide(BigDecimalUtils.addAllValue(loadList), new BigDecimal(4), 4));

        return featureCityDayHisVO;
    }

    /**
     * 统计日负荷特性
     *
     * @param loadCityHisDOS 历史负荷
     * @param peakTimes 尖峰时段
     * @param troughTimes 低谷时段
     */
    public List<LoadFeatureCityDayHisDO> statisticsDayFeature(List<LoadCityHisDO> loadCityHisDOS,
        List<String> peakTimes, List<String> troughTimes) throws Exception {
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = new ArrayList<LoadFeatureCityDayHisDO>();
        if (loadCityHisDOS != null) {
            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS) {
                try {
                    LoadFeatureCityDayHisDO loadFeatureCityDayHisDO = statisticsDayFeature(loadCityHisDO, peakTimes,
                        troughTimes);
                    loadFeatureCityDayHisDOS.add(loadFeatureCityDayHisDO);
                } catch (Exception e) {
                    log.info(e.toString());
                }
            }
        }
        return loadFeatureCityDayHisDOS;
    }

    /**
     * 计算指定时段内的电量
     *
     * @param loadCityHisDOS 偏差率列表
     * @param startPeriod 开始时段
     * @param endPeriod 结束时段
     */
    public BigDecimal calculateEnergy(List<LoadCityHisDO> loadCityHisDOS, String startPeriod, String endPeriod) {
        List<BigDecimal> datas = new ArrayList<BigDecimal>();
        if (loadCityHisDOS != null) {
            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS) {
                Map<String, BigDecimal> map = BasePeriodUtils
                    .toMap(loadCityHisDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
                for (String column : map.keySet()) {
                    if (map.get(column) != null && column.toLowerCase().compareTo(startPeriod) > -1
                        && column.compareTo(endPeriod) < 1) {
                        datas.add(map.get(column));
                    }
                }
            }
        }

        BigDecimal energy = new BigDecimal(0);
        if (datas.size() > 0) {
            energy = BigDecimalUtils.addAllValue(datas).divide(new BigDecimal(4));
        }

        return energy;
    }

    /**
     * 获取日负荷特性
     *
     * @param cityId 城市ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param caliberId 口径ID
     */
    public List<LoadFeatureCityDayHisDO> getLoadFeatureCityDayHisDOs(String cityId, Date startDate, Date endDate,
        String caliberId) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (StringUtils.isNotBlank(cityId)) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = this.query(param).getDatas();
        return loadFeatureCityDayHisDOS;
    }

    /**
     * 获取日负荷特性
     *
     * @param cityId 城市ID
     * @param date 日期
     * @param caliberId 口径ID
     */
    public LoadFeatureCityDayHisDO getLoadFeatureCityDayHisDO(String cityId, Date date, String caliberId)
        throws Exception {

        if (cityId == null) {
            throw new BusinessException("T706", "城市ID不可为空");
        }

        if (date == null) {
            throw new BusinessException("T706", "日期不可为空");
        }

        if (caliberId == null) {
            throw new BusinessException("T706", "口径ID不可为空");
        }

        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        param.getQueryConditions().put("_de_date", new java.sql.Date(date.getTime()));
        param.getQueryConditions().put("_ne_cityId", cityId);
        param.getQueryConditions().put("_ne_caliberId", caliberId);
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = this.query(param).getDatas();
        if (loadFeatureCityDayHisDOS.size() > 0) {
            return loadFeatureCityDayHisDOS.get(0);
        }
        return null;
    }

    /**
     * 保存或更新
     */
    public LoadFeatureCityDayHisDO doSaveOrUpdateLoadFeatureCityDayHisVO(
        LoadFeatureCityDayHisDO loadFeatureCityDayHisDO) throws Exception {
        LoadFeatureCityDayHisDO oldVO = getLoadFeatureCityDayHisDO(loadFeatureCityDayHisDO.getCityId(),
            loadFeatureCityDayHisDO
                .getDate(), loadFeatureCityDayHisDO.getCaliberId());
        if (oldVO != null) {
            String id = oldVO.getId();
            BeanUtils.copyProperties(loadFeatureCityDayHisDO, oldVO,"createtime");
            oldVO.setId(id);
            oldVO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            return (LoadFeatureCityDayHisDO) this.updateAndFlush(oldVO);
        } else {
            loadFeatureCityDayHisDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            return (LoadFeatureCityDayHisDO) this.createAndFlush(loadFeatureCityDayHisDO);
           }
    }

    /**
     * 保存或更新
     */
    public List<LoadFeatureCityDayHisDO> doSaveOrUpdateLoadFeatureCityDayHisDOs(
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS) throws Exception {
        List<LoadFeatureCityDayHisDO> vos = new ArrayList<LoadFeatureCityDayHisDO>();
        for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS) {
            try {
                vos.add(this.doSaveOrUpdateLoadFeatureCityDayHisVO(loadFeatureCityDayHisDO));
            } catch (Exception e) {
                log.error("保存日负荷特性出错了", e);
            }
        }
        return vos;
    }

    /**
     * 获取指定日期范围内的最大负荷日
     *
     * @param cityId 城市ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param caliberId 口径ID
     */
    public LoadFeatureCityDayHisDO getLoadFeatureOfMaxLoadDay(String cityId, Date startDate, Date endDate,
        String caliberId) throws Exception {

        if (cityId == null) {
            throw new BusinessException("T706", "城市ID不可为空");
        }

        if (startDate == null) {
            throw new BusinessException("T706", "开始日期不可为空");
        }

        if (endDate == null) {
            throw new BusinessException("T706", "结束日期不可为空");
        }

        if (caliberId == null) {
            throw new BusinessException("T706", "口径ID不可为空");
        }

        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        param.getQueryConditions().put("cityId", cityId);
        param.getQueryConditions().put("startDate", new java.sql.Date(startDate.getTime()));
        param.getQueryConditions().put("endDate", new java.sql.Date(endDate.getTime()));
        param.getQueryConditions().put("caliberId", caliberId);
        List<Object> loadFeatureCityDayHisDOS = this
            .queryByNamedSqlQuery("getLoadFeatureOfMaxLoadDayBetweenDays", param).getDatas();
        if (loadFeatureCityDayHisDOS != null && loadFeatureCityDayHisDOS.size() > 0) {
            return (LoadFeatureCityDayHisDO) loadFeatureCityDayHisDOS.get(0);
        }

        return null;

    }

    /**
     * 获取日负荷特性
     *
     * @param cityIds 城市ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param caliberId 口径ID
     */
    public List<LoadFeatureCityDayHisDO> getLoadFeatureCityDayHisDOs(List<String> cityIds, Date startDate, Date endDate,
        String caliberId) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (null != cityIds) {
            param.getQueryConditions().put("_sin_cityId", cityIds);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = this.query(param).getDatas();
        return loadFeatureCityDayHisDOS;
    }

    /**
     * 获取历史的最大负荷日
     *
     * @param cityId 城市ID
     * @param caliberId 口径ID
     * @return 最大负荷日的负荷特性VO
     */
    public LoadFeatureCityDayHisDO getLoadFeatureOfMaxLoadDay(String cityId, String caliberId) throws Exception {

        if (cityId == null) {
            throw new BusinessException("T706", "城市ID不可为空");
        }

        if (caliberId == null) {
            throw new BusinessException("T706", "口径ID不可为空");
        }

        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        param.getQueryConditions().put("cityId", cityId);
        param.getQueryConditions().put("caliberId", caliberId);

        List<Object> loadFeatureCityDayHisDOS = this
            .queryByNamedSqlQuery("getLoadFeatureOfMaxLoadDay", param).getDatas();
        if (loadFeatureCityDayHisDOS != null && loadFeatureCityDayHisDOS.size() > 0) {
            return (LoadFeatureCityDayHisDO) loadFeatureCityDayHisDOS.get(0);
        }

        return null;

    }

    /**
     * 获取日负荷特性
     *
     * @param dates 日期
     */
    public List<LoadFeatureCityDayHisDO> getLoadFeatureCityDayHisDOs(String cityId, List<Date> dates, String caliberId)
        throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (dates == null || CollectionUtils.isEmpty(dates)) {
            return null;
        }
        if (null != dates) {
            List<java.sql.Date> sqlDates = new ArrayList<java.sql.Date>();
            for (Date date : dates) {
                sqlDates.add(new java.sql.Date(date.getTime()));
            }
            param.getQueryConditions().put("_din_date", sqlDates);
        }
        if (null != cityId) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = this.query(param).getDatas();
        return loadFeatureCityDayHisDOS;
    }


    public List<LoadFeatureCityDayHisDO> listLoadFeature(String cityId, String caliberId, List<Date> dateList)
        throws Exception {
        DBQueryParamBuilder builder = DBQueryParamBuilder.create().queryDataOnly();
        builder.where(QueryOp.StringEqualTo, "cityId", cityId);
        builder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        List<java.sql.Date> dates = new ArrayList<>();
        dateList.forEach(date -> {
            dates.add(new java.sql.Date(date.getTime()));
        });
        builder.where(QueryOp.DateIsIn, "date", dates);
        builder.addOrderByAsc("date");
        return this.query(builder.build()).getDatas();
    }
}