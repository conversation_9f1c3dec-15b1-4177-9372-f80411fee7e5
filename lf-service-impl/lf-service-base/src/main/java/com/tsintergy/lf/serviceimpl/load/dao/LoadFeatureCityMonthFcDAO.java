/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangchen Date:  2019/3/11 15:01 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.load.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityMonthFcDO;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/7/6
 * @since 1.0.0
 */
@Component
public class LoadFeatureCityMonthFcDAO extends BaseAbstractDAO<LoadFeatureCityMonthFcDO> {

    /**
     * 查询预测气象准确率  ---月
     *
     * @param cityId 城市ID
     */
    public List<LoadFeatureCityMonthFcDO> find(String cityId, String algorithmId,
        String year,
        String month, String caliberId) throws Exception {
        DBQueryParamBuilder param = DBQueryParamBuilder.create().queryDataOnly();
        if (cityId != null && StringUtils.isNoneEmpty(cityId) && !"0".equals(cityId)) {
            param.where(QueryOp.StringEqualTo, "cityId", cityId);
        }
        if (algorithmId != null) {
            param.where(QueryOp.StringEqualTo, "algorithmId", algorithmId);
        }
        if (caliberId != null) {
            param.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (year != null) {
            param.where(QueryOp.StringEqualTo, "year", year);
        }
        if (month != null) {
            param.where(QueryOp.StringEqualTo, "month", month);
        }
        List<LoadFeatureCityMonthFcDO> list = this.query(param.build()).getDatas();
        return list;


    }

    /**
     * 查询预测气象准确率  ---月
     *
     * @param cityId 城市ID
     */
    public List<LoadFeatureCityMonthFcDO> findByYear(String cityId, String algorithmId,
        String startYear,
        String endYear, String caliberId) throws Exception {
        DBQueryParamBuilder param = DBQueryParamBuilder.create().queryDataOnly();
        if (cityId != null && StringUtils.isNoneEmpty(cityId) && !"0".equals(cityId)) {
            param.where(QueryOp.StringEqualTo, "cityId", cityId);
        }
        if (algorithmId != null) {
            param.where(QueryOp.StringEqualTo, "algorithmId", algorithmId);
        }
        if (caliberId != null) {
            param.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (startYear != null) {
            param.where(QueryOp.StringNoLessThan, "year", startYear);
        }
        if (endYear != null) {
            param.where(QueryOp.StringNoMoreThan, "year", endYear);
        }
        param.addOrderByAsc("year,month");
        List<LoadFeatureCityMonthFcDO> list = this.query(param.build()).getDatas();
        return list;


    }

}