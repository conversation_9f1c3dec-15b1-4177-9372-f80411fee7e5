package com.tsintergy.lf.serviceimpl.evalucation.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyCompositeDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/12 14:18
 */
@Component
public class AccuracyCompositeDAO extends BaseAbstractDAO<AccuracyCompositeDO> {


    public List<AccuracyCompositeDO> selectListByName(String cityId, String caliberId, String accuracyName,
        Date startDate, Date endDate) {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (null != cityId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "cityId", cityId);
        }
        if (null != caliberId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (null != accuracyName) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "accuracyName", accuracyName);
        }
        dbQueryParamBuilder.where(QueryOp.DateNoLessThan, "date", new java.sql.Date(startDate.getTime()));
        dbQueryParamBuilder.where(QueryOp.DateNoMoreThan, "date", new java.sql.Date(endDate.getTime()));
        List<AccuracyCompositeDO> datas = this.query(dbQueryParamBuilder.build()).getDatas();
        return datas;
    }


    public List<AccuracyCompositeDO> selectListByAlgorithmId(String cityId, String caliberId, List<String> algorithmIds,
                                                             Date startDate, Date endDate, String batchId) {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (null != cityId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "cityId", cityId);
        }
        if (null != caliberId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (CollectionUtils.isNotEmpty(algorithmIds)) {
            dbQueryParamBuilder.where(QueryOp.StringIsIn, "algorithmId", algorithmIds);
        }
        if (null != batchId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "batchId", batchId);
        }
        dbQueryParamBuilder.where(QueryOp.DateNoLessThan, "date", new java.sql.Date(startDate.getTime()));
        dbQueryParamBuilder.where(QueryOp.DateNoMoreThan, "date", new java.sql.Date(endDate.getTime()));
        List<AccuracyCompositeDO> datas = this.query(dbQueryParamBuilder.build()).getDatas();
        return datas;
    }


}
