package com.tsintergy.lf.serviceimpl.otherload.impl;

import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.enums.OtherLoadTypeEnum;

import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.otherload.api.OtherLoadService;
import com.tsintergy.lf.serviceapi.base.otherload.dto.OtherLoadDataDTO;
import com.tsintergy.lf.serviceapi.base.otherload.dto.OtherLoadReportDTO;
import com.tsintergy.lf.serviceapi.base.otherload.dto.OtherLoadReportInfoDTO;
import com.tsintergy.lf.serviceapi.base.otherload.pojo.LoadCityFcDFDDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingSystemDO;
import com.tsintergy.lf.serviceimpl.otherload.dao.LoadCityFcDFDDAO;
import com.tsintergy.lf.core.constants.SystemConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;

@Service("otherLoadService")
public class OtherLoadServiceImpl implements OtherLoadService {


    @Autowired
    LoadCityFcDFDDAO loadCityFcDFDDAO;

    @Autowired
    SettingSystemService settingSystemService;

    @Autowired
    LoadCityFcService loadCityFcService;

    @Override
    public OtherLoadDataDTO findOtherLoadData(String type, String cityId, Date date) throws Exception {

        List<BigDecimal> fcData = null;
        List<BigDecimal> report = null;
        List<BigDecimal> modify = null;
        try {

            //地方电需求
            if (OtherLoadTypeEnum.DFD.getType().equals(type)) {

                List<BigDecimal> reportLoadCityFcDO = loadCityFcService
                    .findReportLoadCityFcDO(date, cityId, null, null, true);
                if (CollectionUtils.isEmpty(reportLoadCityFcDO)) {
                    List<BigDecimal> yesterdayReportLoadCityFcDO = loadCityFcService
                        .findReportLoadCityFcDO(DateUtils.addDays(date, -1), cityId, null, null, true);
                    if (CollectionUtils.isEmpty(yesterdayReportLoadCityFcDO)) {
                        fcData = ColumnUtil.getZeroOrNullList(Constants.LOAD_CURVE_POINT_NUM, BigDecimal.ZERO);
                    } else {
                        fcData = yesterdayReportLoadCityFcDO;
                    }

                } else {
                    fcData = reportLoadCityFcDO;
                }

                List<LoadCityFcDFDDO> reportLoadCityFcDFDs = loadCityFcDFDDAO.findReportLoadCityFcDFDDOS(cityId, date);
                if (CollectionUtils.isEmpty(reportLoadCityFcDFDs)) {
                    modify = fcData;
                } else {
                    LoadCityFcDFDDO reportLoadCityFcDFDDO = reportLoadCityFcDFDs.get(0);
                    report = BasePeriodUtils.toList(reportLoadCityFcDFDDO, Constants.LOAD_CURVE_POINT_NUM,
                        Constants.LOAD_CURVE_START_WITH_ZERO);
                    modify = report;
                }


            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        OtherLoadDataDTO otherLoadDataDTO = new OtherLoadDataDTO();
        otherLoadDataDTO.setFcData(fcData);
        otherLoadDataDTO.setReport(report);
        otherLoadDataDTO.setModify(modify);
        return otherLoadDataDTO;
    }

    @Override
    public List<BigDecimal> findOtherReportLoadData(String type, String cityId, Date date) throws Exception {
        List<BigDecimal> fcData = null;
        if (OtherLoadTypeEnum.DFD.getType().equals(type)) {
            List<LoadCityFcDFDDO> reportLoadCityFcDFDs = loadCityFcDFDDAO.findReportLoadCityFcDFDDOS(cityId, date);
            if (!CollectionUtils.isEmpty(reportLoadCityFcDFDs)) {
                LoadCityFcDFDDO loadCityFcDFDDO = reportLoadCityFcDFDs.get(0);
                fcData = BasePeriodUtils
                    .toList(loadCityFcDFDDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
            }
        }
        return fcData;
    }

    @Override
    public void doReportLoadData(OtherLoadReportDTO otherLoadReportDTO) throws Exception {

        String type = otherLoadReportDTO.getType();
        List<BigDecimal> reportData = otherLoadReportDTO.getReportData();
        Map<String, BigDecimal> dataMap = ColumnUtil.listToMap(reportData, Constants.LOAD_CURVE_START_WITH_ZERO);
        String cityId = otherLoadReportDTO.getCityId();
        Date date = otherLoadReportDTO.getDate();
        if (OtherLoadTypeEnum.DFD.getType().equals(type)) {
            List<LoadCityFcDFDDO> reportLoadCityFcDFDs = loadCityFcDFDDAO.findReportLoadCityFcDFDDOS(cityId, date);
            if (CollectionUtils.isEmpty(reportLoadCityFcDFDs)) {
                LoadCityFcDFDDO loadCityFcDFDDO = new LoadCityFcDFDDO();
                loadCityFcDFDDO.setCityId(cityId);
                loadCityFcDFDDO.setReport(true);
                loadCityFcDFDDO.setDate(new java.sql.Date(date.getTime()));

                BasePeriodUtils.setAllFiled(loadCityFcDFDDO, dataMap);
                loadCityFcDFDDO.setAlgorithmId("0");
                loadCityFcDFDDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
                loadCityFcDFDDO.setReportTime(new Timestamp(System.currentTimeMillis()));
                loadCityFcDFDDO.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
                loadCityFcDFDDAO.save(loadCityFcDFDDO);
            } else {
                LoadCityFcDFDDO loadCityFcDFDDO = reportLoadCityFcDFDs.get(0);
                BasePeriodUtils.setAllFiled(loadCityFcDFDDO, dataMap);
                loadCityFcDFDDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                loadCityFcDFDDO.setReportTime(new Timestamp(System.currentTimeMillis()));
                loadCityFcDFDDAO.updateAndFlush(loadCityFcDFDDO);
            }
        }
    }

    @Override
    public OtherLoadReportInfoDTO findReportInfo(String cityId, Date date) throws Exception {

        OtherLoadReportInfoDTO otherLoadReportInfoDTO = new OtherLoadReportInfoDTO();

        List<LoadCityFcDFDDO> reportLoadCityFcDFDs = loadCityFcDFDDAO.findReportLoadCityFcDFDDOS(cityId, date);
        if (!CollectionUtils.isEmpty(reportLoadCityFcDFDs)) {
            Timestamp reportTime = reportLoadCityFcDFDs.get(0).getReportTime();
            otherLoadReportInfoDTO.setDfdReportTime(
                DateUtils.date2String(new Date(reportTime.getTime()), DateFormatType.DATE_FORMAT_STR));
        }

        SettingSystemDO byFieldId = settingSystemService.findByFieldId(SystemConstant.END_REPORT_TIME);
        String value = byFieldId.getValue();
        String[] split = value.split(",");
        otherLoadReportInfoDTO.setReportTimeLine(split[1]);

        return otherLoadReportInfoDTO;
    }


}
