/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangfeng Date:  2019/11/6 10:49 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.forecast.impl;


import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.api.CustomizationForecastService;
import com.tsintergy.lf.serviceapi.algorithm.api.ModelFusionForecastService;
import com.tsintergy.lf.serviceapi.algorithm.api.ShortAlgorithmForecastService;
import com.tsintergy.lf.serviceapi.algorithm.dto.*;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.common.enumeration.LongMonthCategoryEnum;
import com.tsintergy.lf.serviceapi.base.forecast.api.AutoForecastService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcBatchService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.recall.api.LoadCityFcRecallService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcLoadForecastService;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 * <p>
 * 预测流程梳理 定时任务--自动预测（正常日和节假日）
 *
 * <AUTHOR>
 * @create 2019/11/6
 * @since 1.0.0
 */
@Service("autoForecastService")
@Slf4j
public class AutoForecastServiceImpl extends BaseServiceImpl implements AutoForecastService {

    private static final Logger logger = LogManager.getLogger(AutoForecastServiceImpl.class);
    @Autowired
    LoadCityFcBatchService loadCityFcBatchService;
    @Autowired
    WeatherCityFcLoadForecastService weatherCityFcLoadForecastService;
    @Autowired
    private SettingSystemService settingSystemService;
    @Autowired
    private LoadCityFcService loadCityFcService;
    @Autowired
    private LoadCityFcRecallService loadCityFcRecallService;
    @Autowired
    private CustomizationForecastService<ForecastParam, GeneralResult> forecastable;
    @Autowired
    private CustomizationForecastService<LongMonthFeatureForecastParam, GeneralResult> longMonthForecastAlgorithmService;
    @Autowired
    private CustomizationForecastService<HolidayParam, GeneralResult> holidayForecastService;
    @Autowired
    private CityService cityService;
    @Autowired
    private ModelFusionForecastService modelFusionForecastService;

    @Autowired
    private ShortAlgorithmForecastService shortAlgorithmForecastService;

    @Override
    public void autoLongMonthForecast(String uid, String cityId, String caliberId, Date startDate, Date endDate,
                                      List<AlgorithmEnum> enums, LongMonthCategoryEnum type) throws Exception {
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        String city = cityService.findCityById(cityId).getCity();
        LongMonthFeatureForecastParam param = new LongMonthFeatureForecastParam(uid, cityId, city, caliberId, enums, dateList, startDate, endDate, type);
        param.setAlgorithmEnum(enums.get(0));
        longMonthForecastAlgorithmService.forecast(param);
    }

    /**
     * 短期每日预测 定时任务触发or实施页面触发 正常日预测： 如果库中有人工修正、台风预测、节假日预测的数据则优先上报这些数据，正常日的数据不上报
     */
    @Override
    public void autoForecast(Integer forecastType, String uid, String cityId, String caliberId, Date startDate,
                             Date endDate, List<AlgorithmEnum> enums, Integer fcstDayWeatherType, Integer type, Integer pointNum)
            throws Exception {
        logger.info(
                "开始预测，startDate:" + startDate + ", endDate:" + endDate + ", cityId:" + cityId + ", caliberId:" + caliberId);
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        String city = cityService.findCityById(cityId).getCity();
        ForecastParam param = new ForecastParam(uid, cityId, city, caliberId, enums, dateList);
        param.setForecastType(forecastType);
        param.setFcstDayWeatherType(fcstDayWeatherType);
        param.setFway(type);
        param.setPointNum(pointNum);
        forecastable.forecast(param);
    }


    /**
     * 节假日预测 节假日预测 ： 如果库里有人工修正的曲线or台风预测的曲线，则优先上报此些曲线 不上报节假日的曲线
     */
    @Override
    public void doForecastHoliday(String uid, String cityId, String caliberId, Date startDate, Date endDate)
            throws Exception {
        HolidayParam param = new HolidayParam(uid, startDate, endDate, cityId, caliberId,
                AlgorithmEnum.HOLIDAY_FEST_SVM, cityService.findCityById(cityId).getCity());
        holidayForecastService.forecast(param);
    }

    @Override
    public void autoModelFusionforecast(String cityId, String caliberId, Date startDate, Date endDate) throws Exception {
        CityDO city = cityService.findCityById(cityId);
        ModelFusionParam modelFusionParam = new ModelFusionParam(city.getCity(), startDate, endDate,
                new String[]{cityId, caliberId, DateUtils.date2String(startDate,
                        DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR)}, caliberId, cityId);
        modelFusionForecastService.forecast(modelFusionParam);
    }

    @Override
    public void completionModelFusionforecast(String cityId, String caliberId, Date startDate, Date endDate, Integer fcWay) throws Exception {
        CityDO city = cityService.findCityById(cityId);
        ModelFusionParam modelFusionParam = new ModelFusionParam(city.getCity(), startDate, endDate,
                new String[]{cityId, caliberId, DateUtils.date2String(startDate,
                        DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR)}, com.tsintergy.aif.algorithm.serviceapi.base.constants.AlgorithmConstants.FC_TYPE_COMPLEMENT, fcWay, caliberId, cityId);
        modelFusionForecastService.forecast(modelFusionParam);
    }

    @Override
    public void autoForecastRecall(Integer forecastType, String uid, String cityId, String caliberId, Date startDate,
                                   Date endDate, List<AlgorithmEnum> enums, Integer fcstDayWeatherType, Integer type, Integer pointNum)
            throws Exception {
        logger.info(
                "开始回溯预测，startDate:" + startDate + ", endDate:" + endDate + ", cityId:" + cityId + ", caliberId:" + caliberId);
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        String city = cityService.findCityById(cityId).getCity();
        ForecastParam param = new ForecastParam(uid, cityId, city, caliberId, enums, dateList);
        param.setForecastType(forecastType);
        param.setFcstDayWeatherType(fcstDayWeatherType);
        param.setFway(type);
        param.setPointNum(pointNum);
        param.setRecall(Constants.IS_RECALL);
        forecastable.forecast(param);
    }

    /**
     * 节假日预测 节假日预测 ： 如果库里有人工修正的曲线or台风预测的曲线，则优先上报此些曲线 不上报节假日的曲线
     */
    @Override
    public void doForecastHolidayRecall(String uid, String cityId, String caliberId, Date startDate, Date endDate)
            throws Exception {
        HolidayParam param = new HolidayParam(uid, startDate, endDate, cityId, caliberId,
                AlgorithmEnum.HOLIDAY_FEST_SVM, cityService.findCityById(cityId).getCity());
        param.setIsRecall(Constants.IS_RECALL);
        holidayForecastService.forecast(param);
    }

    @Override
    public void doGruForecastRecall(Date startDate, Date endDate, Boolean userIndustry, Integer pointNum, String uid) {
        GurFactorsForecastAlgorithmParam param = new GurFactorsForecastAlgorithmParam("福建", startDate, endDate, null);
        param.setCaliberId("1");
        param.setCityId("1");
        param.setAlgorithm(com.tsintergy.aif.algorithm.serviceapi.base.dto.AlgorithmEnum.GUR_FACTORS);
        param.setRecall(Boolean.TRUE);
        param.setUserIndustry(userIndustry);
        param.setForecastNum(DateUtil.getListBetweenDay(startDate, endDate).size() + "");
        param.setNewPoint(pointNum != null ? String.valueOf(pointNum) : null);
        param.setBatchId(uid);
        try {
            shortAlgorithmForecastService.doGruFactorsForecast(param);
        } catch (Exception e) {
            log.error("Gru算法回溯预测失败", e);
            throw TsieExceptionUtils.newBusinessException("Gru算法回溯预测失败", e);
        }
    }

    @Override
    public void transForecastRecall(Date startDate, Date endDate, Integer pointNum, String uid) {
//        TransAlgorithmParam transAlgorithmParam = new TransAlgorithmParam("福建", startDate, endDate, null);
//        transAlgorithmParam.setCityId(Constants.PROVINCE_ID);
//        transAlgorithmParam.setCaliberId(Constants.CALIBER_QUAN);
//        transAlgorithmParam.setForecastNum(DateUtil.getListBetweenDay(startDate, endDate).size() + "");
//        transAlgorithmParam.setAlgorithm(com.tsintergy.aif.algorithm.serviceapi.base.dto.AlgorithmEnum.TRANS);
//        transAlgorithmParam.setTrainModeName("Predict");
//        transAlgorithmParam.setRecall(Boolean.TRUE);
//        transAlgorithmParam.setNewPoint(pointNum != null ? String.valueOf(pointNum) : null);
//        transAlgorithmParam.setBatchId(uid);
//        try {
//            shortAlgorithmForecastService.transForecast(transAlgorithmParam);
//        } catch (Exception e) {
//            log.error("Trans算法回溯预测失败", e);
//            throw TsieExceptionUtils.newBusinessException("Trans算法回溯预测失败", e);
//        }
    }

    @Override
    public void doGtNetForecastRecall(Date startDate, Date endDate, String uid) {
        GtNetForecastAlgorithmParam param = new GtNetForecastAlgorithmParam("福建", startDate, endDate, null);
        param.setCaliberId("1");
        param.setCityId("1");
        param.setAlgorithm(com.tsintergy.aif.algorithm.serviceapi.base.dto.AlgorithmEnum.GT_NET);
        param.setRecall(Boolean.TRUE);
        param.setBatchId(uid);
        try {
            shortAlgorithmForecastService.doGtNetForecast(param);
        } catch (Exception e) {
            log.error("GtNet算法回溯预测失败", e);
            throw TsieExceptionUtils.newBusinessException("GtNet算法回溯预测失败", e);
        }
    }

    @Override
    public void doConvLstmForecastRecall(Date startDate, Date endDate, Integer pointNum, String uid) {
        GurFactorsForecastAlgorithmParam param = new GurFactorsForecastAlgorithmParam("福建", startDate, endDate, null);
        param.setCaliberId("1");
        param.setCityId("1");
        param.setForecastNum(DateUtil.getListBetweenDay(startDate, endDate).size() + "");
        param.setAlgorithm(com.tsintergy.aif.algorithm.serviceapi.base.dto.AlgorithmEnum.CONV_LSTM);
        param.setRecall(Boolean.TRUE);
        param.setBatchId(uid);
        param.setNewPoint(pointNum != null ? String.valueOf(pointNum) : null);
        try {
            shortAlgorithmForecastService.doConvLstmForecast(param);
        } catch (Exception e) {
            log.error("ConvLstm算法回溯预测失败", e);
            throw TsieExceptionUtils.newBusinessException("ConvLstm算法回溯预测失败", e);
        }
    }
}
