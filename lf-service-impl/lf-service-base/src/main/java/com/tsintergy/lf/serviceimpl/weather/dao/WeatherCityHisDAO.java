package com.tsintergy.lf.serviceimpl.weather.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 *
 * <AUTHOR>
 * @version $Id: WeatherCityHisDAO.java, v 0.1 2018-01-31 10:59:58 tao Exp $$
 */
@Component
public class WeatherCityHisDAO extends BaseAbstractDAO<WeatherCityHisDO> {

    /**
     * 查询气象历史数据
     *
     * @param cityId    城市ID
     * @param type      气象类型
     * @param startDate 开始日期
     * @return endDate 结束日期
     */
    public List<WeatherCityHisDO> findWeatherCityHisDO(String cityId, Integer type, Date startDate, Date endDate) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("1");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (null != type) {
            param.getQueryConditions().put("_ne_type", type);
        }
        if (null != cityId) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<WeatherCityHisDO> weatherCityHisVOs = this.query(param).getDatas();
        return weatherCityHisVOs;
    }

    /**
     * 查询气象历史数据
     *
     * @param cityId    城市ID
     * @param startDate 开始日期
     * @return endDate 结束日期
     */
    public List<WeatherCityHisDO> findWeatherCityHisDO(String cityId, Date startDate, Date endDate) throws Exception {
        return findWeatherCityHisDO(cityId, null, startDate, endDate);
    }

    /**
     * get entities by ids and type in a period
     *
     * @param cityIds
     * @param type
     * @param startDate
     * @param endDate
     * @return
     * @throws Exception
     */
    public List<WeatherCityHisDO> getWeatherCityHisDOS(List<String> cityIds, Integer type, Date startDate, Date endDate) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (null != type) {
            param.getQueryConditions().put("_ne_type", type);
        }
        if (null != cityIds) {
            param.getQueryConditions().put("_sin_cityId", cityIds);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<WeatherCityHisDO> weatherCityHisVOS = this.query(param).getDatas();
        return weatherCityHisVOS;
    }

    public List<WeatherCityHisDO> getWeatherCityHisDOSByDates(List<Date> dateList, String cityId, Integer type) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != cityId) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        if (null != type) {
            param.getQueryConditions().put("_ne_type", type);
        }
        if (!CollectionUtils.isEmpty(dateList) && dateList.size() > 0) {
            List<java.sql.Date> dates = new ArrayList<>();
            for (Date date : dateList) {
                dates.add(new java.sql.Date(date.getTime()));
            }
            param.getQueryConditions().put("_din_date", dates);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<WeatherCityHisDO> weatherCityHisVOS = this.query(param).getDatas();
        return weatherCityHisVOS;
    }


    public List<WeatherCityHisDO> getWeatherCityHisDOs(List<String> cityIdList,Integer type,
                                                        Date startDate, Date endDate) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (!CollectionUtils.isEmpty(cityIdList)) {
            param.getQueryConditions().put("_sin_cityId", cityIdList);
        }
        if (null != type) {
            param.getQueryConditions().put("_ne_type", type);
        }
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<WeatherCityHisDO> weatherCityHisVOS = this.query(param).getDatas();
        return weatherCityHisVOS;
    }
}