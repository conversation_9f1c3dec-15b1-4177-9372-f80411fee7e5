/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.load.impl;

import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DataUtil;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsCityDayFcService;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadResultService;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/3/31 14:21
 * @Version: 1.0.0
 */
@Service(value = "loadResultService")
public class LoadResultServiceImpl implements LoadResultService {
    @Autowired
    private LoadCityFcService loadCityFcService;

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private StatisticsCityDayFcService statisticsCityDayFcService;

    @Override
    public LoadAccuracyDTO findAccuracy(String cityId, String caliberId, Date date) throws Exception {
        LoadAccuracyDTO loadAccuracyDTO = new LoadAccuracyDTO();
        //实际负荷
        List<LoadCityHisDO> loadCityHisDOS = loadCityHisService.getLoadCityHisDOS(cityId, caliberId, date, date);
        LoadCityHisDO loadCityHisDO = null;
        if (CollectionUtils.isNotEmpty(loadCityHisDOS)) {
            loadCityHisDO = loadCityHisDOS.get(0);
            loadAccuracyDTO.setHisLoad(BasePeriodUtils
                .toList(loadCityHisDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
        }
        //最终上报
        LoadCityFcDO fcDO = loadCityFcService.getReport(cityId, caliberId, date);
        if (fcDO != null) {
            List<BigDecimal> load = BasePeriodUtils
                .toList(fcDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
            loadAccuracyDTO.setReportCurve(load);
            //预测准确率
            List<BigDecimal> hisLoad = loadAccuracyDTO.getHisLoad();
            List<BigDecimal> reportCurve = loadAccuracyDTO.getReportCurve();
            List<BigDecimal> pointAccuracys = new ArrayList<>();
            for (int i = 0; i <= 95; i++) {
                BigDecimal his = hisLoad.get(i);
                BigDecimal fc = reportCurve.get(i);
                if (his != null && fc != null) {
                    BigDecimal devation = his.subtract(fc);
                    BigDecimal pointAccuracy = BigDecimal.ONE
                        .subtract(devation.abs().divide(his, 4, BigDecimal.ROUND_DOWN));
                    pointAccuracys.add(pointAccuracy);
                } else {
                    pointAccuracys.add(null);
                }
            }
            loadAccuracyDTO.setAccuracy(pointAccuracys);
            List<BigDecimal> deviation = getDeviation(fcDO, loadCityHisDO);
            loadAccuracyDTO.setDeviation(deviation);
            String algorithmId = fcDO.getAlgorithmId();
            List<StatisticsCityDayFcDO> dayFcDOS = statisticsCityDayFcService
                .getDayAccuracyList(cityId, caliberId, algorithmId, date, date);
            if (CollectionUtils.isNotEmpty(dayFcDOS)) {
                loadAccuracyDTO.setDayAccuracy(dayFcDOS.get(0).getAccuracy());
            } else {
                BigDecimal dayAccuracy = LoadCalUtil
                    .getDayAccuracy(loadCityHisDO, fcDO, Constants.LOAD_CURVE_POINT_NUM,null);
                loadAccuracyDTO.setDayAccuracy(dayAccuracy);
            }
        }
        return loadAccuracyDTO;
    }
    /**
     * 预测偏差计算公式，预测偏差=预测值-实际值；
     */
    private List<BigDecimal> getDeviation(LoadCityFcDO loadCityFcDO, LoadCityHisDO loadCityHisDO) {
        Map<String, BigDecimal> map = new HashMap<>();
        if (loadCityFcDO != null && loadCityHisDO != null) {
            Map<String, BigDecimal> hisMap = BasePeriodUtils
                .toMap(loadCityHisDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
            Map<String, BigDecimal> fcMap = BasePeriodUtils
                .toMap(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
            for (String column : hisMap.keySet()) {
                BigDecimal his = hisMap.get(column);
                BigDecimal fc = fcMap.get(column);
                map.put(column, getDeviationNumber(fc, his));
            }
        }
        TreeMap<String, BigDecimal> treeMap = new TreeMap<>(map);
        List<BigDecimal> deviationList = DataUtil.mapToList(treeMap);
        return deviationList;
    }
    /**
     * 两个数相减（减数-被减数）
     */
    private BigDecimal getDeviationNumber(BigDecimal meiosis, BigDecimal minuend) {
        if (meiosis == null || minuend == null) {
            return null;
        }
        return meiosis.subtract(minuend);
    }

}