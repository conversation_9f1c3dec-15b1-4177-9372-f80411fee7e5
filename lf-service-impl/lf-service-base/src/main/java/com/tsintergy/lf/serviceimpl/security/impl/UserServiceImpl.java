package com.tsintergy.lf.serviceimpl.security.impl;


import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.LoadUserDO;
import com.tsintergy.lf.serviceapi.base.security.api.UserService;
import com.tsintergy.lf.serviceimpl.security.dao.LoadUserDAO;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @date: 6/14/18 4:44 PM
 * @author: angel
 **/
@Service("userService")
public class UserServiceImpl extends BaseServiceImpl implements UserService {

    @Autowired
    LoadUserDAO loadUserDAO;

    @Autowired
    CityService cityService;

    @Override
    public DataPackage queryLoaderVO(DBQueryParam param) throws BusinessException {
        try {
            return loadUserDAO.query(param);
        } catch (BusinessException var3) {
            throw var3;
        } catch (Exception var4) {
            throw new BusinessException("",var4.getMessage(), var4);
        }
    }

    @Override
    public LoadUserDO findUserById(String id) throws BusinessException {
        try {
            return (LoadUserDO) this.loadUserDAO.findByPk(id);
        }  catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("",e.getMessage(), e);
        }
    }

    @Override
    public void doDeleteUserById(String id) throws BusinessException {
        try {
            this.loadUserDAO.removeByPk(id);
        }catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("",e.getMessage(), e);
        }
    }

    @Override
    public LoadUserDO doSaveOrUpdateUser(LoadUserDO loadUserDO) throws BusinessException {
        try {
            LoadUserDO queryVO = this.findUserById(loadUserDO.getTsieUId());
            if (null != queryVO) {
                BeanUtils.copyProperties(loadUserDO,queryVO);
                this.loadUserDAO.updateAndFlush(queryVO);
            } else {
                this.loadUserDAO.createAndFlush(loadUserDO);
            }
            return loadUserDO;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("",e.getMessage(), e);
        }
    }

    @Override
    public List<LoadUserDO> findLoadUserList(String cityId) {
        try {
            List<LoadUserDO> queryVO = loadUserDAO.findLoadUser(cityId);
            List<CityDO> allCitys = cityService.findAllCitys();
            Map<String, String> collect = allCitys.stream().collect(
                Collectors.toMap(CityDO::getId, CityDO::getCity));
            if (null != queryVO) {
                for (LoadUserDO use : queryVO) {
                    use.setCity(collect.get(use.getCityId()));
                }
                return queryVO;
            }
            return null;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("",e.getMessage(), e);
        }
    }

    @Override
    public LoadUserDO findByCloudUserId(String cloudUserId) {
        try {
            DBQueryParam dbQueryParam = DBQueryParamBuilder.create()
                    .where(QueryOp.StringLike, "cloudUserId", cloudUserId)
                    .build();
            List<LoadUserDO> datas = loadUserDAO.query(dbQueryParam).getDatas();
            return datas.size() > 0 ? datas.get(0) : null;
        } catch (BusinessException var3) {
            throw var3;
        } catch (Exception var4) {
            throw new BusinessException("",var4.getMessage(), var4);
        }
    }
}
