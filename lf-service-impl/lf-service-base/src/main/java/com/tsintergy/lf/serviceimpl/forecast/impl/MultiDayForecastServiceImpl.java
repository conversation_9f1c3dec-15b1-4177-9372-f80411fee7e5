package com.tsintergy.lf.serviceimpl.forecast.impl;

import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.area.api.BaseAreaService;
import com.tsintergy.lf.serviceapi.base.area.pojo.BaseAreaDO;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.datamanage.dto.*;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcBatchService;
import com.tsintergy.lf.serviceapi.base.forecast.api.MultiDayForecastService;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcBatchDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureStatService;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureFcDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.api.*;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcBatchDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.load.dto.DayValueDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/** @Description
 * <AUTHOR>
 * @Date 2025/8/11 14:08
 **/
@Service("multiDayForecastService")
public class MultiDayForecastServiceImpl implements MultiDayForecastService {

    @Autowired
    private LoadCityFcBatchService loadCityFcBatchService;

    @Autowired
    private LoadCityHisService loadCityHisService;


    @Autowired
    private LoadFeatureStatService loadFeatureStatService;

    @Autowired
    private CityService cityService;

    @Autowired
    private WeatherCityHisAdapterService weatherCityHisService;

    @Autowired
    private WeatherCityFcBatchService weatherCityFcBatchService;

    @Autowired
    private WeatherFeatureStatService weatherFeatureStatService;

    @Autowired
    private BaseAreaService baseAreaService;
    /**
     * 查询多日负荷数据（预测、上报和实际值）
     * @param cityId 城市ID
     * @param caliberId 口径ID
     * @param forecastDate 预测基准日期
     * @param startDay 起始天数偏移（相对于forecastDate）
     * @param endDay 结束天数偏移
     * @param algorithmId 算法ID
     * @param batchId 批次ID
     * @return MultiDayQueryDTO 包含多日负荷数据和特征值
     * @throws Exception
     */
    public MultiDayQueryDTO findMultiDayLoad(String cityId, String caliberId, Date forecastDate,
                                             Integer startDay, Integer endDay, String algorithmId,
                                             String batchId) throws Exception {

        // 1. 计算日期范围
        Date startDate = DateUtil.getMoveDay(forecastDate, startDay);
        Date endDate = DateUtil.getMoveDay(forecastDate, endDay);
        List<Date> dateRange = DateUtil.getListBetweenDay(startDate, endDate);

        // 2. 获取三种类型的数据源
        // 2.1 多日算法预测负荷
        List<LoadCityFcBatchDO> allForecasts = loadCityFcBatchService.findLoadByCreateTime(
                null, forecastDate, caliberId, algorithmId, batchId, null);
        List<LoadCityFcBatchDO> algoForecasts = allForecasts.stream().filter(t->t.getCityId().equals(cityId)).collect(Collectors.toList());
        Map<Date, LoadCityFcBatchDO> algoForecastMap = algoForecasts.stream()
                .collect(Collectors.toMap(LoadCityFcBatchDO::getDate, Function.identity(), (o, n) -> n));

        List<BaseAreaDO> allAreas = baseAreaService.getAllAreas();
        List<String> areaIds = allAreas.stream().map(BaseAreaDO::getId).collect(Collectors.toList());
        List<LoadCityFcBatchDO> areaForecasts = allForecasts.stream().filter(t -> areaIds.contains(t.getCityId())).collect(Collectors.toList());
        Map<Date, List<LoadCityFcBatchDO>> areaForecastMap = areaForecasts.stream()
                .collect(Collectors.groupingBy(LoadCityFcBatchDO::getDate));

        // 2.2 多日人工上报负荷
        List<LoadCityFcBatchDO> manualReports = loadCityFcBatchService.findLoadByCreateTime(
                cityId, forecastDate, caliberId, null, batchId, true);
        Map<Date, LoadCityFcBatchDO> manualReportMap = manualReports.stream()
                .collect(Collectors.toMap(LoadCityFcBatchDO::getDate, Function.identity(), (o, n) -> n));

        // 2.3 多日历史实际负荷
        List<LoadCityHisDO> historicalLoads = loadCityHisService.getLoadCityHisDOS(
                cityId, caliberId, startDate, endDate);
        Map<Date, LoadCityHisDO> historicalLoadMap = historicalLoads.stream()
                .collect(Collectors.toMap(LoadCityHisDO::getDate, Function.identity(), (o, n) -> n));

        // 3. 初始化返回结果结构
        MultiDayQueryDTO result = new MultiDayQueryDTO();
        List<MultiDayLoadQueryDTO> loadResults = new ArrayList<>();
        List<MultiDayFeatureQueryDTO> forecastFeatures = new ArrayList<>();
        List<MultiDayFeatureQueryDTO> historicalFeatures = new ArrayList<>();

        result.setMultiDayLoad(loadResults);
        result.setMultiDayFcFeature(forecastFeatures);
        result.setMultiDayHisFeature(historicalFeatures);

        // 4. 按日期填充数据
        for (Date currentDate : dateRange) {
            // 4.1 创建基础负荷数据对象
            MultiDayLoadQueryDTO dailyLoad = new MultiDayLoadQueryDTO();
            dailyLoad.setDate(currentDate);
            loadResults.add(dailyLoad);

            // 4.2 处理预测数据
            MultiDayFeatureQueryDTO forecastFeature = new MultiDayFeatureQueryDTO();
            forecastFeature.setDate(currentDate);
            forecastFeatures.add(forecastFeature);

            LoadCityFcBatchDO algoForecast = algoForecastMap.get(currentDate);
            if (algoForecast != null) {
                dailyLoad.setFc(algoForecast.getLoadList());
                LoadFeatureFcDTO forecastStats = loadFeatureStatService.findStatisticsDayFeature(algoForecast);
                forecastFeature.setFeature(forecastStats);
            }

            // 4.3 处理上报数据
            LoadCityFcBatchDO manualReport = manualReportMap.get(currentDate);
            if (manualReport != null) {
                dailyLoad.setModify(manualReport.getLoadList());
            }

            // 4.4 处理历史数据
            MultiDayFeatureQueryDTO historicalFeature = new MultiDayFeatureQueryDTO();
            historicalFeature.setDate(currentDate);
            historicalFeatures.add(historicalFeature);

            LoadCityHisDO historicalLoad = historicalLoadMap.get(currentDate);
            if (historicalLoad != null) {
                dailyLoad.setReal(historicalLoad.getloadList());
                LoadFeatureFcDTO historicalStats = loadFeatureStatService.findStatisticsDayFeature(algoForecast);
                historicalFeature.setFeature(historicalStats);
            }

            // 分区预测数据
            List<LoadCityFcBatchDO> areaFcBatchDOS = areaForecastMap.get(currentDate);
            if (!CollectionUtils.isEmpty(areaFcBatchDOS)){
                List<BigDecimal> areaFcList = ColumnUtil.getZeroOrNullList(Constants.LOAD_CURVE_POINT_NUM,BigDecimal.ZERO);
                for (LoadCityFcBatchDO areaFcBatchDO : areaFcBatchDOS) {
                    List<BigDecimal> bigDecimals = areaFcBatchDO.getloadList();
                    areaFcList = BigDecimalFunctions.listAdd(areaFcList, bigDecimals);
                }
                dailyLoad.setFiveFcTotal(areaFcList);
            }
        }

        // 5. 根据forecastFeatures集合计算多日负荷特性并赋值给MultiDayQueryDTO
        calculateMultiDayLoadCharacteristics(result, forecastFeatures);

        return result;
    }


    /**
     * 查询多日天气数据（预测和实际值）
     * @param cityId 城市ID
     * @param forecastDate 预测基准日期
     * @param startDay 起始天数偏移（相对于forecastDate）
     * @param endDay 结束天数偏移
     * @param weatherSource 天气数据源
     * @param batchId 批次ID
     * @return MultiDayQueryWeatherDTO 包含多日天气数据和特征值
     * @throws Exception
     */
    public MultiDayQueryWeatherDTO findMultiDayWeather(String cityId, Date forecastDate,
                                                       Integer startDay, Integer endDay,
                                                       String weatherSource, String batchId)
            throws Exception {
        // 1. 初始化并验证参数
        if (cityId == null || forecastDate == null) {
            throw new IllegalArgumentException("城市ID和预测日期不能为空");
        }

        // 2. 准备日期范围
        String weatherCityId = cityService.findWeatherCityId(cityId);
        Date startDate = DateUtil.getMoveDay(forecastDate, startDay);
        Date endDate = DateUtil.getMoveDay(forecastDate, endDay);
        List<Date> dateRange = DateUtil.getListBetweenDay(startDate, endDate);

        // 3. 获取三种数据源
        // 3.1 天气预测数据
        List<WeatherCityFcBatchDO> weatherForecasts = weatherCityFcBatchService.findByCondition(
                weatherCityId, weatherSource, forecastDate, null, batchId);
        Map<Date, List<WeatherCityFcBatchDO>> weatherForecastMap = weatherForecasts.stream()
                .collect(Collectors.groupingBy(WeatherCityFcBatchDO::getDate));

        // 3.2 历史天气数据
        List<WeatherCityHisDO> historicalWeather = weatherCityHisService.findHisWeather(weatherSource,
                weatherCityId, startDate, endDate);
        Map<Date, List<WeatherCityHisDO>> historicalWeatherMap = historicalWeather.stream()
                .collect(Collectors.groupingBy(WeatherCityHisDO::getDate));


        // 4. 初始化返回结果
        MultiDayQueryWeatherDTO result = new MultiDayQueryWeatherDTO();
        List<MultiDayLoadQueryDTO> weatherDataList = new ArrayList<>();
        List<MultiDayWeatherFeatureQueryDTO> historicalFeatureList = new ArrayList<>();
        List<MultiDayWeatherFeatureQueryDTO> forecastFeatureList = new ArrayList<>();

        result.setMultiDayLoad(weatherDataList);
        result.setMultiDayHisFeature(historicalFeatureList);
        result.setMultiDayFcFeature(forecastFeatureList);

        // 5. 按日期填充数据
        for (Date currentDate : dateRange) {
            // 5.1 创建基础天气数据对象
            MultiDayLoadQueryDTO dailyWeather = new MultiDayLoadQueryDTO();
            dailyWeather.setDate(currentDate);
            weatherDataList.add(dailyWeather);

            // 5.2 处理预测数据
            processForecastData(currentDate, weatherForecastMap, dailyWeather, forecastFeatureList);

            // 5.3 处理历史数据
            processHistoricalData(currentDate, historicalWeatherMap,
                    dailyWeather, historicalFeatureList);
        }

        // 6. 根据forecastFeatureList集合计算多日气象特性并赋值给MultiDayQueryWeatherDTO
        calculateMultiDayWeatherCharacteristics(result, forecastFeatureList);

        return result;
    }

    /**
     * 处理天气预测数据
     */
    private void processForecastData(Date date, Map<Date, List<WeatherCityFcBatchDO>> forecastMap,
                                     MultiDayLoadQueryDTO dailyWeather,
                                     List<MultiDayWeatherFeatureQueryDTO> featureList) throws Exception {
        List<WeatherCityFcBatchDO> forecastList = forecastMap.get(date);
        MultiDayWeatherFeatureQueryDTO forecastFeature = new MultiDayWeatherFeatureQueryDTO();
        forecastFeature.setDate(date);
        featureList.add(forecastFeature);

        if (!CollectionUtils.isEmpty(forecastList)) {
            // 获取温度预测数据
            List<WeatherCityFcBatchDO> tempForecasts = forecastList.stream()
                    .filter(t -> WeatherEnum.TEMPERATURE.getType().equals(t.getType()))
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(tempForecasts)) {
                dailyWeather.setFc(tempForecasts.get(0).getWeatherList());
            }

            // 计算天气特征
            WeatherFeatureDTO featureDTO = new WeatherFeatureDTO();
            WeatherFeatureCityDayFcDO featureData = weatherFeatureStatService
                    .doStatWeatherByCityAndType(forecastList);
            BeanUtils.copyProperties(featureData, featureDTO);
            forecastFeature.setFeature(featureDTO);
        }
    }

    /**
     * 处理历史天气数据
     */
    private void processHistoricalData(Date date, Map<Date, List<WeatherCityHisDO>> historicalMap,
                                       MultiDayLoadQueryDTO dailyWeather,
                                       List<MultiDayWeatherFeatureQueryDTO> featureList) throws Exception {
        // 设置历史天气数据
        // 设置历史天气特征
        MultiDayWeatherFeatureQueryDTO historicalFeature = new MultiDayWeatherFeatureQueryDTO();
        historicalFeature.setDate(date);
        featureList.add(historicalFeature);
        List<WeatherCityHisDO> historicalData = historicalMap.get(date);
        if (historicalData != null) {
            List<WeatherCityHisDO> tempList = historicalData.stream().filter(t -> t.getType().equals(WeatherEnum.TEMPERATURE.getType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(tempList)) {
                dailyWeather.setReal(tempList.get(0).getloadList());
            }
            WeatherFeatureDTO featureDTO = new WeatherFeatureDTO();
            WeatherFeatureCityDayFcDO featureData = weatherFeatureStatService
                    .doStatWeatherByCityAndType(historicalData);
            BeanUtils.copyProperties(featureData, featureDTO);
            historicalFeature.setFeature(featureDTO);

        }
    }

    /**
     * 根据预测特性集合计算多日负荷特性并赋值给MultiDayQueryDTO
     * @param result 结果对象
     * @param forecastFeatures 预测特性集合
     */
    private void calculateMultiDayLoadCharacteristics(MultiDayQueryDTO result,
                                                     List<MultiDayFeatureQueryDTO> forecastFeatures) {
        if (CollectionUtils.isEmpty(forecastFeatures)) {
            return;
        }



        // 初始化所有极值对象
        ExtremeValue maxLoad = new ExtremeValue();
        ExtremeValue maxNoonLoad = new ExtremeValue();
        ExtremeValue maxEveningLoad = new ExtremeValue();
        ExtremeValue minLoad = new ExtremeValue();
        ExtremeValue maxDifferent = new ExtremeValue();
        ExtremeValue minNoonLoad = new ExtremeValue();
        ExtremeValue minEveningLoad = new ExtremeValue();
        ExtremeValue maxGradient = new ExtremeValue();

        // 遍历所有预测特性数据
        for (MultiDayFeatureQueryDTO featureQuery : forecastFeatures) {
            if (featureQuery.getFeature() == null) {
                continue;
            }

            LoadFeatureFcDTO feature = featureQuery.getFeature();
            Date currentDate = featureQuery.getDate();

            // 计算各种极值
            maxLoad.updateMax(feature.getMaxLoad(), currentDate);
            maxNoonLoad.updateMax(feature.getNoonMaxLoad(), currentDate);
            maxEveningLoad.updateMax(feature.getEveningMaxLoad(), currentDate);
            minLoad.updateMin(feature.getMinLoad(), currentDate);
            maxDifferent.updateMax(feature.getDifferent(), currentDate);
            minNoonLoad.updateMin(feature.getNoonMaxLoad(), currentDate);
            minEveningLoad.updateMin(feature.getEveningMaxLoad(), currentDate);
            maxGradient.updateMax(feature.getGradient(), currentDate);
        }

        // 赋值给结果对象
        setDayValueIfNotNull(result::setMultiDayMaxLoad, maxLoad.value, maxLoad.date);
        setDayValueIfNotNull(result::setMultiDayMaxNoonLoad, maxNoonLoad.value, maxNoonLoad.date);
        setDayValueIfNotNull(result::setMultiDayMaxEveningLoad, maxEveningLoad.value, maxEveningLoad.date);
        setDayValueIfNotNull(result::setMultiDayMinLoad, minLoad.value, minLoad.date);
        setDayValueIfNotNull(result::setMultiDayDifMaxLoad, maxDifferent.value, maxDifferent.date);
        setDayValueIfNotNull(result::setMultiDayMinNoonLoad, minNoonLoad.value, minNoonLoad.date);
        setDayValueIfNotNull(result::setMultiDayMinEveningLoad, minEveningLoad.value, minEveningLoad.date);
        setDayValueIfNotNull(result::setMultiDayDifMaxRate, maxGradient.value, maxGradient.date);
    }

    /**
     * 设置DayValueDTO如果值不为null
     */
    private void setDayValueIfNotNull(java.util.function.Consumer<DayValueDTO> setter, BigDecimal value, Date date) {
        if (value != null) {
            DayValueDTO dto = new DayValueDTO();
            dto.setValue(value);
            dto.setDate(date);
            setter.accept(dto);
        }
    }

    /**
     * 根据预测气象特性集合计算多日气象特性并赋值给MultiDayQueryWeatherDTO
     * @param result 结果对象
     * @param forecastFeatureList 预测气象特性集合
     */
    private void calculateMultiDayWeatherCharacteristics(MultiDayQueryWeatherDTO result,
                                                        List<MultiDayWeatherFeatureQueryDTO> forecastFeatureList) {
        if (CollectionUtils.isEmpty(forecastFeatureList)) {
            return;
        }
        

        // 初始化所有气象极值对象
        ExtremeValue maxTemp = new ExtremeValue();           // 多日最高温度
        ExtremeValue minTemp = new ExtremeValue();           // 多日最低温度
        ExtremeValue maxEffectiveTemp = new ExtremeValue();  // 多日最高体感温度
        ExtremeValue minEffectiveTemp = new ExtremeValue();  // 多日最低体感温度
        ExtremeValue maxRainfall = new ExtremeValue();       // 多日最大日降雨量
        ExtremeValue maxHumidity = new ExtremeValue();       // 多日最大相对湿度
        ExtremeValue maxWindSpeed = new ExtremeValue();      // 多日最大风速

        // 遍历所有预测气象特性数据
        for (MultiDayWeatherFeatureQueryDTO featureQuery : forecastFeatureList) {
            if (featureQuery.getFeature() == null) {
                continue;
            }

            WeatherFeatureDTO feature = featureQuery.getFeature();
            Date currentDate = featureQuery.getDate();

            // 计算各种气象极值
            maxTemp.updateMax(feature.getHighestTemperature(), currentDate);
            minTemp.updateMin(feature.getLowestTemperature(), currentDate);
            maxEffectiveTemp.updateMax(feature.getHighestEffectiveTemperature(), currentDate);
            minEffectiveTemp.updateMin(feature.getLowestEffectiveTemperature(), currentDate);
            maxHumidity.updateMax(feature.getAveHumidity(), currentDate);
            maxWindSpeed.updateMax(feature.getMaxWinds(), currentDate);
            maxRainfall.updateMax(feature.getRainfall(), currentDate);
        }

        // 赋值给结果对象
        setDayValueIfNotNull(result::setMultiDayMaxTemp, maxTemp.value, maxTemp.date);
        setDayValueIfNotNull(result::setMultiDayMinTemp, minTemp.value, minTemp.date);
        setDayValueIfNotNull(result::setMultiDayMaxEfTemp, maxEffectiveTemp.value, maxEffectiveTemp.date);
        setDayValueIfNotNull(result::setMultiDayMinEfTemp, minEffectiveTemp.value, minEffectiveTemp.date);
        setDayValueIfNotNull(result::setMultiDayMaxRain, maxRainfall.value, maxRainfall.date);
        setDayValueIfNotNull(result::setMultiDayMaxHum, maxHumidity.value, maxHumidity.date);
        setDayValueIfNotNull(result::setMultiDayMaxWind, maxWindSpeed.value, maxWindSpeed.date);
    }

    // 使用内部类来存储极值信息
    class ExtremeValue {
        BigDecimal value;
        Date date;

        void updateMax(BigDecimal newValue, Date newDate) {
            if (newValue != null && (value == null || newValue.compareTo(value) > 0)) {
                value = newValue;
                date = newDate;
            }
        }

        void updateMin(BigDecimal newValue, Date newDate) {
            if (newValue != null && (value == null || newValue.compareTo(value) < 0)) {
                value = newValue;
                date = newDate;
            }
        }
    }
}
