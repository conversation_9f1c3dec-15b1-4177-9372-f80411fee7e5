
package com.tsintergy.lf.serviceimpl.load.impl;


import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityQuarterHisService;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureExtendDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityQuarterHisDO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadFeatureCityQuarterHisDAO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $Id: LoadFeatureCityQuarterHisServiceImpl.java, v 0.1 2018-01-31 10:52:00 tao Exp $$
 */

@Service("loadFeatureCityQuarterHisService")
public class LoadFeatureCityQuarterHisServiceImpl extends BaseServiceImpl implements LoadFeatureCityQuarterHisService {
    private static final Logger logger = LogManager.getLogger( LoadFeatureCityQuarterHisServiceImpl.class);



   @Autowired
    CityService cityService;

   @Autowired
    LoadFeatureCityQuarterHisDAO loadFeatureCityQuarterHisDAO;

    @Override
    public DataPackage queryLoadFeatureCityQuarterHisVO(DBQueryParam param) throws Exception{
        try {
            return loadFeatureCityQuarterHisDAO.query(param);
        }
        catch (BusinessException e) {
            throw e;
        }
        catch (Exception e) {
             throw e;
        }
    }
    
    @Override
    public LoadFeatureCityQuarterHisDO doCreate(LoadFeatureCityQuarterHisDO vo) throws Exception{
        try {
            return (LoadFeatureCityQuarterHisDO)loadFeatureCityQuarterHisDAO.createAndFlush(vo);
        }
        catch (BusinessException e) {
            throw e;
        }
        catch (Exception e) {
             throw e;
        }
    }

    @Override
    public void doRemoveLoadFeatureCityQuarterHisVO(LoadFeatureCityQuarterHisDO vo) throws Exception{
         try {
            loadFeatureCityQuarterHisDAO.remove(vo);
        }
        catch (BusinessException e) {
            throw e;
        }
        catch (Exception e) {
             throw e;
        }
    }
    
    @Override
    public void doRemoveLoadFeatureCityQuarterHisVOByPK(Serializable pk) throws Exception{
        try {
            loadFeatureCityQuarterHisDAO.removeByPk(pk);
        }
        catch (BusinessException e) {
            throw e;
        }
        catch (Exception e) {
             throw e;
        }
    }

    @Override
    public LoadFeatureCityQuarterHisDO doUpdateLoadFeatureCityQuarterHisVO(LoadFeatureCityQuarterHisDO vo) throws Exception{
         try {
            return (LoadFeatureCityQuarterHisDO)loadFeatureCityQuarterHisDAO.updateAndFlush(vo);
        }
        catch (BusinessException e) {
            throw e;
        }
        catch (Exception e) {
             throw e;
        }
    }

    @Override
    public LoadFeatureCityQuarterHisDO findLoadFeatureCityQuarterHisVOByPk(Serializable pk) throws Exception{
         try {
            return (LoadFeatureCityQuarterHisDO)loadFeatureCityQuarterHisDAO.findVOByPk(pk);
        }
        catch (BusinessException e) {
            throw e;
        }
        catch (Exception e) {
             throw e;
        }
    }

    @Override
    public LoadFeatureCityQuarterHisDO getLoadFeatureCityQuarterHisVO(String cityId, String year, String quarter, String caliberId) throws Exception {
        return loadFeatureCityQuarterHisDAO.getLoadFeatureCityQuarterHisVO(cityId,year,quarter,caliberId);
    }


    @Override
    public List<LoadFeatureCityQuarterHisDO> getLoadFeatureCityQuarterHisVOS(String cityId, String startYQ, String endYQ, String caliberId) throws Exception {
        return loadFeatureCityQuarterHisDAO.getLoadFeatureCityQuarterHisVOs(cityId, startYQ, endYQ, caliberId);
    }


    @Override
    public List<LoadFeatureExtendDTO> findLoadFeatureExtendDTOS(String cityId, Date startDate, Date endDate, String caliberId) throws Exception {
        String startYQ = DateUtil.getQuarterByDate(startDate);
        String endYQ = DateUtil.getQuarterByDate(endDate);
        List<LoadFeatureCityQuarterHisDO> loadFeatureCityQuarterHisVOS = this.getLoadFeatureCityQuarterHisVOS(cityId,startYQ,endYQ,caliberId);
        List<LoadFeatureExtendDTO> loadFeatureExtendDTOS = new ArrayList<LoadFeatureExtendDTO>(30);
        for (LoadFeatureCityQuarterHisDO loadFeatureCityQuarterHisVO : loadFeatureCityQuarterHisVOS) {
            LoadFeatureExtendDTO loadFeatureExtendDTO = new LoadFeatureExtendDTO();
            BeanUtils.copyProperties(loadFeatureExtendDTO,loadFeatureCityQuarterHisVO);
            loadFeatureExtendDTO.setDate(loadFeatureCityQuarterHisVO.getYear() + "-" + loadFeatureCityQuarterHisVO.getQuarter());
            loadFeatureExtendDTO.setCity(cityService.findCityById(loadFeatureCityQuarterHisVO.getCityId()).getCity());
            loadFeatureExtendDTOS.add(loadFeatureExtendDTO);
        }
        return loadFeatureExtendDTOS;
    }
}
