package com.tsintergy.lf.serviceimpl.weather.dao;


import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityMonthHisDO;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @version $Id: WeatherFeatureCityMonthHisDAO.java, v 0.1 2018-04-04 09:53:47 tao Exp $$
 */

@Component
@Slf4j
public class WeatherFeatureCityMonthHisDAO extends BaseAbstractDAO<WeatherFeatureCityMonthHisDO> {

    /**
     * 获取月气象特性
     *
     * @param cityId 城市ID
     * @param year   年(yyyy)
     * @param month  月(MM)
     * @return
     */
    public WeatherFeatureCityMonthHisDO getWeatherFeatureCityMonthHisDO(String cityId, String year, String month) throws Exception {

        if (cityId == null) {
            throw new BusinessException("","城市ID不可为空");
        }

        if (year == null) {
            throw new BusinessException("","年份不可为空");
        }

        if (month == null) {
            throw new BusinessException("","月份不可为空");
        }

        DBQueryParam param = DBQueryParamBuilder.create().build();
        
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        param.getQueryConditions().put("_ne_year", year);
        param.getQueryConditions().put("_ne_month", month);
        param.getQueryConditions().put("_ne_cityId", cityId);
        List<WeatherFeatureCityMonthHisDO> weatherFeatureCityMonthHisVOs = this.query(param).getDatas();
        if (weatherFeatureCityMonthHisVOs.size() > 0) {
            return weatherFeatureCityMonthHisVOs.get(0);
        }
        return null;
    }

    /**
     * 统计月气象特性
     *
     * @param weatherFeatureCityDayHisVOs 一个月的日气象特性
     * @return
     * @throws Exception
     */
    public WeatherFeatureCityMonthHisDO statisticsMonthFeature(List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisVOs) throws Exception {

        if (weatherFeatureCityDayHisVOs == null || weatherFeatureCityDayHisVOs.size() == 0) {
            return null;
        }

        if (weatherFeatureCityDayHisVOs.size() > 31) {
            log.error("统计月气象特性有误：日气象特性数据的超过31天，无法统计");
            return null;
        }

        Map<String, List<BigDecimal>> dataMap = new HashMap<String, List<BigDecimal>>();
        Field[] field = WeatherFeatureCityMonthHisDO.class.getDeclaredFields();
        for(int i=0 ; i<field.length ; i++) {     //遍历所有属性
            String name = field[i].getName();    //获取属性的名字
            if(!name.equals("id") && !name.equals("cityId") && !name.equals("year") && !name.equals("month") && !name.equals("createtime") && !name.equals("updatetime")) {
                dataMap.put(name, new ArrayList<BigDecimal>());
            }
        }

        String cityId = weatherFeatureCityDayHisVOs.get(0).getCityId();
        String ym = DateUtils.date2String(weatherFeatureCityDayHisVOs.get(0).getDate(), DateFormatType.YEAR_MONTH_STR);

        for (WeatherFeatureCityDayHisDO weatherFeatureCityDayHisVO : weatherFeatureCityDayHisVOs) {

            if (!ym.equals(DateUtil.getMonthByDate(weatherFeatureCityDayHisVO.getDate()))) {
                log.error("统计月气象特性有误：日气象特性数据的日期不是同一个月，无法统计");
                return null;
            }

            if (!cityId.equals(weatherFeatureCityDayHisVO.getCityId())) {
                log.error("统计月气象特性有误：日气象特性数据的日期不是同一城市，无法统计");
                return null;
            }

            Field[] field2 = weatherFeatureCityDayHisVO.getClass().getDeclaredFields();
            for(int j=0 ; j<field2.length ; j++) {     //遍历所有属性
                String name = field2[j].getName();    //获取属性的名字
                if(dataMap.get(name) != null) {
                    Method m = weatherFeatureCityDayHisVO.getClass().getMethod("get"+name.substring(0,1).toUpperCase()+name.substring(1));
                    BigDecimal value = (BigDecimal)m.invoke(weatherFeatureCityDayHisVO);
                    if(value == null) {
                        value = BigDecimal.ZERO;
                    }
                    dataMap.get(name).add(value);
                }
            }

        }

        WeatherFeatureCityMonthHisDO weatherFeatureCityMonthHisVO = new WeatherFeatureCityMonthHisDO();
        weatherFeatureCityMonthHisVO.setCityId(cityId);
        weatherFeatureCityMonthHisVO.setYear(ym.substring(0, 4));
        weatherFeatureCityMonthHisVO.setMonth(ym.substring(5, 7));
        weatherFeatureCityMonthHisVO.setAveColdness(BigDecimalUtils.avgList(dataMap.get("aveColdness"),4,false));
        weatherFeatureCityMonthHisVO.setAveComfort(BigDecimalUtils.avgList(dataMap.get("aveComfort"),4,false));
        weatherFeatureCityMonthHisVO.setAveEffectiveTemperature(BigDecimalUtils.avgList(dataMap.get("aveEffectiveTemperature"),4,false));
        weatherFeatureCityMonthHisVO.setAveHumidity(BigDecimalUtils.avgList(dataMap.get("aveHumidity"),4,false));
        weatherFeatureCityMonthHisVO.setAveTemperature(BigDecimalUtils.avgList(dataMap.get("aveTemperature"),4,false));
        weatherFeatureCityMonthHisVO.setAveTemperatureHumidity(BigDecimalUtils.avgList(dataMap.get("aveTemperatureHumidity"),4,false));
        weatherFeatureCityMonthHisVO.setAveWinds(BigDecimalUtils.avgList(dataMap.get("aveWinds"),4,false));
        weatherFeatureCityMonthHisVO.setHighestComfort(LoadCalUtil.max(dataMap.get("highestComfort")));
        weatherFeatureCityMonthHisVO.setHighestEffectiveTemperature(LoadCalUtil.max(dataMap.get("highestEffectiveTemperature")));
        weatherFeatureCityMonthHisVO.setHighestHumidity(LoadCalUtil.max(dataMap.get("highestHumidity")));
        weatherFeatureCityMonthHisVO.setHighestTemperature(LoadCalUtil.max(dataMap.get("highestTemperature")));
        weatherFeatureCityMonthHisVO.setLowestComfort(LoadCalUtil.min(dataMap.get("lowestComfort")));
        weatherFeatureCityMonthHisVO.setLowestEffectiveTemperature(LoadCalUtil.min(dataMap.get("lowestEffectiveTemperature")));
        weatherFeatureCityMonthHisVO.setLowestHumidity(LoadCalUtil.min(dataMap.get("lowestHumidity")));
        weatherFeatureCityMonthHisVO.setLowestTemperature(LoadCalUtil.min(dataMap.get("lowestTemperature")));
        weatherFeatureCityMonthHisVO.setMaxColdness(LoadCalUtil.max(dataMap.get("maxColdness")));
        weatherFeatureCityMonthHisVO.setMaxTemperatureHumidity(LoadCalUtil.max(dataMap.get("maxTemperatureHumidity")));
        weatherFeatureCityMonthHisVO.setMaxWinds(LoadCalUtil.max(dataMap.get("maxWinds")));
        weatherFeatureCityMonthHisVO.setMinColdness(LoadCalUtil.max(dataMap.get("minColdness")));
        weatherFeatureCityMonthHisVO.setMinTemperatureHumidity(LoadCalUtil.max(dataMap.get("minTemperatureHumidity")));
        weatherFeatureCityMonthHisVO.setMinWinds(LoadCalUtil.max(dataMap.get("minWinds")));
        weatherFeatureCityMonthHisVO.setRainfall(BigDecimalUtils.addAllValue(dataMap.get("rainfall")));

        return weatherFeatureCityMonthHisVO;
    }


    /**
     * 统计月气象特性
     *
     * @param weatherFeatureCityDayHisVOs 日气象特性列表
     * @return
     * @throws Exception
     */
    public List<WeatherFeatureCityMonthHisDO> statisticsMonthFeatures(List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisVOs) throws Exception {

        List<WeatherFeatureCityMonthHisDO> weatherFeatureCityMonthHisVOs = new ArrayList<WeatherFeatureCityMonthHisDO>();

        if (weatherFeatureCityDayHisVOs != null) {

            Map<String, List<WeatherFeatureCityDayHisDO>> weatherFeatureMap = new HashMap<String, List<WeatherFeatureCityDayHisDO>>(); // Map<cityId_ym,List<WeatherFeatureCityDayHisDO>>
            for (WeatherFeatureCityDayHisDO weatherFeatureCityDayHisVO : weatherFeatureCityDayHisVOs) {
                String ym = DateUtil.getMonthByDate(weatherFeatureCityDayHisVO.getDate());
                if (!weatherFeatureMap.containsKey(weatherFeatureCityDayHisVO.getCityId() + "_" + ym)) {
                    weatherFeatureMap.put(weatherFeatureCityDayHisVO.getCityId() + "_" + ym, new ArrayList<WeatherFeatureCityDayHisDO>());
                }
                weatherFeatureMap.get(weatherFeatureCityDayHisVO.getCityId() + "_" + ym).add(weatherFeatureCityDayHisVO);
            }

            for (String key : weatherFeatureMap.keySet()) {
                try {
                    WeatherFeatureCityMonthHisDO weatherFeatureCityMonthHisVO = statisticsMonthFeature(weatherFeatureMap.get(key));
                    weatherFeatureCityMonthHisVOs.add(weatherFeatureCityMonthHisVO);
                } catch (Exception e) {
                    log.error("统计月气象特性出错了", e);
                }
            }

        }

        return weatherFeatureCityMonthHisVOs;
    }

    /**
     * 保存或更新
     *
     * @param weatherFeatureCityMonthHisVO
     * @return
     */
    public WeatherFeatureCityMonthHisDO doSaveOrUpdateWeatherFeatureCityMonthHisDO(WeatherFeatureCityMonthHisDO weatherFeatureCityMonthHisVO) throws Exception {
        if (weatherFeatureCityMonthHisVO == null) {
            return null;
        }
        WeatherFeatureCityMonthHisDO oldVO = getWeatherFeatureCityMonthHisDO(weatherFeatureCityMonthHisVO.getCityId(), weatherFeatureCityMonthHisVO.getYear(), weatherFeatureCityMonthHisVO.getMonth());
        if (oldVO != null) {
            String id = oldVO.getId();
            BeanUtils.copyProperties(weatherFeatureCityMonthHisVO, oldVO,"createtime");
            oldVO.setId(id);
            return (WeatherFeatureCityMonthHisDO) this.updateAndFlush(oldVO);
        } else {
            return (WeatherFeatureCityMonthHisDO) this.createAndFlush(weatherFeatureCityMonthHisVO);
        }
    }

    /**
     * 保存或更新
     *
     * @param weatherFeatureCityMonthHisVOs
     * @return
     */
    public List<WeatherFeatureCityMonthHisDO> doSaveOrUpdateWeatherFeatureCityMonthHisDOs(List<WeatherFeatureCityMonthHisDO> weatherFeatureCityMonthHisVOs) throws Exception {
        List<WeatherFeatureCityMonthHisDO> vos = new ArrayList<WeatherFeatureCityMonthHisDO>();
        for (WeatherFeatureCityMonthHisDO weatherFeatureCityMonthHisVO : weatherFeatureCityMonthHisVOs) {
            try {
                vos.add(this.doSaveOrUpdateWeatherFeatureCityMonthHisDO(weatherFeatureCityMonthHisVO));
            } catch (Exception e) {
                log.error("保存月气象特性出错了", e);
            }
        }
        return vos;
    }

    /**
     * 获取月气象特性
     *
     * @param cityId 城市ID
     * @param year 年(yyyy)
     */
    public List<WeatherFeatureCityMonthHisDO> getWeatherFeatureCityYearHisDO(String cityId, String year)
            throws Exception {

        if (cityId == null) {
            throw new BusinessException("","城市ID不可为空");
        }

        if (year == null) {
            throw new BusinessException("","年份不可为空");
        }
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        param.getQueryConditions().put("_ne_year", year);
        param.getQueryConditions().put("_ne_cityId", cityId);
        param.setOrderby("month");
        return this.query(param).getDatas();
    }

    /**
     * 获取月气象特性
     *
     * @param cityId 城市ID
     * @param startYM 开始月份 yyyy-MM
     * @param endYM 结束月份 yyyy-MM
     */
    public List<WeatherFeatureCityMonthHisDO> getLoadFeatureCityMonthHisDOs(String cityId, String startYM, String endYM)
            throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startYM) {
            param.getQueryConditions().put("_snl_year", startYM.substring(0, 4));
        }
        if (null != endYM) {
            param.getQueryConditions().put("_snm_year", endYM.substring(0, 4));
        }
        if (null != cityId) {
            param.getQueryConditions().put("_se_cityId", cityId);
        }
        param.setOrderby("year,month");
        param.setDesc("0,0");
        List<WeatherFeatureCityMonthHisDO> weatherMonthList = this.query(param).getDatas();
        List<WeatherFeatureCityMonthHisDO> loadFeatureCityMonthHisVOs2 = new ArrayList<>();
        for (WeatherFeatureCityMonthHisDO weatherMonth : weatherMonthList) {
            if ((weatherMonth.getYear() + "-" + weatherMonth.getMonth()).compareTo(startYM) > -1
                    && (weatherMonth.getYear() + "-" + weatherMonth.getMonth()).compareTo(endYM) < 1) {
                loadFeatureCityMonthHisVOs2.add(weatherMonth);
            }
        }
        return loadFeatureCityMonthHisVOs2;
    }


}