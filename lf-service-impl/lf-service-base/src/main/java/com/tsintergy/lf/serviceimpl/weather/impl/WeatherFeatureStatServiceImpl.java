package com.tsintergy.lf.serviceimpl.weather.impl;


import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.BeanUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.weather.api.*;
import com.tsintergy.lf.serviceapi.base.weather.pojo.*;
import com.tsintergy.lf.serviceimpl.system.dao.SettingSystemDAO;
import com.tsintergy.lf.serviceimpl.weather.dao.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 气象特性统计接口实现 User:taojingui Date:18-2-23 Time:下午12:04
 */
@Service("weatherFeatureStatService")
@Slf4j
public class WeatherFeatureStatServiceImpl extends BaseServiceImpl implements WeatherFeatureStatService {

    @Autowired
    StatisticsSynthesizeWeatherCityDayFcService statisticsSynthesizeWeatherCityDayFcService;

    @Autowired
    WeatherCityHisDAO weatherCityHisDAO;

    @Autowired
    WeatherCityFcDAO weatherCityFcDAO;

    @Autowired
    WeatherFeatureCityDayHisDAO weatherFeatureCityDayHisDAO;

    @Autowired
    WeatherFeatureCityDayFcDAO weatherFeatureCityDayFcDAO;

    @Autowired
    WeatherFeatureCityMonthHisDAO weatherFeatureCityMonthHisDAO;

    @Autowired
    WeatherFeatureCityQuarterHisDAO weatherFeatureCityQuarterHisDAO;

    @Autowired
    SettingSystemDAO settingSystemDAO;

    @Autowired
    private CityService cityService;

    @Autowired
    StatisticsSynthesizeWeatherCityDayHisService statisticsSynthesizeWeatherCityDayHisService;

    @Autowired
    private WeatherCityFcService weatherCityFcService;

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Autowired
    private WeatherFeatureCityDayLongFcService weatherFeatureCityDayLongFcService;

    private static final Integer FIRST_INDEX = 0;

    @Override
    public List<WeatherFeatureCityDayHisDO> doStatWeatherFeatureCityDay(List<String> cityIds, Date startDate,
                                                                        Date endDate)
            throws Exception {
        try {
            List<WeatherCityHisDO> weatherCityHisVOs = weatherCityHisDAO
                    .getWeatherCityHisDOS(cityIds, null, startDate, endDate);
            List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisVOs = this
                    .statisticsDayFeature(weatherCityHisVOs);
            return weatherFeatureCityDayHisDAO.doSaveOrUpdateWeatherFeatureCityDayHisDOs(weatherFeatureCityDayHisVOs);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("", "统计日气象特性出错了", e);
        }
    }

    @Override
    public List<WeatherFeatureCityDayHisDO> statisticsDayFeature(List<WeatherCityHisDO> weatherCityHisVOs) {
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisVOS = new ArrayList<WeatherFeatureCityDayHisDO>();
        if (weatherCityHisVOs != null) {
            for (WeatherCityHisDO weatherCityHisVO : weatherCityHisVOs) {
                try {
                    WeatherFeatureCityDayHisDO weatherFeatureCityDayHisVO = statisticsDayFeature(weatherCityHisVO);
                    //statisticsSynthesizeWeatherCityDayHisService.doSetFeature(weatherFeatureCityDayHisVO);
                    weatherFeatureCityDayHisVOS.add(weatherFeatureCityDayHisVO);
                } catch (Exception e) {
                    log.warn(e.toString());
                }
            }

            // 合并气象特性
            Map<String, WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisVOMap = new HashMap<String, WeatherFeatureCityDayHisDO>();
            for (WeatherFeatureCityDayHisDO weatherFeatureCityDayHisVO : weatherFeatureCityDayHisVOS) {
                try {
                    String key =
                            weatherFeatureCityDayHisVO.getCityId() + "-" + weatherFeatureCityDayHisVO.getDate().getTime();
                    if (weatherFeatureCityDayHisVOMap.get(key) == null) {
                        weatherFeatureCityDayHisVOMap.put(key, weatherFeatureCityDayHisVO);
                    } else {
                        BeanUtils
                                .copyPropertiesNotNull(weatherFeatureCityDayHisVOMap.get(key), weatherFeatureCityDayHisVO);
                    }
                } catch (Exception e) {
                    log.warn(e.toString());
                }
            }

            weatherFeatureCityDayHisVOS = new ArrayList<WeatherFeatureCityDayHisDO>(
                    weatherFeatureCityDayHisVOMap.values());
        }
        return weatherFeatureCityDayHisVOS;
    }

    /**
     * 统计气象特性
     *
     * @param weatherCityHisVO 气象数据
     */
    public WeatherFeatureCityDayHisDO statisticsDayFeature(WeatherCityHisDO weatherCityHisVO) throws Exception {
        List<BigDecimal> weatherList = BasePeriodUtils
                .toList(weatherCityHisVO, Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO);
        Map<String, BigDecimal> maxMixAvg = BasePeriodUtils.getMaxMinAvg(weatherList, 4);
        WeatherFeatureCityDayHisDO weatherFeatureCityDayHisVO = new WeatherFeatureCityDayHisDO();
        weatherFeatureCityDayHisVO.setCityId(weatherCityHisVO.getCityId());
        weatherFeatureCityDayHisVO.setDate(weatherCityHisVO.getDate());
        if (WeatherEnum.HUMIDITY.value().equals(weatherCityHisVO.getType())) {
            weatherFeatureCityDayHisVO.setHighestHumidity(maxMixAvg.get("max"));
            weatherFeatureCityDayHisVO.setAveHumidity(maxMixAvg.get("avg"));
            weatherFeatureCityDayHisVO.setLowestHumidity(maxMixAvg.get("min"));
        } else if (WeatherEnum.TEMPERATURE.value().equals(weatherCityHisVO.getType())) {
            weatherFeatureCityDayHisVO.setHighestTemperature(maxMixAvg.get("max"));
            weatherFeatureCityDayHisVO.setAveTemperature(maxMixAvg.get("avg"));
            weatherFeatureCityDayHisVO.setLowestTemperature(maxMixAvg.get("min"));
        } else if (WeatherEnum.RAINFALL.value().equals(weatherCityHisVO.getType())) {
            //set近三日降雨量
            weatherFeatureCityDayHisVO.setRecentlyRainfall(countRecentlyRainfall(weatherCityHisVO));
            List<BigDecimal> bigDecimals = BasePeriodUtils
                    .toList(weatherCityHisVO, Constants.WEATHER_CURVE_POINT_NUM_24,
                            Constants.WEATHER_CURVE_START_WITH_ZERO);
            bigDecimals.set(FIRST_INDEX, BigDecimal.ZERO);
            if (weatherCityHisVO.getT2400() != null) {
                bigDecimals.add(weatherCityHisVO.getT2400());
                weatherFeatureCityDayHisVO.setRainfall(BigDecimalUtils.addAllValue(bigDecimals));
            } else {
                java.sql.Date date = weatherCityHisVO.getDate();
                java.sql.Date tomorrowDate = new java.sql.Date(
                        DateUtils.addDays(new Date(date.getTime()), 1).getTime());
                List<WeatherCityHisDO> tomorrowData = weatherCityHisService
                        .findWeatherCityHisDOs(weatherCityHisVO.getCityId(), WeatherEnum.RAINFALL.getType(), tomorrowDate,
                                tomorrowDate);
                BigDecimal T0000 = null;
                if (!CollectionUtils.isEmpty(tomorrowData)) {
                    WeatherCityHisDO weatherCityHisDO = tomorrowData.get(0);
                    T0000 = weatherCityHisDO.getT0000();
                }
                bigDecimals.add(T0000);
                weatherFeatureCityDayHisVO.setRainfall(BigDecimalUtils.addAllValue(bigDecimals));
            }
        } else if (WeatherEnum.WINDSPEED.value().equals(weatherCityHisVO.getType())) {
            weatherFeatureCityDayHisVO.setMaxWinds(maxMixAvg.get("max"));
            weatherFeatureCityDayHisVO.setAveWinds(maxMixAvg.get("avg"));
            weatherFeatureCityDayHisVO.setMinWinds(maxMixAvg.get("min"));
        } else {
            // ** 暂缺综合气象指标统计，包括实感温度、温湿指数、寒冷指数和人体舒适度
            log.error("统计气象特性有误，未知气象类型：" + weatherCityHisVO.getType());
        }

        return weatherFeatureCityDayHisVO;
    }

    @Override
    public List<WeatherFeatureCityMonthHisDO> doStatWeatherFeatureCityMonth(List<String> cityIds, Date startDate,
                                                                            Date endDate)
            throws Exception {
        try {
            List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisVOS = weatherFeatureCityDayHisDAO
                    .listWeatherFeatureCityDayHisDO(cityIds, startDate, endDate);
            List<WeatherFeatureCityMonthHisDO> weatherFeatureCityMonthHisVOS = weatherFeatureCityMonthHisDAO
                    .statisticsMonthFeatures(weatherFeatureCityDayHisVOS);
            return weatherFeatureCityMonthHisDAO
                    .doSaveOrUpdateWeatherFeatureCityMonthHisDOs(weatherFeatureCityMonthHisVOS);
        } catch (Exception e) {
            throw new BusinessException("", "统计月气象特性出错了", e);
        }
    }


    @Override
    public List<WeatherFeatureCityQuarterHisDO> doStatWeatherFeatureCityQuarter(List<String> cityIds, Date startDate,
                                                                                Date endDate) throws Exception {
        try {
            List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisVOS = weatherFeatureCityDayHisDAO
                    .listWeatherFeatureCityDayHisDO(cityIds, startDate, endDate);
            List<WeatherFeatureCityQuarterHisDO> weatherFeatureCityQuarterHisVOS = weatherFeatureCityQuarterHisDAO
                    .statisticsQuarterFeatures(weatherFeatureCityDayHisVOS);
            return weatherFeatureCityQuarterHisDAO
                    .doSaveOrUpdateWeatherFeatureCityQuarterHisDOs(weatherFeatureCityQuarterHisVOS);
        } catch (Exception e) {
            throw new BusinessException("", "统计季气象特性出错了", e);
        }
    }

    @Override
    public List<WeatherFeatureCityDayFcDO> doStatWeatherFeatureCityDayFc(String cityId, Date startDate, Date endDate)
            throws Exception {
        List<WeatherCityFcDO> weatherCityFcVOs = weatherCityFcDAO
                .findWeatherCityFcDO(cityId, null, startDate, endDate);
        List<WeatherFeatureCityDayFcDO> weatherFeatureCityDayFcVOs = this
                .statisticsDayFeatureInDAO(weatherCityFcVOs);
//        for (WeatherFeatureCityDayFcDO one : weatherFeatureCityDayFcVOs) {
//            WeatherFeatureCityDayLongFcDO weatherFeatureCityDayLongFcDO = new WeatherFeatureCityDayLongFcDO();
//            org.springframework.beans.BeanUtils
//                    .copyProperties(one, weatherFeatureCityDayLongFcDO);
//            weatherFeatureCityDayLongFcService.saveOrUpdate(weatherFeatureCityDayLongFcDO);
//        }
        return weatherFeatureCityDayFcDAO.doSaveOrUpdateWeatherFeatureCityDayFcDOs(weatherFeatureCityDayFcVOs);
    }

    @Override
    public WeatherFeatureCityDayFcDO doStatWeatherByCityAndType(List<?extends BaseWeatherDO> srcList) throws Exception {
        List<WeatherFeatureCityDayFcDO> resultList = this.statisticsDayFeatureInDAO(srcList);
        if (resultList.size() != 0) {
            return resultList.get(0);
        } else {
            return null;
        }
    }

    public List<WeatherFeatureCityDayFcDO> statisticsDayFeatureInDAO(List<?extends BaseWeatherDO> weatherCityFcVOs) {
        List<WeatherFeatureCityDayFcDO> weatherFeatureCityDayFcVOS = new ArrayList<WeatherFeatureCityDayFcDO>();
        if (weatherCityFcVOs != null) {
            for (BaseWeatherDO weatherCityFcVO : weatherCityFcVOs) {
                try {
                    WeatherFeatureCityDayFcDO weatherFeatureCityDayFcVO = statisticsDayFeatureSrcInDAO(weatherCityFcVO);
//                    statisticsSynthesizeWeatherCityDayFcService.doSetFeature(weatherFeatureCityDayFcVO);
                    weatherFeatureCityDayFcVOS.add(weatherFeatureCityDayFcVO);
                } catch (Exception e) {
                    log.warn(e.toString());
                }
            }

            // 合并气象特性
            Map<String, WeatherFeatureCityDayFcDO> weatherFeatureCityDayFcVOMap = new HashMap<String, WeatherFeatureCityDayFcDO>();
            for (WeatherFeatureCityDayFcDO weatherFeatureCityDayFcVO : weatherFeatureCityDayFcVOS) {
                try {
                    String key =
                            weatherFeatureCityDayFcVO.getCityId() + "-" + weatherFeatureCityDayFcVO.getDate().getTime();
                    if (weatherFeatureCityDayFcVOMap.get(key) == null) {
                        weatherFeatureCityDayFcVOMap.put(key, weatherFeatureCityDayFcVO);
                    } else {
                        BeanUtils
                                .copyPropertiesNotNull(weatherFeatureCityDayFcVOMap.get(key), weatherFeatureCityDayFcVO);
                    }
                } catch (Exception e) {
                    log.warn(e.toString());
                }
            }

            weatherFeatureCityDayFcVOS = new ArrayList<WeatherFeatureCityDayFcDO>(
                    weatherFeatureCityDayFcVOMap.values());
        }
        return weatherFeatureCityDayFcVOS;
    }

    /**
     * 统计气象特性
     *
     * @param weatherCityFcVO 气象数据
     */
    public WeatherFeatureCityDayFcDO statisticsDayFeatureSrcInDAO(BaseWeatherDO weatherCityFcVO) throws Exception {

        List<BigDecimal> weatherList = BasePeriodUtils
                .toList(weatherCityFcVO, Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO);
        Map<String, BigDecimal> maxMixAvg = BasePeriodUtils.getMaxMinAvg(weatherList, 4);

        WeatherFeatureCityDayFcDO weatherFeatureCityDayFcVO = new WeatherFeatureCityDayFcDO();
        weatherFeatureCityDayFcVO.setCityId(weatherCityFcVO.getCityId());
        weatherFeatureCityDayFcVO.setDate(weatherCityFcVO.getDate());
        if (WeatherEnum.HUMIDITY.value().equals(weatherCityFcVO.getType())) {
            weatherFeatureCityDayFcVO.setHighestHumidity(maxMixAvg.get("max"));
            weatherFeatureCityDayFcVO.setAveHumidity(maxMixAvg.get("avg"));
            weatherFeatureCityDayFcVO.setLowestHumidity(maxMixAvg.get("min"));
        } else if (WeatherEnum.TEMPERATURE.value().equals(weatherCityFcVO.getType())) {
            weatherFeatureCityDayFcVO.setHighestTemperature(maxMixAvg.get("max"));
            weatherFeatureCityDayFcVO.setAveTemperature(maxMixAvg.get("avg"));
            weatherFeatureCityDayFcVO.setLowestTemperature(maxMixAvg.get("min"));
        } else if (WeatherEnum.RAINFALL.value().equals(weatherCityFcVO.getType())) {
            List<BigDecimal> bigDecimals = BasePeriodUtils
                    .toList(weatherCityFcVO, Constants.WEATHER_CURVE_POINT_NUM_24, Constants.WEATHER_CURVE_START_WITH_ZERO);
            bigDecimals.set(FIRST_INDEX, BigDecimal.ZERO);
            if (weatherCityFcVO.getT2400() != null) {
                bigDecimals.add(weatherCityFcVO.getT2400());
                weatherFeatureCityDayFcVO.setRainfall(BigDecimalUtils.addAllValue(bigDecimals));
            } else {
                java.sql.Date date = weatherCityFcVO.getDate();
                java.sql.Date tomorrowDate = new java.sql.Date(
                        DateUtils.addDays(new Date(date.getTime()), 1).getTime());
                WeatherCityFcDO tomorrowData = weatherCityFcService
                        .findWeatherCityFcDO(weatherCityFcVO.getCityId(), WeatherEnum.RAINFALL.getType(), tomorrowDate);
                BigDecimal T0000 = null;
                if (tomorrowData != null) {
                    T0000 = tomorrowData.getT0000();
                }
                bigDecimals.add(T0000);
                weatherFeatureCityDayFcVO.setRainfall(BigDecimalUtils.addAllValue(bigDecimals));
            }
        } else if (WeatherEnum.WINDSPEED.value().equals(weatherCityFcVO.getType())) {
            weatherFeatureCityDayFcVO.setMaxWinds(maxMixAvg.get("max"));
            weatherFeatureCityDayFcVO.setAveWinds(maxMixAvg.get("avg"));
            weatherFeatureCityDayFcVO.setMinWinds(maxMixAvg.get("min"));
        }
        else if (WeatherEnum.EFFECTIVE_TEMPERATURE.value().equals(weatherCityFcVO.getType())) {
            weatherFeatureCityDayFcVO.setHighestEffectiveTemperature(maxMixAvg.get("max"));
            weatherFeatureCityDayFcVO.setAveEffectiveTemperature(maxMixAvg.get("avg"));
            weatherFeatureCityDayFcVO.setLowestEffectiveTemperature(maxMixAvg.get("min"));
        }
        else {
            // ** 暂缺综合气象指标统计，包括实感温度、温湿指数、寒冷指数和人体舒适度
            log.error("统计气象特性有误，未知气象类型：" + weatherCityFcVO.getType());
        }

        return weatherFeatureCityDayFcVO;
    }

    /**
     * 计算近三日降雨量
     *
     * <AUTHOR>
     */
    private BigDecimal countRecentlyRainfall(WeatherCityHisDO weatherCityHisVO) throws Exception {
        List<WeatherCityHisDO> weatherCityHisDOs = this.weatherCityHisService
                .findWeatherCityHisDOs(weatherCityHisVO.getCityId(), WeatherEnum.RAINFALL.value(),
                        DateUtil.getMoveDay(weatherCityHisVO.getDate(), -2),
                        DateUtil.getMoveDay(weatherCityHisVO.getDate(), -1));
        weatherCityHisDOs.add(weatherCityHisVO);
        BigDecimal resut = BigDecimal.ZERO;
        for (WeatherCityHisDO data : weatherCityHisDOs) {
            List<BigDecimal> weatherList = BasePeriodUtils
                    .toList(data, Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO);
            BigDecimal reduce = weatherList.stream().filter(t -> t != null).reduce(BigDecimal.ZERO, BigDecimal::add);
            resut = resut.add(reduce);
        }
        return resut;
    }
}
