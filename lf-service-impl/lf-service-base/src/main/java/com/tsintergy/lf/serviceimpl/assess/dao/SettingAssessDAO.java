package com.tsintergy.lf.serviceimpl.assess.dao;

import cn.hutool.core.collection.CollectionUtil;
import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsintergy.aif.algorithm.serviceapi.base.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.assess.pojo.SettingAssessDO;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/4/12 14:18
 */
@Component
public class SettingAssessDAO extends BaseAbstractDAO<SettingAssessDO> {


    public List<SettingAssessDO> selectListByYear(String year, String caliberId, Boolean valid) {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (null != year) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "year", year);
        }
        if (null != caliberId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (null != valid) {
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "valid", valid);
        }
        List<SettingAssessDO> datas = this.query(dbQueryParamBuilder.build()).getDatas();
        return datas;
    }


    public List<SettingAssessDO> selectListByName(String year, String caliberId, String assessName) {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (null != year) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "year", year);
        }
        if (null != assessName) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "assessName", assessName);
        }
        if (null != caliberId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        List<SettingAssessDO> datas = this.query(dbQueryParamBuilder.build()).getDatas();
        return datas;
    }

    public List<SettingAssessDO> selectListDataByName(String year, String month, String caliberId, String assessName) {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (null != year) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "year", year);
        }
        if (null != month) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "month", month);
        }
        if (null != assessName) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "assessName", assessName);
        }
        if (null != caliberId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        List<SettingAssessDO> datas = this.query(dbQueryParamBuilder.build()).getDatas();
        return datas;
    }

    public List<SettingAssessDO> selectListByStartEndValid(Date startDate, Date endDate, String caliberId) {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (startDate == null || endDate == null) {
            return null;
        }
        String startYM = DateUtil.getMonthByDate(startDate);
        String endYM = DateUtil.getMonthByDate(endDate);
        dbQueryParamBuilder.where(QueryOp.StringNoLessThan, "year", startYM.substring(0,4));
        dbQueryParamBuilder.where(QueryOp.StringNoMoreThan, "year", endYM.substring(0,4));
        dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "valid", true);
        if (null != caliberId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        List<SettingAssessDO> datas = this.query(dbQueryParamBuilder.build()).getDatas();
        List<SettingAssessDO> resultList = new ArrayList<>();
        for (SettingAssessDO data : datas) {
            if ((data.getYear() + "-" + data.getMonth()).compareTo(startYM)
                > -1
                && (data.getYear() + "-" + data.getMonth()).compareTo(endYM)
                < 1) {
                resultList.add(data);
            }
        }
        return resultList;
    }


    public List<SettingAssessDO> selectListByIds(List<String> settingAssessIds) {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (CollectionUtil.isEmpty(settingAssessIds)){
            return null;
        }
        dbQueryParamBuilder.where(QueryOp.StringIsIn, "id", settingAssessIds);
        List<SettingAssessDO> datas = this.query(dbQueryParamBuilder.build()).getDatas();
        return datas;
    }
}
