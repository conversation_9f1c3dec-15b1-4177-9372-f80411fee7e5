
package com.tsintergy.lf.serviceimpl.forecast.impl;


import com.tsieframework.cloud.security.serviceapi.system.api.RedisService;
import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsintergy.lf.core.constants.CacheConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.ParamConstants;
import com.tsintergy.lf.core.enums.AnalyzeEnum;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.api.CustomizationForecastService;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.algorithm.dto.SimilarDateBean;
import com.tsintergy.lf.serviceapi.algorithm.dto.SimilarParam;
import com.tsintergy.lf.serviceapi.algorithm.dto.SimilarResult;
import com.tsintergy.lf.serviceapi.base.analyze.dto.CompositePowerDTO;
import com.tsintergy.lf.serviceapi.base.analyze.dto.CompositeRequestDTO;
import com.tsintergy.lf.serviceapi.base.analyze.dto.WeatherElement;
import com.tsintergy.lf.serviceapi.base.forecast.api.SimilarDayService;
import com.tsintergy.lf.serviceapi.base.forecast.dto.SimilarDTO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.SimilarDayDO;
import com.tsintergy.lf.serviceimpl.base.dao.HolidayDAO;
import com.tsintergy.lf.serviceimpl.forecast.dao.SimilarDayDAO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $Id: SimilarDayServiceImpl.java, v 0.1 2018-01-31 10:44:27 tao Exp $$
 */
@Service("similarDayService")
public class SimilarDayServiceImpl extends BaseServiceImpl implements SimilarDayService {

    private static final Logger logger = LogManager.getLogger(SimilarDayServiceImpl.class);

    @Autowired
    private SimilarDayDAO similarDayDAO;

    @Autowired
    private HolidayDAO holidayDAO;

    @Resource
    private CustomizationForecastService<SimilarParam, SimilarResult> similarDayForecast;

    @Autowired
    private RedisService redisService;

    @Override
    public DataPackage querySimilarDayDO(DBQueryParam param) throws Exception {
        try {
            return similarDayDAO.query(param);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public SimilarDayDO doCreate(SimilarDayDO vo) throws Exception {
        try {
            return (SimilarDayDO) similarDayDAO.create(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public void doRemoveSimilarDayDO(SimilarDayDO vo) throws Exception {
        try {
            similarDayDAO.remove(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public void doRemoveSimilarDayDOByPK(Serializable pk) throws Exception {
        try {
            similarDayDAO.removeByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public SimilarDayDO doUpdateSimilarDayDO(SimilarDayDO vo) throws Exception {
        try {
            return (SimilarDayDO) similarDayDAO.update(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public SimilarDayDO findSimilarDayDOByPk(Serializable pk) throws Exception {
        try {
            return (SimilarDayDO) similarDayDAO.findByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public SimilarDayDO findMaxSimilarDay(String cityId, Date targetDate, String caliberId) throws Exception {
        List<SimilarDayDO> SimilarDayDOS = similarDayDAO
            .findSimilarDayDOSByDate(cityId, targetDate, caliberId);
        if (SimilarDayDOS.size() < 1) {
            throw TsieExceptionUtils.newBusinessException("01C20180002");
        }
        SimilarDayDO maxSimilarDayDO = SimilarDayDOS.get(0);
        return maxSimilarDayDO;
    }

    @Override
    public List<SimilarDTO> findTargetSimilarDay(String cityId, Date date, Integer forecastDay, String caliberId)
        throws Exception {
        Date targetStartDate = date;
        Date targetEndDate = DateUtil.getMoveDay(date, forecastDay.intValue());
        List<SimilarDayDO> SimilarDayDOS = similarDayDAO
            .findSimilarDayDOSByDate(cityId, targetStartDate, targetEndDate, caliberId);
        List<SimilarDTO> similarDTOS = this.SimilarDayDOStoSimilarDTOS(SimilarDayDOS, targetStartDate, targetEndDate);
        //如果不相等，证明后面的几天数据库中没有存相似日，执行相似日算法
        // Todo 相似算法中需要将结果入库并返回至这里
        if (forecastDay.compareTo(similarDTOS.size()) != 0) {
            //执行算法,应该返回的是SimilarDayDOS
//            similarDTOS.addAll(this.SimilarDayDOStoSimilarDTOS(将算法返回结果reformat))
        }
        return similarDTOS;
    }

    private List<SimilarDTO> SimilarDayDOStoSimilarDTOS(List<SimilarDayDO> SimilarDayDOS, Date targetStartDate,
        Date targetEndDate) throws Exception {
        List<SimilarDTO> similarDTOS = new ArrayList<SimilarDTO>(7);
        List<Date> holidays = holidayDAO.getAllHolidays();
        while (targetStartDate.before(DateUtil.getMoveDay(targetEndDate, 1))) {
            SimilarDTO similarDTO = new SimilarDTO();
            similarDTO.setTargetDay(targetStartDate);
            similarDTO.setHoliday(holidays.contains(targetStartDate));
            for (SimilarDayDO SimilarDayDO : SimilarDayDOS) {
                if (DateUtils.isSameDay(targetStartDate, SimilarDayDO.getDate())) {
                    similarDTO.getSimilarDay()
                        .add(similarDTO.setSimilarDay(SimilarDayDO.getSimilarDay(), SimilarDayDO.getDegree()));
                }
            }
            similarDTOS.add(similarDTO);
            targetStartDate = DateUtil.getMoveDay(targetStartDate, 1);
        }
        return similarDTOS;
    }


/*    @Override
    public List<SimilarDateBean> listSimilarDay(String cityId, Date date, Date startDate, Date endDate, Integer size) throws Exception {
        try {
            Result result = getSimilarDegreeService().doSimilarAlgorithm(cityId, date, startDate, endDate, size);
            return result.getSimilarData();
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            throw TsieExceptionUtils.newBusinessException("01C20180002");
        }
    }*/


    /**
     * 功能描述:当前的预测气象作为targetDate <br> 〈〉
     *
     * @return:java.util.List<com.load.algorithm.bean.SimilarDateBean>
     * @since: 1.0.0
     * @Author:yangjin
     * @Date: 2019/7/29 9:55
     */
    @Override
    public List<SimilarDateBean> listSimilarDayWithFcWeather(String cityId, String caliberId, Date date, Date startDate,
        Date endDate, Integer size, String userId) throws Exception {
        try {
            SimilarParam similarParam = defaultParam(cityId, caliberId, date, startDate, endDate, size, userId);
            similarDayForecast.forecast(similarParam);
            SimilarResult result = (SimilarResult) redisService
                .redisGet(userId + Constants.SEPARATOR_BROKEN_LINE + CacheConstants.CACHE_SIMILARRESULT_KEY,
                    SimilarResult.class);
            if (result == null){
                return null;
            }
            return result.getSimilarDatas();
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            throw new Exception("01C20180002");
        }
    }

    private SimilarParam defaultParam(String cityId, String caliberId, Date date, Date startDate,
        Date endDate, Integer size, String userId) {
        //降水丰枯期选项
        List<Integer> phase = Arrays.asList(1, 2, 3);
        //默认气象参数：查询当日的 实际降水  预测降水 实际温度 预测温度
        List<WeatherElement> weatherList = new ArrayList<>();
        WeatherElement weather = new WeatherElement(0, Arrays.asList(3));
        weatherList.add(weather);
        SimilarParam param = new SimilarParam();
        param.setElements(weatherList);
        param.setPageSize(size);
        param.setCityId(cityId);
        param.setCaliberId(caliberId);
        param.setDate(date);
        param.setStartDate(startDate);
        param.setEndDate(endDate);
        param.setUserId(userId);
        param.setPhase(phase);
        //默认负荷选择当日
        param.setLoadElements(Arrays.asList(0));
        param.setAlgorithmEnum(AlgorithmEnum.SIMILAR_DAY_SEARCH);
        //默认为趋势相似
        param.setCompute(AnalyzeEnum.TEMPERATURE.getType());
        //默认最新一天数据使用预测数据
        param.setNewestLoad(ParamConstants.STRING_COMPOSITE_ON);
        return param;
    }

    @Override
    public List<CompositePowerDTO> listSimilarComposite(CompositeRequestDTO requestDTO) throws Exception {
        SimilarParam param = new SimilarParam();
        param.setAlgorithmEnum(AlgorithmEnum.SIMILAR_DAY_SEARCH);
        BeanUtils.copyProperties(requestDTO, param);
        param.setDate(requestDTO.getBasicDate());
        List<SimilarDateBean> similarData;
        try {
            similarDayForecast.forecast(param);
            SimilarResult result = (SimilarResult) redisService
                .redisGet(
                    requestDTO.getUserId() + Constants.SEPARATOR_BROKEN_LINE + CacheConstants.CACHE_SIMILARRESULT_KEY,
                    SimilarResult.class);
            similarData = result.getSimilarDatas();
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
        List<CompositePowerDTO> result = new ArrayList<>();
        if (similarData == null || similarData.size() == 0) {
            return null;
        }
        for (SimilarDateBean data : similarData) {
            CompositePowerDTO composite = new CompositePowerDTO();
            BeanUtils.copyProperties(data, composite);
            composite.setSimilarDay(data.getDate());
            if (AnalyzeEnum.DISTANCE.getValue().equals(requestDTO.getCompute())) {
                composite.setSimilarity(data.getDegree().setScale(4, BigDecimal.ROUND_FLOOR));
            } else if (AnalyzeEnum.TEMPERATURE.getValue().equals(requestDTO.getCompute())) {
                composite.setCoefficient(data.getDegree().setScale(4, BigDecimal.ROUND_FLOOR));
            }
            result.add(composite);
        }
        List<CompositePowerDTO> finalResult = new ArrayList<>();
        if (requestDTO.getCompute() == 1) {
            finalResult = result.stream()
                .sorted(Comparator.comparing(CompositePowerDTO::getSimilarity).reversed()).collect(Collectors.toList());
        } else if (requestDTO.getCompute() == 3) {
            finalResult = result.stream()
                .sorted(Comparator.comparing(CompositePowerDTO::getCoefficient).reversed())
                .collect(Collectors.toList());
        }
        return finalResult;

    }

    @Override
    public List<SimilarDayDO> findSimilarDays(String cityId, String caliberId, Date date) throws Exception {
        return similarDayDAO.findSimilarDayDOSByDate(cityId, date, caliberId);
    }

}
