/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  wangfeng Date:  2020/1/7 9:20 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.report.impl;


import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.check.api.SettingCheckService;
import com.tsintergy.lf.serviceapi.base.check.pojo.SettingCheckDO;
import com.tsintergy.lf.serviceapi.base.check.pojo.SettingCheckPrecisionDO;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsCityDayFcService;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayFcService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAccuracyDayService;
import com.tsintergy.lf.serviceapi.base.report.dto.AccuracyStatDTO;
import com.tsintergy.lf.serviceapi.base.report.dto.ReportAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.report.dto.ReportDay96AccuracyDTO;
import com.tsintergy.lf.serviceapi.base.report.dto.ReportDay96AndDateDTO;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportAccuracyDayDO;
import com.tsintergy.lf.serviceimpl.report.dao.ReportAccuracyDayDAO;
import com.tsintergy.lf.serviceimpl.report.support.ReportAccuracySupport;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/1/7
 * @since 1.0.0
 */
@Service("reportAccuracyDayService")
public class ReportAccuracyDayServiceImpl extends BaseServiceImpl implements ReportAccuracyDayService {

    @Autowired
    private ReportAccuracyDayDAO reportAccuracyDayDAO;

    @Autowired
    private CityService cityService;

    @Autowired
    private LoadFeatureCityDayFcService loadFeatureCityDayFcService;

    @Autowired
    private SettingCheckService settingCheckService;

    @Autowired
    private LoadFeatureCityDayHisService loadFeatureCityDayHisService;

    @Autowired
    private StatisticsCityDayFcService statisticsCityDayFcService;


    /**
     * 查询日负荷填报（多日）
     */
    @Override
    public List<ReportAccuracyDTO> findReportAccuracy(String cityId, String start, String end,String caliberId) throws Exception {
        Date startDate = null;
        Date endDate = null;
        if (start == null && end == null) {
            //如果两个都是空，首次加载，传库里现有的最近日期
            Date date = reportAccuracyDayDAO.getLatelyDate(cityId);
            if (date == null) {
                return null;
            }
            startDate = date;
            endDate = date;
        } else {
            startDate = DateUtils.string2Date(start, DateFormatType.SIMPLE_DATE_FORMAT_STR);
            endDate = DateUtils.string2Date(end, DateFormatType.SIMPLE_DATE_FORMAT_STR);
        }
        List<ReportAccuracyDayDO> ReportAccuracyDayDOS = reportAccuracyDayDAO
            .findReportAccuracy(cityId, startDate, endDate,caliberId);
        List<CityDO> CityDOList = cityService.findAllCitys();
        Map<String, CityDO> cityMap = CityDOList.stream()
            .collect(Collectors.toMap(CityDO::getId, Function.identity(), (o, n) -> n));
        //查询目标准确率
        Map<String, SettingCheckPrecisionDO> settingCheckPrecisionVOMap = settingCheckService.findStandMap();
        List<ReportAccuracyDTO> dayDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ReportAccuracyDayDOS)) {
            for (ReportAccuracyDayDO dayVO : ReportAccuracyDayDOS) {
                ReportAccuracyDTO reportAccuracyDTO = new ReportAccuracyDTO();
                BeanUtils.copyProperties(dayVO, reportAccuracyDTO);
                reportAccuracyDTO.setCity(cityMap.get(dayVO.getCityId()).getCity());
                reportAccuracyDTO
                    .setDate(DateUtils.date2String(dayVO.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR));
                String year = DateUtil.getYearByDate(dayVO.getDate());
                SettingCheckPrecisionDO checkPrecisionVO = settingCheckPrecisionVOMap
                    .get(dayVO.getCityId() + "-" + year);
                Map<String, BigDecimal> decimalMap = ReportAccuracySupport.toMap(checkPrecisionVO);
                BigDecimal standard = null;
                if (decimalMap != null) {
                    String month = DateUtil.getMonthDate(dayVO.getDate());
                    standard = decimalMap.get(month);
                }
                reportAccuracyDTO.setStandardAccuracy(standard);
                dayDTOS.add(reportAccuracyDTO);
            }
            return dayDTOS;
        }
        return null;
    }

    @Override
    public ReportDay96AndDateDTO findAccuracyDayFor96DTO(String cityId, Date startDate, Date endDate, String userId,String caliberId)
        throws Exception {
        List<CityDO> allCitysExcludeAoLocal = new ArrayList<>();
        if (cityId.equals("all")) {
            allCitysExcludeAoLocal = cityService.findAllCitysExcludeAoLocal(false);
        } else {
            CityDO cityById = cityService.findCityById(cityId);
            allCitysExcludeAoLocal.add(cityById);
        }
        List<Date> listBetweenDay = DateUtil.getListBetweenDay(startDate, endDate);
        List<Date> dateDto = new ArrayList<>();
        List<ReportDay96AccuracyDTO> reportDay96AccuracyDTOS = new ArrayList<>();
        //初始化 返回数据 保证返回的cityname与下面遍历所有的数据下标一致 方便后续数据映射cityName
        for (CityDO cityDO : allCitysExcludeAoLocal) {
            ReportDay96AccuracyDTO reportDay96AccuracyDTO = new ReportDay96AccuracyDTO();
            reportDay96AccuracyDTO.setCityName(cityDO.getCity());
            reportDay96AccuracyDTOS.add(reportDay96AccuracyDTO);
        }
        //添加免考部分
        List<SettingCheckDO> citySettingCheckDTOS = settingCheckService.findByUserIdAndAccess().stream()
            .collect(Collectors.toList());
        Map<String, List<SettingCheckDO>> stringListMap = citySettingCheckDTOS.stream()
            .collect(Collectors.groupingBy(SettingCheckDO::getCityId));
        List<List<BigDecimal>> listList = new ArrayList<>();
        List<List<Integer>> flagList = new ArrayList<>();
        //遍历所有 进行日期列判断  列数据全为空 则删除这列date 的数据
        for (Date date : listBetweenDay) {
            Date formatDate = DateUtil.getFormatDate(date);
            List<BigDecimal> bigDecimals = new ArrayList<>();
            List<Integer> integers = new ArrayList<>();
            for (int i = 0; i < allCitysExcludeAoLocal.size(); i++) {
                CityDO cityDO = allCitysExcludeAoLocal.get(i);
                Integer flag = 0;
                if (stringListMap.containsKey(cityDO.getId())) {
                    List<SettingCheckDO> citySettingCheckDTOS1 = stringListMap.get(cityDO.getId());
                    List<Date> list = new ArrayList<>();
                    for (SettingCheckDO checkDO : citySettingCheckDTOS1) {
                        List<Date> listBetweenDay1 = DateUtil
                            .getListBetweenDay(checkDO.getStartDate(), checkDO.getEndDate());
                        list.addAll(listBetweenDay1);
                    }
                    if (list.contains(formatDate)) {
                        flag = 1;
                    }
                }
                integers.add(flag);
                ReportAccuracyDayDO reportAccuracy = reportAccuracyDayDAO
                    .findReportAccuracy(cityDO.getId(), date,caliberId);
                if (reportAccuracy == null) {
                    bigDecimals.add(null);
                } else {
                    bigDecimals.add(reportAccuracy.getPointAccuracy());
                }
            }
            List<BigDecimal> collect = bigDecimals.stream().filter(e -> e != null).collect(Collectors.toList());
            if (collect != null && collect.size() > 0) {
                listList.add(bigDecimals);
                dateDto.add(date);
                flagList.add(integers);
            }
        }
        for (int i = 0; i < listList.size(); i++) {
            if (listList != null && listList.size() > 0) {
                int size = listList.get(0).size();
                BigDecimal divide = BigDecimal.ZERO;
                for (int j = 0; j < size; j++) {
                    BigDecimal bigDecimal = listList.get(i).get(j);
                    if (bigDecimal != null) {
                        divide = divide.add(bigDecimal);
                        reportDay96AccuracyDTOS.get(j).getAccuracyList()
                            .add(bigDecimal.setScale(4, BigDecimal.ROUND_HALF_UP));
                    } else {
                        reportDay96AccuracyDTOS.get(j).getAccuracyList()
                            .add(null);
                    }
                    if (flagList.get(i).get(j) == 1) {
                        reportDay96AccuracyDTOS.get(j).getIndex().add(i);
                    }
                }
            }
        }
        for (ReportDay96AccuracyDTO reportDay96AccuracyDTO : reportDay96AccuracyDTOS) {
            BigDecimal count = BigDecimal.ZERO;
            Integer size = 0;
            for (int i = 0; i < reportDay96AccuracyDTO.getAccuracyList().size(); i++) {
                BigDecimal bigDecimal = reportDay96AccuracyDTO.getAccuracyList().get(i);
                if (reportDay96AccuracyDTO.getIndex().contains(i)) {
                    continue;
                }
                size++;
                if (bigDecimal != null) {
                    count = count.add(bigDecimal);
                } else {
                    count = count.add(BigDecimal.ZERO);
                }
            }
            if (size != 0) {
                count = count.divide(new BigDecimal(size), 4, BigDecimal.ROUND_HALF_UP);
            }
            reportDay96AccuracyDTO.setAvgAccuracy(count);
        }
        ReportDay96AndDateDTO reportDay96AndDateDTO = new ReportDay96AndDateDTO();
        reportDay96AndDateDTO.setDateList(dateDto);
        reportDay96AndDateDTO.setRowList(reportDay96AccuracyDTOS);
        return reportDay96AndDateDTO;
    }

    /**
     * 计算日最大最小负荷预测月平均准确率
     */
    @Override
    public AccuracyStatDTO findDayMonth(String cityId, String year, String month) throws Exception {
        String starDate = DateUtil.getFirstDayOfMonth(year, month);
        String endDate = year + "-" + month + "-" + DateUtil.getLastDayOfMonth(year, month);
        //查询 日准确率
        List<ReportAccuracyDayDO> reportAccuracyDayVOS = reportAccuracyDayDAO
            .findReportAccuracy(cityId, DateUtils.string2Date(starDate, DateFormatType.SIMPLE_DATE_FORMAT_STR),
                DateUtils.string2Date(endDate, DateFormatType.SIMPLE_DATE_FORMAT_STR),null);
        if (reportAccuracyDayVOS == null || reportAccuracyDayVOS.size() < 1) {
            return null;
        }
        Map<String, List<Date>> checkMap = settingCheckService.findPassCheckMap(cityId);
        List<Date> dateList = checkMap.get(cityId);
        ReportAccuracyDTO accuracyDTO = calculateMonthAvg(dateList, reportAccuracyDayVOS);
        AccuracyStatDTO accuracyStatDTO = new AccuracyStatDTO();
        accuracyStatDTO.setPointAvg(accuracyDTO.getPointAccuracy());
        accuracyStatDTO.setDayMaxAvg(accuracyDTO.getMaxAccuracy());
        accuracyStatDTO.setDayMinAvg(accuracyDTO.getMinAccuracy());
        return accuracyStatDTO;
    }

    /**
     * /** 统计96点负荷预测月平均准确率 统计最大负荷预测月平均准确率 统计最小荷预测月平均准确率
     *
     * @param noCheckDates 免考的日期集合
     */

    private ReportAccuracyDTO calculateMonthAvg(List<Date> noCheckDates, List<ReportAccuracyDayDO> ReportAccuracyDayDOS)
        throws Exception {
        // 年月
        String ym = DateUtil.getMonthByDate(ReportAccuracyDayDOS.get(0).getDate());
        List<BigDecimal> maxAccuracy = new ArrayList<>();
        List<BigDecimal> minAccuracy = new ArrayList<>();
        List<BigDecimal> pointAccuracy = new ArrayList<>();
        for (ReportAccuracyDayDO ReportAccuracyDayDO : ReportAccuracyDayDOS) {
            //如果免考通过了，则过滤，不参与考核计算
            if (noCheckDates != null && noCheckDates.contains(ReportAccuracyDayDO.getDate())) {
                continue;
            }
            pointAccuracy.add(ReportAccuracyDayDO.getPointAccuracy());
            maxAccuracy.add(ReportAccuracyDayDO.getMaxAccuracy());
            minAccuracy.add(ReportAccuracyDayDO.getMinAccuracy());
        }
        ReportAccuracyDTO reportAccuracyDTO = new ReportAccuracyDTO();
        reportAccuracyDTO.setCityId(ReportAccuracyDayDOS.get(0).getCityId());
        reportAccuracyDTO.setCity(cityService.findCityById(reportAccuracyDTO.getCityId()).getCity());
        reportAccuracyDTO.setDate(ym);
        reportAccuracyDTO.setMaxAccuracy(BigDecimalUtils.avgList(maxAccuracy, 4, true));
        reportAccuracyDTO.setMinAccuracy(BigDecimalUtils.avgList(minAccuracy, 4, true));
        reportAccuracyDTO.setPointAccuracy(BigDecimalUtils.avgList(pointAccuracy, 4, true));
        return reportAccuracyDTO;
    }

    /**
     * 统计并保存日负荷填报准确率
     */
    @Override
    public List<ReportAccuracyDayDO> doStatSaveReportAccuracy(String cityId, Date startDate, Date endDate ,String caliberId)
        throws Exception {
        List<ReportAccuracyDayDO> reportAccuracyDayVOS = new ArrayList<>();
        //查询预测数据 其准确率来自  预测特性统计
        List<LoadFeatureCityDayFcDO> reportLoads = loadFeatureCityDayFcService
            .findReportLoadFeatureCityDayFcList(null, new java.sql.Date(startDate.getTime()),
                new java.sql.Date(endDate.getTime()), caliberId);
        //查询真实的数据 只计算 口径为1的
        List<LoadFeatureCityDayHisDO> realLoads = loadFeatureCityDayHisService
            .findLoadFeatureCityDayHisDOS(cityId, startDate, endDate, caliberId);
        //查询上报的准确率
        List<StatisticsCityDayFcDO> statisticsCityDayFcVOS = statisticsCityDayFcService
            .getReportAccuracy(cityId, caliberId, startDate, endDate);
        Map<String, LoadFeatureCityDayFcDO> reportMap = new HashMap<>();
        for (LoadFeatureCityDayFcDO reportLoadDayVO : reportLoads) {
            String key = reportLoadDayVO.getCityId() + "-" + reportLoadDayVO.getDate().getTime();
            reportMap.put(key, reportLoadDayVO);
        }
        Map<String, StatisticsCityDayFcDO> statisticsCityDayFcVOMap = new HashMap<>();
        for (StatisticsCityDayFcDO statisticsCityDayFcVO : statisticsCityDayFcVOS) {
            String key = statisticsCityDayFcVO.getCityId() + "-" + statisticsCityDayFcVO.getDate().getTime();
            statisticsCityDayFcVOMap.put(key, statisticsCityDayFcVO);
        }
        if (realLoads == null) {
            return new ArrayList<>();
        }
        for (LoadFeatureCityDayHisDO featureDTO : realLoads) {
            //特殊处理，澳门和地方电不做统计
            if (featureDTO.getCityId().equals("23") || featureDTO.getCityId().equals("24")) {
                continue;
            }
            String key = featureDTO.getCityId() + "-" + featureDTO.getDate().getTime();
            LoadFeatureCityDayFcDO loadDayVO = reportMap.get(key);
            StatisticsCityDayFcDO cityDayFcVO = statisticsCityDayFcVOMap.get(key);
            ReportAccuracyDayDO reportAccuracyDayVO = new ReportAccuracyDayDO();
            reportAccuracyDayVO.setCityId(featureDTO.getCityId());
            reportAccuracyDayVO.setDate(featureDTO.getDate());
            reportAccuracyDayVO.setCaliberId(caliberId);
            if (loadDayVO != null) {
                BigDecimal hisMaxLoad = null;
                BigDecimal hisMinLoad = null;
                hisMaxLoad = featureDTO.getMaxLoad();
                hisMinLoad = featureDTO.getMinLoad();
                reportAccuracyDayVO
                    .setMaxAccuracy(LoadCalUtil.calcMaxMinAccuracy(hisMaxLoad, loadDayVO.getMaxLoad()));
                reportAccuracyDayVO
                    .setMinAccuracy(LoadCalUtil.calcMaxMinAccuracy(hisMinLoad, loadDayVO.getMinLoad()));
            } else {
                reportAccuracyDayVO.setMinAccuracy(BigDecimal.ZERO);
                reportAccuracyDayVO.setMaxAccuracy(BigDecimal.ZERO);
            }
            if (cityDayFcVO != null) {
                reportAccuracyDayVO.setPointAccuracy(cityDayFcVO.getAccuracy());
            } else {
                reportAccuracyDayVO.setPointAccuracy(BigDecimal.ZERO);
            }
            reportAccuracyDayVOS.add(reportAccuracyDayVO);
        }
        return reportAccuracyDayVOS;
    }


    @Override
    public void doSaveOrUpdate(List<ReportAccuracyDayDO> ReportAccuracyDayDOS) throws Exception {
        if (CollectionUtils.isEmpty(ReportAccuracyDayDOS) || ReportAccuracyDayDOS.size() < 1) {
            return;
        }
        for (ReportAccuracyDayDO accuracyDayVO : ReportAccuracyDayDOS) {
            List<ReportAccuracyDayDO> accuracyDayVOS = reportAccuracyDayDAO
                .findReportAccuracy(accuracyDayVO.getCityId(), accuracyDayVO.getDate(), accuracyDayVO.getDate(),accuracyDayVO.getCaliberId());
            if (CollectionUtils.isNotEmpty(accuracyDayVOS) && accuracyDayVOS.size() > 0) {
                ReportAccuracyDayDO ReportAccuracyDayDO = accuracyDayVOS.get(0);
                BeanUtils.copyProperties(accuracyDayVO, ReportAccuracyDayDO, "id");
                ReportAccuracyDayDO.setUpdateTime(new Date());
                reportAccuracyDayDAO.updateAndFlush(ReportAccuracyDayDO);
            } else {
                accuracyDayVO.setCreateTime(new Date());
                reportAccuracyDayDAO.createAndFlush(accuracyDayVO);
            }
        }
    }


    @Override
    public List<ReportAccuracyDayDO> findAccuracyDay(String cityId, Date startDate, Date endDate) throws Exception {
        return reportAccuracyDayDAO.findReportAccuracy(cityId, startDate, endDate,null);
    }
}