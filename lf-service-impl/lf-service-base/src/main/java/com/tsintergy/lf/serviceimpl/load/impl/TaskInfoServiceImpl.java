/**
 * Copyright(C),2015‐2020,北京清能互联科技有限公司
 * Author:yangjin
 * Date:2020/2/2613:29
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.load.impl;

import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.util.compatible.HttpClientUtils;
import com.tsintergy.lf.serviceapi.base.base.pojo.TaskInfoDO;
import com.tsintergy.lf.serviceapi.base.load.api.TaskInfoService;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;

import com.tsintergy.lf.serviceimpl.load.dao.TaskInfoDAO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * Description: <br>
 *
 * <AUTHOR>
 * @create 2020/2/26
 * @since 1.0.0
 */
@Service("taskInfoService")
@Transactional
public class TaskInfoServiceImpl implements TaskInfoService {

    private static final Logger logger = LoggerFactory.getLogger(TaskInfoServiceImpl.class);


    @Value("${xxl.job.db.driverClass}")
    public String xxljobDriver;

    @Value("${TaskReSendUrl}")
    public String reSendTaskUrl;

    @Value("${xxl.job.db.url}")
    public String xxljobUrl;

    @Value("${xxl.job.db.user}")
    public String xxljobUsername;

    @Value("${xxl.job.db.password}")
    public String xxljobPassword;

    @Value("${xxl.job.taskId}")
    public String xxljobTaskId;

    @Value("${xxl.job.id}")
    public String xxljobId;


    @Autowired
    TaskInfoDAO taskInfoDAO;


    @Override
    public List<TaskInfoDO> getTaskInfo(String type, String date) {
        return taskInfoDAO.findByTypeAndDate(type, date);
    }

    @Override
    public TaskInfoDO findById(String id) {
        List<TaskInfoDO> byTaskId = taskInfoDAO.findByTaskId(id);
        if(CollectionUtils.isEmpty(byTaskId)){
            return null;
        }else {
            return byTaskId.get(0);
        }
    }


    @Override
    public void reSendTask(String taskId, Date date, String xxljobId) {
        logger.info("注入xxljob连接信息为" + xxljobDriver + "," + xxljobUrl + "," + xxljobUsername + "," + xxljobPassword + ","
                + reSendTaskUrl);
        Connection connection = null;
        try {
            Class.forName(xxljobDriver);
            connection = DriverManager.getConnection(xxljobUrl, xxljobUsername, xxljobPassword);
            PreparedStatement statement = connection
                    .prepareStatement("select id,executor_param from xxl_job_qrtz_trigger_info where id = ?");
            statement.setString(1, xxljobId);

            ResultSet resultSet = statement.executeQuery();
            String name = null;
            String param = null;
            while (resultSet.next()) {
                name = resultSet.getString(1);
                param = resultSet.getString(2);
            }
            statement.close();

            String updatesql = "update xxl_job_qrtz_trigger_info set executor_param = ? where id = ?";
            connection.setAutoCommit(false);
            PreparedStatement prepareStatement = connection.prepareStatement(updatesql);
            String value = null;
            String startDate = DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
            value = startDate+","+startDate;
            prepareStatement.setString(1, value);
            prepareStatement.setString(2, xxljobId);
            prepareStatement.executeUpdate();
            connection.commit();


            Map<String, String> map = new HashMap<>();
            map.put("id", xxljobId);
            map.put("executorParam",value);

            try {

                String s = HttpClientUtils.doPost(reSendTaskUrl, map, null);
                //因为xxljob执行属于异步请求，当xxljob请求执行相关参数时可能已经被修改为原来的值
                Thread.sleep(1000);

            } catch (Exception e) {
                logger.info(e.getMessage());
                e.printStackTrace();
            }
            prepareStatement = connection.prepareStatement(updatesql);
            prepareStatement.setString(1, param);
            prepareStatement.setString(2, xxljobId);
            prepareStatement.executeUpdate();
            connection.commit();
            prepareStatement.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                connection.close();
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }
}