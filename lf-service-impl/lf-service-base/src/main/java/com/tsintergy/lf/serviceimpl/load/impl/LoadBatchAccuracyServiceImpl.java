package com.tsintergy.lf.serviceimpl.load.impl;

import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.load.api.LoadBatchAccuracyService;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadBatchAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadBatchAccuracyRequest;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadShortFcAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityFcShortDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHis288DO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.serviceimpl.load.dao.LoadCityFcShortDAO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadCityHis288DAO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadCityHisDAO;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service("loadBatchAccuracyService")
public class LoadBatchAccuracyServiceImpl implements LoadBatchAccuracyService {

    @Autowired
    private LoadCityFcShortDAO loadCityFcShortDAO;

    @Autowired
    private LoadCityHis288DAO loadCityHis288DAO;

    @Autowired
    private LoadCityHisDAO loadCityHisDAO;


    @Override
    public List<LoadBatchAccuracyDTO> findBatchFcAccuracy(LoadBatchAccuracyRequest request) throws Exception {
        List<LoadBatchAccuracyDTO> resultList = new ArrayList<>();
        //获取条件内所有批次预测的数据
        List<LoadCityFcShortDO> loadCityFcShortDOS = loadCityFcShortDAO.findLoadCityFcDOS(request.getStartDate(),
            request.getEndDate(), request.getPeriodType(), request.getStartPeriodTime(), request.getEndPeriodTime(),
            request.getCityId(), request.getCaliberId(), request.getType());
        if (CollectionUtils.isEmpty(loadCityFcShortDOS)) {
            return resultList;
        }
        //获取时间段内的历史负荷数据
        Map<Date, List<BigDecimal>> hisloadDateMap = getHisloadDateMap(request.getCityId(), request.getStartDate(), DateUtils.addDays(request.getEndDate() , 1), request.getCaliberId(), request.getType());
        //循环计算批次预测的准确率
        for (LoadCityFcShortDO loadCityFcShortDO : loadCityFcShortDOS) {
            resultList.add(calculateBatchAccuray(loadCityFcShortDO, hisloadDateMap, request.getType(), request.getCustomPoint()));
        }
        //排序
        resultList.stream().sorted(Comparator.comparing(e -> e.getDate() + e.getStartTime())).collect(Collectors.toList());
        Collections.reverse(resultList);
        //计算最后一行 平均值
        List<BigDecimal> maxAccuracys = resultList.stream().map(LoadBatchAccuracyDTO::getMaxAccuracy).filter(Objects::nonNull).collect(Collectors.toList());
        BigDecimal maxAccuracyAvg = CollectionUtils.isEmpty(maxAccuracys) ? null : maxAccuracys.stream().reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(maxAccuracys.size()), 4, BigDecimal.ROUND_HALF_UP);
        List<BigDecimal> minAccuracys = resultList.stream().map(LoadBatchAccuracyDTO::getMinAccuracy).filter(Objects::nonNull).collect(Collectors.toList());
        BigDecimal minAccuracyAvg = CollectionUtils.isEmpty(minAccuracys) ? null : minAccuracys.stream().reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(minAccuracys.size()), 4, BigDecimal.ROUND_HALF_UP);
        List<BigDecimal> firstPointAccuracys = resultList.stream().map(LoadBatchAccuracyDTO::getFirstPointAccuracy).filter(Objects::nonNull).collect(Collectors.toList());
        BigDecimal firstPointAccuracyAvg = CollectionUtils.isEmpty(firstPointAccuracys) ? null : firstPointAccuracys.stream().reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(firstPointAccuracys.size()), 4, BigDecimal.ROUND_HALF_UP);
        List<BigDecimal> firstHourAccuracys = resultList.stream().map(LoadBatchAccuracyDTO::getFirstHourAccuracy).filter(Objects::nonNull).collect(Collectors.toList());
        BigDecimal firstHourAccuracyAvg = CollectionUtils.isEmpty(firstHourAccuracys) ? null : firstHourAccuracys.stream().reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(firstHourAccuracys.size()), 4, BigDecimal.ROUND_HALF_UP);
        List<BigDecimal> twoHourAccuracys = resultList.stream().map(LoadBatchAccuracyDTO::getTwoHourAccuracy).filter(Objects::nonNull).collect(Collectors.toList());
        BigDecimal twoHourAccuracyAvg = CollectionUtils.isEmpty(twoHourAccuracys) ? null : twoHourAccuracys.stream().reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(twoHourAccuracys.size()), 4, BigDecimal.ROUND_HALF_UP);
        List<BigDecimal> customAccuracys = resultList.stream().map(LoadBatchAccuracyDTO::getCustomAccuracy).filter(Objects::nonNull).collect(Collectors.toList());
        BigDecimal customAccuracyAvg = CollectionUtils.isEmpty(customAccuracys) ? null : customAccuracys.stream().reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(customAccuracys.size()), 4, BigDecimal.ROUND_HALF_UP);

        LoadBatchAccuracyDTO dto = new LoadBatchAccuracyDTO();
        dto.setDate("平均");
        dto.setFcHour(4);
        dto.setMaxAccuracy(maxAccuracyAvg);
        dto.setMinAccuracy(minAccuracyAvg);
        dto.setFirstPointAccuracy(firstPointAccuracyAvg);
        dto.setFirstHourAccuracy(firstHourAccuracyAvg);
        dto.setTwoHourAccuracy(twoHourAccuracyAvg);
        dto.setCustomAccuracy(customAccuracyAvg);
        resultList.add(dto);
        return resultList;
    }

    @Override
    public List<LoadShortFcAccuracyDTO> findBatchFcTimeAccuracy(String batchId) throws Exception {
        LoadCityFcShortDO loadCityFcShortDO = loadCityFcShortDAO.getByPk(batchId);
        if (loadCityFcShortDO == null) {
            TsieExceptionUtils.newBusinessException("预测负荷为空");
            return new ArrayList<>();
        }
        String startTime = loadCityFcShortDO.getStartTime();
        List<BigDecimal> hisLoads = new ArrayList<>();
        //最多需要查两天的历史负荷
        Map<Date, List<BigDecimal>> hisloadDateMap = getHisloadDateMap(
            loadCityFcShortDO.getCityId(), loadCityFcShortDO.getDate(), DateUtils.addDays(loadCityFcShortDO.getDate(), 1), loadCityFcShortDO.getCaliberId(), loadCityFcShortDO.getType());
        hisLoads.addAll(hisloadDateMap.get(loadCityFcShortDO.getDate()));
        hisLoads.addAll(hisloadDateMap.get(DateUtils.addDays(loadCityFcShortDO.getDate(), 1)));
        int startTimePointNum = DateUtil.getTimePoint(startTime, loadCityFcShortDO.getType() == 1 ? 288 : 96);
        //批次预测时刻点所对应的 实际负荷
        List<BigDecimal> batchHisLoads = hisLoads.subList(startTimePointNum - 1, loadCityFcShortDO.getType() == 1 ? startTimePointNum + 47 : startTimePointNum + 15);
        //获取批次预测时刻点对应的 预测负荷
        List<BigDecimal> batchFcLoads = getShortFcList(loadCityFcShortDO);
        List<LoadShortFcAccuracyDTO> resultList = new ArrayList<>();
        for (int i = 0; i < Math.min(batchFcLoads.size(), batchHisLoads.size()); i++) {
            LoadShortFcAccuracyDTO dto = new LoadShortFcAccuracyDTO();
            dto.setMomentTime(DateUtil.getHHmmTime(loadCityFcShortDO.getStartTime(), loadCityFcShortDO.getType(), i + 1));
            dto.setHisLoad(batchHisLoads.get(i));
            dto.setFcLoad(batchFcLoads.get(i));
            dto.setAccuracy(LoadCalUtil.calcMaxMinAccuracy(batchHisLoads.get(i), batchFcLoads.get(i)));
            resultList.add(dto);
        }
        return resultList;
    }

    private LoadBatchAccuracyDTO calculateBatchAccuray(LoadCityFcShortDO loadCityFcShortDO, Map<Date, List<BigDecimal>> hisloadDateMap, Integer type, Integer customPoint) throws Exception {
        //获取批次预测时刻点所对应的 实际负荷
        List<BigDecimal> batchHisLoads = getBatchHisLoads(loadCityFcShortDO.getDate(), loadCityFcShortDO.getStartTime(), hisloadDateMap, type);
        //获取批次预测时刻点对应的 预测负荷
        List<BigDecimal> batchFcLoads = getShortFcList(loadCityFcShortDO);
        List<BigDecimal> accuracyList = new ArrayList<>(batchFcLoads.size());
        for (int i = 0; i < Math.min(batchFcLoads.size(), batchHisLoads.size()); i++) {
            accuracyList.add(LoadCalUtil.calcMaxMinAccuracy(batchHisLoads.get(i), batchFcLoads.get(i)));
        }
        BigDecimal maxAccuracy = null;
        BigDecimal minAccuracy = null;
        BigDecimal firstHourAccuracy = null;
        BigDecimal twoHourAccuracy = null;
        BigDecimal customAccuracy = null;
        accuracyList = accuracyList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(accuracyList)) {
            maxAccuracy = accuracyList.stream().max(BigDecimal::compareTo).get();
            minAccuracy = accuracyList.stream().min(BigDecimal::compareTo).get();
            firstHourAccuracy = accuracyList.subList(0, Math.min((type == 1 ? 12 : 4), accuracyList.size())).stream().reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(Math.min((type == 1 ? 12 : 4), accuracyList.size())), 4, BigDecimal.ROUND_HALF_UP);
            twoHourAccuracy = accuracyList.subList(0, Math.min((type == 1 ? 24 : 8), accuracyList.size())).stream().reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(Math.min((type == 1 ? 24 : 8), accuracyList.size())), 4, BigDecimal.ROUND_HALF_UP);
            customAccuracy = accuracyList.subList(0, Math.min(customPoint, accuracyList.size())).stream().reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(Math.min(customPoint, accuracyList.size())), 4, BigDecimal.ROUND_HALF_UP);
        }

        LoadBatchAccuracyDTO dto = new LoadBatchAccuracyDTO();
        dto.setBatchId(loadCityFcShortDO.getId());
        dto.setDate(DateUtil.getStrDate(new Date(loadCityFcShortDO.getDate().getTime()), "yyyy-MM-dd"));
        dto.setStartTime(loadCityFcShortDO.getStartTime().substring(0 , 2) + ":" + loadCityFcShortDO.getStartTime().substring(2));
        dto.setFcHour(4);
        dto.setMaxAccuracy(maxAccuracy);
        dto.setMinAccuracy(minAccuracy);
        dto.setFirstPointAccuracy(accuracyList.size() > 0 ? accuracyList.get(0) : null);
        dto.setFirstHourAccuracy(firstHourAccuracy);
        dto.setTwoHourAccuracy(twoHourAccuracy);
        dto.setCustomAccuracy(customAccuracy);
        return dto;
    }

    private List<BigDecimal> getBatchHisLoads(java.sql.Date date, String startTime, Map<Date, List<BigDecimal>> hisloadDateMap, Integer type) {
        List<BigDecimal> allHisLoads = new ArrayList<>();
        if (hisloadDateMap.get(date) == null) {
            return allHisLoads;
        }
        allHisLoads.addAll(hisloadDateMap.get(date));
        if (Integer.valueOf(startTime) > 2000) {
            //批次预测跨天 需要第二天的历史负荷数据
            java.util.Date utilDate = new Date(date.getTime());
            allHisLoads.addAll(hisloadDateMap.get(new java.sql.Date(DateUtils.addDays(utilDate, 1).getTime())));
        }
        int startTimePointNum = DateUtil.getTimePoint(startTime, type == 1 ? 288 : 96);
        return allHisLoads.subList(startTimePointNum - 1, type == 1 ? startTimePointNum + 47 : startTimePointNum + 15);

    }

    private Map<Date, List<BigDecimal>> getHisloadDateMap(String cityId, Date startDate, Date endDate, String caliberId, Integer type) throws Exception {
        //查询日期内的历史负荷，没有用null值补充
        Map<Date, List<BigDecimal>> map = new HashMap<>();
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        if (type == 1) {
            //查询288点 历史负荷
            List<LoadCityHis288DO> his288DOS = loadCityHis288DAO.getLoadCityHisDO(cityId, startDate, endDate, caliberId);
            Map<Date, LoadCityHis288DO> dateMap = his288DOS == null ? new HashMap<>() : his288DOS.stream().collect(Collectors.toMap(e -> e.getDate(), e -> e, (oldv, curv) -> curv));
            for (Date date : dateList) {
                map.put(date, dateMap.get(date) == null ? BasePeriodUtils.toList(new LoadCityHis288DO(), 288, Constants.LOAD_CURVE_START_WITH_ZERO)
                    : BasePeriodUtils.toList(dateMap.get(date), 288, Constants.LOAD_CURVE_START_WITH_ZERO));
            }
        } else if (type == 2) {
            //查询96点 历史负荷
            List<LoadCityHisDO> hisDOList = loadCityHisDAO.getLoadCityHisDO(cityId, startDate, endDate, caliberId);
            Map<Date, LoadCityHisDO> dateMap = hisDOList == null ? new HashMap<>() : hisDOList.stream().collect(Collectors.toMap(e -> e.getDate(), e -> e, (oldv, curv) -> curv));
            for (Date date : dateList) {
                map.put(date, dateMap.get(date) == null ? BasePeriodUtils.toList(new LoadCityHisDO(), 96, Constants.LOAD_CURVE_START_WITH_ZERO)
                    : BasePeriodUtils.toList(dateMap.get(date), 96, Constants.LOAD_CURVE_START_WITH_ZERO));
            }
        }
        return map;
    }


    private List<BigDecimal> getShortFcList(LoadCityFcShortDO loadCityFcShortDO) {
        List<BigDecimal> list = new ArrayList<>();
        Class clazz = loadCityFcShortDO.getClass();
        Field[] fields = clazz.getDeclaredFields();
        //匹配带有数字结尾的字段
        Pattern pattern = Pattern.compile("\\d+$");
        Matcher matcher = null;
        try {
            for (Field field : fields) {
                field.setAccessible(true);
                matcher = pattern.matcher(field.getName());
                if (matcher.find()) {
                    if (field.get(loadCityFcShortDO) == null) {
                        continue;
                    }
                    list.add((BigDecimal) field.get(loadCityFcShortDO));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;

    }

}
