/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.load.impl;

import com.tsintergy.lf.serviceapi.base.airconditioner.api.LoadAcFcBasicService;
import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.LoadAcFcBasicDO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadAcFcBasicDAO;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * Description:<br>
 *
 * @Author:<EMAIL>
 * @Date: 2022/5/23 18:51
 * @Version:1.0.0
 */
@Service("loadAcFcBasicService")
public class LoadAcFcBasicServiceImpl implements LoadAcFcBasicService {

    @Autowired
    LoadAcFcBasicDAO loadAcFcBasicDAO;

    @Override
    public List<LoadAcFcBasicDO> getloadAcFcBasicDOList(String cityId, String caliberId, Date startDate,
        Date endDate) {
        return loadAcFcBasicDAO.getLoadFeatureCityDayFcDO(cityId, startDate, endDate, caliberId);
    }

    @Override
    public List<LoadAcFcBasicDO> getloadAcFcBasicDOList(String cityId, String caliberId, Date startDate, Date endDate,String algorithmId) {
        return loadAcFcBasicDAO.getLoadAcHisDOS(cityId, startDate, endDate, caliberId,algorithmId);
    }

    @Override
    public void doSave(LoadAcFcBasicDO loadAcFcBasicDO) {
        loadAcFcBasicDAO.save(loadAcFcBasicDO);
    }

    @Override
    public void doUpdate(LoadAcFcBasicDO loadAcFcBasicDO) {
        loadAcFcBasicDAO.update(loadAcFcBasicDO);
    }
}