package com.tsintergy.lf.serviceimpl.system.impl;

import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.system.api.SettingBatchInitService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingBatchInitDO;
import com.tsintergy.lf.serviceimpl.system.dao.SettingBatchInitDAO;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/3/5 14:00
 **/
@Service("settingBatchInitService")
public class SettingBatchInitServiceImpl implements SettingBatchInitService {

    @Autowired
    private SettingBatchInitDAO settingBatchInitDAO;

    @Override
    public List<SettingBatchInitDO> getBatchList() {
        return settingBatchInitDAO.getBatchList();
    }

    @Override
    public Map<String,String > getBatchNameMap() {
        List<SettingBatchInitDO> batchList = this.getBatchList();
        Map<String, String> collect = batchList.stream()
            .collect(Collectors.toMap(SettingBatchInitDO::getId, SettingBatchInitDO::getName, (o, n) -> o));
        return collect;
    }

    @Override
    public Integer getBatchIdByTime(Date date) {
        String day = DateUtil.getStrDate(date, DateUtil.DATE_FORMAT2);
        List<SettingBatchInitDO> batchList = this.getBatchList();
        for (SettingBatchInitDO batchInitDO : batchList) {
            Date startDateTime = DateUtil
                .getDateFromString(day + " " + batchInitDO.getStartTime() + ":00", DateUtil.DATE_FORMAT1);
            Date endDateTime = DateUtil
                .getDateFromString(day + " " + batchInitDO.getEndTime() + ":00", DateUtil.DATE_FORMAT1);
            if (date.after(startDateTime) && date.before(endDateTime)) {
                return Integer.valueOf(batchInitDO.getId());
            }
        }
        //没有匹配上系统设置的批次执行时间端，返回默认批次id；
        return 1;
    }

    @Override
    public SettingBatchInitDO getBatchById(String id) {
        if (id != null) {
            return settingBatchInitDAO.getBatchById(id);
        } else {
            SettingBatchInitDO batchInitDO = new SettingBatchInitDO();
            batchInitDO.setStartTime("00:00");
            batchInitDO.setEndTime("23:59");
            return batchInitDO;
        }
    }
}
