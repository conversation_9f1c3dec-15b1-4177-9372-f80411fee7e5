
package com.tsintergy.lf.serviceimpl.system.impl;


import static java.util.stream.Collectors.toList;

import com.alibaba.fastjson.JSONObject;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.core.component.cache.business.CacheService;
import com.tsieframework.core.component.cache.entity.CacheRequest;
import com.tsieframework.core.component.cache.entity.CacheResponse;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.NotIncludedDate;
import com.tsintergy.lf.core.constants.*;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.NotIncludedDateDTO;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LongForecastService;
import com.tsintergy.lf.serviceapi.base.forecast.dto.LabelInfoDTO;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.dto.SaveSystemStepDTO;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemData;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemStepDTO;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemWeatherDTO;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingSystemDO;
import com.tsintergy.lf.serviceimpl.system.dao.SettingSystemDAO;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @version $Id: SettingSystemServiceImpl.java, v 0.1 2018-01-31 10:57:18 tao Exp $$
 */
@Service("settingSystemService")
public class SettingSystemServiceImpl extends BaseServiceImpl implements SettingSystemService {

    @Autowired
    CacheService cacheService;

    @Autowired
    SettingSystemDAO settingSystemDAO;

    @Autowired
    private LongForecastService longForecastService;

    @Override
    public List<NotIncludedDate> getNotIncludedDateList() {
        List<NotIncludedDate> dateNotInclude = new ArrayList<>();
        List<LabelInfoDTO> labelInfo = longForecastService.findLabelInfo(null, null);
        for (LabelInfoDTO labelInfoDTO : labelInfo) {
            Date startDate = labelInfoDTO.getStartDate();
            Date endDate = labelInfoDTO.getEndDate();
            List<Date> listBetweenDay = DateUtil.getListBetweenDay(startDate, endDate);
            for (Date date : listBetweenDay) {
                NotIncludedDateDTO notIncludedDateDTO = new NotIncludedDateDTO();
                notIncludedDateDTO.setDateTime(date);
                notIncludedDateDTO.setCauses(labelInfoDTO.getLabelType());
                dateNotInclude.add(notIncludedDateDTO);
            }
        }
        return dateNotInclude;
    }

    @Override
    public SettingSystemDO doCreate(SettingSystemDO vo) throws Exception {
        try {
            return (SettingSystemDO) settingSystemDAO.create(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException(e.getMessage(), "");
        }
    }


    @Override
    public String getValue(String field) throws Exception {
        String value = null;
        // 从缓存取
        CacheRequest request = new CacheRequest();
        request.setCacheKey(CacheConstants.CACHE_ID_SETTING_SYSTEM_PREFIX + field);
        CacheResponse response = cacheService.queryCacheItemList(request);
        if (response.getData() != null && response.getData().size() > 0) {
            value = ((SettingSystemDO) response.getData().get(0)).getValue();
        }
        return value;
    }

    @Override
    public List<String> getPeakTimes() throws Exception {
        List<String> times = new ArrayList<String>();
        String peakTimeStr = this.getValue(SystemConstant.PEAK_TIME);
        if (StringUtils.isNotEmpty(peakTimeStr)) {
            for (String time : peakTimeStr.split(Constants.SEPARATOR_PUNCTUATION)) {
                String[] startAndEndcolumns = time.split("~");
                if (startAndEndcolumns.length == 2) {
                    List<String> columns = ColumnUtil.getColumnsBetween(startAndEndcolumns[0].replace(":", ""),
                        startAndEndcolumns[1].replace(Constants.SEPARATOR_PUNCTUATION, ""),
                        Constants.LOAD_CURVE_POINT_NUM,
                        Constants.LOAD_CURVE_START_WITH_ZERO, false);
                    times.addAll(columns);
                }
            }
        }
        return times;
    }

    @Override
    public List<String> getTroughTimes() throws Exception {
        List<String> times = new ArrayList<String>();
        String peakTimeStr = this.getValue(SystemConstant.TROUGH_TIME);
        if (StringUtils.isNotEmpty(peakTimeStr)) {
            for (String time : peakTimeStr.split(Constants.SEPARATOR_PUNCTUATION)) {
                String[] startAndEndcolumns = time.split("~");
                if (startAndEndcolumns.length == 2) {
                    List<String> columns = ColumnUtil.getColumnsBetween(startAndEndcolumns[0].replace(":", ""),
                        startAndEndcolumns[1].replace(":", ""), Constants.LOAD_CURVE_POINT_NUM,
                        Constants.LOAD_CURVE_START_WITH_ZERO, false);
                    times.addAll(columns);
                }
            }
        }
        return times;
    }

    @Override
    public String[] getPeak() throws Exception {
        String peakTimeStr = this.getValue(SystemConstant.TROUGH_TIME);
        return peakTimeStr.split(Constants.SEPARATOR_PUNCTUATION);
    }

    @Override
    public SettingSystemDO doSaveValue(String field, String value) throws Exception {
        return (SettingSystemDO) settingSystemDAO.doSaveSettingSystemVO(field, value);
    }


    @Override
    public SystemWeatherDTO getWeatherSwitchAndValue(String weatherType) throws Exception {
        SystemWeatherDTO weatherDTO = new SystemWeatherDTO();
        SettingSystemDO settingSystemVO = settingSystemDAO.getSettingSystemVO(weatherType);
        String peakTimeStr = settingSystemVO.getValue();
        if (StringUtils.isNotEmpty(peakTimeStr)) {
            List<String> strings = Arrays.asList(peakTimeStr.split(Constants.SEPARATOR_PUNCTUATION));
            weatherDTO.setType(WeatherEnum.getTypeBySettingName(weatherType));
            weatherDTO.setPercent(new BigDecimal(strings.get(1)));
            weatherDTO.setWeatherSwitch(strings.get(0).equals("1"));
        }
        return weatherDTO;
    }


    @Override
    public List<SystemWeatherDTO> getWeatherSwitchList(Boolean open) throws Exception {
        List<SystemWeatherDTO> result = new ArrayList<>();
        SystemWeatherDTO humidity = this
            .getWeatherSwitchAndValue(WeatherEnum.HUMIDITY.getSystemSettingName());
        SystemWeatherDTO temperature = this
            .getWeatherSwitchAndValue(WeatherEnum.TEMPERATURE.getSystemSettingName());
        SystemWeatherDTO rainfall = this
            .getWeatherSwitchAndValue(WeatherEnum.RAINFALL.getSystemSettingName());
        SystemWeatherDTO windspeed = this
            .getWeatherSwitchAndValue(WeatherEnum.WINDSPEED.getSystemSettingName());
        result.add(humidity);
        result.add(temperature);
        result.add(rainfall);
        result.add(windspeed);
        if (CollectionUtils.isEmpty(result)) {
            return null;
        }
        if (open) {
            return result.stream().filter(SystemWeatherDTO::getWeatherSwitch)
                .collect(toList());
        } else {
            return result;
        }

    }

    @Override
    public SettingSystemDO findByFieldId(String fieldId) throws Exception {
        return settingSystemDAO.getSettingSystemVO(fieldId);
    }


    @Override
    public void updateWeatherSwitch(List<SystemWeatherDTO> dtos) throws Exception {
        for (SystemWeatherDTO dto : dtos) {
            SettingSystemDO byFieldId = findByFieldId(
                Objects.requireNonNull(WeatherEnum.getEnumByType(dto.getType())).getSystemSettingName());
            BigDecimal divide = dto.getPercent().divide(new BigDecimal(100), 4, RoundingMode.HALF_DOWN);
            byFieldId.setValue(
                (dto.getWeatherSwitch() ? "1" : "0") + Constants.SEPARATOR_PUNCTUATION + divide);
            settingSystemDAO.update(byFieldId);
        }
    }

    @Override
    public void doUpdate(SystemData data) throws Exception {
        List<SettingSystemDO> systemDOList = getSystemDOList();
        Map<String, SettingSystemDO> realMap = systemDOList.stream()
            .collect(Collectors.toMap(SettingSystemDO::getField, Function
                .identity(), (key1, key2) -> key2));
        String forecastDay = data.getForecastDay();
        realMap.get(SystemConstant.FORECAST_DAY).setValue(forecastDay);
        realMap.get(SystemConstant.NORMAL_ALGORITHM).setValue(
            data.getProvinceNormalAlgorithm() + Constants.SEPARATOR_PUNCTUATION + data.getCityNormalAlgorithm());
        realMap.get(SystemConstant.HOLIDAY_ALGORITHM).setValue(
            data.getProvinceHolidayAlgorithm() + Constants.SEPARATOR_PUNCTUATION + data.getCityHolidayAlgorithm());
        realMap.get(SystemConstant.AUTO_REPORT).setValue(
            data.getProvinceAutoReportSwitch() + Constants.SEPARATOR_PUNCTUATION + data.getCityAutoReportSwitch());
        realMap.get(SystemConstant.END_REPORT_TIME).setValue(
            data.getEndReportSwitch() + Constants.SEPARATOR_PUNCTUATION + data.getEndReportTime());
        realMap.get(SystemConstant.PROVINCE_SHORT_5).setValue(
            data.getProvinceShortFiveSwitch() + Constants.SEPARATOR_PUNCTUATION + data.getProvinceShortFiveTime());
        realMap.get(SystemConstant.PROVINCE_SHORT_15).setValue(
            data.getProvinceShortFifteenSwitch() + Constants.SEPARATOR_PUNCTUATION + data
                .getProvinceShortFifteenTime());
        realMap.get(SystemConstant.CITY_SHORT_5).setValue(
            data.getCityShortFiveSwitch() + Constants.SEPARATOR_PUNCTUATION + data.getCityShortFiveTime());
        realMap.get(SystemConstant.CITY_SHORT_15).setValue(
            data.getCityShortFifteenSwitch() + Constants.SEPARATOR_PUNCTUATION + data.getCityShortFifteenTime());
        realMap.get(SystemConstant.TARGET_ACCURACY).setValue(data.getTargetAccuracy());
        realMap.get(SystemConstant.PROVINCE_END_REPORT_TIME).setValue(data.getProvinceEndReportSwitch()
            +Constants.SEPARATOR_PUNCTUATION+data.getProvinceEndReportTime());
        realMap.get(SystemConstant.ACLOAD_SENDD5000_SWITCH).setValue(data.getAcLoadSendD5000());
        realMap.get(SystemConstant.BASICLOAD_INCREASERATE).setValue(data.getBasicLoadIncreaseRate());
        realMap.get(SystemConstant.IMPORTDATASHOW_SENDD5000).setValue(data.getImportDataShowAndSendD5000());
        for (Map.Entry<String, SettingSystemDO> map : realMap.entrySet()) {
            settingSystemDAO.updateAndFlush(map.getValue());
        }
    }

    @Override
    public SystemData getSystemSetting() throws Exception {
        return process(this.getSystemDOList());
    }

    /**
     * 一次性获取系统设置页的多个配置
     */
    private List<SettingSystemDO> getSystemDOList() throws Exception {
        List<String> fileIds = new ArrayList<>();
        fileIds.add(SystemConstant.FORECAST_DAY);
        fileIds.add(SystemConstant.NORMAL_ALGORITHM);
        fileIds.add(SystemConstant.HOLIDAY_ALGORITHM);
        fileIds.add(SystemConstant.AUTO_REPORT);
        fileIds.add(SystemConstant.END_REPORT_TIME);
        fileIds.add(SystemConstant.PROVINCE_SHORT_5);
        fileIds.add(SystemConstant.PROVINCE_SHORT_15);
        fileIds.add(SystemConstant.CITY_SHORT_5);
        fileIds.add(SystemConstant.CITY_SHORT_15);
        fileIds.add(SystemConstant.TARGET_ACCURACY);
        fileIds.add(SystemConstant.PROVINCE_END_REPORT_TIME);
        fileIds.add(SystemConstant.ACLOAD_SENDD5000_SWITCH);
        fileIds.add(SystemConstant.BASICLOAD_INCREASERATE);
        fileIds.add(SystemConstant.IMPORTDATASHOW_SENDD5000);
        fileIds.add(SystemConstant.GUODIAO_UPLOADFILE_ALGO_CALI);
        fileIds.add(SystemConstant.CM_ACCURACY_ALGORITHM_IDS);
        fileIds.add(SystemConstant.GUODIAO_UPLOAD_FILE_ALGORITHM_LIST);
        List<SettingSystemDO> settingSystemVO = this.settingSystemDAO.getSettingSystemVO(fileIds);
        if (CollectionUtils.isEmpty(settingSystemVO) || settingSystemVO.size() != 17) {
            throw TsieExceptionUtils.newBusinessException("系统设置数据异常！请检查数据是否已初始化");
        }
        return settingSystemVO;
    }


    private SystemData process(List<SettingSystemDO> dataList) {
        SystemData data = new SystemData();
        Map<String, String> collect = dataList.stream().collect(Collectors.toMap(SettingSystemDO::getField,
            SettingSystemDO::getValue));
        data.setForecastDay(collect.get(SystemConstant.FORECAST_DAY));
        data.setProvinceNormalAlgorithm(
            collect.get(SystemConstant.NORMAL_ALGORITHM).split(Constants.SEPARATOR_PUNCTUATION)[0]);
        data.setCityNormalAlgorithm(
            collect.get(SystemConstant.NORMAL_ALGORITHM).split(Constants.SEPARATOR_PUNCTUATION)[1]);
        data.setProvinceHolidayAlgorithm(
            collect.get(SystemConstant.HOLIDAY_ALGORITHM).split(Constants.SEPARATOR_PUNCTUATION)[0]);
        data.setCityHolidayAlgorithm(
            collect.get(SystemConstant.HOLIDAY_ALGORITHM).split(Constants.SEPARATOR_PUNCTUATION)[1]);
        data.setProvinceAutoReportSwitch(
            collect.get(SystemConstant.AUTO_REPORT).split(Constants.SEPARATOR_PUNCTUATION)[0]);
        data.setCityAutoReportSwitch(collect.get(SystemConstant.AUTO_REPORT).split(Constants.SEPARATOR_PUNCTUATION)[1]);
        data.setEndReportSwitch(collect.get(SystemConstant.END_REPORT_TIME).split(Constants.SEPARATOR_PUNCTUATION)[0]);
        data.setEndReportTime(collect.get(SystemConstant.END_REPORT_TIME).split(Constants.SEPARATOR_PUNCTUATION)[1]);
        data.setProvinceShortFiveSwitch(
            collect.get(SystemConstant.PROVINCE_SHORT_5).split(Constants.SEPARATOR_PUNCTUATION)[0]);
        data.setProvinceShortFiveTime(
            collect.get(SystemConstant.PROVINCE_SHORT_5).split(Constants.SEPARATOR_PUNCTUATION)[1]);
        data.setProvinceShortFifteenSwitch(
            collect.get(SystemConstant.PROVINCE_SHORT_15).split(Constants.SEPARATOR_PUNCTUATION)[0]);
        data.setProvinceShortFifteenTime(
            collect.get(SystemConstant.PROVINCE_SHORT_15).split(Constants.SEPARATOR_PUNCTUATION)[1]);
        data.setCityShortFiveSwitch(collect.get(SystemConstant.CITY_SHORT_5).split(Constants.SEPARATOR_PUNCTUATION)[0]);
        data.setCityShortFiveTime(collect.get(SystemConstant.CITY_SHORT_5).split(Constants.SEPARATOR_PUNCTUATION)[1]);
        data.setCityShortFifteenSwitch(
            collect.get(SystemConstant.CITY_SHORT_15).split(Constants.SEPARATOR_PUNCTUATION)[0]);
        data.setCityShortFifteenTime(
            collect.get(SystemConstant.CITY_SHORT_15).split(Constants.SEPARATOR_PUNCTUATION)[1]);
        data.setTargetAccuracy(collect.get(SystemConstant.TARGET_ACCURACY));
        data.setProvinceEndReportSwitch(collect.get(SystemConstant.PROVINCE_END_REPORT_TIME).split(Constants.SEPARATOR_PUNCTUATION)[0]);
        data.setProvinceEndReportTime(collect.get(SystemConstant.PROVINCE_END_REPORT_TIME).split(Constants.SEPARATOR_PUNCTUATION)[1]);
        data.setAcLoadSendD5000(collect.get(SystemConstant.ACLOAD_SENDD5000_SWITCH));
        data.setBasicLoadIncreaseRate(collect.get(SystemConstant.BASICLOAD_INCREASERATE));
        data.setImportDataShowAndSendD5000(collect.get(SystemConstant.IMPORTDATASHOW_SENDD5000));
        data.setGuodiaoUploadFileAlgorithm(collect.get(SystemConstant.GUODIAO_UPLOADFILE_ALGO_CALI).split(Constants.SEPARATOR_PUNCTUATION)[0]);
        data.setGuodiaoUploadFileCaliber(collect.get(SystemConstant.GUODIAO_UPLOADFILE_ALGO_CALI).split(Constants.SEPARATOR_PUNCTUATION)[1]);
        data.setCmAccuracyAlgorithmIds(Arrays.asList(collect.get(SystemConstant.CM_ACCURACY_ALGORITHM_IDS).split(Constants.SEPARATOR_PUNCTUATION)));
        data.setGuodiaoUploadFileAlgorithmList(JSONObject.parseObject(collect.get(SystemConstant.GUODIAO_UPLOAD_FILE_ALGORITHM_LIST), Map.class));
        return data;
    }


    /**
     * 获取所有系统参数设置
     */
    @Override
    public List<SettingSystemDO> getAllSettingSystemDO() throws Exception {
        try {
            return (List<SettingSystemDO>) settingSystemDAO.findAll();
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException(e.getMessage(), "");
        }
    }

    @Autowired
    CityService cityService;

    @Override
    public boolean findIsLater(Date report, LoadCityFcDO loadCityFcVO) throws Exception {

        Integer cityType = cityService.findCityById(loadCityFcVO.getCityId()).getType();

        //子网的上报时间要查询系统设置
        String[] value = null;
        if(CityConstants.PROVINCE_TYPE.equals(cityType)){
             value = settingSystemDAO.getValue(SystemConstant.PROVINCE_END_REPORT_TIME).split(SystemConstant.SEPARATOR_PUNCTUATION);

        }else {
            //上报截止时间
            value = this.settingSystemDAO.getValue(SystemConstant.END_REPORT_TIME)
                .split(Constants.SEPARATOR_PUNCTUATION);
        }

        //子网上报截止时间 开启
        if (value[0].equals(ParamConstants.STRING_COMPOSITE_ON)) {
            String reportTime = value[1] + ":00";
            String before = DateUtils.date2String(DateUtils.addDays(loadCityFcVO.getDate(), -1),
                DateFormatType.SIMPLE_DATE_FORMAT_STR);
            String dateStr = before + " " + reportTime;
            Date date = DateUtils.string2Date(dateStr, DateFormatType.DATE_FORMAT_STR);
            if (report.before(date) || report.equals(date)) {
                return false;
            }
            return true;
        } else {
            return false;
        }
    }

    @Override
    public Map<Integer, List<String>> getTimes(String filedId) throws Exception {
        String str = getValue(filedId);
        Map<Integer, List<String>> map = new HashMap<>();
        if (StringUtils.isNotEmpty(str)) {
            String[] strings = str.split(Constants.SEPARATOR_PUNCTUATION);
            for (int i = 0; i < strings.length; i++) {
                String[] startAndEndcolumns = strings[i].split("~");
                if (startAndEndcolumns.length == 2) {
                    List<String> columns = ColumnUtil.getColumnsBetween(startAndEndcolumns[0].replace(":", ""),
                        startAndEndcolumns[1].replace(Constants.SEPARATOR_PUNCTUATION, "").replace(":", ""),
                        Constants.LOAD_CURVE_POINT_NUM,
                        Constants.LOAD_CURVE_START_WITH_ZERO, false);
                    map.put(i, columns);
                }
            }
        }
        return map;
    }

    @Override
    public void doUpdateOrSaveValueByKey(String key, String value) throws Exception {
        SettingSystemDO byFieldId = this.findByFieldId(key);
        if (byFieldId != null) {
            byFieldId.setValue(value);
            settingSystemDAO.update(byFieldId);
        } else {
            this.doSaveValue(key, value);
        }
    }

    @Override
    public String findEndReportTime() throws Exception {
        String[] value = settingSystemDAO.getValue(SystemConstant.END_REPORT_TIME)
            .split(SystemConstant.SEPARATOR_PUNCTUATION);

        return value[1];
    }

    @Override
    public List<SettingSystemDO> findByKey(String key) {
        return settingSystemDAO.selectList(key);
    }

    @Override
    public SettingSystemDO getTargetAccuracy(String key) {
        DBQueryParamBuilder paramBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (StringUtils.isNotBlank(key)) {
            paramBuilder.where(QueryOp.StringEqualTo, "field", key);
        }
        return settingSystemDAO.query(paramBuilder.build()).getDatas().get(0);
    }

    @Override
    public SystemStepDTO findStepSetting() throws Exception {
        List<SettingSystemDO> dataList = this.getStepBySystemDOList();
        SystemStepDTO data = new SystemStepDTO();
        Map<String, String> collect = dataList.stream().collect(Collectors.toMap(SettingSystemDO::getField,
            SettingSystemDO::getValue));
        data.setParameterSubstitutionStep(collect.get(SystemConstant.PARAMETER_SUBSTITUTION_STEP));
        data.setIncreaseAndDecreaseStep(collect.get(SystemConstant.INCREASE_AND_DECREASE_STEP));
        data.setStretchStep(collect.get(SystemConstant.STRETCH_STEP));
        data.setExtremumStep(collect.get(SystemConstant.EXTREMUM_STEP));
        return data;
    }

    @Override
    public void updateStepSetting(SaveSystemStepDTO request) throws Exception {
        List<SettingSystemDO> stepBySystemDOList = getStepBySystemDOList();
        Map<String, SettingSystemDO> realMap = stepBySystemDOList.stream()
            .collect(Collectors.toMap(SettingSystemDO::getField, Function
                .identity(), (key1, key2) -> key2));
        realMap.get(SystemConstant.PARAMETER_SUBSTITUTION_STEP).setValue(request.getParameterSubstitutionStep());
        realMap.get(SystemConstant.INCREASE_AND_DECREASE_STEP).setValue(request.getIncreaseAndDecreaseStep());
        realMap.get(SystemConstant.STRETCH_STEP).setValue(request.getStretchStep());
        realMap.get(SystemConstant.EXTREMUM_STEP).setValue(request.getExtremumStep());
        for (Map.Entry<String, SettingSystemDO> map : realMap.entrySet()) {
            settingSystemDAO.updateAndFlush(map.getValue());
        }
    }



    /**
     * 一次性获取系统设置页的多个配置
     */
    private List<SettingSystemDO> getStepBySystemDOList() throws Exception {
        List<String> fileIds = new ArrayList<>();
        fileIds.add(SystemConstant.PARAMETER_SUBSTITUTION_STEP);
        fileIds.add(SystemConstant.INCREASE_AND_DECREASE_STEP);
        fileIds.add(SystemConstant.STRETCH_STEP);
        fileIds.add(SystemConstant.EXTREMUM_STEP);
        List<SettingSystemDO> settingSystemVO = this.settingSystemDAO.getSettingSystemVO(fileIds);
        if (CollectionUtils.isEmpty(settingSystemVO) || settingSystemVO.size() != 4) {
            throw TsieExceptionUtils.newBusinessException("系统设置数据异常！请检查数据是否已初始化");
        }
        return settingSystemVO;
    }
}


