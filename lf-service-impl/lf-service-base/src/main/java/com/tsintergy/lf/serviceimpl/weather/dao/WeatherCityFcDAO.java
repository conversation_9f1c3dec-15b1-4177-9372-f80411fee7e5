package com.tsintergy.lf.serviceimpl.weather.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 *
 * <AUTHOR>
 * @version $Id: WeatherCityFcDAO.java, v 0.1 2018-01-31 10:59:42 tao Exp $$
 */
@Component
public class WeatherCityFcDAO extends BaseAbstractDAO<WeatherCityFcDO> {

    public List<WeatherCityFcDO> findWeatherCityFcDO(String cityId, Integer type, Date startDate, Date endDate) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("1");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if(null != startDate){
            param.getQueryConditions().put("_dnl_date",new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate){
            param.getQueryConditions().put("_dnm_date",new java.sql.Date(endDate.getTime()));
        }
        if (null != type){
            param.getQueryConditions().put("_ne_type", type);
        }
        if (null != cityId) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<WeatherCityFcDO> weatherCityFcVOs = this.query(param).getDatas();
        return weatherCityFcVOs;
    }

    public List<WeatherCityFcDO> findWeatherCityHisDOs(List<String> cityIdList, Integer type, Date startDate, Date endDate) {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if(null != startDate){
            param.getQueryConditions().put("_dnl_date",new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate){
            param.getQueryConditions().put("_dnm_date",new java.sql.Date(endDate.getTime()));
        }
        if (null != type){
            param.getQueryConditions().put("_ne_type", type);
        }
        if (!CollectionUtils.isEmpty(cityIdList)) {
            param.getQueryConditions().put("_sin_cityId", cityIdList);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<WeatherCityFcDO> weatherCityFcVOs = this.query(param).getDatas();
        return weatherCityFcVOs;
    }


    public List<WeatherCityFcDO> findWeatherCityFcDO(String cityId, Date startDate, Date endDate) throws Exception {
        return findWeatherCityFcDO(cityId,null,startDate,endDate);
    }
}