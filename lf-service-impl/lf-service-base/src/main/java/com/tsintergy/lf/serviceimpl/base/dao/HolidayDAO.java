package com.tsintergy.lf.serviceimpl.base.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceapi.base.common.enumeration.HolidayType;
import com.tsintergy.lf.core.enums.HolidayEnum;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @version $Id: HolidayDAO.java, v 0.1 2018-01-31 09:45:43 tao Exp $$
 */
@Component
public class HolidayDAO extends BaseAbstractDAO<HolidayDO> {


    public List<HolidayDO> findHoliday(Date startDate, Date endDate) throws Exception {
        DBQueryParamBuilder param = DBQueryParamBuilder.create().queryDataOnly();
        param.pageSize("0");
        if(startDate != null) {
            param.where(QueryOp.DateNoMoreThan, "startDate", new java.sql.Date(endDate.getTime()));
        }

        if(endDate != null) {
            param.where(QueryOp.DateNoLessThan, "endDate", new java.sql.Date(startDate.getTime()));
        }
        param.addOrderByDesc("date");
        return query(param.build()).getDatas();
    }

    public List<Date> findHolidayDataByYear(String year) throws Exception {
        DBQueryParamBuilder param = DBQueryParamBuilder.create().queryDataOnly();
        param.pageSize("0");
        if(year != null) {
            param.where(QueryOp.StringEqualTo, "year", year);
        }
        param.addOrderByDesc("date");
        List<HolidayDO> holidayVOS = query(param.build()).getDatas();
        List<Date> dates = new ArrayList<Date>();
        for (HolidayDO holidayVO : holidayVOS) {
            String offDates = holidayVO.getOffDates();
            if (StringUtils.isNotBlank(offDates)) {
                String[] offDateArr = offDates.split(",");
                for (String offDate : offDateArr) {
                    dates.add(DateUtil.getDateFromString(offDate, "yyyy-MM-dd"));
                }
            }
        }
        return dates.stream().distinct().collect(Collectors.toList());
    }

    public List<Date> getAllHolidays() throws Exception {
        List<Date> dates = new ArrayList<Date>();
        DBQueryParamBuilder param = DBQueryParamBuilder.create().queryDataOnly();
        param.pageSize("0");
        List<HolidayDO> holidayVOS = query(param.build()).getDatas();
        for (HolidayDO holidayVO : holidayVOS) {
            if (holidayVO.getStartDate() != null && holidayVO.getEndDate() != null) {
                dates.addAll(DateUtil.getListBetweenDay(holidayVO.getStartDate(), holidayVO.getEndDate()));
            }
        }
        return dates;
    }

    /**
     * 导入节假日
     */
    public void importHolidays(List<Map<Integer, Object>> list) throws BusinessException {
        try {
            if (list != null) {
                // 从第2行开始，忽略表头
                for (int i = 1; i < list.size(); i++) {
                    Map<Integer, Object> maps = list.get(i);
                    //一个map集合就是一个HolidayVo
                    if (maps.get(0) != null && StringUtils.isNotBlank(maps.get(0).toString())) {
                        if(StringUtils.equals("注", maps.get(0).toString().substring(0,1))){
                            break;
                        }
                        HolidayDO holidayVO = new HolidayDO();
                        holidayVO.setYear(maps.get(0).toString());
                        holidayVO.setHoliday(String.valueOf(maps.get(1)));
                        holidayVO.setDate(java.sql.Date.valueOf(maps.get(2).toString()));
                        holidayVO.setStartDate(java.sql.Date.valueOf(maps.get(3).toString()));
                        holidayVO.setEndDate(java.sql.Date.valueOf(maps.get(4).toString()));
                        if (maps.get(6) != null && NumberUtils.isNumber(maps.get(6).toString())) {
                            holidayVO.setPreEffectDays(Integer.parseInt(maps.get(6).toString()));
                        } else {
                            holidayVO.setPreEffectDays(0);
                        }
                        if (maps.get(7) != null && NumberUtils.isNumber(maps.get(7).toString())) {
                            holidayVO.setAfterEffectDays(Integer.parseInt(maps.get(7).toString()));
                        } else {
                            holidayVO.setAfterEffectDays(0);
                        }
                        holidayVO.setOffDates(String.valueOf(maps.get(5)));
                        holidayVO.setCode(HolidayEnum.getCodeByName(holidayVO.getHoliday()));
                        holidayVO.setType(
                            holidayVO.getCode() == HolidayEnum.CUSTOMIZE.getCode() ? HolidayType.CUSTOMIZE.value()
                                : HolidayType.LEGAL.value());
                        List<HolidayDO> holidayDTOS = findByCodeAndYear(holidayVO.getYear(), holidayVO.getCode());
                        if(!CollectionUtils.isEmpty(holidayDTOS)){
                            HolidayDO holidayDO = holidayDTOS.get(0);
                            holidayVO.setId(holidayDO.getId());
                            BeanUtils.copyProperties(holidayVO, holidayDO);
                            holidayDO.setUpdateTime(new java.sql.Date(System.currentTimeMillis()));
                            update(holidayDO);
                        }else {
                            holidayVO.setCreateTime(new java.sql.Date(System.currentTimeMillis()));
                            createAndFlush(holidayVO);
                        }

                    }
                }
            }
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("导入节假日出错了,请检查文件内容是否有误", e);
        }
    }

    public HolidayDO getHolidayDO(Date date) throws Exception {
        List<HolidayDO> holidayVOS = this.findHoliday(date, date);
        if (holidayVOS.size() > 1) {
            return holidayVOS.get(0);
        }
        return null;
    }

    public List<HolidayDO> findHolidayByYear(String year) throws Exception {
        DBQueryParamBuilder param = DBQueryParamBuilder.create().queryDataOnly();
        param.pageSize("0");
        param.where(QueryOp.DateNoLessThan, "date",
            new java.sql.Date(
                DateUtils.string2Date(year + "-01-01", DateFormatType.SIMPLE_DATE_FORMAT_STR).getTime()));
        param.where(QueryOp.DateNoMoreThan, "date",
            new java.sql.Date(DateUtils.string2Date(year + "-12-31", DateFormatType.SIMPLE_DATE_FORMAT_STR).getTime()));
        param.addOrderByDesc("date");
        return query(param.build()).getDatas();
    }


    public List<HolidayDO> findByCodeAndYear(String year, Integer code) throws Exception {
        DBQueryParamBuilder param = DBQueryParamBuilder.create().queryDataOnly();
        param.pageSize("0");
        param.where(QueryOp.DateNoLessThan,"date",
            new java.sql.Date(DateUtils.string2Date(year + "-01-01", DateFormatType.SIMPLE_DATE_FORMAT_STR).getTime()));
        param.where(QueryOp.DateNoMoreThan,"date",
            new java.sql.Date(DateUtils.string2Date(year + "-12-31", DateFormatType.SIMPLE_DATE_FORMAT_STR).getTime()));
        param.where(QueryOp.StringEqualTo,"code", code);
        param.addOrderByDesc("date");
        return query(param.build()).getDatas();
    }


    /**
     * 查询年区间内的节日
     *
     * @param startYear 开始年
     * @param endYear 结束年
     * @param holidayName 节日名称
     * @return 节日列表
     */
    public List<HolidayDO> findHolidayByYearAndName(String startYear, String endYear, String holidayName)
        throws Exception {
        DBQueryParamBuilder builder = DBQueryParamBuilder.create().queryDataOnly();
        if (StringUtils.isNotBlank(startYear)) {
            builder.where(QueryOp.DateNoLessThan, "date", new java.sql.Date(DateUtil.getYearStat(startYear).getTime()));
        }
        if (StringUtils.isNotBlank(endYear)) {
            builder.where(QueryOp.DateNoMoreThan, "date", new java.sql.Date(DateUtil.getYearEnd(endYear).getTime()));
        }
        if (StringUtils.isNotBlank(endYear)) {
            builder.where(QueryOp.StringEqualTo, "holiday", holidayName);
        }
        builder.addOrderByAsc("date");
        DBQueryParam dbQueryParam = builder.build();
        List<HolidayDO> holidayVOS = query(dbQueryParam).getDatas();
        return holidayVOS;
    }

    /**
     * 功能描述: <br> 查询时间段内同一个节假日
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    public List<HolidayDO> listHolidayByCode(String startYear, String endYear, Integer code) throws Exception {
        DBQueryParamBuilder builder = DBQueryParamBuilder.create().queryDataOnly();
        if (StringUtils.isNotBlank(startYear)) {
            builder.where(QueryOp.DateNoLessThan, "date", new java.sql.Date(DateUtil.getYearStat(startYear).getTime()));
        }
        if (StringUtils.isNotBlank(endYear)) {
            builder.where(QueryOp.DateNoMoreThan, "date", new java.sql.Date(DateUtil.getYearEnd(endYear).getTime()));
        }
        if (StringUtils.isNotBlank(endYear)) {
            builder.where(QueryOp.NumberEqualTo, "code", code);
        }
        builder.addOrderByAsc("date");
        DBQueryParam dbQueryParam = builder.build();
        List<HolidayDO> holidayVOS = query(dbQueryParam).getDatas();
        return holidayVOS;
    }


    public List<HolidayDO> listHolidayList(Date startDate, Date endDate) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().queryDataOnly().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        param.setOrderby("date");
        List<HolidayDO> holidayVOS = this.query(param).getDatas();
        return holidayVOS;
    }
}