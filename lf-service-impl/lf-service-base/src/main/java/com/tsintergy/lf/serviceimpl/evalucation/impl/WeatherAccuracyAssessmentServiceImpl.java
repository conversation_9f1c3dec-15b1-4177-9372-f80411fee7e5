package com.tsintergy.lf.serviceimpl.evalucation.impl;

import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.evalucation.api.WeatherAccuracyAssessmentService;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.*;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcBatchService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcBatchDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 气象预测准确率评估服务实现
 */
@Service("weatherAccuracyAssessmentService")
public class WeatherAccuracyAssessmentServiceImpl implements WeatherAccuracyAssessmentService {

    @Autowired
    private WeatherCityFcBatchService weatherCityFcBatchService;

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Override
    public WeatherAccuracyAssessmentDTO getWeatherAccuracyAssessment(String cityId, Date startDate, Date endDate,
                                                                     String weatherSource, String batchId,
                                                                     Integer weatherIndicatorType, Date selectedDate,
                                                                     Integer selectedDay) throws Exception {
        WeatherAccuracyAssessmentDTO result = new WeatherAccuracyAssessmentDTO();

        // 获取气象预测准确率表格数据
        WeatherAccuracyTableDTO accuracyTable = getWeatherAccuracyTable(cityId, startDate, endDate, weatherSource, batchId, weatherIndicatorType);
        result.setAccuracyTable(accuracyTable);

        // 获取预测结果对比数据（EC气象源和气象站）
        List<WeatherSourceComparisonDTO> sourceComparison = getWeatherSourceComparison(cityId, startDate, endDate, batchId, weatherIndicatorType);
        result.setSourceComparison(sourceComparison);

        // 获取气象预测偏差详情
        if (selectedDate != null && selectedDay != null) {
            WeatherDeviationDetailDTO deviationDetail = getWeatherDeviationDetail(cityId, selectedDate, selectedDay, weatherSource, batchId);
            result.setDeviationDetail(deviationDetail);

            // 获取气象预测对比数据（默认温度类型）
            WeatherForecastComparisonDTO forecastComparison = getWeatherForecastComparison(cityId, selectedDate, selectedDay, weatherSource, batchId, 2);
            result.setForecastComparison(forecastComparison);
        } else {
            // 默认选择左下角单元格（最后一个日期的D-10）
            if (!CollectionUtils.isEmpty(accuracyTable.getDates())) {
                Date defaultSelectedDate = accuracyTable.getDates().get(accuracyTable.getDates().size() - 1);
                Integer defaultSelectedDay = 10;

                WeatherDeviationDetailDTO deviationDetail = getWeatherDeviationDetail(cityId, defaultSelectedDate, defaultSelectedDay, weatherSource, batchId);
                result.setDeviationDetail(deviationDetail);

                WeatherForecastComparisonDTO forecastComparison = getWeatherForecastComparison(cityId, defaultSelectedDate, defaultSelectedDay, weatherSource, batchId, 2);
                result.setForecastComparison(forecastComparison);
            }
        }

        return result;
    }

    @Override
    public WeatherAccuracyTableDTO getWeatherAccuracyTable(String cityId, Date startDate, Date endDate,
                                                            String weatherSource, String batchId,
                                                            Integer weatherIndicatorType) throws Exception {
        WeatherAccuracyTableDTO result = new WeatherAccuracyTableDTO();

        // 设置气象指标选项
        List<WeatherAccuracyTableDTO.WeatherIndicatorOption> indicatorOptions = Arrays.asList(
                createIndicatorOption(0, "最高温度"),
                createIndicatorOption(1, "最低温度"),
                createIndicatorOption(2, "平均温度"),
                createIndicatorOption(3, "相对湿度"),
                createIndicatorOption(4, "累计降雨量"),
                createIndicatorOption(5, "最大风速")
        );
        result.setIndicatorOptions(indicatorOptions);
        result.setWeatherIndicatorType(weatherIndicatorType != null ? weatherIndicatorType : 0);

        // 生成D-1到D-10的日期列表
        List<Date> dates = DateUtil.getListBetweenDay(startDate, endDate);
        result.setDates(dates);

        // 计算准确率数据
        Map<Date, List<BigDecimal>> accuracyData = new HashMap<>();
        Integer weatherType = getWeatherTypeByIndicator(weatherIndicatorType != null ? weatherIndicatorType : 0);

        for (Date date : dates) {
            List<BigDecimal> dayAccuracyList = new ArrayList<>();

            // 计算D-1到D-10的准确率
            for (int day = 1; day <= 10; day++) {
                Date predDate = DateUtil.getMoveDay(date, -day);
                BigDecimal accuracy = calculateWeatherAccuracy(cityId, date, predDate, weatherSource, batchId, weatherType);
                dayAccuracyList.add(accuracy);
            }

            accuracyData.put(date, dayAccuracyList);
        }

        result.setAccuracyData(accuracyData);
        return result;
    }

    @Override
    public WeatherDeviationDetailDTO getWeatherDeviationDetail(String cityId, Date selectedDate, Integer selectedDay,
                                                                String weatherSource, String batchId) throws Exception {
        WeatherDeviationDetailDTO result = new WeatherDeviationDetailDTO();
        result.setSelectedDate(selectedDate);
        result.setSelectedDay(selectedDay);

        Date predDate = DateUtil.getMoveDay(selectedDate, -selectedDay);

        // 获取预测和实际的温度数据
        BigDecimal[] maxTempData = getTemperatureData(cityId, selectedDate, predDate, weatherSource, batchId, 0); // 最高温度
        BigDecimal[] minTempData = getTemperatureData(cityId, selectedDate, predDate, weatherSource, batchId, 2); // 最低温度
        BigDecimal[] avgTempData = getTemperatureData(cityId, selectedDate, predDate, weatherSource, batchId, 1); // 平均温度

        if (maxTempData != null) {
            result.setForecastMaxTemperature(maxTempData[0]);
            result.setActualMaxTemperature(maxTempData[1]);
            result.setMaxTemperatureDeviation(maxTempData[0] != null && maxTempData[1] != null ?
                maxTempData[0].subtract(maxTempData[1]) : null);
        }

        if (minTempData != null) {
            result.setForecastMinTemperature(minTempData[0]);
            result.setActualMinTemperature(minTempData[1]);
            result.setMinTemperatureDeviation(minTempData[0] != null && minTempData[1] != null ?
                minTempData[0].subtract(minTempData[1]) : null);
        }

        if (avgTempData != null) {
            result.setForecastAvgTemperature(avgTempData[0]);
            result.setActualAvgTemperature(avgTempData[1]);
            result.setAvgTemperatureDeviation(avgTempData[0] != null && avgTempData[1] != null ?
                avgTempData[0].subtract(avgTempData[1]) : null);
        }

        return result;
    }

    @Override
    public WeatherForecastComparisonDTO getWeatherForecastComparison(String cityId, Date selectedDate, Integer selectedDay,
                                                                      String weatherSource, String batchId,
                                                                      Integer weatherType) throws Exception {
        WeatherForecastComparisonDTO result = new WeatherForecastComparisonDTO();
        result.setSelectedDate(selectedDate);
        result.setSelectedDay(selectedDay);
        result.setWeatherType(weatherType);
        result.setWeatherTypeName(getWeatherTypeName(weatherType));

        Date predDate = DateUtil.getMoveDay(selectedDate, -selectedDay);
        result.setForecastCreateTime(predDate);

        // 获取预测气象数据
        List<WeatherCityFcBatchDO> forecastData = weatherCityFcBatchService.findByCondition(cityId, weatherSource, predDate, weatherType, batchId);
        if (!CollectionUtils.isEmpty(forecastData)) {
            WeatherCityFcBatchDO forecast = forecastData.stream()
                    .filter(f -> f.getDate().equals(new java.sql.Date(selectedDate.getTime())))
                    .findFirst()
                    .orElse(null);
            if (forecast != null) {
                result.setForecastWeatherData(forecast.getWeatherList());
            }
        }

        // 获取实际气象数据
        List<WeatherCityHisDO> actualData = weatherCityHisService.findWeatherCityHisDOSBySQLDate(cityId, weatherType,
                new java.sql.Date(selectedDate.getTime()), new java.sql.Date(selectedDate.getTime()));
        if (!CollectionUtils.isEmpty(actualData)) {
            WeatherCityHisDO actual = actualData.get(0);
            result.setActualWeatherData(actual.getloadList());
        }

        return result;
    }

    /**
     * 获取气象源对比数据
     */
    private List<WeatherSourceComparisonDTO> getWeatherSourceComparison(String cityId, Date startDate, Date endDate,
                                                                         String batchId, Integer weatherIndicatorType) throws Exception {
        List<WeatherSourceComparisonDTO> result = new ArrayList<>();

        // EC气象源和气象站
        String[] sources = {"EC", "气象站"};
        Integer weatherType = getWeatherTypeByIndicator(weatherIndicatorType != null ? weatherIndicatorType : 0);

        for (String source : sources) {
            WeatherSourceComparisonDTO sourceComparison = new WeatherSourceComparisonDTO();
            sourceComparison.setSourceName(source);

            List<BigDecimal> averageAccuracyList = new ArrayList<>();
            List<BigDecimal> allAccuracies = new ArrayList<>();

            // 计算D-1到D-10的平均准确率
            for (int day = 1; day <= 10; day++) {
                List<BigDecimal> dayAccuracies = new ArrayList<>();

                List<Date> dates = DateUtil.getListBetweenDay(startDate, endDate);
                for (Date date : dates) {
                    Date predDate = DateUtil.getMoveDay(date, -day);
                    BigDecimal accuracy = calculateWeatherAccuracy(cityId, date, predDate, source, batchId, weatherType);
                    if (accuracy != null) {
                        dayAccuracies.add(accuracy);
                        allAccuracies.add(accuracy);
                    }
                }

                // 计算该天数的平均准确率
                BigDecimal dayAverage = dayAccuracies.isEmpty() ? BigDecimal.ZERO :
                        dayAccuracies.stream()
                                .reduce(BigDecimal.ZERO, BigDecimal::add)
                                .divide(BigDecimal.valueOf(dayAccuracies.size()), 4, RoundingMode.HALF_UP);
                averageAccuracyList.add(dayAverage);
            }

            sourceComparison.setAverageAccuracyList(averageAccuracyList);

            // 计算总体平均准确率
            BigDecimal overallAverage = allAccuracies.isEmpty() ? BigDecimal.ZERO :
                    allAccuracies.stream()
                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                            .divide(BigDecimal.valueOf(allAccuracies.size()), 4, RoundingMode.HALF_UP);
            sourceComparison.setOverallAverageAccuracy(overallAverage);

            result.add(sourceComparison);
        }

        return result;
    }

    /**
     * 计算气象准确率
     */
    private BigDecimal calculateWeatherAccuracy(String cityId, Date targetDate, Date predDate, String weatherSource,
                                                String batchId, Integer weatherType) throws Exception {
        try {
            // 获取预测数据
            List<WeatherCityFcBatchDO> forecastData = weatherCityFcBatchService.findByCondition(cityId, weatherSource, predDate, weatherType, batchId);
            WeatherCityFcBatchDO forecast = forecastData.stream()
                    .filter(f -> f.getDate().equals(new java.sql.Date(targetDate.getTime())))
                    .findFirst()
                    .orElse(null);

            // 获取实际数据
            List<WeatherCityHisDO> actualData = weatherCityHisService.findWeatherCityHisDOSBySQLDate(cityId, weatherType,
                    new java.sql.Date(targetDate.getTime()), new java.sql.Date(targetDate.getTime()));
            WeatherCityHisDO actual = CollectionUtils.isEmpty(actualData) ? null : actualData.get(0);

            if (forecast != null && actual != null) {
                // 使用96点数据计算准确率
                List<BigDecimal> forecastList = forecast.getWeatherList();
                List<BigDecimal> actualList = actual.getloadList();

                if (!CollectionUtils.isEmpty(forecastList) && !CollectionUtils.isEmpty(actualList) &&
                    forecastList.size() == actualList.size()) {
                    return calculateAccuracyFromLists(actualList, forecastList);
                }
            }
        } catch (Exception e) {
            // 记录日志但不抛出异常，返回null
        }
        return null;
    }

    /**
     * 获取温度数据（预测值和实际值）
     */
    private BigDecimal[] getTemperatureData(String cityId, Date targetDate, Date predDate, String weatherSource,
                                            String batchId, Integer tempType) throws Exception {
        try {
            // 获取预测数据
            List<WeatherCityFcBatchDO> forecastData = weatherCityFcBatchService.findByCondition(cityId, weatherSource, predDate, 2, batchId); // 温度类型为2
            WeatherCityFcBatchDO forecast = forecastData.stream()
                    .filter(f -> f.getDate().equals(new java.sql.Date(targetDate.getTime())))
                    .findFirst()
                    .orElse(null);

            // 获取实际数据
            List<WeatherCityHisDO> actualData = weatherCityHisService.findWeatherCityHisDOSBySQLDate(cityId, 2,
                    new java.sql.Date(targetDate.getTime()), new java.sql.Date(targetDate.getTime()));
            WeatherCityHisDO actual = CollectionUtils.isEmpty(actualData) ? null : actualData.get(0);

            if (forecast != null && actual != null) {
                BigDecimal forecastValue = getTemperatureValue(forecast.getWeatherList(), tempType);
                BigDecimal actualValue = getTemperatureValue(actual.getloadList(), tempType);
                return new BigDecimal[]{forecastValue, actualValue};
            }
        } catch (Exception e) {
            // 记录日志但不抛出异常
        }
        return null;
    }

    /**
     * 从96点数据中提取温度值
     */
    private BigDecimal getTemperatureValue(List<BigDecimal> weatherData, Integer tempType) {
        if (CollectionUtils.isEmpty(weatherData)) {
            return null;
        }

        List<BigDecimal> validData = weatherData.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (validData.isEmpty()) {
            return null;
        }

        switch (tempType) {
            case 0: // 最高温度
                return validData.stream().max(BigDecimal::compareTo).orElse(null);
            case 1: // 平均温度
                return validData.stream()
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        .divide(BigDecimal.valueOf(validData.size()), 2, RoundingMode.HALF_UP);
            case 2: // 最低温度
                return validData.stream().min(BigDecimal::compareTo).orElse(null);
            default:
                return null;
        }
    }

    /**
     * 根据气象指标类型获取对应的气象类型
     */
    private Integer getWeatherTypeByIndicator(Integer indicatorType) {
        switch (indicatorType) {
            case 0: // 最高温度
            case 1: // 最低温度
            case 2: // 平均温度
                return 2; // 温度
            case 3: // 相对湿度
                return 1; // 湿度
            case 4: // 累计降雨量
                return 3; // 降雨量
            case 5: // 最大风速
                return 4; // 风速
            default:
                return 2; // 默认温度
        }
    }

    /**
     * 获取气象类型名称
     */
    private String getWeatherTypeName(Integer weatherType) {
        switch (weatherType) {
            case 1:
                return "湿度";
            case 2:
                return "温度";
            case 3:
                return "降雨量";
            case 4:
                return "风速";
            default:
                return "未知";
        }
    }

    /**
     * 创建气象指标选项
     */
    private WeatherAccuracyTableDTO.WeatherIndicatorOption createIndicatorOption(Integer type, String name) {
        WeatherAccuracyTableDTO.WeatherIndicatorOption option = new WeatherAccuracyTableDTO.WeatherIndicatorOption();
        option.setType(type);
        option.setName(name);
        return option;
    }

    /**
     * 从数据列表计算准确率
     */
    private BigDecimal calculateAccuracyFromLists(List<BigDecimal> actualList, List<BigDecimal> forecastList) {
        if (CollectionUtils.isEmpty(actualList) || CollectionUtils.isEmpty(forecastList) ||
            actualList.size() != forecastList.size()) {
            return null;
        }

        BigDecimal correctCount = BigDecimal.ZERO;
        int totalCount = 0;

        for (int i = 0; i < actualList.size(); i++) {
            BigDecimal actual = actualList.get(i);
            BigDecimal forecast = forecastList.get(i);

            if (actual != null && forecast != null) {
                totalCount++;
                // 如果实际值和预测值的差值小于等于2，则认为是正确预报
                if (forecast.subtract(actual).abs().compareTo(new BigDecimal(2)) <= 0) {
                    correctCount = correctCount.add(BigDecimal.ONE);
                }
            }
        }

        if (totalCount == 0) {
            return null;
        }

        return correctCount.divide(BigDecimal.valueOf(totalCount), 4, RoundingMode.HALF_UP);
    }
}
