
package com.tsintergy.lf.serviceimpl.evalucation.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.tsieframework.core.base.exception.DAOException;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.JsonObject;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.util.ColumnUtil;
import com.tsintergy.aif.algorithm.serviceapi.base.util.DateUtil;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.assess.api.SettingAssessService;
import com.tsintergy.lf.serviceapi.base.assess.dto.SettingAssessDataDTO;
import com.tsintergy.lf.serviceapi.base.assess.pojo.SettingAssessDO;
import com.tsintergy.lf.serviceapi.base.assess.pojo.SettingCompositeAccuracyDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.BaseLoadFcCityDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.BaseStatisticsCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.evalucation.api.AccuracyAssessService;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyAssessDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyAssessUnitDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyDistributionDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.CompositeAccuracyUnitDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.*;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcBatchService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcBatchDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.BaseLoadIntervalDO;
import com.tsintergy.lf.serviceapi.base.recall.api.LoadCityFcRecallService;
import com.tsintergy.lf.serviceapi.base.recall.dto.StatisticsAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.recall.pojo.LoadCityFcRecallDO;
import com.tsintergy.lf.serviceapi.base.recall.pojo.StatisticsCityDayFcRecallDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingBatchInitService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingBatchInitDO;
import com.tsintergy.lf.serviceimpl.assess.dao.SettingAssessDAO;
import com.tsintergy.lf.serviceimpl.assess.dao.SettingCompositeAccuracyDAO;
import com.tsintergy.lf.serviceimpl.enums.AssessEnum;
import com.tsintergy.lf.serviceimpl.evalucation.dao.*;
import com.tsintergy.lf.serviceimpl.recall.dao.StatisticsCityDayFcRecallDAO;
import lombok.Getter;
import org.apache.commons.collections4.map.HashedMap;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service("accuracyAssessService")
public class AccuracyAssessServiceImpl implements AccuracyAssessService {


    @Resource
    private AccuracyAssessDAO accuracyAssessDAO;

    @Resource
    private LoadCityFcBatchService loadCityFcBatchService;

    @Resource
    private SettingAssessService settingAssessService;

    @Resource
    private SettingAssessDAO settingAssessDAO;

    @Resource
    private AccuracyCompositeDAO accuracyCompositeDAO;

    @Resource
    private SettingCompositeAccuracyDAO settingCompositeAccuracyDAO;

    @Resource
    private StatisticsCityDayFcBatchDAO statisticsCityDayFcBatchDAO;

    @Resource
    private LoadCityHisService loadCityHisService;

    private final BigDecimal HUNDRED = new BigDecimal(100);

    @Resource
    private SettingBatchInitService settingBatchInitService;

    @Resource
    private AccuracyAssessRecallDAO accuracyAssessRecallDAO;

    @Resource
    private AccuracyCompositeRecallDAO accuracyCompositeRecallDAO;

    @Resource
    private StatisticsCityDayFcRecallDAO statisticsCityDayFcRecallDAO;

    @Resource
    private LoadCityFcRecallService loadCityFcRecallService;
    @Getter
    enum AccuracyRange {
        //区间范围；
        RANGE1("98", "100"),
        RANGE2("95", "98"),
        RANGE3("90", "95"),
        RANGE4("80", "90"),
        RANGE5("0", "80");

        private String max;

        private String min;

        AccuracyRange(String min, String max) {
            this.min = min;
            this.max = max;
        }

    }

    @Override
    public void doCalculateAssessAccuracy(String cityId, String caliberId, String algorithmId, Date startDate,
        Date endDate) throws Exception {
        List<AccuracyAssessDO> toSaveList = new ArrayList<>();
        Map<String, List<SettingAssessDO>> assessSettingByData = settingAssessService
            .findAssessSettingByData(startDate, endDate, caliberId);
        if (assessSettingByData == null) {
            return;
        }
        //极值统一从自定义间隔负荷表里读取后计算；
        List hisLoadSecond = loadCityHisService.selectListData(cityId, caliberId, startDate, endDate);
//        List hisLoadSecond = findOtherIntervalData(cityId, caliberId, startDate,
//            endDate);
        List<LoadCityFcBatchDO> fcList = loadCityFcBatchService
            .findByConditionByBatchId(cityId, startDate, endDate, caliberId, algorithmId, null);

        Map<String, List<LoadCityFcBatchDO>> collectFc = fcList.stream().collect(
            Collectors.groupingBy(src -> src.getCityId() + Constants.SEPARATOR_PUNCTUATION + src.getCaliberId()
                + Constants.SEPARATOR_PUNCTUATION + DateUtil.getDateToStr(src.getDate())));

        //96点数据预测准确率
        List<StatisticsCityDayFcBatchDO> statBatchList = statisticsCityDayFcBatchDAO
                .getStatisticsCityDayFcBatchDOs(cityId == null ? null: Arrays.asList(cityId), caliberId,
                        algorithmId, startDate, endDate, null);

        hisLoadSecond.forEach(src -> {
            if (src instanceof BaseLoadIntervalDO) {
                String monthDate = DateUtil.getMonthByDate(((BaseLoadIntervalDO) src).getDate());
                List<LoadCityFcBatchDO> fcData = collectFc
                    .get(((BaseLoadIntervalDO) src).getCityId() + Constants.SEPARATOR_PUNCTUATION
                        + ((BaseLoadIntervalDO) src).getCaliberId()
                        + Constants.SEPARATOR_PUNCTUATION + DateUtil
                        .getDateToStr(((BaseLoadIntervalDO) src).getDate()));
                if (!CollectionUtils.isEmpty(fcData)) {
                    fcData.forEach(oneFc -> {
                        Optional<StatisticsCityDayFcBatchDO> statisticsCityDayFcBatchOptional = statBatchList.stream().filter(
                                x -> x.getBatchId().equals(oneFc.getBatchId()) && x.getDate().equals(oneFc.getDate())
                                        && x.getCityId().equals(oneFc.getCityId()) && x.getCaliberId().equals(oneFc.getCaliberId())
                                        && x.getAlgorithmId().equals(oneFc.getAlgorithmId())
                        ).findFirst();
                        BaseStatisticsCityDayFcDO statisticsCityDayFcBatchDO = new BaseStatisticsCityDayFcDO();
                        if (statisticsCityDayFcBatchOptional.isPresent()){
                            BeanUtils.copyProperties(statisticsCityDayFcBatchOptional.get(), statisticsCityDayFcBatchDO);
                        }
                        //统计准确率
                        List<AccuracyAssessDO> statistics = statistics(((BaseLoadIntervalDO) src), oneFc,
                            assessSettingByData.get(monthDate), statisticsCityDayFcBatchDO);
                        toSaveList.addAll(statistics);
                    });
                }
            }
        });
        toSaveList.forEach(one -> {
            try {
                one.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                accuracyAssessDAO.saveOrUpdateEntityByTemplate(one);
            } catch (DAOException e) {
                e.printStackTrace();
            }
        });
    }

    @Override
    public List<AccuracyAssessDO> findAccuracyList(String cityId, String caliberId, String algorithmId, Date startDate,
        Date endDate, String batchId) {
        return accuracyAssessDAO.selectList(cityId, caliberId, algorithmId, startDate, endDate, batchId);
    }

    @Override
    public List<AccuracyAssessDO> findAccuracyList(String cityId, String caliberId, String accuracyName, List<String> algorithmId, Date startDate, Date endDate) {
        return accuracyAssessDAO.selectList(cityId, caliberId, accuracyName,algorithmId, startDate, endDate);
    }

    /**
     * 根据 日前n天  + 是否为最新生成 过滤考核点准确率
     *
     * @param assessDOS 考核点准确率列表
     * @param day 日前n天
     */
    private List<AccuracyAssessDO> filterAssessByBatch(List<AccuracyAssessDO> assessDOS, Integer day) {
        List<AccuracyAssessDO> resultList = new ArrayList<>();
        List<AccuracyAssessDO> collect = assessDOS.stream()
            .filter(src -> {
                Date anObject = DateUtils.addDays(src.getDate(), (-day));
                return DateUtil.getDateToStr(new Date(src.getCreatetime().getTime()))
                    .equals(DateUtil.getDateToStr(anObject));
            }).collect(Collectors.toList());
        Map<String, List<AccuracyAssessDO>> map = collect.stream().collect(Collectors.groupingBy(
            src -> src.getAssessName() + Constants.SEPARATOR_BROKEN_LINE + src.getDate()
                + Constants.SEPARATOR_BROKEN_LINE + src.getAlgorithmId() + Constants.SEPARATOR_BROKEN_LINE + src
                .getCityId() + Constants.SEPARATOR_BROKEN_LINE + src.getAssessType(), TreeMap::new, Collectors.toList())
        );
        map.forEach((key, list)->{
            list.sort(Comparator.comparing(AccuracyAssessDO::getCreatetime).reversed());
            resultList.add(list.get(0));
        });
        return resultList;
    }

    /**
     * 根据批次+日前n天过滤综合准确率
     *
     * @param assessDOS 综合准确率列表
     * @param day 日前n天
     */
    private List<AccuracyCompositeDO> filterCompositeByBatch(List<AccuracyCompositeDO> assessDOS, Integer day) {
        List<AccuracyCompositeDO> resultList = new ArrayList<>();
        List<AccuracyCompositeDO> collect = assessDOS.stream()
            .filter(src -> {
                Date anObject = DateUtils.addDays(src.getDate(), (-day));
                return DateUtil.getDateToStr(new Date(src.getCreatetime().getTime()))
                    .equals(DateUtil.getDateToStr(anObject));
            }).collect(Collectors.toList());
        Map<String, List<AccuracyCompositeDO>> map = collect.stream().collect(Collectors.groupingBy(
            src -> src.getAccuracyName() + Constants.SEPARATOR_BROKEN_LINE + src.getDate()
                + Constants.SEPARATOR_BROKEN_LINE + src.getAlgorithmId() + Constants.SEPARATOR_BROKEN_LINE + src
                .getCityId() + Constants.SEPARATOR_BROKEN_LINE , TreeMap::new, Collectors.toList())
        );
        map.forEach((key, list)->{
            list.sort(Comparator.comparing(AccuracyCompositeDO::getCreatetime).reversed());
            resultList.add(list.get(0));
        });
        return resultList;
    }


    /**
     * 根据批次+日前n天过滤批次预测数据
     *
     * @param assessDOS 批次预测列表
     * @param day 日前n天
     */
    private List<LoadCityFcBatchDO> filterForecastByBatch(List<LoadCityFcBatchDO> assessDOS, Integer day) {
        List<LoadCityFcBatchDO> resultList = new ArrayList<>();
        List<LoadCityFcBatchDO> collect = assessDOS.stream()
            .filter(src -> {
                Date anObject = DateUtils.addDays(src.getDate(), (-day));
                return DateUtil.getDateToStr(new Date(src.getCreatetime().getTime()))
                    .equals(DateUtil.getDateToStr(anObject));
            }).collect(Collectors.toList());
        Map<String, List<LoadCityFcBatchDO>> map = collect.stream().collect(Collectors.groupingBy(
            src -> src.getDate()
                + Constants.SEPARATOR_BROKEN_LINE + src.getAlgorithmId() + Constants.SEPARATOR_BROKEN_LINE + src
                .getCityId() + Constants.SEPARATOR_BROKEN_LINE , TreeMap::new, Collectors.toList())
        );
        map.forEach((key, list)->{
            list.sort(Comparator.comparing(LoadCityFcBatchDO::getCreatetime).reversed());
            resultList.add(list.get(0));
        });
        return resultList;
    }

    @Override
    public List<AccuracyAssessDTO> findAccuracyDTO(String cityId, String caliberId, String algorithmId,
        Date startDate, Date endDate, String batchId, Integer day) throws Exception {
        //综合准确率&考核点准确率合并对象；
        List<AccuracyAssessDTO> mixList = new ArrayList<>();
        List<AccuracyAssessDO> srcAssessDOS = selectListInBatchAssessTime(cityId, caliberId, algorithmId, startDate,
            endDate, batchId);
        //按日前n天+最新的一条 清洗数据
        List<AccuracyAssessDO> assessDOS = filterAssessByBatch(srcAssessDOS, day);
        List<SettingAssessDO> settingAssessDOS = settingAssessDAO
            .selectListByStartEndValid(startDate, endDate, caliberId);
        List<SettingAssessDataDTO> assessDataList = settingAssessService
            .findAssessDataList(DateUtil.getYearByDate(endDate), caliberId);
        Map<String, SettingAssessDO> settingAssessDOMap = settingAssessDOS.stream()
            .collect(Collectors.toMap(SettingAssessDO::getAssessName, Function
                .identity(), (key1, key2) -> key2));
        List<SettingCompositeAccuracyDO> accuracyDOS = settingCompositeAccuracyDAO
            .selectListByStartEndValid(startDate, endDate, caliberId);
        Map<String, SettingCompositeAccuracyDO> settingCompositeMap = accuracyDOS.stream()
            .collect(Collectors.toMap(SettingCompositeAccuracyDO::getAccuracyName, Function
                .identity(), (key1, key2) -> key2));
        List<String> nameList = assessDOS.stream().map(AccuracyAssessDO::getAssessName)
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(assessDOS)) {
            return Collections.emptyList();
        }
        List<AccuracyCompositeDO> srcCompositeDOS = selectListInBatchCompositeTime(cityId, caliberId, algorithmId,
            startDate, endDate, batchId);
        //按批次+日前n天清洗数据
        List<AccuracyCompositeDO> compositeDOS = filterCompositeByBatch(srcCompositeDOS, day);

        Map<Date, List<AccuracyAssessDO>> assessMap = assessDOS.stream()
            .collect(Collectors.groupingBy(AccuracyAssessDO::getDate));
        Map<Date, List<AccuracyCompositeDO>> compositeMap = compositeDOS.stream()
            .collect(Collectors.groupingBy(AccuracyCompositeDO::getDate));
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        List<Integer> collect = assessDOS.stream().map(AccuracyAssessDO::getAssessType)
            .collect(Collectors.toList());
        Map<Date, BaseLoadIntervalDO> collectHis = new HashedMap<>();
        Map<Date, LoadCityFcBatchDO> collectFc = new HashedMap<>();
        //如果考核点结果里包含最大or最小负荷的，需要额外查询预测or实际负荷特性数据；按照考核点时间判定最大最小，因此从fc和his表里获取；
        if (collect.contains(AssessEnum.MAX.getType()) || collect.contains(AssessEnum.MIN.getType())) {
            List<BaseLoadIntervalDO> hisLoadSecond = loadCityHisService.selectListData(cityId, caliberId, startDate, endDate);
//            List<LoadCityHisDO> hisList = loadCityHisService
//                .getLoadCityHisDOS(cityId, caliberId, startDate, endDate);
//            List<LoadCityFcDO> fcList = loadCityFcService
//                .findFcByAlgorithmId(cityId, caliberId, algorithmId, startDate, endDate);
            //需求调整；预测批次数据也需要按照批次+日前n天对应筛选；否则和计算考核点&综合准确率使用的预测数据对不上；
            List<LoadCityFcBatchDO> fcListsrc = selectListInBatchForecastTime(cityId,caliberId, algorithmId, startDate, endDate, batchId);
            //按批次+日前n天清洗数据
            List<LoadCityFcBatchDO> fcList = filterForecastByBatch(fcListsrc, day);
            collectHis = hisLoadSecond.stream()
                .collect(Collectors.toMap(BaseLoadIntervalDO::getDate, Function
                    .identity(), (key1, key2) -> key2));
            collectFc = fcList.stream()
                .collect(Collectors.toMap(LoadCityFcBatchDO::getDate, Function
                    .identity(), (key1, key2) -> key2));
        }
        Map<Date, BaseLoadIntervalDO> finalCollectHis = collectHis;
        Map<Date, LoadCityFcBatchDO> finalCollectFc = collectFc;
        dateList.forEach(date -> {
            AccuracyAssessDTO mix = new AccuracyAssessDTO();
            mix.setDate(date);
            List<AccuracyAssessDO> assessDOList = assessMap.get(date);
            if (!CollectionUtils.isEmpty(assessDOList)) {
                List<AccuracyAssessUnitDTO> assessUnitList = new ArrayList<>();
                Map<String, AccuracyAssessDO> accuracyAssessMap = assessDOList.stream().collect(
                    Collectors.toMap(AccuracyAssessDO::getAssessName, Function.identity(), (key1, key2) -> key2));
                assessDataList.forEach(name -> {
                    AccuracyAssessDO assessDO = accuracyAssessMap.get(name.getAssessName());
                    SettingAssessDO settingAssessDO = settingAssessDOMap.get(assessDO.getAssessName());
                    //只装载可用状态的考核点数据
                    if (settingAssessDO != null) {
                        AccuracyAssessUnitDTO assessUnit = new AccuracyAssessUnitDTO();
                        assessUnit.setAssessName(assessDO.getAssessName());
                        assessUnit.setAssessAccuracy(assessDO.getAccuracy());
                        assessUnit.setType(assessDO.getAssessType());
                        assessUnit.setDate(settingAssessDO.getCreatetime());
                        //如果考核点配置的是最大or最小负荷，需要额外装载目标日的预测日最大（or最小）负荷以及实际日最大（or最小）负荷
                        if (AssessEnum.MAX.getType().equals(assessDO.getAssessType())) {
                            BaseLoadIntervalDO dayHisDO = finalCollectHis.get(date);
                            if (dayHisDO != null) {
                                List<BigDecimal> hisLoadList = dayHisDO.getHisLoadList();
                                BigDecimal max = getMaxOrMinFromTimeQuantumBySecondData(hisLoadList,
                                    settingAssessDO.getStartTime(), settingAssessDO.getEndTime(), AssessEnum.MAX);
                                assessUnit.setDailyExtremumHis(max.divide(new BigDecimal(10), 0, RoundingMode.HALF_UP));
                            }
                            LoadCityFcBatchDO dayFcDO = finalCollectFc.get(date);
                            if (dayFcDO != null) {
                                BigDecimal max = getMaxOrMinFromTimeQuantum(dayFcDO.getloadList(),
                                    settingAssessDO.getStartTime(), settingAssessDO.getEndTime(), AssessEnum.MAX);
                                assessUnit.setDailyExtremumFc(max.divide(new BigDecimal(10), 0, RoundingMode.HALF_UP));
                            }
                        }
                        if (AssessEnum.MIN.getType().equals(assessDO.getAssessType())) {
                            BaseLoadIntervalDO dayHisDO = finalCollectHis.get(date);
                            if (dayHisDO != null) {
                                List<BigDecimal> hisLoadList = dayHisDO.getHisLoadList();
                                BigDecimal min = getMaxOrMinFromTimeQuantumBySecondData(hisLoadList,
                                    settingAssessDO.getStartTime(), settingAssessDO.getEndTime(), AssessEnum.MIN);
                                assessUnit.setDailyExtremumHis(min.divide(new BigDecimal(10), 0, RoundingMode.HALF_UP));
                            }
                            LoadCityFcBatchDO dayFcDO = finalCollectFc.get(date);
                            if (dayFcDO != null) {
                                BigDecimal min = getMaxOrMinFromTimeQuantum(dayFcDO.getloadList(),
                                    settingAssessDO.getStartTime(), settingAssessDO.getEndTime(), AssessEnum.MIN);
                                assessUnit.setDailyExtremumFc(min.divide(new BigDecimal(10), 0, RoundingMode.HALF_UP));
                            }
                        }
                        //添加偏差；偏差=实际-预测
                        assessUnit.setDeviation(
                            BigDecimalUtils.sub(assessUnit.getDailyExtremumHis(), assessUnit.getDailyExtremumFc()));
                        assessUnitList.add(assessUnit);
                    }
                });
                mix.setAssessUnitList(assessUnitList);
            }
            //获取目标日的综合准确率数据
            List<AccuracyCompositeDO> compositeDOList = compositeMap.get(date);
            if (!CollectionUtils.isEmpty(compositeDOList)) {
                compositeDOList.sort(Comparator.comparing(AccuracyCompositeDO::getCreatetime));
                List<CompositeAccuracyUnitDTO> unitList = new ArrayList<>();
                compositeDOList.forEach(one -> {
                    //只装载可用状态的考核点数据
                    SettingCompositeAccuracyDO settingCompositeAccuracyDO = settingCompositeMap
                        .get(one.getAccuracyName());
                    if (settingCompositeAccuracyDO != null) {
                        CompositeAccuracyUnitDTO dto = new CompositeAccuracyUnitDTO();
                        BeanUtils.copyProperties(one, dto);
                        dto.setCreatetime(settingCompositeAccuracyDO.getCreatetime());
                        //把综合准确率名称放入名称筛选列表
                        nameList.add(one.getAccuracyName());
                        unitList.add(dto);
                    }
                });
                unitList.sort(Comparator.comparing(CompositeAccuracyUnitDTO::getCreatetime));
                mix.setCompositeAccuracyList(unitList);
            }
            mixList.add(mix);
        });
        return mixList;
    }

    @Override
    public List<AccuracyAssessDO> selectListInBatchAssessTime(String cityId, String caliberId, String algorithmId,
        Date startDate,
        Date endDate, String batchId) {
        List<AccuracyAssessDO> resultList = new ArrayList<>();
        //逻辑调整；传入的批次id对应批次设置的时间范围，查询时间范围内最新的一条数据；（数据表的批次id和此处的batchId不是一个概念；）
        List<AccuracyAssessDO> accuracyAssessDOS = this.accuracyAssessDAO
            .selectList(cityId, caliberId, algorithmId, startDate, endDate, null);
        SettingBatchInitDO batchById = settingBatchInitService.getBatchById(batchId);
        for (AccuracyAssessDO one : accuracyAssessDOS) {
            if (com.tsintergy.lf.core.util.DateUtil
                .isWithinTimeRange(one.getCreatetime(), batchById.getStartTime(), batchById.getEndTime())) {
                resultList.add(one);
            }
        }
        return resultList;
    }

    @Override
    public List<AccuracyAssessDO> selectListNewestInNameAndBatchTime(String cityId, String caliberId,
        String accuracyName, Date startDate, Date endDate, String batchId) {
        List<AccuracyAssessDO> resultList = new ArrayList<>();
        List<AccuracyAssessDO> srcList = this.accuracyAssessDAO
            .selectListByName(cityId, caliberId, accuracyName, startDate, endDate);
        SettingBatchInitDO batchById = settingBatchInitService.getBatchById(batchId);
        List<AccuracyAssessDO> collect = srcList.stream().filter(one -> com.tsintergy.lf.core.util.DateUtil
            .isWithinTimeRange(one.getCreatetime(), batchById.getStartTime(), batchById.getEndTime()))
            .collect(Collectors.toList());
        Map<String, List<AccuracyAssessDO>> map = collect.stream().collect(Collectors.groupingBy(
            src->src.getAlgorithmId()+"-"+src.getDate(), TreeMap::new, Collectors.toList())
        );
        //获取批次时间段内最新的一条数据
        map.forEach((key, list)->{
            list.sort(Comparator.comparing(AccuracyAssessDO::getCreatetime).reversed());
            resultList.add(list.get(0));
        });
        return resultList;
    }

    @Override
    public List<AccuracyAssessDO> selectListNewestInNameAndBatchData(String cityId, String caliberId,
        String accuracyName, Date startDate, Date endDate, String batchId) {
        List<AccuracyAssessDO> srcList = this.accuracyAssessDAO
            .selectListByName(cityId, caliberId, accuracyName, startDate, endDate);
        //根据批次id设置的时间范围过滤
        List<AccuracyAssessDO> filterList = new ArrayList<>();
        SettingBatchInitDO batchById = settingBatchInitService.getBatchById(batchId);
        for (AccuracyAssessDO one : srcList) {
            if (com.tsintergy.lf.core.util.DateUtil
                .isWithinTimeRange(one.getCreatetime(), batchById.getStartTime(), batchById.getEndTime())) {
                filterList.add(one);
            }
        }
        //取批次内最新的一条数据
        List<AccuracyAssessDO> collect = filterList.stream()
            .filter(src -> {
                Date anObject = DateUtils.addDays(src.getDate(), (-1));
                return DateUtil.getDateToStr(new Date(src.getCreatetime().getTime()))
                    .equals(DateUtil.getDateToStr(anObject));
            }).collect(Collectors.toList());
        Map<String, List<AccuracyAssessDO>> map = collect.stream().collect(Collectors.groupingBy(
            src -> src.getAssessName() + Constants.SEPARATOR_BROKEN_LINE + src.getDate()
                + Constants.SEPARATOR_BROKEN_LINE + src.getAlgorithmId() + Constants.SEPARATOR_BROKEN_LINE + src
                .getCityId() + Constants.SEPARATOR_BROKEN_LINE + src.getAssessName(), TreeMap::new, Collectors.toList())
        );

        List<AccuracyAssessDO> resultList = new ArrayList<>();
        map.forEach((key, list) -> {
            list.sort(Comparator.comparing(AccuracyAssessDO::getCreatetime).reversed());
            resultList.add(list.get(0));
        });
        return resultList;
    }

    @Override
    public List<AccuracyCompositeDO> selectListNewestInNameAndBatch(String cityId, String caliberId, String accuracyName,
        Date startDate, Date endDate, String batchId) {
        List<AccuracyCompositeDO> srcList = this.accuracyCompositeDAO
            .selectListByName(cityId, caliberId, accuracyName, startDate, endDate);
        //根据批次id设置的时间范围过滤
        List<AccuracyCompositeDO> filterList = new ArrayList<>();
        SettingBatchInitDO batchById = settingBatchInitService.getBatchById(batchId);
        for (AccuracyCompositeDO one : srcList) {
            if (com.tsintergy.lf.core.util.DateUtil
                .isWithinTimeRange(one.getCreatetime(), batchById.getStartTime(), batchById.getEndTime())) {
                filterList.add(one);
            }
        }
        //取批次内最新的一条数据
        List<AccuracyCompositeDO> collect = filterList.stream()
            .filter(src -> {
                Date anObject = DateUtils.addDays(src.getDate(), (-1));
                return DateUtil.getDateToStr(new Date(src.getCreatetime().getTime()))
                    .equals(DateUtil.getDateToStr(anObject));
            }).collect(Collectors.toList());
        Map<String, List<AccuracyCompositeDO>> map = collect.stream().collect(Collectors.groupingBy(
            src -> src.getAccuracyName() + Constants.SEPARATOR_BROKEN_LINE + src.getDate()
                + Constants.SEPARATOR_BROKEN_LINE + src.getAlgorithmId() + Constants.SEPARATOR_BROKEN_LINE + src
                .getCityId() + Constants.SEPARATOR_BROKEN_LINE + src.getAccuracyName(), TreeMap::new, Collectors.toList())
        );

        List<AccuracyCompositeDO> resultList = new ArrayList<>();
        map.forEach((key, list) -> {
            list.sort(Comparator.comparing(AccuracyCompositeDO::getCreatetime).reversed());
            resultList.add(list.get(0));
        });
        return resultList;
    }

    @Override
    public List<AccuracyCompositeDO> selectCompositeNewestInNameAndBatchTime(String cityId, String caliberId,
        String accuracyName, Date startDate, Date endDate, String batchId) {
        List<AccuracyCompositeDO> resultList = new ArrayList<>();
        //逻辑调整；传入的批次id对应批次设置的时间范围，查询时间范围内最新的一条数据；（数据表的批次id和此处的batchId不是一个概念；）
        List<AccuracyCompositeDO> srcList = this.accuracyCompositeDAO
            .selectListByName(cityId, caliberId, accuracyName, startDate, endDate);
        SettingBatchInitDO batchById = settingBatchInitService.getBatchById(batchId);
        List<AccuracyCompositeDO> collect = srcList.stream().filter(one -> com.tsintergy.lf.core.util.DateUtil
            .isWithinTimeRange(one.getCreatetime(), batchById.getStartTime(), batchById.getEndTime()))
            .collect(Collectors.toList());
        Map<String, List<AccuracyCompositeDO>> map = collect.stream().collect(Collectors.groupingBy(
            src->src.getAlgorithmId()+"-"+src.getDate(), TreeMap::new, Collectors.toList())
        );
        //获取批次时间段内最新的一条数据
        map.forEach((key, list)->{
            list.sort(Comparator.comparing(AccuracyCompositeDO::getCreatetime).reversed());
            resultList.add(list.get(0));
        });
        return resultList;
    }

    public List<AccuracyCompositeDO> selectListInBatchCompositeTime(String cityId, String caliberId, String algorithmId,
        Date startDate,
        Date endDate, String batchId) {
        List<AccuracyCompositeDO> resultList = new ArrayList<>();
        //逻辑调整；传入的批次id对应批次设置的时间范围，查询时间范围内最新的一条数据；（数据表的批次id和此处的batchId不是一个概念；）
        List<AccuracyCompositeDO> accuracyCompositeDOS = this.accuracyCompositeDAO
                .selectListByAlgorithmId(cityId, caliberId, Collections.singletonList(algorithmId), startDate, endDate, null);
        SettingBatchInitDO batchById = settingBatchInitService.getBatchById(batchId);
        for (AccuracyCompositeDO one : accuracyCompositeDOS) {
            if (com.tsintergy.lf.core.util.DateUtil
                .isWithinTimeRange(one.getCreatetime(), batchById.getStartTime(), batchById.getEndTime())) {
                resultList.add(one);
            }
        }
        return resultList;
    }

    public List<LoadCityFcBatchDO> selectListInBatchForecastTime(String cityId, String caliberId, String algorithmId,
        Date startDate,
        Date endDate, String batchId) throws Exception {
        List<LoadCityFcBatchDO> resultList = new ArrayList<>();
        //逻辑调整；传入的批次id对应批次设置的时间范围，查询时间范围内最新的一条数据；（数据表的批次id和此处的batchId不是一个概念；）
        List<LoadCityFcBatchDO> fcList = loadCityFcBatchService
            .findByConditionByBatchId(cityId, startDate, endDate, caliberId, algorithmId, null);
        SettingBatchInitDO batchById = settingBatchInitService.getBatchById(batchId);
        for (LoadCityFcBatchDO one : fcList) {
            if (com.tsintergy.lf.core.util.DateUtil
                .isWithinTimeRange(one.getCreatetime(), batchById.getStartTime(), batchById.getEndTime())) {
                resultList.add(one);
            }
        }
        return resultList;
    }


    @Override
    public List<String> findNameList(String cityId, String caliberId, String algorithmId,
        Date startDate, Date endDate) {
        //查询可用状态的考核点名称
        List<SettingAssessDO> settingAssessDOS = settingAssessDAO
            .selectListByStartEndValid(startDate, endDate, caliberId);
        settingAssessDOS.sort(Comparator.comparing(src -> src.getSort() == null ? 1 : src.getSort()));
        List<String> assessNameList = settingAssessDOS.stream().map(SettingAssessDO::getAssessName)
            .collect(Collectors.toList());
        //查询可用状态的国调考核名称
        List<SettingCompositeAccuracyDO> accuracyDOS = settingCompositeAccuracyDAO
            .selectListByStartEndValid(startDate, endDate, caliberId);
        List<String> compositeNameList = accuracyDOS.stream().map(SettingCompositeAccuracyDO::getAccuracyName)
            .collect(Collectors.toList());
        compositeNameList.addAll(assessNameList);
        return compositeNameList.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 计算回溯预测考核点准确率
     * <AUTHOR>
     * @param cityId: 城市ID
     * @param caliberId: 口径ID
     * @param algorithmId: 算法ID
     * @param startDate: 开始时间
     * @param endDate: 结束时间
     * @Return void
     * @Since version
     */
    @Override
    public void doCalculateRecallAssessAccuracy(String cityId, String caliberId, String algorithmId, Date startDate,
                                                Date endDate) throws Exception {
        Map<String, List<SettingAssessDO>> assessSettingByData = settingAssessService
                .findAssessSettingByData(startDate, endDate, caliberId);
        if (assessSettingByData == null) {
            return;
        }
        //极值统一从逐5秒负荷表里读取后计算；
        List hisLoadSecond = loadCityHisService.selectListData(cityId, caliberId, startDate, endDate);
        List<LoadCityFcRecallDO> fcRecallList = loadCityFcRecallService
                .getLoadCityFc(cityId, caliberId, startDate, endDate, algorithmId);
        Map<String, List<LoadCityFcRecallDO>> collectFcRecall = fcRecallList.stream().collect(
                Collectors.groupingBy(x -> x.getCityId() + Constants.SEPARATOR_PUNCTUATION + x.getCaliberId()
                        + Constants.SEPARATOR_PUNCTUATION + DateUtil.getDateToStr(x.getDate())));
        if (CollectionUtil.isEmpty(collectFcRecall)){
            return;
        }
        List<StatisticsCityDayFcRecallDO> statisticsCityDayFcDOs = statisticsCityDayFcRecallDAO
                .getStatisticsCityDayFcDOs(cityId, caliberId, algorithmId, startDate, endDate, null);
        List<AccuracyAssessDO> statisticsList = new ArrayList<>();
        hisLoadSecond.forEach( x -> {
            BaseLoadIntervalDO src = (BaseLoadIntervalDO) x;
            String monthDate = DateUtil.getMonthByDate(src.getDate());
            List<LoadCityFcRecallDO> fcData = collectFcRecall
                    .get(src.getCityId() + Constants.SEPARATOR_PUNCTUATION + src.getCaliberId()
                            + Constants.SEPARATOR_PUNCTUATION + DateUtil.getDateToStr(src.getDate()));
            if (!CollectionUtils.isEmpty(fcData)) {
                fcData.forEach(fcRecall -> {
                    Optional<StatisticsCityDayFcRecallDO> statisticsCityDayFcRecallOptional = statisticsCityDayFcDOs.stream().filter(
                            sta -> sta.getDate().equals(fcRecall.getDate()) && sta.getCityId().equals(fcRecall.getCityId())
                                    && sta.getCaliberId().equals(fcRecall.getCaliberId()) && sta.getAlgorithmId().equals(fcRecall.getAlgorithmId())
                    ).findFirst();
                    List<AccuracyAssessDO> statistics = statistics(src, fcRecall,
                            assessSettingByData.get(monthDate), statisticsCityDayFcRecallOptional.orElse(new StatisticsCityDayFcRecallDO()));
                    statisticsList.addAll(statistics);
                });
            }
        });
        List<AccuracyAssessRecallDO> toSaveList = JSONArray
                .parseArray(JsonObject.toJSONString(statisticsList), AccuracyAssessRecallDO.class);
        toSaveList.forEach(one -> {
            try {
                one.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                accuracyAssessRecallDAO.saveOrUpdateEntityByTemplate(one);
            } catch (DAOException e) {
                e.printStackTrace();
            }
        });
    }


    @Override
    public List<AccuracyDistributionDTO> findAccuracyDistribution(String cityId, String caliberId, String algorithmId,
        String accuracyName, Date startDate, Date endDate, String batchIds, Integer day) throws Exception {
        List<AccuracyAssessDTO> assessDTOS = this
            .findAccuracyDTO(cityId, caliberId, algorithmId, startDate, endDate, batchIds, day);
        if (CollectionUtils.isEmpty(assessDTOS)) {
            return null;
        }
        List<AccuracyAssessUnitDTO> assessUnitList = new ArrayList<>();
        assessDTOS.forEach(assess -> {
            List<AccuracyAssessUnitDTO> list = assess.getAssessUnitList();
            if (list != null) {
                assessUnitList.addAll(list);
            }
        });

        List<AccuracyDistributionDTO> accuracyDistributionDTOS = new ArrayList<>();
        //过滤后的准确率集合
        List<BigDecimal> collect = null;
        List<AccuracyAssessUnitDTO> assessDTOList;
        //名称筛选考核点准确率列表
        assessDTOList = assessUnitList.stream().filter(t -> t.getAssessName().equals(accuracyName))
            .collect(Collectors.toList());
        //考核点准确率列表没有时，进一步筛选综合准确率列表；
        if (CollectionUtils.isEmpty(assessDTOList)) {
            List<CompositeAccuracyUnitDTO> lists = new ArrayList<>();
            assessDTOS.forEach(one -> {
                List<CompositeAccuracyUnitDTO> compositeAccuracyList = one.getCompositeAccuracyList();
                if (!CollectionUtils.isEmpty(compositeAccuracyList)) {
                    lists.addAll(compositeAccuracyList);
                }
            });
            List<CompositeAccuracyUnitDTO> unitDTOS = lists.stream()
                .filter(t -> t.getAccuracyName().equals(accuracyName))
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(unitDTOS)) {
                return null;
            } else {
                collect = unitDTOS.stream().map(CompositeAccuracyUnitDTO::getAccuracy)
                    .collect(Collectors.toList());
            }
        } else {
            collect = assessDTOList.stream().map(AccuracyAssessUnitDTO::getAssessAccuracy)
                .collect(Collectors.toList());
        }

        List<AccuracyDistributionDTO> accuracyDistributionDTOList = getAccuracyDistributionDTOList(collect);
        accuracyDistributionDTOS.addAll(accuracyDistributionDTOList);
        return accuracyDistributionDTOS.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 查询回溯预测考核点准确率
     * <AUTHOR>
     * @param cityId:
     * @param caliberId:
     * @param algorithmId:
     * @param startDate:
     * @param endDate:
     * @Return java.util.List<com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyAssessRecallDO>
     * @Since version
     */
    @Override
    public List<AccuracyAssessRecallDO> findAccuracyRecallList(String cityId, String caliberId, String algorithmId
            , Date startDate, Date endDate) {
        return accuracyAssessRecallDAO.selectList(cityId, caliberId, algorithmId, startDate, endDate);
    }

    /**
     * 查询回溯预测考核点准确率-批次预测考核点准确率-提升
     * <AUTHOR>
     * @param cityId:
     * @param caliberId:
     * @param algorithmId:
     * @param startDate:
     * @param endDate:
     * @param batchId:
     * @Return java.util.List<com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyAssessDTO>
     * @Since version
     */
    @Override
    public List<AccuracyAssessDTO> findAccuracyAccessRecallList(String cityId, String caliberId, String algorithmId
            , Date startDate, Date endDate, String batchId, Integer day) {
        List<AccuracyAssessDTO> accuracyAssessDTOS = new ArrayList<>();
        //1.1） 预测综合准确率
        List<AccuracyCompositeDO> srcCompositeDOS = selectListInBatchCompositeTime(cityId, caliberId, algorithmId,
                startDate, endDate, batchId);
        //按批次+日前n天清洗数据
        List<AccuracyCompositeDO> accuracyCompositeDOS = filterCompositeByBatch(srcCompositeDOS, day);
        // 1.2） 预测回溯综合准确率
        List<AccuracyCompositeReCallDO> accuracyCompositeReCallDOS = accuracyCompositeRecallDAO.selectListByAlgorithmId(
                cityId, caliberId, algorithmId, startDate, endDate);
        // 1.3） 计算综合准确率提升
        processAccuracyComposite(accuracyAssessDTOS, accuracyCompositeDOS, accuracyCompositeReCallDOS);
        //2.1) 查询预测回溯考核点准确率
        List<AccuracyAssessRecallDO> accuracyAssessRecallDOList = accuracyAssessRecallDAO
                .selectList(cityId, caliberId, algorithmId, startDate, endDate);
        //2.2） 查询预测考核点准确率
        List<AccuracyAssessDO> srcAssessDOS = selectListInBatchAssessTime(cityId, caliberId, algorithmId, startDate,
                endDate, batchId);
        //按日前n天+最新的一条 清洗数据
        List<AccuracyAssessDO> accuracyAssessDOS = filterAssessByBatch(srcAssessDOS, day);
//        List<AccuracyAssessDO> accuracyAssessDOS = accuracyAssessDAO
//                .selectList(cityId, caliberId, algorithmId, startDate, endDate, batchId);
        //2.3） 计算考核点准确率提升
        processAccuracyAssess(accuracyAssessDTOS, accuracyAssessRecallDOList, accuracyAssessDOS);
        List<AccuracyAssessDTO> collect = accuracyAssessDTOS.stream()
                .filter(x -> CollectionUtil.isNotEmpty(x.getAssessUnitList()))
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(AccuracyAssessDTO::getDate, src -> src,
                                this::mergeAccuracyAssessDTOs),
                        map -> new ArrayList<>(map.values())));

        return collect;
    }

    /**
     * 回溯预测考核点准确率分析
     * <AUTHOR>
     * @param cityId:
     * @param caliberId:
     * @param startDate:
     * @param endDate:
     * @param algorithmId:
     * @param batchId:
     * @param accuracyName:
     * @Return com.tsintergy.lf.serviceapi.base.recall.dto.StatisticsAccuracyDTO
     * @Since version
     */
    @Override
    public StatisticsAccuracyDTO getStatisticsAccuracyAccessRecall(String cityId, String caliberId, Date startDate,
                                                                   Date endDate, String algorithmId, String batchId,
                                                                   String accuracyName, Integer day) {
        StatisticsAccuracyDTO statisticsAccuracyDTO = new StatisticsAccuracyDTO();
        List<AccuracyAssessDTO> accuracyAccessRecallList = this.findAccuracyAccessRecallList(cityId, caliberId,
                algorithmId, startDate, endDate, batchId, day);
        if (CollectionUtil.isEmpty(accuracyAccessRecallList)){
            return null;
        }
        List<AccuracyAssessUnitDTO> accuracyAssessUnitDTOList = accuracyAccessRecallList.stream()
                .filter(x -> CollectionUtil.isNotEmpty(x.getAssessUnitList())) // 过滤掉null值
                .flatMap(x -> x.getAssessUnitList().stream())
                .collect(Collectors.toList());
        List<BigDecimal> accuracyFcList = accuracyAssessUnitDTOList.stream()
                .filter(x -> x.getAssessAccuracy() != null && accuracyName.equals(x.getAssessName())) // 过滤掉null值
                .map(AccuracyAssessUnitDTO::getAssessAccuracy)
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(accuracyFcList)) {
            BigDecimal accuracyFc = BigDecimalUtils.avgList(accuracyFcList, 4, false);
            if (accuracyFc != null) {
                statisticsAccuracyDTO.setFcAccuracy(accuracyFc);
            }
        }
        List<BigDecimal> accuracyRecallList = accuracyAssessUnitDTOList.stream()
                .filter(x -> x.getAssessAccuracyRecall() != null && accuracyName.equals(x.getAssessName())) // 过滤掉null值
                .map(AccuracyAssessUnitDTO::getAssessAccuracyRecall)
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(accuracyRecallList)) {
            BigDecimal accuracyRecall = BigDecimalUtils.avgList(accuracyRecallList, 4, false);
            if (accuracyRecall != null) {
                statisticsAccuracyDTO.setRecallAccuracy(accuracyRecall);
            }
        }

        int areaLhZeroCount = 0;
        int areaZeroToOneCount = 0;
        int areaGhOneCount = 0;
        int notEqualsNullNum = 0;
        ArrayList<BigDecimal> recallDeviationList = accuracyAssessUnitDTOList.stream()
                .filter(x -> x.getRecallDeviation() != null && accuracyName.equals(x.getAssessName()))
                .map(AccuracyAssessUnitDTO::getRecallDeviation)
                .collect(Collectors.toCollection(ArrayList::new));
        for (BigDecimal recallDeviation : recallDeviationList) {
            if (recallDeviation != null) {
                notEqualsNullNum++;
                if (recallDeviation.compareTo(new BigDecimal("0")) == -1) {
                    areaLhZeroCount++;
                } else if (recallDeviation.compareTo(new BigDecimal("0.01")) == 1) {
                    areaGhOneCount++;
                } else {
                    areaZeroToOneCount++;
                }
            }
        }

        if (notEqualsNullNum == 0) {
            areaLhZeroCount = 0;
            areaZeroToOneCount = 0;
            areaGhOneCount = 0;
            notEqualsNullNum = 1;
        }
        BigDecimal area1 = BigDecimal.valueOf((int) areaLhZeroCount).divide(BigDecimal.valueOf((int) notEqualsNullNum), 2, BigDecimal.ROUND_DOWN);
        BigDecimal area2 = BigDecimal.valueOf((int) areaZeroToOneCount).divide(BigDecimal.valueOf((int) notEqualsNullNum), 2, BigDecimal.ROUND_DOWN);
        BigDecimal area3 = BigDecimal.valueOf((int) areaGhOneCount).divide(BigDecimal.valueOf((int) notEqualsNullNum), 2, BigDecimal.ROUND_DOWN);
        if (area1 != null) {
            statisticsAccuracyDTO.setArea1(area1);
        }
        if (area2 != null) {
            statisticsAccuracyDTO.setArea2(area2);
        }
        if (area3 != null) {
            statisticsAccuracyDTO.setArea3(area3);
        }
        return statisticsAccuracyDTO;
    }

    /**
     * 统计一天预测结果情况
     */
    public <T extends BaseLoadIntervalDO> List<AccuracyAssessDO> statistics(T intervalDO, BaseLoadFcCityDO fc,
                            List<SettingAssessDO> settingAssessList, BaseStatisticsCityDayFcDO statisticsCityDayFcDO) {
        if (CollectionUtils.isEmpty(settingAssessList)) {
            return null;
        }
        List<AccuracyAssessDO> resultList = new ArrayList<>();
        settingAssessList.forEach(src -> {
            AccuracyAssessDO result = new AccuracyAssessDO();
            BeanUtils.copyProperties(fc, result, "id");
            result.setAssessName(src.getAssessName());
            result.setAssessType(src.getType());
            result.setCreatetime(fc.getCreatetime());
            result.setAssessId(src.getId());
            try {
                result.setAccuracy(statisticsAccuracy(src, fc, intervalDO, statisticsCityDayFcDO));
            } catch (Exception e) {
                e.printStackTrace();
            }
            resultList.add(result);
        });
        return resultList;
    }

    /**
     * 获取 时间段内的最大值 or最小值
     *
     * @param dataList 数据列表
     * @param startTime 开始时间 00:15
     * @param endTime 结束时间 00:15
     * @return >=开始时间  <=结束时间范围的极值；
     */
    private BigDecimal getMaxOrMinFromTimeQuantum(List<BigDecimal> dataList, String startTime, String endTime,
        AssessEnum type) {
        List<String> columns = ColumnUtil.getColumns(96,
            Constants.LOAD_CURVE_START_WITH_ZERO, false);
        startTime = startTime.replace(":", "");
        endTime = endTime.replace(":", "");
        List<BigDecimal> decimalsHis = dataList.subList(columns.indexOf(startTime), columns.indexOf(endTime) + 1);
        if (AssessEnum.MAX.equals(type)) {
            return decimalsHis.stream().filter(Objects::nonNull).max(BigDecimal::compareTo)
                .orElse(new BigDecimal(0));
        } else {
            return decimalsHis.stream().filter(Objects::nonNull).min(BigDecimal::compareTo)
                .orElse(new BigDecimal(0));
        }
    }

    private BigDecimal getMaxOrMinFromTimeQuantumBySecondData(List<BigDecimal> dataList, String startTime,
        String endTime,
        AssessEnum type) {
        //采集的数据是从0开始的
        List<String> allTimeListBySecond = com.tsintergy.lf.core.util.DateUtil.getAllTimeListByInterval(2, 15, false);
        Integer assessType = type.getType();
        List<BigDecimal> decimalsHis = null;
        try {
            //配置的时间段可能会有2400，这里特殊处理一下
            decimalsHis = dataList.subList(allTimeListBySecond.indexOf(startTime + ":00"),
                    "24:00".equals(endTime) ? allTimeListBySecond.size() : allTimeListBySecond.indexOf(endTime + ":00") + 1);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (decimalsHis == null) {
            return BigDecimal.ZERO;
        }
        if (AssessEnum.MAX.equals(type)) {
            return decimalsHis.stream().filter(Objects::nonNull).max(BigDecimal::compareTo)
                    .orElse(new BigDecimal(0));
        } else {
            return decimalsHis.stream().filter(Objects::nonNull).min(BigDecimal::compareTo)
                    .orElse(new BigDecimal(0));
        }
    }

    private <T extends BaseLoadIntervalDO> BigDecimal statisticsAccuracy(SettingAssessDO assess,
                                                                         BaseLoadFcCityDO fcBatchDO, T intervalDO,
                                                                         BaseStatisticsCityDayFcDO statisticsCityDayFcDO) throws Exception {
        List<BigDecimal> fcLoad = BasePeriodUtils.toList(fcBatchDO, Constants.LOAD_CURVE_POINT_NUM,Constants.LOAD_CURVE_START_WITH_ZERO);;
        List<String> columns = ColumnUtil.getColumns(96,
            Constants.LOAD_CURVE_START_WITH_ZERO, false);
        List<String> allTimeListBySecond = intervalDO.getAllTimeList();
        Integer assessType = assess.getType();
        String startTime = assess.getStartTime().replace(":", "");
        String endTime = assess.getEndTime().replace(":", "");
        List<BigDecimal> hisLoadList = intervalDO.getHisLoadList();
        List<BigDecimal> decimalsHis = null;
        List<BigDecimal> decimalsFc = null;
        try {
            if ("2400".equals(endTime)) {
                decimalsHis = hisLoadList
                    .subList(allTimeListBySecond.indexOf(assess.getStartTime() + ":00"),
                        hisLoadList.size());
                decimalsFc = fcLoad.subList(columns.indexOf(startTime), fcLoad.size());
            } else {
                decimalsHis = hisLoadList
                    .subList(allTimeListBySecond.indexOf(assess.getStartTime() + ":00"),
                        Math.min(hisLoadList.size(), allTimeListBySecond.indexOf(assess.getEndTime() + ":00") + 1));
                decimalsFc = fcLoad.subList(columns.indexOf(startTime), columns.indexOf(endTime) + 1);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        //最大值准确率
        if (AssessEnum.MAX.getType().equals(assessType)) {
            BigDecimal maxHis;
            maxHis = decimalsHis.stream().filter(Objects::nonNull).max(BigDecimal::compareTo)
                .orElse(new BigDecimal(0));
            BigDecimal maxFc = decimalsFc.stream().filter(Objects::nonNull).max(BigDecimal::compareTo)
                .orElse(new BigDecimal(0));
            return getDailyLoadAccuracy(maxHis, maxFc);
        }//最小值准确率
        else if (AssessEnum.MIN.getType().equals(assessType)) {
            BigDecimal minHis;
            minHis = decimalsHis.stream().filter(Objects::nonNull).min(BigDecimal::compareTo)
                .orElse(new BigDecimal(0));
            BigDecimal minFc = decimalsFc.stream().filter(Objects::nonNull).min(BigDecimal::compareTo)
                .orElse(new BigDecimal(0));
            return getDailyLoadAccuracy(minHis, minFc);
        } //平均值准确率
        else if (AssessEnum.AVG.getType().equals(assessType)) {
            //20240624客户需求：考核点设置为全天时段，且考核类型【平均值】时，从批次准确率计算好的准确率获取（根本区别为该计算方式基于96点负荷）
            if (decimalsFc.size() == 96) {
                return statisticsCityDayFcDO.getAccuracy();
            } else {
                List<BigDecimal> dataList = getLoadAccuracyBySecondHisAndFc96(intervalDO,
                    fcLoad, startTime, endTime);
                //每个点的准确率求平均；
                return dataList.stream().map(f -> f == null ? BigDecimal.ZERO : f)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .divide(BigDecimal.valueOf(dataList.size()), 4, BigDecimal.ROUND_HALF_UP);
            }

        }
        return null;
    }

    /**
     * 根据逐5s历史数据和96点预测数据计算平均准确率；会根据15分钟时刻筛选出5s数据中的对应时刻
     */
    public List<BigDecimal> getLoadAccuracyBySecondHisAndFc96(BaseLoadIntervalDO intervalDO,
        List<BigDecimal> fcListSrc, String startTime, String endTime) {
        String str = intervalDO.getAllTimeList().get(0);
        boolean secondFlag = true;
        if (str.length() < 6) {
            secondFlag = false;
        }
        List<String> allTimeListBySecond = intervalDO.getAllTimeList();
        List<String> allTimeListByMinSrc = ColumnUtil.getColumns(96,
            Constants.LOAD_CURVE_START_WITH_ZERO, false);
        List<String> allTimeListByMin = allTimeListByMinSrc
            .subList(allTimeListByMinSrc.indexOf(startTime), allTimeListByMinSrc.indexOf(endTime) + 1);
        List<BigDecimal> fcList = fcListSrc
            .subList(allTimeListByMinSrc.indexOf(startTime), allTimeListByMinSrc.indexOf(endTime) + 1);
        Map<String, BigDecimal> hisMapBySecond = new HashedMap<>();
        Map<String, BigDecimal> fc96 = new HashedMap<>();
        List<BigDecimal> hisListByInterval = intervalDO.getHisLoadList();
        for (int i = 0; i < Math.min(allTimeListBySecond.size(), hisListByInterval.size()); i++) {
            hisMapBySecond.put(allTimeListBySecond.get(i), hisListByInterval.get(i));
        }
        for (int i = 0; i < allTimeListByMin.size(); i++) {
            fc96.put(allTimeListByMin.get(i), fcList.get(i));
        }
        Map<String, BigDecimal> result = new HashedMap<>();
        for (Map.Entry<String, BigDecimal> entry : fc96.entrySet()) {
            String key =
                entry.getKey().substring(0, 2) + ":" + entry.getKey().substring(2, 4) + (secondFlag ? ":00" : "");
            BigDecimal bigDecimaBySecond = hisMapBySecond.get(key);
            if (bigDecimaBySecond != null) {
                result.put(entry.getKey(), getDailyLoadAccuracy(bigDecimaBySecond, entry.getValue()));
            }
        }
        List<BigDecimal> list = new ArrayList<>(result.values());
        return list;
    }


    /**
     * 判断极值点时间是否属于某个时间段；
     *
     * @param startTime 开始时间 00:15
     * @param endTime 结束时间 00:15
     * @param extremumTime 极值点出现时间；可能不是整点时刻，例如：01:16:05  注意：我方计算数据精度到分钟，采集的特性数据到秒；这里为了兼容旧版本，判断一下时分秒长度，是hh:mm还是hh:mm:ss
     */
    private boolean checkExtremumTimeEncircle(String startTime, String endTime, String extremumTime) {
        String day = DateUtil.formateDate(new Date());
        Date startDateTime = DateUtils.string2Date(day + " " + startTime + ":00", DateFormatType.DATE_FORMAT_STR);
        Date endDateTime = DateUtils.string2Date(day + " " + endTime + ":00", DateFormatType.DATE_FORMAT_STR);
        Date check;
        //hh:mm:ss
        if (extremumTime.split(":").length > 2) {
            check = DateUtils.string2Date(day + " " + extremumTime, DateFormatType.DATE_FORMAT_STR);
        } else {
            check = DateUtils.string2Date(day + " " + extremumTime + ":00", DateFormatType.DATE_FORMAT_STR);
        }
        if (check.after(startDateTime) && check.before(endDateTime)) {
            return true;
        }
        return false;
    }

    /**
     * @param his 实际
     * @param fc 预测
     */
    private BigDecimal getDailyLoadAccuracy(BigDecimal his, BigDecimal fc) {
        if (fc == null || his == null) {
            return null;
        }
        //做差
        BigDecimal subtract = his.subtract(fc);
        //求绝对值
        //相除后求绝对值
        BigDecimal divide = (subtract.divide(his, 4, BigDecimal.ROUND_HALF_UP)).abs();
        BigDecimal result = BigDecimal.ONE.subtract(divide.abs());
        return result.abs();
    }

    private List<AccuracyDistributionDTO> getAccuracyDistributionDTOList(List<BigDecimal> list) {
        List<AccuracyDistributionDTO> accuracyDistributionDTOS = new ArrayList<>();
        for (AccuracyRange value : AccuracyRange.values()) {
            AccuracyDistributionDTO accuracyDistributionDTO;
            if (("80").equals(value.getMin())) {
                accuracyDistributionDTO = getAccuracyDistributionDTO(list,
                    new BigDecimal(value.max).divide(HUNDRED, 2, BigDecimal.ROUND_HALF_UP),
                    new BigDecimal(value.min).divide(HUNDRED, 2, BigDecimal.ROUND_HALF_UP), false, false);
            } else if (("80").equals(value.getMax())) {
                accuracyDistributionDTO = getAccuracyDistributionDTO(list,
                    new BigDecimal(value.max).divide(HUNDRED, 2, BigDecimal.ROUND_HALF_UP),
                    new BigDecimal(value.min).divide(HUNDRED, 2, BigDecimal.ROUND_HALF_UP), false, true);
            } else {
                accuracyDistributionDTO = getAccuracyDistributionDTO(list,
                    new BigDecimal(value.max).divide(HUNDRED, 2, BigDecimal.ROUND_HALF_UP),
                    new BigDecimal(value.min).divide(HUNDRED, 2, BigDecimal.ROUND_HALF_UP), true, false);
            }
            accuracyDistributionDTOS.add(accuracyDistributionDTO);
        }
        return accuracyDistributionDTOS;
    }

    /**
     * 获取准确率分布对象数据
     *
     * @param list 准确率集合
     * @param max 上限
     * @param min 下限
     * @param minIsEq 前闭后开
     * @param maxIsEq 前开后闭
     */
    private AccuracyDistributionDTO getAccuracyDistributionDTO(List<BigDecimal> list, BigDecimal max, BigDecimal min,
        boolean minIsEq, boolean maxIsEq) {
        AccuracyDistributionDTO accuracyDistributionDTO = new AccuracyDistributionDTO();
        int length = 0;
        StringBuffer range = new StringBuffer();
        if (minIsEq) {
            length = list.stream().filter(t -> t.compareTo(min) == 1 && t.compareTo(max) == -1 || t.compareTo(min) == 0)
                .toArray().length;
            range.append("[" + min.multiply(HUNDRED) + "%," + max.multiply(HUNDRED) + "%)");
        } else if (maxIsEq) {
            length = list.stream().filter(t -> t.compareTo(min) == 1 && t.compareTo(max) == -1 || t.compareTo(max) == 0)
                .toArray().length;
            range.append("(" + min.multiply(HUNDRED) + "%," + max.multiply(HUNDRED) + "%]");
        } else {
            length = list.stream().filter(t -> t.compareTo(min) == 1 && t.compareTo(max) == -1).toArray().length;
            range.append("(" + min.multiply(HUNDRED) + "%," + max.multiply(HUNDRED) + "%)");
        }
        accuracyDistributionDTO.setAccuracyRange(range.toString());
        accuracyDistributionDTO.setStatisticalValue(length);
        accuracyDistributionDTO.setProportion(
            new BigDecimal(length).multiply(HUNDRED).divide(new BigDecimal(list.size()), 2, BigDecimal.ROUND_HALF_UP)
                .setScale(2) + "");
        return accuracyDistributionDTO;
    }

    private void processAccuracyComposite(List<AccuracyAssessDTO> accuracyAssessDTOS, List<AccuracyCompositeDO> accuracyCompositeDOS, List<AccuracyCompositeReCallDO> accuracyCompositeReCallDOS) {
        if (CollectionUtil.isNotEmpty(accuracyCompositeDOS) && CollectionUtil.isNotEmpty(accuracyCompositeReCallDOS)) {
            Map<String, List<AccuracyCompositeReCallDO>> compositeRecallMap = accuracyCompositeReCallDOS.stream()
                    .filter(x -> x.getAccuracy() != null).collect(
                            Collectors.groupingBy( x -> DateUtil.getDateToStr(x.getDate())));
            Map<String, AccuracyCompositeDO> compositeMap = accuracyCompositeDOS.stream().filter(x -> x.getAccuracy() != null).collect(
                    Collectors.toMap(x -> DateUtil.getDateToStr(x.getDate()) +
                                    Constants.SEPARATOR_PUNCTUATION + x.getAccuracyName() +
                                    Constants.SEPARATOR_PUNCTUATION + x.getAccuracyId(),
                            Function.identity(), (oldValue, newValue) -> oldValue));
            for (String key : compositeRecallMap.keySet()) {
                List<AccuracyCompositeReCallDO> accuracyCompositeReCallDOList = compositeRecallMap.get(key);
                if (CollectionUtil.isEmpty(accuracyCompositeReCallDOList)) {
                    continue;
                }
                AccuracyAssessDTO accuracyAssessDTO = new AccuracyAssessDTO();
                List<AccuracyAssessUnitDTO> assessUnitList = new ArrayList<>();
                for (AccuracyCompositeReCallDO recallDO : accuracyCompositeReCallDOList) {
                    String accuracyKey = DateUtil.getDateToStr(recallDO.getDate()) +
                            Constants.SEPARATOR_PUNCTUATION + recallDO.getAccuracyName()+
                            Constants.SEPARATOR_PUNCTUATION + recallDO.getAccuracyId();
                    if (compositeMap.containsKey(accuracyKey)) {
                        AccuracyCompositeDO accuracyDO = compositeMap.get(accuracyKey);
                        AccuracyAssessUnitDTO assessUnitDTO = new AccuracyAssessUnitDTO();
                        BigDecimal accuracy = recallDO.getAccuracy();
                        assessUnitDTO.setAssessAccuracy(accuracyDO.getAccuracy());
                        assessUnitDTO.setAssessAccuracyRecall(accuracy);
                        assessUnitDTO.setAssessName(accuracyDO.getAccuracyName());
                        assessUnitDTO.setDate(accuracyDO.getDate());
                        assessUnitDTO.setType(-1);
                        assessUnitDTO.setRecallDeviation(accuracy.subtract(accuracyDO.getAccuracy()));
                        assessUnitDTO.setSortNo(-1);
                        assessUnitList.add(assessUnitDTO);
                    }
                }
                accuracyAssessDTO.setAssessUnitList(assessUnitList);
                accuracyAssessDTO.setDate(accuracyCompositeReCallDOList.get(0).getDate());
                accuracyAssessDTOS.add(accuracyAssessDTO);
            }
        }
    }

    /**
     * 计算考核点准确率提升，
     * <AUTHOR>
     * @param accuracyAssessDTOS:
     * @param accuracyAssessRecallDOList:
     * @param accuracyAssessDOS:
     * @Return void
     * @Since version
    */
    private void processAccuracyAssess(List<AccuracyAssessDTO> accuracyAssessDTOS,
                                       List<AccuracyAssessRecallDO> accuracyAssessRecallDOList, List<AccuracyAssessDO> accuracyAssessDOS) {
        if (CollectionUtil.isNotEmpty(accuracyAssessRecallDOList) && CollectionUtil.isNotEmpty(accuracyAssessDOS)) {
            // 1.3） 计算预测准确率和回溯准确率差值，组装数据
            Map<String, List<AccuracyAssessRecallDO>> dateAccuracyRecallMap = accuracyAssessRecallDOList.stream()
                    .filter(x -> x.getAccuracy() != null).collect(Collectors.groupingBy(
                            x -> DateUtil.getDateToStr(x.getDate())));
            Map<String, AccuracyAssessDO> accuracyMap = accuracyAssessDOS.stream().filter(x -> x.getAccuracy() != null).collect(Collectors.toMap(
                    x -> DateUtil.getDateToStr(x.getDate()) + Constants.SEPARATOR_PUNCTUATION + x.getAssessName() +
                            Constants.SEPARATOR_PUNCTUATION + x.getAssessId(),
                    Function.identity(), (oldValue, newValue) -> oldValue));
            List<String> settingAccessIds = accuracyAssessDOS.stream().map(AccuracyAssessDO::getAssessId).collect(Collectors.toList());
            List<SettingAssessDO> settingAssessDOS = settingAssessDAO.selectListByIds(settingAccessIds);
            for (String key : dateAccuracyRecallMap.keySet()) {
                List<AccuracyAssessRecallDO> accuracyAssessRecallList = dateAccuracyRecallMap.get(key);
                if (CollectionUtil.isEmpty(accuracyAssessRecallList)) {
                    continue;
                }
                AccuracyAssessDTO accuracyAssessDTO = new AccuracyAssessDTO();
                List<AccuracyAssessUnitDTO> assessUnitList = new ArrayList<>();
                for (AccuracyAssessRecallDO recallDO : accuracyAssessRecallList) {
                    String accuracyKey = DateUtil.getDateToStr(recallDO.getDate()) +
                            Constants.SEPARATOR_PUNCTUATION + recallDO.getAssessName() +
                            Constants.SEPARATOR_PUNCTUATION + recallDO.getAssessId();
                    if (accuracyMap.containsKey(accuracyKey)) {
                        SettingAssessDO settingAssessDO = new SettingAssessDO();
                        if (CollectionUtil.isNotEmpty(settingAssessDOS)){
                            settingAssessDO = settingAssessDOS.stream().filter(
                                    x -> x.getId().equals(recallDO.getAssessId())).findFirst().orElse(new SettingAssessDO());
                        }
                        AccuracyAssessDO accuracyDO = accuracyMap.get(accuracyKey);
                        AccuracyAssessUnitDTO assessUnitDTO = new AccuracyAssessUnitDTO();
                        BigDecimal accuracy = recallDO.getAccuracy();
                        assessUnitDTO.setAssessAccuracy(accuracyDO.getAccuracy());
                        assessUnitDTO.setAssessAccuracyRecall(accuracy);
                        assessUnitDTO.setAssessName(accuracyDO.getAssessName());
                        assessUnitDTO.setDate(accuracyDO.getDate());
                        assessUnitDTO.setType(accuracyDO.getAssessType());
                        assessUnitDTO.setRecallDeviation(accuracy.subtract(accuracyDO.getAccuracy()));
                        assessUnitDTO.setSortNo(settingAssessDO.getSort());
                        assessUnitList.add(assessUnitDTO);
                    }
                }
                accuracyAssessDTO.setAssessUnitList(assessUnitList);
                accuracyAssessDTO.setDate(accuracyAssessRecallList.get(0).getDate());
                accuracyAssessDTOS.add(accuracyAssessDTO);
            }
        }
    }

    /**
     * 合并两个AccuracyAssessDTO对象。通过将value2的AssessUnitList合并到value1中。
     * @param value1 第一个AccuracyAssessDTO对象
     * @param value2 第二个AccuracyAssessDTO对象
     * @return 合并后的第一个AccuracyAssessDTO对象
     */
    private AccuracyAssessDTO mergeAccuracyAssessDTOs(AccuracyAssessDTO value1, AccuracyAssessDTO value2) {
        value1.getAssessUnitList().addAll(value2.getAssessUnitList());
        List<AccuracyAssessUnitDTO> collect = value1.getAssessUnitList().stream().sorted(
                Comparator.comparing(AccuracyAssessUnitDTO::getSortNo)).collect(Collectors.toList());
        value1.setAssessUnitList(collect);
        return value1;
    }
}
