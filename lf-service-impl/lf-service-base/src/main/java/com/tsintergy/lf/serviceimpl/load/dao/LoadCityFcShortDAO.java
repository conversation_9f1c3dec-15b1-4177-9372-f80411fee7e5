package com.tsintergy.lf.serviceimpl.load.dao;


import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityFcShortDO;
import java.util.Date;
import java.util.List;

import org.jsoup.helper.StringUtil;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Repository
public class LoadCityFcShortDAO extends BaseAbstractDAO<LoadCityFcShortDO> {

    public LoadCityFcShortDO findShortData(String startTime, Date date, String cityId, String caliberId, Integer type) {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != date) {
            param.getQueryConditions().put("_ne_date", new java.sql.Date(date.getTime()));
        }
        if (null != cityId) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        if (null != startTime) {
            param.getQueryConditions().put("_ne_startTime", startTime);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        if (null != type) {
            param.getQueryConditions().put("_ne_type", type);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<LoadCityFcShortDO> loadCityHisDOS = this.query(param).getDatas();
        if (CollectionUtils.isEmpty(loadCityHisDOS)) {
            return null;
        }
        return loadCityHisDOS.get(0);
    }

    public List<LoadCityFcShortDO> findLoadCityFcDOS(String startTime, Date date, String cityId, String caliberId, Integer type) {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != date) {
            param.getQueryConditions().put("_ne_date", new java.sql.Date(date.getTime()));
        }
        if (null != cityId) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        if (null != startTime) {
            param.getQueryConditions().put("_ne_startTime", startTime);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        if (null != type) {
            param.getQueryConditions().put("_ne_type", type);
        }
        return this.query(param).getDatas();
    }

    public List<LoadCityFcShortDO> findLoadCityFcDOS(Date startDate , Date endDate, Integer periodType , String startPeriodTime , String endPeriodTime , String cityId, String caliberId, Integer type) {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (null != cityId) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        if (periodType == 2 && StringUtil.isBlank(startPeriodTime)) {
            param.getQueryConditions().put("_dnl_startTime", startPeriodTime);
        }
        if (periodType == 2 && StringUtil.isBlank(endPeriodTime)) {
            param.getQueryConditions().put("_dnm_startTime", endPeriodTime);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        if (null != type) {
            param.getQueryConditions().put("_ne_type", type);
        }
        return this.query(param).getDatas();
    }

}