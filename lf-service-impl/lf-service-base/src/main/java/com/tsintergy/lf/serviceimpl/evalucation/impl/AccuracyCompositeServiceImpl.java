
package com.tsintergy.lf.serviceimpl.evalucation.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.util.DateUtil;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.assess.api.SettingCompositeAccuracyService;
import com.tsintergy.lf.serviceapi.base.assess.pojo.SettingAssessDO;
import com.tsintergy.lf.serviceapi.base.assess.pojo.SettingCompositeAccuracyDO;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceapi.base.evalucation.api.AccuracyAssessService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.AccuracyCompositeService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.BatchDataFilterService;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.AssessAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.BatchAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.CityAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.*;
import com.tsintergy.lf.serviceapi.base.forecast.api.AlgorithmService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.dto.AlgorithmAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingBatchInitService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingBatchInitDO;
import com.tsintergy.lf.serviceimpl.assess.dao.SettingAssessDAO;
import com.tsintergy.lf.serviceimpl.assess.dao.SettingCompositeAccuracyDAO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.AccuracyAssessDAO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.AccuracyCompositeDAO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.AccuracyCompositeRecallDAO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.StatisticsCityDayFcBatchDAO;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service("accuracyCompositeService")
public class AccuracyCompositeServiceImpl implements AccuracyCompositeService {

    private final String REPORT = "report";

    @Resource
    private AccuracyCompositeDAO accuracyCompositeDAO;

    @Resource
    private AccuracyAssessDAO accuracyAssessDAO;

    @Autowired
    private HolidayService holidayService;

    @Resource
    private SettingCompositeAccuracyService settingCompositeAccuracyService;

    @Resource
    private AccuracyAssessService accuracyAssessService;

    @Resource
    private LoadCityFcService loadCityFcService;

    @Autowired
    private AlgorithmService algorithmService;

    @Autowired
    private SettingCompositeAccuracyDAO settingCompositeAccuracyDAO;

    @Autowired
    private SettingAssessDAO settingAssessDAO;

    @Autowired
    private AccuracyCompositeRecallDAO accuracyCompositeRecallDAO;

    @Autowired
    private SettingBatchInitService settingBatchInitService;

    @Autowired
    private StatisticsCityDayFcBatchDAO statisticsCityDayFcBatchDAO;

    @Autowired
    private BatchDataFilterService batchDataFilterService;

    @Override
    public List<CityAccuracyDTO> getCityAccuracy(String cityId, String caliberId, String accuracyName, Date startDate, Date endDate, String isHoliday, String batchId) {
        List<CityAccuracyDTO> results = new ArrayList<CityAccuracyDTO>();
        try {
            //查询国调综合准确率or考核点准确率数据
            Map<Date, Map<String, BigDecimal>> dataMap = findAccuracyMap(cityId, caliberId, accuracyName, startDate, endDate, batchId);
            //查找算法推荐标识
            List<LoadCityFcDO> LoadCityFcDOs = loadCityFcService.listLoadCityFc(cityId, caliberId, startDate, endDate, null);
            Map<Date, Map<String, Boolean>> recommendMap = new HashMap();
            if (CollectionUtils.isNotEmpty(LoadCityFcDOs)) {
                Map<Date, List<LoadCityFcDO>> map = LoadCityFcDOs.stream()
                        .collect(Collectors.groupingBy(LoadCityFcDO::getDate));
                map.keySet().forEach(date -> {
                    recommendMap.put(date, map.get(date).stream().collect(
                        Collectors.toMap(LoadCityFcDO::getAlgorithmId, LoadCityFcDO::getRecommend, (o, v) -> v)));
                });
            }

            if (dataMap.size() != 0) {
                List<String> days = DateUtil
                    .getListBetweenDay(DateUtil.formateDate(startDate), DateUtil.formateDate(endDate), "yyyy-MM-dd");
                List<AlgorithmDO> AlgorithmDOS = algorithmService.getAllAlgorithmsNotCache();
                //获取日期区间内的节假日区间
                List<String> holidayList = getHolidayList(startDate, endDate);
                for (String dayStr : days) {
                    //判断是否包含节假日，该天是否是节假日
                    if ("0".equals(isHoliday) && holidayList.contains(dayStr)) {
                        continue;
                    }
                    Date day = DateUtil.getDate(dayStr, "yyyy-MM-dd");
                    CityAccuracyDTO cityAccuracyDTO = new CityAccuracyDTO();
                    cityAccuracyDTO.setAlgorithmDetail(new ArrayList<AlgorithmAccuracyDTO>());
                    cityAccuracyDTO.setDate(day);
                    for (AlgorithmDO AlgorithmDO : AlgorithmDOS) {
                        //排除不展示
                        if (isExclude(cityId, AlgorithmDO.getId())) {
                            continue;
                        }
                        AlgorithmAccuracyDTO algorithmAccuracyDTO = new AlgorithmAccuracyDTO();
                        algorithmAccuracyDTO.setAlgorithmId(AlgorithmDO.getId());
                        algorithmAccuracyDTO.setName(AlgorithmDO.getAlgorithmCn());
                        if (dataMap.get(day) != null && dataMap.get(day).get(AlgorithmDO.getId()) != null) {
                            algorithmAccuracyDTO.setAccuracy(dataMap.get(day).get(AlgorithmDO.getId()));
                        }
                        cityAccuracyDTO.getAlgorithmDetail().add(algorithmAccuracyDTO);
                    }
                    List<AlgorithmAccuracyDTO> collect = cityAccuracyDTO.getAlgorithmDetail().stream()
                        .sorted(Comparator.comparing(AlgorithmAccuracyDTO::getAlgorithmId))
                        .collect(Collectors.toList());
                    cityAccuracyDTO.setAlgorithmDetail(collect);
                    results.add(cityAccuracyDTO);
                }
            }
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("02F20180001");
        }
        return results;
    }

    private Map<Date, Map<String, BigDecimal>> findAccuracyMap(String cityId, String caliberId, String accuracyName,
        Date startDate,
        Date endDate, String batchId) {
        Map<Date, Map<String, BigDecimal>> dataMap = new HashMap<Date, Map<String, BigDecimal>>();
        //先查询考核点准确率 按createtime正序排列，最终放入map的是最新生成的一个批次
        List<AccuracyAssessDO> assessDOS = accuracyAssessService
            .selectListNewestInNameAndBatchData(cityId, caliberId, accuracyName, startDate, endDate, batchId);
        if (!org.springframework.util.CollectionUtils.isEmpty(assessDOS)) {
            List<SettingAssessDO> settingAssessDOS = settingAssessDAO
                .selectListByStartEndValid(startDate, endDate, caliberId);
            Map<String, SettingAssessDO> settingAssessDOMap = settingAssessDOS.stream()
                .collect(Collectors.toMap(SettingAssessDO::getAssessName, Function
                    .identity(), (key1, key2) -> key2));
            for (AccuracyAssessDO assess : assessDOS) {
                SettingAssessDO settingAssessDO = settingAssessDOMap.get(assess.getAssessName());
                if (settingAssessDO == null || !settingAssessDO.getValid()) {
                    continue;
                }
                if (dataMap.get(assess.getDate()) == null) {
                    dataMap.put(assess.getDate(), new HashMap<String, BigDecimal>());
                }
                Map<String, BigDecimal> stringBigDecimalMap = dataMap.get(assess.getDate());
                stringBigDecimalMap
                    .put(assess.getAlgorithmId(), assess.getAccuracy());
                if (assess.getReport() != null && assess.getReport()) {
                    dataMap.get(assess.getDate()).put(REPORT, assess.getAccuracy());
                }
            }
        } else {
            // 查询国调准确率
            List<AccuracyCompositeDO> accuracyCompositeDOS = accuracyAssessService
                .selectListNewestInNameAndBatch(cityId, caliberId, accuracyName, startDate, endDate, batchId);
            List<SettingCompositeAccuracyDO> accuracyDOS = settingCompositeAccuracyDAO
                .selectListByStartEndValid(startDate, endDate, caliberId);
            Map<String, SettingCompositeAccuracyDO> settingMap = accuracyDOS.stream()
                .collect(Collectors.toMap(SettingCompositeAccuracyDO::getAccuracyName, Function
                    .identity(), (key1, key2) -> key2));
            for (AccuracyCompositeDO compositeDO : accuracyCompositeDOS) {
                SettingCompositeAccuracyDO accuracyDO = settingMap.get(compositeDO.getAccuracyName());
                if (accuracyDO == null || !accuracyDO.getValid()) {
                    continue;
                }
                if (dataMap.get(compositeDO.getDate()) == null) {
                    dataMap.put(compositeDO.getDate(), new HashMap<String, BigDecimal>());
                }
                dataMap.get(compositeDO.getDate())
                    .put(compositeDO.getAlgorithmId(), compositeDO.getAccuracy());
                if (compositeDO.getReport() != null && compositeDO.getReport()) {
                    dataMap.get(compositeDO.getDate()).put(REPORT, compositeDO.getAccuracy());
                }
            }
        }
        return dataMap;
    }

    @Override
    public void doCalculateCompositeAccuracy(String cityId, String caliberId, String algorithmId, Date startDate,
        Date endDate) {
        List<AccuracyCompositeDO> saveList = new ArrayList<>();
        //查询考核点准确率
        List<AccuracyAssessDO> accuracyList = accuracyAssessService
            .findAccuracyList(cityId, caliberId, algorithmId, startDate, endDate, null);
        if (CollectionUtils.isEmpty(accuracyList)) {
            return;
        }
        //key yy  vale 年份对应的综合准确率公式对象
        Map<String, List<SettingCompositeAccuracyDO>> accuracySettingMap = this.settingCompositeAccuracyService
            .findAssessSettingByData(startDate, endDate, caliberId);
        //考核点准确率单天单个算法数据
        Map<String, List<AccuracyAssessDO>> collectAccuracyMap = accuracyList.stream().collect(
            Collectors.groupingBy(src -> src.getCityId() + Constants.SEPARATOR_PUNCTUATION + src.getCaliberId()
                + Constants.SEPARATOR_PUNCTUATION + src.getAlgorithmId() + Constants.SEPARATOR_PUNCTUATION + DateUtil
                .getDateToStr(src.getDate()) + Constants.SEPARATOR_PUNCTUATION + src.getBatchId()
                + Constants.SEPARATOR_PUNCTUATION + DateUtils
                .date2String(src.getCreatetime(), DateFormatType.DATE_FORMAT_STR)));
        for (Map.Entry<String, List<AccuracyAssessDO>> entry : collectAccuracyMap.entrySet()) {
            //key 考核点名称
            //批次
            Map<Integer, List<AccuracyAssessDO>> batchAccuracyMap = entry.getValue().stream()
                .collect(Collectors.groupingBy(
                    AccuracyAssessDO::getBatchId));
            batchAccuracyMap.forEach((batchId, value) -> {
                Map<String, AccuracyAssessDO> assessDOMap = value.stream()
                    .collect(Collectors.toMap(AccuracyAssessDO::getAssessName, Function
                        .identity(), (key1, key2) -> key2));
                String year = entry.getKey().split(Constants.SEPARATOR_PUNCTUATION)[3].substring(0, 4);
                //当前应用状态的准确率公式列表
                List<SettingCompositeAccuracyDO> accuracyDOList = accuracySettingMap.get(year);
                for (SettingCompositeAccuracyDO accuracyDO : accuracyDOList) {
                    AccuracyCompositeDO saveData = new AccuracyCompositeDO();
                    //参与计算的考核点集合
                    String[] assessArray = accuracyDO.getAssessList().split(Constants.SEPARATOR_PUNCTUATION);
                    //参与计算的考核点权重集合
                    String[] weightArray = accuracyDO.getWeightList().split(Constants.SEPARATOR_PUNCTUATION);
                    BigDecimal accuracyValue = BigDecimal.ZERO;
                    for (int i = 0; i < assessArray.length; i++) {
                        AccuracyAssessDO accuracyAssessDO = assessDOMap.get(assessArray[i]);
                        if (accuracyAssessDO != null) {
                            //考核点的准确率乘以公式中配置的权重
                            BigDecimal accuracy = accuracyAssessDO.getAccuracy();
                            if (accuracy != null) {
                                accuracyValue = accuracyValue
                                    .add(accuracy.multiply(new BigDecimal(weightArray[i]))
                                        .setScale(4, RoundingMode.HALF_UP));
                            }
                        }
                    }
                    //从列表中get(0)为拼装城市等基础数据
                    AccuracyAssessDO accuracyAssessDO = value.get(0);
                    BeanUtils.copyProperties(accuracyAssessDO, saveData, "id");
                    saveData.setBatchId(accuracyAssessDO.getBatchId());
                    //创建时间+批次id两者一起可以确定为一个批次；createtime与算法预测fc的实体一致
                    saveData.setCreatetime(accuracyAssessDO.getCreatetime());
                    saveData.setAccuracy(accuracyValue);
                    saveData.setAccuracyName(accuracyDO.getAccuracyName());
                    saveData.setAccuracyId(accuracyDO.getId());
                    saveList.add(saveData);
                }
            });
        }
        saveList.forEach(one -> {
                one.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                accuracyCompositeDAO.saveOrUpdateEntityByTemplate(one);
            }
        );
    }

    @Override
    public List<AlgorithmAccuracyDTO> getCityAvgAccuracy(List<CityAccuracyDTO> cityAccuracy, String cityId) throws Exception {
        List<AlgorithmAccuracyDTO> resultList = new ArrayList<>();
        List<AlgorithmDO> AlgorithmDOS = algorithmService.getAllAlgorithmsNotCache();
        List<AlgorithmAccuracyDTO> algorithmAccuracyDTOList = new ArrayList<>();
        if (org.apache.dubbo.common.utils.CollectionUtils.isNotEmpty(cityAccuracy)) {
            for (CityAccuracyDTO accuracyDTO : cityAccuracy) {
                algorithmAccuracyDTOList.addAll(accuracyDTO.getAlgorithmDetail());
            }
        }
        if (org.apache.dubbo.common.utils.CollectionUtils.isNotEmpty(algorithmAccuracyDTOList)) {
            // 按照算法Id进行分组
            Map<String, List<AlgorithmAccuracyDTO>> accuracyMap = algorithmAccuracyDTOList.stream()
                    .collect(Collectors.groupingBy(AlgorithmAccuracyDTO::getAlgorithmId));
            for (AlgorithmDO AlgorithmDO : AlgorithmDOS) {
                //排除不展示
                if (isExclude(cityId, AlgorithmDO.getId())) {
                    continue;
                }
                List<AlgorithmAccuracyDTO> algorithmAccuracyDTOS = accuracyMap.get(AlgorithmDO.getId());
                if (!CollectionUtils.isEmpty(algorithmAccuracyDTOS)) {
                    AlgorithmAccuracyDTO algorithmAccuracyDTO = algorithmAccuracyDTOS.get(0);
                    List<BigDecimal> dataList = new ArrayList<>();
                    for (AlgorithmAccuracyDTO dto : algorithmAccuracyDTOS) {
                        if (dto.getAccuracy() != null) {
                            dataList.add(dto.getAccuracy());
                        }
                    }
                    if (CollectionUtils.isNotEmpty(dataList)) {
                        algorithmAccuracyDTO.setAccuracy(BigDecimalFunctions.listAvg(dataList));
                        int i = 0;
                        for (BigDecimal bigDecimal : dataList) {
                            if (bigDecimal != null && bigDecimal.compareTo(BigDecimal.valueOf(0.97)) >= 0) {
                                i = i + 1;
                            }
                        }
                        if (i == 0) {
                            algorithmAccuracyDTO.setPass(BigDecimal.ZERO);
                        } else {
                            BigDecimal divide = BigDecimal.valueOf(i)
                                .divide(BigDecimal.valueOf(dataList.size()), 4, RoundingMode.HALF_UP);
                            algorithmAccuracyDTO.setPass(divide);
                        }
                    }
                    resultList.add(algorithmAccuracyDTO);
                }
            }
        }
        return resultList;
    }

    /**
     * 计算预测回溯准确率，综合准确率
     * <AUTHOR>
     * @param cityId:
     * @param caliberId:
     * @param algorithmId:
     * @param startDate:
     * @param endDate:
     * @Return void
     * @Since version
     */
    @Override
    public void doCalculateRecallCompositeAccuracy(String cityId, String caliberId, String algorithmId, Date startDate, Date endDate) {
        List<AccuracyAssessRecallDO> accuracyRecallList = accuracyAssessService.
                findAccuracyRecallList(cityId, caliberId, algorithmId, startDate, endDate);
        //key yy  vale 年份对应的综合准确率公式对象
        Map<String, List<SettingCompositeAccuracyDO>> accuracySettingMap = this.settingCompositeAccuracyService
                .findAssessSettingByData(startDate, endDate, caliberId);
        if (CollectionUtil.isEmpty(accuracyRecallList) || CollectionUtil.isEmpty(accuracySettingMap)){
            return;
        }
        //考核点准确率单天单个算法数据
        Map<String, List<AccuracyAssessRecallDO>> collectAccuracyMap = accuracyRecallList.stream().filter(src -> src.getAccuracy() != null)
                .collect(Collectors.groupingBy(src -> src.getCityId() + Constants.SEPARATOR_PUNCTUATION + src.getCaliberId()
                        + Constants.SEPARATOR_PUNCTUATION + src.getAlgorithmId() + Constants.SEPARATOR_PUNCTUATION + DateUtil
                        .getDateToStr(src.getDate())));
        List<AccuracyCompositeReCallDO> saveList = new ArrayList<>();
        for (Map.Entry<String, List<AccuracyAssessRecallDO>> entry : collectAccuracyMap.entrySet()) {
            List<AccuracyAssessRecallDO> accuracyAssessRecallDailyList = entry.getValue();
            Map<String, AccuracyAssessRecallDO> assessDOMap = accuracyAssessRecallDailyList.stream()
                    .collect(Collectors.toMap(AccuracyAssessRecallDO::getAssessName, Function
                            .identity(), (key1, key2) -> key2));
            String year = entry.getKey().split(Constants.SEPARATOR_PUNCTUATION)[3].substring(0, 4);
            //当前应用状态的准确率公式列表
            List<SettingCompositeAccuracyDO> accuracyDOList = accuracySettingMap.get(year);
            for (SettingCompositeAccuracyDO accuracyDO : accuracyDOList) {
                AccuracyCompositeReCallDO saveData = new AccuracyCompositeReCallDO();
                //参与计算的考核点集合
                String[] assessArray = accuracyDO.getAssessList().split(Constants.SEPARATOR_PUNCTUATION);
                //参与计算的考核点权重集合
                String[] weightArray = accuracyDO.getWeightList().split(Constants.SEPARATOR_PUNCTUATION);
                BigDecimal accuracyValue = BigDecimal.ZERO;
                for (int i = 0; i < assessArray.length; i++) {
                    AccuracyAssessRecallDO accuracyAssessDO = assessDOMap.get(assessArray[i]);
                    if (accuracyAssessDO != null) {
                        //考核点的准确率乘以公式中配置的权重
                        accuracyValue = accuracyValue
                                .add(accuracyAssessDO.getAccuracy().multiply(new BigDecimal(weightArray[i]))
                                        .setScale(4, RoundingMode.HALF_UP));
                    }
                }
                //从列表中get(0)为拼装城市等基础数据
                AccuracyAssessRecallDO accuracyAssessRecallDO = accuracyAssessRecallDailyList.get(0);
                BeanUtils.copyProperties(accuracyAssessRecallDO, saveData, "id");
                saveData.setAccuracy(accuracyValue);
                saveData.setAccuracyName(accuracyDO.getAccuracyName());
                saveData.setAccuracyId(accuracyDO.getId());
                saveList.add(saveData);
            }
        }
        saveList.forEach(one -> {
                    one.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                    accuracyCompositeRecallDAO.saveOrUpdateEntityByTemplate(one);
                }
        );
    }

    /**
     * 获取节假日日期列表
     */
    private List<String> getHolidayList(Date startDate, Date endDate) {
        List<HolidayDO> holidayLists = holidayService.findHolidayVOS(startDate, endDate);
        List<String> holidayList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(holidayLists)) {
            for (HolidayDO holidayVO : holidayLists) {
                List<String> listBetweenDay = DateUtil.getListBetweenDay(DateUtil.formateDate(holidayVO.getStartDate()),
                    DateUtil.formateDate(holidayVO.getEndDate()));
                holidayList.addAll(listBetweenDay);
            }
        }
        return holidayList;
    }

    private boolean isExclude(String cityId, String algorithmId) throws Exception {

        List<AlgorithmDO> allAlgorithmsNotCache = algorithmService.getAllAlgorithmsNotCache();
        List<AlgorithmDO> pageAlgorithms = allAlgorithmsNotCache.stream().filter(
            t -> AlgorithmConstants.COMMON_ALGORITHM_TYPE.equals(t.getType())
                || AlgorithmConstants.NORMAL_ALGORITHM_TYPE
                .equals(t.getType()))
            .collect(Collectors.toList());
        List<AlgorithmDO> viewAlgorithms = null;
        List<String> collect = null;
        if (StringUtils.isNotBlank(cityId) && (cityId.equals(Constants.PROVINCE_ID))) {
            viewAlgorithms = pageAlgorithms.stream().filter(t -> t.getProvinceView() == true)
                .collect(Collectors.toList());
            collect = viewAlgorithms.stream().map(AlgorithmDO::getId).collect(Collectors.toList());
            if (collect.contains(algorithmId)) {
                return false;
            }
        } else {
            viewAlgorithms = pageAlgorithms.stream().filter(t -> t.getCityView() == true).collect(Collectors.toList());
            collect = viewAlgorithms.stream().map(AlgorithmDO::getId).collect(Collectors.toList());
            if (collect.contains(algorithmId)) {
                return false;
            }
        }
        return true;
    }

    @SneakyThrows
    @Override
    public List<BatchAccuracyDTO> getBatchAccuracy(String cityId, String caliberId, List<String> algorithmIds, Date startDate, Date endDate, String batchId, String statMethod) {
        Map<String, String> algorithmNameMap = algorithmService.getAllAlgorithmsNotCache().stream().collect(Collectors.toMap(AlgorithmDO::getId, AlgorithmDO::getAlgorithmCn));
        SettingBatchInitDO batchById = settingBatchInitService.getBatchById(batchId);
        //综合准确率
        if ("1".equals(statMethod)) {
            List<AccuracyCompositeDO> accuracyCompositeDOS = accuracyCompositeDAO.selectListByAlgorithmId(cityId, caliberId, algorithmIds, startDate, endDate, null);
            List<AccuracyCompositeDO> compositeDOList = accuracyCompositeDOS.stream()
                    // 先根据创建时间范围进行筛选
                    .filter(src -> com.tsintergy.lf.core.util.DateUtil.isWithinTimeRange(src.getCreatetime(), batchById.getStartTime(), batchById.getEndTime()))
                    // 按照日期和创建时间分组，同一天同一创建时间的会分到同一组
                    .collect(Collectors.groupingBy(acc -> acc.getAlgorithmId() + "-" + acc.getDate() + "_" + DateUtil.getDateToStr(new Date(acc.getCreatetime().getTime()))))
                    // 将分组后的结果转换为一个新的列表，每个分组中只保留最早创建的数据（即时间戳最小的数据）
                    .values().stream().map(group -> group.stream().min(Comparator.comparing(AccuracyCompositeDO::getCreatetime)).get()).collect(Collectors.toList());

            List<BatchAccuracyDTO> result = compositeDOList.stream().map(src -> {
                BatchAccuracyDTO batchAccuracyDTO = new BatchAccuracyDTO();
                BeanUtils.copyProperties(src, batchAccuracyDTO);
                //计算日前天数
                Date date = src.getDate();
                Date createDate = DateUtil.getFormatDate(new Date(src.getCreatetime().getTime()));
                Integer days = com.tsintergy.lf.core.util.DateUtil.differentDaysByMillisecond(createDate, date);
                batchAccuracyDTO.setBatchName("D-" + days);
                batchAccuracyDTO.setDays(days);
                batchAccuracyDTO.setCreateTime(new DateTime(src.getCreatetime().getTime()));
                batchAccuracyDTO.setBatchId(String.valueOf(src.getBatchId()));
                batchAccuracyDTO.setAlgorithmName(Objects.requireNonNull(AlgorithmEnum.findById(src.getAlgorithmId())).getDescription());
                return batchAccuracyDTO;
            }).filter(dto -> dto.getDays() >= 1 && dto.getDays() <= 10).collect(Collectors.toList());
            return result;
        } else {
            List<AccuracyAssessDO> cityDayFcBatchDOS = accuracyAssessService.findAccuracyList(cityId, caliberId, statMethod, algorithmIds, startDate, endDate)
                    .stream()
                    // 先根据创建时间范围进行筛选
                    .filter(src -> com.tsintergy.lf.core.util.DateUtil.isWithinTimeRange(src.getCreatetime(), batchById.getStartTime(), batchById.getEndTime()))
                    // 按照日期和创建时间分组，同一天同一创建时间的会分到同一组
                    .collect(Collectors.groupingBy(acc -> acc.getAlgorithmId() + "-" + acc.getDate() + "_" + DateUtil.getDateToStr(new Date(acc.getCreatetime().getTime()))))
                    // 将分组后的结果转换为一个新的列表，每个分组中只保留最早创建的数据（即时间戳最小的数据）
                    .values().stream().map(group -> group.stream().min(Comparator.comparing(AccuracyAssessDO::getCreatetime)).get()).collect(Collectors.toList());

            //计算日前天数
            return cityDayFcBatchDOS.stream().map(src -> {
                BatchAccuracyDTO batchAccuracyDTO = new BatchAccuracyDTO();
                BeanUtils.copyProperties(src, batchAccuracyDTO);
                //计算日前天数
                Date date = src.getDate();
                Date createDate = DateUtil.getFormatDate(new Date(src.getCreatetime().getTime()));
                Integer days = com.tsintergy.lf.core.util.DateUtil.differentDaysByMillisecond(createDate, date);
                batchAccuracyDTO.setBatchName("D-" + days);
                batchAccuracyDTO.setDays(days);
                batchAccuracyDTO.setCreateTime(new DateTime(src.getCreatetime().getTime()));
                batchAccuracyDTO.setBatchId(String.valueOf(src.getBatchId()));
                batchAccuracyDTO.setAlgorithmName(Objects.requireNonNull(AlgorithmEnum.findById(src.getAlgorithmId())).getDescription());
                return batchAccuracyDTO;
            }).filter(dto -> dto.getDays() >= 1 && dto.getDays() <= 10).collect(Collectors.toList());
        }
    }

    @Override
    public List<AssessAccuracyDTO> getAssessAccuracy(String cityId, String caliberId, List<String> algorithmIds, String batchId, Date startDate, Date endDate, Integer days) {
        Map<String, String> algorithmNameMap = algorithmService.getAllAlgorithms().stream().collect(Collectors.toMap(AlgorithmDO::getId, AlgorithmDO::getAlgorithmCn));
        //查询综合准确率
        List<AccuracyCompositeDO> accuracyCompositeDOS = accuracyCompositeDAO.selectListByAlgorithmId(cityId, caliberId, algorithmIds, startDate, endDate, null);
        Map<String, Map<Date, List<AccuracyCompositeDO>>> filterComositeMap = batchDataFilterService.filterCompositeByBatchId(accuracyCompositeDOS, batchId, days).stream()
                .collect(Collectors.groupingBy(AccuracyCompositeDO::getAlgorithmId,
                        Collectors.groupingBy(AccuracyCompositeDO::getDate)));
        //查询考核点准确率
        List<AccuracyAssessDO> accuracyAssessDOS = accuracyAssessDAO.selectListByAlgorithmIds(cityId, caliberId, algorithmIds, startDate, endDate);
        Map<String, Map<Date, List<AccuracyAssessDO>>> filterAssessDOMap = batchDataFilterService.filterAssessByBatchId(accuracyAssessDOS, batchId, days).stream()
                .collect(Collectors.groupingBy(AccuracyAssessDO::getAlgorithmId,
                        Collectors.groupingBy(AccuracyAssessDO::getDate)));

        List<AssessAccuracyDTO> result = new ArrayList<>();
        List<Date> dates = DateUtil.getListBetweenDay(startDate, endDate);
        for (String algorithmId : algorithmIds) {
            for (Date date : dates) {
                AssessAccuracyDTO assessAccuracyDTO = new AssessAccuracyDTO();
                assessAccuracyDTO.setAlgorithmId(algorithmId);
                assessAccuracyDTO.setAlgorithmName(algorithmNameMap.get(algorithmId));
                assessAccuracyDTO.setDate(date);
                List<AccuracyCompositeDO> compositeDOS = filterComositeMap.get(algorithmId) != null ? filterComositeMap.get(algorithmId).get(date) : null;
                if (CollectionUtils.isEmpty(compositeDOS)) {
                    continue;
                }
                assessAccuracyDTO.setAccuracy(compositeDOS.get(0).getAccuracy());
                List<AccuracyAssessDO> assessDOS = filterAssessDOMap.get(algorithmId) != null ? filterAssessDOMap.get(algorithmId).get(date) : null;
                if (CollectionUtils.isNotEmpty(assessDOS)) {
                    setAssessAccuracy(assessAccuracyDTO, assessDOS);
                }
                result.add(assessAccuracyDTO);
            }
        }
        result.sort(Comparator.comparing(AssessAccuracyDTO::getDate)
                .thenComparing(AssessAccuracyDTO::getAlgorithmId));
        return result;
    }

    private void setAssessAccuracy(AssessAccuracyDTO accuracyDTO, List<AccuracyAssessDO> accuracyAssessDOS) {
        for (AccuracyAssessDO assessDO : accuracyAssessDOS) {
            if (assessDO.getAssessName().contains("日最大")) {
                accuracyDTO.setMaxLoadAccuracy(assessDO.getAccuracy());
            }
            if (assessDO.getAssessName().contains("夜间最小")) {
                accuracyDTO.setNighttimeMinLoadAccuracy(assessDO.getAccuracy());
            }
            if (assessDO.getAssessName().contains("午间最小")) {
                accuracyDTO.setMiddayMinLoadAccuracy(assessDO.getAccuracy());
            }
            if (assessDO.getAssessName().contains("保供关键时段")) {
                accuracyDTO.setSupplyAccuracy(assessDO.getAccuracy());
            }
        }
    }
}
