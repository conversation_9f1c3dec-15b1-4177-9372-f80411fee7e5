package com.tsintergy.lf.serviceimpl.common.util;

import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.LoadCalUtil;
import org.joda.time.DateTime;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * @date: 3/2/18 10:16 AM
 * @author: angel
 **/
public class AlgorithmUtil {

    /**
     * 曲线平滑，保留极值
     *
     * @param singleLoadCityVOList
     * @param n
     * @return
     * @throws Exception
     */
    public static List<BigDecimal> smoothLine(List<BigDecimal> singleLoadCityVOList, int n) throws Exception {
        // 1. 找出最大值和最小值的位置
        int maxIndex = 0;
        int minIndex = 0;
        BigDecimal currentMax = singleLoadCityVOList.get(0);
        BigDecimal currentMin = singleLoadCityVOList.get(0);

        for (int i = 1; i < singleLoadCityVOList.size(); i++) {
            BigDecimal value = singleLoadCityVOList.get(i);
            if (value.compareTo(currentMax) > 0) {
                currentMax = value;
                maxIndex = i;
            }
            if (value.compareTo(currentMin) < 0) {
                currentMin = value;
                minIndex = i;
            }
        }

        // 2. 创建新列表，避免修改原数据
        List<BigDecimal> result = new ArrayList<>(singleLoadCityVOList);

        // 3. 平滑处理（不包括最大值和最小值点）
        int windowSize = n / 2;

        for (int i = windowSize; i < result.size() - windowSize; i++) {
            // 跳过最大值和最小值点
            if (i == maxIndex || i == minIndex) {
                continue;
            }

            BigDecimal sum = BigDecimal.ZERO;
            int count = 0;

            // 计算窗口内的和
            for (int j = -windowSize; j <= windowSize; j++) {
                // 跳过最大值和最小值点
                if (i + j == maxIndex || i + j == minIndex) {
                    continue;
                }

                sum = sum.add(result.get(i + j));
                count++;
            }

            // 计算平均值
            result.set(i, sum.divide(new BigDecimal(count), 4, BigDecimal.ROUND_HALF_UP));
        }

        // 4. 特殊处理边界点（避免硬编码）
        for (int i = 0; i < windowSize; i++) {
            if (i == maxIndex || i == minIndex) {
                continue;
            }

            BigDecimal sum = BigDecimal.ZERO;
            int count = 0;

            for (int j = 0; j <= windowSize * 2; j++) {
                int idx = Math.min(i + j, result.size() - 1);
                if (idx == maxIndex || idx == minIndex) {
                    continue;
                }

                sum = sum.add(result.get(idx));
                count++;
            }

            result.set(i, sum.divide(new BigDecimal(count), 4, BigDecimal.ROUND_HALF_UP));
        }

        for (int i = result.size() - windowSize; i < result.size(); i++) {
            if (i == maxIndex || i == minIndex) {
                continue;
            }

            BigDecimal sum = BigDecimal.ZERO;
            int count = 0;

            for (int j = -windowSize * 2; j <= 0; j++) {
                int idx = Math.max(i + j, 0);
                if (idx == maxIndex || idx == minIndex) {
                    continue;
                }

                sum = sum.add(result.get(idx));
                count++;
            }

            result.set(i, sum.divide(new BigDecimal(count), 4, BigDecimal.ROUND_HALF_UP));
        }

        return result;
    }

    public static List<BigDecimal> recorrectLoad(List<BigDecimal> originLoad, BigDecimal distMax, BigDecimal distMin) {
        //获取最小值
        final BigDecimal originMin = LoadCalUtil.min(originLoad);
        //=原始最大值-最小值
        final BigDecimal originDiff = BigDecimalUtils.sub(LoadCalUtil.max(originLoad), originMin);
        // 设置的最大值-最小值
        final BigDecimal distDiff = BigDecimalUtils.sub(distMax, distMin);
        List<BigDecimal> distLoad = new ArrayList<BigDecimal>();
        for (BigDecimal origin : originLoad) {
            if (!origin.equals(BigDecimal.ZERO)) {
                BigDecimal temp = BigDecimalUtils.divide(BigDecimalUtils.sub(origin, originMin), originDiff, Constants.SCALE);
                distLoad.add(BigDecimalUtils.add(distMin, BigDecimalUtils.multiply(temp, distDiff)));
            }
        }
        return distLoad;
    }

    /**
     * 曲线平滑，保留极值（滑动窗口实现）
     *
     * @param singleLoadCityVOList 原始96点负荷曲线
     * @param originalAssessIndex  需要保留的5个极值点索引数组
     * @return 平滑后的曲线
     * @throws Exception
     */
    public static List<BigDecimal> smoothLineByAssess(List<BigDecimal> singleLoadCityVOList, int[] originalAssessIndex) throws Exception {
        // 参数校验
        if (singleLoadCityVOList == null || singleLoadCityVOList.size() != 96) {
            throw new IllegalArgumentException("负荷数据必须包含96个点");
        }
        if (originalAssessIndex == null || originalAssessIndex.length != 5) {
            throw new IllegalArgumentException("必须提供5个极值点索引");
        }

        // 1. 创建固定点标记数组
        boolean[] isFixedPoint = new boolean[96];
        for (int index : originalAssessIndex) {
            if (index < 0 || index >= 96) {
                throw new IllegalArgumentException("极值点索引必须在0-95范围内");
            }
            isFixedPoint[index] = true;
        }

        // 2. 创建结果列表并复制原始值
        List<BigDecimal> result = new ArrayList<>(singleLoadCityVOList);

        // 3. 滑动窗口平滑处理（窗口大小5，跳过固定点）
        final int WINDOW_SIZE = 5;
        final int HALF_WINDOW = WINDOW_SIZE / 2;

        for (int i = 0; i < 96; i++) {
            if (!isFixedPoint[i]) {
                BigDecimal sum = BigDecimal.ZERO;
                int count = 0;

                // 计算窗口内平均值（跳过固定点）
                for (int j = i - HALF_WINDOW; j <= i + HALF_WINDOW; j++) {
                    if (j >= 0 && j < 96 && !isFixedPoint[j]) {
                        sum = sum.add(result.get(j));
                        count++;
                    }
                }

                if (count > 0) {
                    result.set(i, sum.divide(new BigDecimal(count), 4, RoundingMode.HALF_UP));
                }
            }
        }

        return result;
    }

    public static String getUid() {
        String uid = DateTime.now().toString("yyyyMMddHHmmssSSS");
        return uid;
    }


}
