/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  wangfeng Date:  2020/3/4 2:19 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.load.impl;


import com.tsintergy.lf.serviceapi.base.load.api.LoadCityFcShortService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityFcShortDO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadCityFcShortDAO;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 超短期数据操作
 *
 * <AUTHOR>
 * @create 2020/6/17
 * @since 1.0.0
 */
@Service("loadCityFcShortService")
public class LoadCityFcShortServiceImpl implements LoadCityFcShortService {

    @Autowired
    private LoadCityFcShortDAO loadCityFcShortDAO;


    @Override
    public void saveOrUpdate(LoadCityFcShortDO loadCityFcShortDO) throws Exception {
        loadCityFcShortDAO.saveOrUpdateEntityByTemplate(loadCityFcShortDO);
    }


    @Override
    public LoadCityFcShortDO findShortData(String startTime, Date date, String cityId, String caliberId, Integer type) {
        LoadCityFcShortDO shortData = loadCityFcShortDAO.findShortData(startTime, date, cityId, caliberId, type);
        return shortData;
    }
}