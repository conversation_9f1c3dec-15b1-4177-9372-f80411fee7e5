package com.tsintergy.lf.serviceimpl.assess.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsintergy.aif.algorithm.serviceapi.base.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.assess.pojo.SettingCompositeAccuracyDO;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @date 2024/4/12 14:18
 */
@Component
public class SettingCompositeAccuracyDAO extends BaseAbstractDAO<SettingCompositeAccuracyDO> {

    public SettingCompositeAccuracyDO selectListByName(String year, String caliberId, String accuracyName) {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (year != null) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "year", year);
        }
        if (caliberId != null) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (accuracyName != null) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "accuracyName", accuracyName);
        }
        List<SettingCompositeAccuracyDO> datas = this.query(dbQueryParamBuilder.build()).getDatas();
        if (CollectionUtils.isEmpty(datas)) {
            return null;
        }
        return datas.get(0);
    }

    public List<SettingCompositeAccuracyDO> selectListByAssessName(String year, String caliberId, String assessName) {
        List<SettingCompositeAccuracyDO> settingCompositeAccuracyDOS = this.selectAll(year, caliberId, null);
        return settingCompositeAccuracyDOS.stream()
            .filter(src -> src.getAssessList().contains(assessName)).collect(Collectors.toList());
    }

    public List<SettingCompositeAccuracyDO> selectAll(String year, String caliberId, Boolean valid) {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (year != null) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "year", year);
        }
        if (caliberId != null) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (valid != null) {
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "valid", valid);
        }
        List<SettingCompositeAccuracyDO> datas = this.query(dbQueryParamBuilder.build()).getDatas();
        return datas;
    }


    public List<SettingCompositeAccuracyDO> selectListByStartEndValid(Date startDate, Date endDate, String caliberId) {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (startDate == null || endDate == null) {
            return null;
        }
        String startYM = DateUtil.getMonthByDate(startDate);
        String endYM = DateUtil.getMonthByDate(endDate);
        dbQueryParamBuilder.where(QueryOp.StringNoLessThan, "year", startYM.substring(0,4));
        dbQueryParamBuilder.where(QueryOp.StringNoMoreThan, "year", endYM.substring(0,4));
        dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "valid", true);
        if (null != caliberId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        dbQueryParamBuilder.addOrderByAsc("createtime");
        return this.query(dbQueryParamBuilder.build()).getDatas();
    }


}
