package com.tsintergy.lf.serviceimpl.forecast.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFc288DO;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 */

@Component
public class LoadCityFc288DAO extends BaseAbstractDAO<LoadCityFc288DO> {
    /**
     * 查询预测负荷数据
     *
     * @param cityId      城市ID
     * @param caliberId   口径ID
     * @param algorithmId 算法ID
     * @param date        日期
     * @return
     */
    public LoadCityFc288DO getLoadCityFcDO(String cityId, String caliberId, String algorithmId, Date date) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        param.getQueryConditions().put("_ne_date", new java.sql.Date(date.getTime()));
        if(StringUtils.isNoneBlank(algorithmId)){
            param.getQueryConditions().put("_ne_algorithmId", algorithmId);
        }
        param.getQueryConditions().put("_ne_cityId", cityId);
        param.getQueryConditions().put("_ne_caliberId", caliberId);
        List<LoadCityFc288DO> loadCityFcDOS =this.query(param).getDatas();
        if (loadCityFcDOS != null && loadCityFcDOS.size() > 0) {
            return loadCityFcDOS.get(0);
        }
        return null;
    }
    /**
     * 保存或更新
     *
     * @param loadCityFcDO
     * @return
     */
    public LoadCityFc288DO doSaveOrUpdateLoadCityFcDO(LoadCityFc288DO loadCityFcDO) throws Exception {
        LoadCityFc288DO oldVO = this.getLoadCityFcDO(loadCityFcDO.getCityId(), loadCityFcDO.getCaliberId(),
            loadCityFcDO.getAlgorithmId(), loadCityFcDO.getDate());
        if (oldVO != null) {
            String id = oldVO.getId();
            ColumnUtil.copyPropertiesIgnoreNull(loadCityFcDO, oldVO);
            oldVO.setId(id);
            oldVO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            this.getSession().flush();
            this.getSession().clear();
            return (LoadCityFc288DO) this.updateAndFlush(oldVO);
        } else {
            return (LoadCityFc288DO) this.createAndFlush(loadCityFcDO);
        }
    }

    public LoadCityFc288DO getReportLoadCityFcDO(Date date, String cityId, String caliberId, String algorithmId) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (date != null) {
            param.getQueryConditions().put("_ne_date", new java.sql.Date(date.getTime()));
        }
        if (StringUtils.isNotBlank(algorithmId)) {
            param.getQueryConditions().put("_ne_algorithmId", algorithmId);
        }
        if (StringUtils.isNotBlank(cityId)) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        if (StringUtils.isNotBlank(caliberId)) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        List<LoadCityFc288DO> loadCityFcDOS = this.query(param).getDatas();
        if (loadCityFcDOS != null && loadCityFcDOS.size() > 0) {
            return loadCityFcDOS.get(0);
        }
        return null;
    }

    public List<LoadCityFc288DO> getLoadCityFc288DOs(String cityId, Date startDate, Date endDate, String caliberId) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (startDate!=null){
            param.getQueryConditions().put("_dnl_date",new java.sql.Date(startDate.getTime()));
        }
        if (endDate!=null){
            param.getQueryConditions().put("_dnm_date",new java.sql.Date(endDate.getTime()));
        }
        if (StringUtils.isNotBlank(cityId)) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        if (StringUtils.isNotBlank(caliberId)) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        param.setOrderby("date");
        param.setDesc("0");
        return this.query(param).getDatas();
    }
}