package com.tsintergy.lf.serviceimpl.recall.dao;


import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.serviceapi.base.check.pojo.SettingReportDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyLoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.DeviationLoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.DispersionLoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.recall.pojo.StatisticsCityDayFcRecallDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;

@Slf4j
@Component
public class StatisticsCityDayFcRecallDAO extends BaseAbstractDAO<StatisticsCityDayFcRecallDO> {

    /**
     *      功能描述: <br>  获取预测结果统计数据
     *
     * @param cityId      城市ID
     * @param caliberId   口径ID
     * @param algorithmId 算法ID
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @return  @since: 1.0.0     @Author:zhaoml     @Date: 2018/4/24 13:21   
     */
    public List<StatisticsCityDayFcRecallDO> getStatisticsCityDayFcDOs(String cityId, String caliberId, String algorithmId,
                                                                       Date startDate, Date endDate, Boolean isReport) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);

        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (StringUtils.isNotBlank(algorithmId)) {
            param.getQueryConditions().put("_ne_algorithmId", algorithmId);
        }
        if (StringUtils.isNotBlank(cityId)) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        if (null != isReport) {
            param.getQueryConditions().put("_ne_report", isReport);
        }
        if (StringUtils.isNotBlank(caliberId)) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        param.setDesc("date");
        return query(param).getDatas();
    }


    /**
     *      功能描述: <br>  获取预测结果统计
     *
     * @param cityId      城市ID
     * @param caliberId   口径ID
     * @param algorithmId 算法ID
     * @param date        日期
     * @return  @since: 1.0.0     @Author:zhaoml     @Date: 2018/4/24 13:23   
     */
    public StatisticsCityDayFcRecallDO getStatisticsCityDayFcDO(String cityId, String caliberId, String algorithmId,
                                                                Date date) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != date) {
            param.getQueryConditions().put("_de_date", new java.sql.Date(date.getTime()));
        }
        if (StringUtils.isNotBlank(algorithmId)) {
            param.getQueryConditions().put("_ne_algorithmId", algorithmId);
        }
        if (StringUtils.isNotBlank(cityId)) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        if (StringUtils.isNotBlank(caliberId)) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        List<StatisticsCityDayFcRecallDO> datas = query(param).getDatas();
        if (datas.size() > 0) {
            return datas.get(0);
        }
        return null;
    }

    /**
     * 保存或更新
     */
    public StatisticsCityDayFcRecallDO doSaveOrUpdateStatisticsCityDayFcDO(StatisticsCityDayFcRecallDO statisticsCityDayFcVO)
            throws Exception {
        StatisticsCityDayFcRecallDO oldVO = getStatisticsCityDayFcDO(statisticsCityDayFcVO.getCityId(),
                statisticsCityDayFcVO.getCaliberId(), statisticsCityDayFcVO.getAlgorithmId(),
                statisticsCityDayFcVO.getDate());
        if (oldVO != null) {
            String id = oldVO.getId();
            BeanUtils.copyProperties(statisticsCityDayFcVO, oldVO);
            oldVO.setId(id);
            oldVO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            return updateAndFlush(oldVO);
        } else {
            return createAndFlush(statisticsCityDayFcVO);
        }
    }

    /**
     * 保存或更新
     */
    public List<StatisticsCityDayFcRecallDO> doSaveOrUpdateStatisticsCityDayFcDOs(
            List<StatisticsCityDayFcRecallDO> statisticsCityDayFcVOS) throws Exception {
        List<StatisticsCityDayFcRecallDO> vos = new ArrayList<StatisticsCityDayFcRecallDO>();
        for (StatisticsCityDayFcRecallDO statisticsCityDayFcVO : statisticsCityDayFcVOS) {
            try {
                vos.add(this.doSaveOrUpdateStatisticsCityDayFcDO(statisticsCityDayFcVO));
            } catch (Exception e) {
                log.error("保存预测统计结果出错了", e);
            }
        }
        return vos;
    }

    /**
     *      功能描述: <br>  获取预测结果统计数据
     *
     * @param cityIds     城市ID列表
     * @param algorithmId 算法ID
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @return  @since: 1.0.0     @Author:zhaoml     @Date: 2018/4/24 13:25   
     */
    public List<StatisticsCityDayFcRecallDO> getStatisticsCityDayFcDOs(List<String> cityIds, String caliberId,
                                                                       String algorithmId, Date startDate, Date endDate, Boolean isReport) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (StringUtils.isNotBlank(algorithmId)) {
            param.getQueryConditions().put("_ne_algorithmId", algorithmId);
        }
        if (null != cityIds && cityIds.size() > 0) {
            param.getQueryConditions().put("_sin_cityId", cityIds);
        }
        if (null != isReport) {
            param.getQueryConditions().put("_ne_report", isReport);
        }
        if (StringUtils.isNotBlank(caliberId)) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        param.setDesc("date");
        return query(param)
                .getDatas();
    }

    /**
     * 获取上报预测结果统计
     *
     * @param cityId    城市ID
     * @param caliberId 口径ID
     * @param date      日期
     */
    public StatisticsCityDayFcRecallDO getReportStatisticsCityDayFcDO(String cityId, String caliberId, Date date)
            throws Exception {

        if (cityId == null) {
            throw TsieExceptionUtils.newBusinessException("城市ID不可为空");
        }
        if (caliberId == null) {
            throw TsieExceptionUtils.newBusinessException("口径ID不可为空");
        }
        if (date == null) {
            throw TsieExceptionUtils.newBusinessException("日期不可为空");
        }
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        param.getQueryConditions().put("_de_date", new java.sql.Date(date.getTime()));
        param.getQueryConditions().put("_se_cityId", cityId);
        param.getQueryConditions().put("_se_caliberId", caliberId);
        param.getQueryConditions().put("_ne_report", true);
        List<StatisticsCityDayFcRecallDO> datas = query(param)
                .getDatas();
        if (datas.size() > 0) {
            return datas.get(0);
        }
        return null;
    }

    /**
     * 统计一天预测结果情况
     *
     * @param accuracyLoadCityFcDO   准确率
     * @param dispersionLoadCityFcDO 偏差率
     * @param standardAccuracy       考核标准准确率
     */
    public StatisticsCityDayFcRecallDO statistics(List<LoadCityHisDO> loadCityHisDOS, LoadCityFcDO loadCityFcDO,
                                                  AccuracyLoadCityFcDO accuracyLoadCityFcDO, DispersionLoadCityFcDO dispersionLoadCityFcDO,
                                                  DeviationLoadCityFcDO deviationLoadCityFcVO, BigDecimal standardAccuracy) throws Exception {

        if (accuracyLoadCityFcDO == null) {
            log.error("统计预测结果有误：缺失准确率");
            return null;
        }

        String cityId = accuracyLoadCityFcDO.getCityId();
        String algorithmId = accuracyLoadCityFcDO.getAlgorithmId();
        String caliberId = accuracyLoadCityFcDO.getCaliberId();
        java.sql.Date date = accuracyLoadCityFcDO.getDate();

        if (cityId == null
                || !(dispersionLoadCityFcDO != null && cityId.equals(dispersionLoadCityFcDO.getCityId()))
                || !(deviationLoadCityFcVO != null && cityId.equals(deviationLoadCityFcVO.getCityId()))
        ) {
            log.error("统计预测结果有误：准确率、偏差率、离散率的城市不一致，不可以统计");
            return null;
        }

        if (algorithmId == null
                || !(dispersionLoadCityFcDO != null && algorithmId.equals(dispersionLoadCityFcDO.getAlgorithmId()))
                || !(deviationLoadCityFcVO != null && algorithmId.equals(deviationLoadCityFcVO.getAlgorithmId()))
        ) {
            log.error("统计预测结果有误：准确率、偏差率、离散率算法不一致，不可以统计");
            return null;
        }

        if (date == null
                || !(dispersionLoadCityFcDO != null && date.equals(dispersionLoadCityFcDO.getDate()))
                || !(deviationLoadCityFcVO != null && date.equals(deviationLoadCityFcVO.getDate()))
        ) {
            log.error("统计预测结果有误：准确率、偏差率、离散率、合格率的日期不一致，不可以统计");
            throw TsieExceptionUtils.newBusinessException("T706", "统计预测结果有误：准确率、偏差率、离散率日期不一致，不可以统计");
        }
        if (caliberId == null
                || !(dispersionLoadCityFcDO != null && caliberId.equals(dispersionLoadCityFcDO.getCaliberId()))
                || !(deviationLoadCityFcVO != null && caliberId.equals(deviationLoadCityFcVO.getCaliberId()))
        ) {
            log.error("统计预测结果有误：准确率、偏差率、离散率、合格率的口径不一致，不可以统计");
            throw TsieExceptionUtils.newBusinessException("T706", "统计预测结果有误：准确率、偏差率、离散率口径不一致，不可以统计");
        }

        StatisticsCityDayFcRecallDO statisticsCityDayFcDO = new StatisticsCityDayFcRecallDO();
        statisticsCityDayFcDO.setCityId(cityId);
        statisticsCityDayFcDO.setAlgorithmId(algorithmId);
        statisticsCityDayFcDO.setCaliberId(caliberId);
        statisticsCityDayFcDO.setDate(date);
        statisticsCityDayFcDO.setReport(accuracyLoadCityFcDO.getReport());

        /*// 准确率   准确率修改为rmspe
        if (accuracyLoadCityFcDO != null) {
            statisticsCityDayFcDO.setAccuracy(BigDecimalUtils.avgList(BasePeriodUtils.toList(accuracyLoadCityFcDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO), 4, false));
        }*/

        Map<String, LoadCityHisDO> loadCityHisVOMap = new HashMap<>();
        if (loadCityHisDOS != null) {
            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS) {
                String key =
                        loadCityHisDO.getCityId() + "-" + loadCityHisDO.getCaliberId() + "-" + loadCityHisDO.getDate()
                                .getTime();
                loadCityHisVOMap.put(key, loadCityHisDO);
            }
        }
        /**
         * 预测数据的key
         */
        BigDecimal dayAccuracy = null;
        if (loadCityFcDO != null) {
            String fcKey =
                    loadCityFcDO.getCityId() + "-" + loadCityFcDO.getCaliberId() + "-" + loadCityFcDO.getDate().getTime();
            LoadCityHisDO loadCityHisDO = loadCityHisVOMap.get(fcKey);
            //获取单日的平均准确率
            dayAccuracy = LoadCalUtil.getDayAccuracy(loadCityHisDO, loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM, null);
            statisticsCityDayFcDO.setAccuracy(dayAccuracy);

        }
        // 偏差率   目前库中为了做置信上限or下限    偏差率的表存储为带符号的 + -都有 所以在做偏差率的计算 需要将数据做abs()函数处理
        if (deviationLoadCityFcVO != null) {
            List<BigDecimal> list = BasePeriodUtils.toList(deviationLoadCityFcVO, Constants.LOAD_CURVE_POINT_NUM,
                    Constants.LOAD_CURVE_START_WITH_ZERO);
            List<BigDecimal> bigDecimals = new ArrayList<>();
            for (BigDecimal bigDecimal : list) {
                if (bigDecimal == null) {
                    continue;
                }
                bigDecimals.add(bigDecimal.abs());
            }
            BigDecimal decimal = BigDecimalUtils.avgList(bigDecimals, 4, false);
            statisticsCityDayFcDO.setDeviation(decimal);
        }

        // 离散率
        if (dispersionLoadCityFcDO != null) {
            statisticsCityDayFcDO
                    .setDispersion(LoadCalUtil.getStandardDeviation(BigDecimalUtils.avgList(BasePeriodUtils.toList(
                            dispersionLoadCityFcDO, Constants.LOAD_CURVE_POINT_NUM,
                            Constants.LOAD_CURVE_START_WITH_ZERO), 4, false)));
        }
        // 合格率  合格率经过确认计算错误  所以废弃了合格率的的表  最终统计的只有合格或者不合格
     /*   if (passLoadCityFcVO != null) {
            int passCount = 0;
            int totalCount = 0;
            for (BigDecimal pass : BasePeriodUtils.toList(passLoadCityFcVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO)) {
                if (pass != null) {
                    if (pass.compareTo(BigDecimal.ZERO) > 0) {
                        passCount++;
                    }
                    totalCount++;
                }
            }
        if (totalCount != 0) {
            statisticsCityDayFcDO.setPass(BigDecimalUtils.divide(new BigDecimal(passCount), new BigDecimal(totalCount), 4));
        }
    }*/
        if (dayAccuracy != null && standardAccuracy != null) {  //如果准确率大于标准准确率 则合格率为1 为合格 否则为不合格 为0
            if (dayAccuracy.compareTo(standardAccuracy) >= 0) {
                statisticsCityDayFcDO.setPass(new BigDecimal(1));
            } else {
                statisticsCityDayFcDO.setPass(new BigDecimal(0));
            }
        }
        statisticsCityDayFcDO.setStandardAccuracy(standardAccuracy);
        return statisticsCityDayFcDO;
    }


    /**
     * 统计一天预测结果情况
     *
     * @param accuracyLoadCityFcDOS   准确率
     * @param dispersionLoadCityFcDOS 偏差率
     * @param deviationLoadCityFcVOs  离散率
     * @param settingReportDOS        考核设置
     */
    public List<StatisticsCityDayFcRecallDO> statistics(List<LoadCityHisDO> loadCityHisDOS, List<LoadCityFcDO> loadCityFcDOS,
                                                        List<AccuracyLoadCityFcDO> accuracyLoadCityFcDOS, List<DispersionLoadCityFcDO> dispersionLoadCityFcDOS,
                                                        List<DeviationLoadCityFcDO> deviationLoadCityFcVOs, List<SettingReportDO> settingReportDOS) throws Exception {

        // 准确率list to map
        Map<String, AccuracyLoadCityFcDO> accuracyLoadCityFcVOMap = new HashMap<String, AccuracyLoadCityFcDO>();
        if (accuracyLoadCityFcDOS != null) {
            for (AccuracyLoadCityFcDO accuracyLoadCityFcDO : accuracyLoadCityFcDOS) {
                String key = accuracyLoadCityFcDO.getCityId() + "-" + accuracyLoadCityFcDO.getCaliberId() + "-"
                        + accuracyLoadCityFcDO
                        .getAlgorithmId() + "-" + accuracyLoadCityFcDO.getDate().getTime();
                accuracyLoadCityFcVOMap.put(key, accuracyLoadCityFcDO);
            }
        }
        // 离散度list to map
        Map<String, DispersionLoadCityFcDO> dispersionLoadCityFcVOMap = new HashMap<String, DispersionLoadCityFcDO>();
        if (dispersionLoadCityFcDOS != null) {
            for (DispersionLoadCityFcDO dispersionLoadCityFcDO : dispersionLoadCityFcDOS) {
                String key = dispersionLoadCityFcDO.getCityId() + "-" + dispersionLoadCityFcDO.getCaliberId() + "-"
                        + dispersionLoadCityFcDO
                        .getAlgorithmId() + "-" + dispersionLoadCityFcDO.getDate().getTime();
                dispersionLoadCityFcVOMap.put(key, dispersionLoadCityFcDO);
            }
        }
        // 偏差率list to map
        Map<String, DeviationLoadCityFcDO> deviationLoadCityFcVOMap = new HashMap<String, DeviationLoadCityFcDO>();
        if (deviationLoadCityFcVOs != null) {
            for (DeviationLoadCityFcDO deviationLoadCityFcVO : deviationLoadCityFcVOs) {
                String key = deviationLoadCityFcVO.getCityId() + "-" + deviationLoadCityFcVO.getCaliberId() + "-"
                        + deviationLoadCityFcVO.getAlgorithmId() + "-" + deviationLoadCityFcVO.getDate().getTime();
                deviationLoadCityFcVOMap.put(key, deviationLoadCityFcVO);
            }
        }
        // 预测负荷list to map
        Map<String, LoadCityFcDO> loadCityFcVOMap = new HashMap<>();
        if (loadCityFcDOS != null) {
            for (LoadCityFcDO loadCityFcDO : loadCityFcDOS) {
                String key =
                        loadCityFcDO.getCityId() + "-" + loadCityFcDO.getCaliberId() + "-" + loadCityFcDO.getAlgorithmId()
                                + "-" + loadCityFcDO.getDate().getTime();
                loadCityFcVOMap.put(key, loadCityFcDO);
            }
        }

        // Map<城市ID,考核准确率>
        Map<String, BigDecimal> standardAccuracyMap = new HashMap<String, BigDecimal>();
        if (settingReportDOS != null) {
            for (SettingReportDO settingReportDO : settingReportDOS) {
                standardAccuracyMap.put(settingReportDO.getCityId(), settingReportDO.getStandardAccuracy());
            }
        }

        List<StatisticsCityDayFcRecallDO> statisticsCityDayFcDOS = new ArrayList<StatisticsCityDayFcRecallDO>();
        for (String key : accuracyLoadCityFcVOMap.keySet()) {
            try {
                BigDecimal standardAccuracy = standardAccuracyMap.get(key.substring(0, key.indexOf("-")));
                StatisticsCityDayFcRecallDO statisticsCityDayFcDO = statistics(loadCityHisDOS, loadCityFcVOMap.get(key),
                        accuracyLoadCityFcVOMap.get(key), dispersionLoadCityFcVOMap.get(key),
                        deviationLoadCityFcVOMap.get(key), standardAccuracy);
                if (statisticsCityDayFcDO != null) {
                    statisticsCityDayFcDOS.add(statisticsCityDayFcDO);
                }
            } catch (Exception e) {
                log.error("统计预测结果出错了", e);
            }
        }

        return statisticsCityDayFcDOS;

    }


}