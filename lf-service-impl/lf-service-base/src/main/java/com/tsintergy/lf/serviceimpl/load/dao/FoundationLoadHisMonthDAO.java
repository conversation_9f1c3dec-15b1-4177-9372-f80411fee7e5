/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.load.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.FoundationLoadHisMonthDO;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

/**
 * Description:<br>
 *
 * @Author:<EMAIL>
 * @Date: 2022/5/24 10:49
 * @Version:1.0.0
 */
public class FoundationLoadHisMonthDAO extends BaseAbstractDAO<FoundationLoadHisMonthDO> {

    public List<FoundationLoadHisMonthDO> getFoundationLoadHisMonth(String cityId, String caliberId, String year,
        String month,Integer type) {
        DBQueryParamBuilder paramBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if (StringUtils.isNotBlank(cityId)) {
            paramBuilder.where(QueryOp.StringEqualTo, "cityId", cityId);
        }
        if (StringUtils.isNotBlank(caliberId)) {
            paramBuilder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (StringUtils.isNotBlank(year)) {
            paramBuilder.where(QueryOp.StringEqualTo, "year" + "", year);
        }
        if (StringUtils.isNotBlank(month)) {
            paramBuilder.where(QueryOp.StringEqualTo, "month" + "", month);
        }
        if (type != null) {
            paramBuilder.where(QueryOp.StringEqualTo, "type" + "", type);
        }
        return this.query(paramBuilder.build()).getDatas();
    }
}