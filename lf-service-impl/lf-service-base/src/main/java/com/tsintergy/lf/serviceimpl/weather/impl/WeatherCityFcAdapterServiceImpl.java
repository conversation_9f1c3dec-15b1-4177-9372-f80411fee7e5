package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsintergy.lf.core.enums.WeatherSourceEnum;
import com.tsintergy.lf.serviceapi.base.forecast.api.AlgorithmService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcAdapterService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcMeteoService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayFcService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcMeteoDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayFcDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcDAO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service("weatherCityFcAdapterService")
public class WeatherCityFcAdapterServiceImpl implements WeatherCityFcAdapterService {

    @Autowired
    private WeatherCityFcDAO weatherCityFcDAO;

    @Autowired
    private WeatherCityFcMeteoService weatherCityFcMeteoService;

    @Autowired
    private AlgorithmService algorithmService;

    @Autowired
    private WeatherFeatureCityDayFcService weatherFeatureCityDayFcService;

    @Autowired
    private WeatherFeatureStatServiceImpl weatherFeatureStatService;

    @Override
    public List<WeatherCityFcDO> findFcWeather(String cityId, String algorithmId, Integer type, Date start, Date end) throws Exception {
        if (StringUtils.isEmpty(algorithmId)) {
            return weatherCityFcDAO.findWeatherCityFcDO(cityId, type, start, end);
        }
        AlgorithmDO algorithmDO = algorithmService.getAlgorithmDOById(algorithmId);
        if (algorithmDO == null || algorithmDO.getFcWeatherSource().equals(WeatherSourceEnum.FC.name())) {
            return weatherCityFcDAO.findWeatherCityFcDO(cityId, type, start, end);
        }
        if (algorithmDO.getFcWeatherSource().equalsIgnoreCase(WeatherSourceEnum.METEO.name())) {
            List<WeatherCityFcDO> fcMeteoDOS = getWeatherCityFcDOS(cityId, type, start, end);
            if (fcMeteoDOS != null) return fcMeteoDOS;
        }
        return weatherCityFcDAO.findWeatherCityFcDO(cityId, type, start, end);
    }

    @Override
    public List<WeatherCityFcDO> findWeatherCityFcDOS(String sourceType, String cityId, Integer type, Date start, Date end) throws Exception {
        if (WeatherSourceEnum.METEO.getName().equals(sourceType)) {
            List<WeatherCityFcDO> fcMeteoDOS = getWeatherCityFcDOS(cityId, type, start, end);
            if (fcMeteoDOS != null) return fcMeteoDOS;
        }
        return weatherCityFcDAO.findWeatherCityFcDO(cityId, type, start, end);
    }

    private List<WeatherCityFcDO> getWeatherCityFcDOS(String cityId, Integer type, Date start, Date end) {
        List<WeatherCityFcMeteoDO> fcMeteoDOS = weatherCityFcMeteoService.getListByCondition(cityId, type, start, end);
        if (CollectionUtils.isNotEmpty(fcMeteoDOS)) {
            return fcMeteoDOS.stream().map(
                    src -> {
                        WeatherCityFcDO weatherCityFcDO = new WeatherCityFcDO();
                        BeanUtils.copyProperties(src, weatherCityFcDO);
                        return weatherCityFcDO;
                    }).collect(Collectors.toList());
        }
        return null;
    }


    @Override
    public List<WeatherCityFcDO> findFcWeather(String sourceType, String cityId, Date start, Date endDate) throws Exception {
        return this.findWeatherCityFcDOS(sourceType, cityId, null, start, endDate);
    }

    @Override
    public List<WeatherFeatureCityDayFcDO> findWeatherFeatureCityDayFcDO(String sourceType, String cityId, Date startDate, Date endDate) throws Exception {
        if (WeatherSourceEnum.METEO.name().equals(sourceType)) {
            List<WeatherCityFcDO> weatherCityFcDOS =finMeteoDOS(cityId, null, startDate, endDate);
            return weatherFeatureStatService.statisticsDayFeatureInDAO(weatherCityFcDOS);
        }
        return weatherFeatureCityDayFcService.getWeatherFeatureCityDayFcDOList(cityId, startDate, endDate);
    }

    @Override
    public List<WeatherFeatureCityDayFcDO> findWeatherFeatureByAlgorithmId(String algorithmId, String cityId, Date startDate, Date endDate) throws Exception {
        if (StringUtils.isEmpty(algorithmId)) {
            return weatherFeatureCityDayFcService.getWeatherFeatureCityDayFcDOList(cityId, startDate, endDate);
        }
        AlgorithmDO algorithmDO = algorithmService.getAlgorithmDOById(algorithmId);
        if (algorithmDO == null || algorithmDO.getFcWeatherSource().equals(WeatherSourceEnum.FC.name())) {
            return weatherFeatureCityDayFcService.getWeatherFeatureCityDayFcDOList(cityId, startDate, endDate);
        }

        if (algorithmDO.getFcWeatherSource().equalsIgnoreCase(WeatherSourceEnum.METEO.name())) {
            List<WeatherCityFcDO> weatherCityFcDOS =finMeteoDOS(cityId, null, startDate, endDate);
            return weatherFeatureStatService.statisticsDayFeatureInDAO(weatherCityFcDOS);
        }
        return weatherFeatureCityDayFcService.getWeatherFeatureCityDayFcDOList(cityId, startDate, endDate);
    }

    List<WeatherCityFcDO> finMeteoDOS(String cityId, Integer type, Date startDate, Date endDate) {
        List<WeatherCityFcMeteoDO> fcMeteoDOS = weatherCityFcMeteoService.getListByCondition(cityId, null, startDate, endDate);
        return fcMeteoDOS.stream().map(
                src -> {
                    WeatherCityFcDO weatherCityFcDO = new WeatherCityFcDO();
                    BeanUtils.copyProperties(src, weatherCityFcDO);
                    return weatherCityFcDO;
                }).collect(Collectors.toList());
    }

    @Override
    public List<WeatherCityFcDO> findFcWeather(List<String> cityIds, String algorithmId, Integer type, Date start, Date end) throws Exception {
        if (StringUtils.isEmpty(algorithmId)) {
            return weatherCityFcDAO.findWeatherCityHisDOs(cityIds, type, start, end);
        }
        AlgorithmDO algorithmDO = algorithmService.getAlgorithmDOById(algorithmId);
        if (algorithmDO == null || algorithmDO.getFcWeatherSource().equals(WeatherSourceEnum.FC.name())) {
            return weatherCityFcDAO.findWeatherCityHisDOs(cityIds, type, start, end);
        }
        if (algorithmDO.getFcWeatherSource().equalsIgnoreCase(WeatherSourceEnum.METEO.getName())) {
            return weatherCityFcMeteoService.getWeatherCityFcDOS(cityIds, type, start, end).stream().map(
                    src -> {
                        WeatherCityFcDO weatherCityFcDO = new WeatherCityFcDO();
                        BeanUtils.copyProperties(src, weatherCityFcDO);
                        return weatherCityFcDO;
                    }).collect(Collectors.toList());
        }
        return weatherCityFcDAO.findWeatherCityHisDOs(cityIds, type, start, end);
    }
}
