/**
 * Copyright(C),2015‐2019,北京清能互联科技有限公司 Author:yangjin Date:2019/6/2513:40 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.typhoon.impl;

import com.google.common.collect.Lists;
import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.typhoon.api.ExtremeWeatherService;
import com.tsintergy.lf.serviceapi.base.typhoon.dto.TyphoonCorrelationDTO;
import com.tsintergy.lf.serviceapi.base.typhoon.dto.TyphoonScatterDTO;
import com.tsintergy.lf.serviceapi.base.typhoon.dto.TyphoonScatterRequestv2DTO;
import com.tsintergy.lf.serviceapi.base.typhoon.pojo.TyphoonArchiveDO;
import com.tsintergy.lf.serviceapi.base.typhoon.pojo.TyphoonHisDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceimpl.base.dao.CityDAO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadFeatureCityDayHisDAO;
import com.tsintergy.lf.serviceimpl.typhoon.dao.TyphoonHisDAO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherFeatureCityDayHisDAO;
import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import org.apache.commons.math3.stat.correlation.PearsonsCorrelation;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * Description:极端天气业务实现类 <br>
 *
 * <AUTHOR>
 * @create 2019/6/25
 * @since 1.0.0
 */
@Service("extremeWeatherService")
public class ExtremeWeatherServiceImpl extends BaseServiceImpl implements ExtremeWeatherService {

    private static final Logger logger = LogManager.getLogger(ExtremeWeatherServiceImpl.class);

    @Autowired
    private CityService cityService;

    @Autowired
    WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    WeatherFeatureCityDayHisDAO weatherFeatureCityDayHisDAO;

    @Autowired
    CityDAO cityDAO;

    @Autowired
    LoadFeatureCityDayHisDAO loadFeatureCityDayHisDAO;

    @Autowired
    TyphoonHisDAO typhoonHisDao;


    /**
     * 功能描述:根据日期和城市查询天气特性 <br>
     * 〈〉
     *
     * @param typhoonScatterRequestv2DTO
     * @return:java.util.List<com.load.weather.persistent.WeatherFeatureCityDayHisDO>
     * @since: 1.0.0
     * @Author:yangjin
     * @Date: 2019/6/28 17:40
     */
    @Override
    public List<WeatherFeatureCityDayHisDO> getTyphoonCalender(TyphoonScatterRequestv2DTO typhoonScatterRequestv2DTO)
        throws Exception {
        DateFormat formate = new SimpleDateFormat(DateUtil.DATE_FORMAT2);
        List<WeatherFeatureCityDayHisDO> vOs = null;
        Date startDate = formate.parse(typhoonScatterRequestv2DTO.getStartDate());
        Date endDate = formate.parse(typhoonScatterRequestv2DTO.getEndDate());


        //查询城市是否存在
        CityDO CityDO = cityService.findCityById(typhoonScatterRequestv2DTO.getCityId());
        if (CityDO == null) {
            logger.error("查询城市为空，cityId:" + CityDO.getId());
            throw new BusinessException("T706", "");
        }

        //如果该城市是省,就用省会市去查询weather_feature_city_day_his_service
        if (CityDO.getType() == 1) {
            CityDO cityOfProvince = findCityByBelongIdAndOrderNo(2, CityDO.getBelongId());
            String cityId = cityService.findWeatherCityId(cityOfProvince.getId());
            vOs = weatherFeatureCityDayHisDAO
                .getWeatherFeatureCityDayHisDOs(cityId, startDate, endDate);
        } else {
            vOs = weatherFeatureCityDayHisDAO
                .getWeatherFeatureCityDayHisDOs(typhoonScatterRequestv2DTO.getCityId(), startDate, endDate);
        }


        return vOs;
    }


    /**
     * 功能描述: 查询极端天气灵敏度<br>
     * 〈〉
     *
     * @param dates
     * @param rowName
     * @param columnName
     * @param cityId
     * @param caliberId
     * @return:java.util.List<com.load.typhoon.dto.TyphoonScatterDTO>
     * @since: 1.0.0
     * @Author:yangjin
     * @Date: 2019/6/28 17:39
     */
    @Override
    public List<TyphoonScatterDTO> getTyphoonWeatherScatter(List<Date> dates, String rowName, String columnName,
        String cityId, String caliberId) throws Exception {
        List<TyphoonScatterDTO> results = Lists.newArrayList();
        CityDO CityDO = cityDAO.findByPk(cityId);
        if (CityDO == null) {
            logger.error("查询城市为空，cityId:" + CityDO.getId());
            throw new BusinessException("T706", "");
        }
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = loadFeatureCityDayHisDAO
            .getLoadFeatureCityDayHisDOs(cityId, dates, caliberId);
        if (CollectionUtils.isEmpty(loadFeatureCityDayHisDOS)) {
            logger.error("该城市的出力特性不存在,cityId:" + cityId);
            throw new BusinessException("T706", "");
        }
        cityId = cityService.findWeatherCityId(cityId);
        List<WeatherFeatureCityDayHisDO> WeatherFeatureCityDayHisDOS = weatherFeatureCityDayHisService
            .listWeatherFeatureCityDayHisDO(dates, cityId);
        if (CollectionUtils.isEmpty(WeatherFeatureCityDayHisDOS)) {
            logger.info("该城市的气象信息不存在,cityId为:" + cityId);
            throw new BusinessException("T706", "");
        }
        //比较天气特征和出力特征数据的大小是否相同，不同需要做处理
        wearherAndLoadIsEqual(WeatherFeatureCityDayHisDOS, loadFeatureCityDayHisDOS);
        //获得灵敏度分析结果
        results = getScatterValues(rowName, columnName, results, loadFeatureCityDayHisDOS, WeatherFeatureCityDayHisDOS);
        return results;

    }


    private List<TyphoonScatterDTO> getScatterValues(String rowName, String columnName, List<TyphoonScatterDTO> results,
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS,
        List<WeatherFeatureCityDayHisDO> WeatherFeatureCityDayHisDOS)
        throws IllegalAccessException, InvocationTargetException {


        if (WeatherFeatureCityDayHisDOS.size() == loadFeatureCityDayHisDOS.size()) {
            for (int i = 0; i < WeatherFeatureCityDayHisDOS.size(); i++) {
                //处理横坐标
                Method rMethod = getMethodByName(rowName, LoadFeatureCityDayHisDO.class);
                if (rMethod == null) {
                    throw new BusinessException("", "该" + rowName + "对应的字段不存在");
                }
                BigDecimal rValue = (BigDecimal) rMethod.invoke(loadFeatureCityDayHisDOS.get(i), null);


                //处理纵坐标
                Method cMethod = getMethodByName(columnName, WeatherFeatureCityDayHisDO.class);
                if (cMethod == null) {
                    throw new BusinessException("", "该" + columnName + "对应的字段不存在");
                }
                BigDecimal cValue = (BigDecimal) cMethod.invoke(WeatherFeatureCityDayHisDOS.get(i), null);

                TyphoonScatterDTO dto = new TyphoonScatterDTO();
                dto.setDate(WeatherFeatureCityDayHisDOS.get(i).getDate());
                dto.setRow(rValue);
                dto.setColumn(cValue);
                results.add(dto);
            }
            return results;
        } else {
            logger.error("天气特征数据和出力特征数据不相等");
            throw new BusinessException("T706", "T706");
        }
    }


    private List<TyphoonCorrelationDTO> getPeason(double[] maxL, double[] minL, double[] aveL, double[] difL,
        double[] graL, List<WeatherFeatureCityDayHisDO> WeatherFeatureCityDayHisDOS) {
        List<TyphoonCorrelationDTO> results = Lists.newArrayList();
        double[] highestTemperature;
        double[] aveTemperature;
        double[] lowestTemperature;
        double[] maxWinds;
        double[] aveWinds;
        double[] rainfall;
        double[] minWinds;
        double[] aveHumidity;
        //最高温度
        highestTemperature = new double[WeatherFeatureCityDayHisDOS.size()];
        //平均温度
        aveTemperature = new double[WeatherFeatureCityDayHisDOS.size()];
        //最低温度
        lowestTemperature = new double[WeatherFeatureCityDayHisDOS.size()];
        //最大风速
        maxWinds = new double[WeatherFeatureCityDayHisDOS.size()];
        //平均风速
        aveWinds = new double[WeatherFeatureCityDayHisDOS.size()];
        //日降水量
        rainfall = new double[WeatherFeatureCityDayHisDOS.size()];

        //最低风速
        minWinds = new double[WeatherFeatureCityDayHisDOS.size()];

        //平均相对湿度
        aveHumidity = new double[WeatherFeatureCityDayHisDOS.size()];

        //气象类型(1：湿度，2：温度，3：降雨量，4：风速)
        for (int i = 0; i < WeatherFeatureCityDayHisDOS.size(); i++) {
            WeatherFeatureCityDayHisDO vo = WeatherFeatureCityDayHisDOS.get(i);
            if (vo.getHighestTemperature() != null){
                highestTemperature[i] = vo.getHighestTemperature().doubleValue();
            }
            if (vo.getAveTemperature() != null){
                aveTemperature[i] = vo.getAveTemperature().doubleValue();
            }
            if (vo.getLowestTemperature() != null){
                lowestTemperature[i] = vo.getLowestTemperature().doubleValue();
            }
            if (vo.getMaxWinds() != null) {
                maxWinds[i] = vo.getMaxWinds().doubleValue();
            }
            if (vo.getAveWinds() != null) {
                aveWinds[i] = vo.getAveWinds().doubleValue();
            }
            if (vo.getRainfall() != null) {
                rainfall[i] = vo.getRainfall().doubleValue();
            }
            if (vo.getMinWinds() != null) {
                minWinds[i] = vo.getMinWinds().doubleValue();
            }
            if (vo.getAveHumidity() != null) {
                aveHumidity[i] = vo.getAveHumidity().doubleValue();
            }
        }
        List<double[]> columnList = Lists.newArrayList();
        columnList.add(highestTemperature);    //第0列 日最高温 （0，0）（1，0）（2，0） 最高温与x轴的相关性系数
        columnList.add(aveTemperature);        //第1列 日平均温
        columnList.add(lowestTemperature);     //第2列 日最低温
        columnList.add(aveHumidity);           //第3列 日平均相对湿度
        columnList.add(maxWinds);              //第4列 日最大风速
        columnList.add(aveWinds);              //第5列 日平均风速
        columnList.add(minWinds);              //第6列 日最低风速
        columnList.add(rainfall);              //第7列 日降雨量


        List<double[]> rowList = Lists.newArrayList();
        rowList.add(maxL);                      //第0行 最大负荷 (0,1) (0,2) (0,3) (0,4)
        rowList.add(minL);                      //第1行 最小负荷
        rowList.add(aveL);                      //第2行 平均负荷
        rowList.add(difL);                      //第3行 峰谷差
        rowList.add(graL);                      //第4行 峰谷差率


        PearsonsCorrelation t = new PearsonsCorrelation();
        for (int i = 0; i < rowList.size(); i++) {
            for (int j = 0; j < columnList.size(); j++) {
                double v = t.correlation(rowList.get(i), columnList.get(j));
                TyphoonCorrelationDTO dto = new TyphoonCorrelationDTO();
                dto.setX(i);
                dto.setY(j);
                try {
                    dto.setV(new BigDecimal(v));
                } catch (NumberFormatException e) {
                    dto.setV(new BigDecimal(0));
                }
                results.add(dto);
            }
        }
        return results;
    }


    private void completeArrs(List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS, double[] maxL, double[] minL,
        double[] aveL, double[] difL, double[] graL) {
        for (int i = 0; i < loadFeatureCityDayHisDOS.size(); i++) {
            LoadFeatureCityDayHisDO vo = loadFeatureCityDayHisDOS.get(i);
            maxL[i] = vo.getMaxLoad().doubleValue();
            minL[i] = vo.getMinLoad().doubleValue();
            aveL[i] = vo.getAveLoad().doubleValue();
            difL[i] = vo.getDifferent().doubleValue();
            graL[i] = vo.getGradient().doubleValue();
        }
    }


    private static Method getMethodByName(String methodName, Class clazz) {
        try {
            BeanInfo beanInfo = Introspector.getBeanInfo(clazz);
            PropertyDescriptor[] pds = beanInfo.getPropertyDescriptors();
            for (PropertyDescriptor pd : pds) {
                if (pd.getName().equals(methodName)) {
                    return pd.getReadMethod();
                }
            }
        } catch (IntrospectionException e) {
            e.printStackTrace();
        }
        return null;
    }


    @Override
    public List<TyphoonHisDO> listTyphoonHisVO(Date startDate, Date endDate) throws Exception {
        return typhoonHisDao.listTyphoonHisVOs(startDate, endDate);
    }


    /**
     * 功能描述:计算极端天气相关性分析数据 <br>
     * 〈〉
     *
     * @param
     * @param cityId
     * @param caliberId
     * @return:java.util.List<com.load.typhoon.dto.TyphoonCorrelationDTO>
     * @since: 1.0.0
     * @Author:yangjin
     * @Date: 2019/6/28 17:38
     */
    @Override
    public List<TyphoonCorrelationDTO> getTyphoonCorrelation(List<Date> dateList, String cityId, String caliberId)
        throws Exception {
        List<TyphoonCorrelationDTO> results;
        DateFormat formate = new SimpleDateFormat(DateUtil.DATE_FORMAT2);
        CityDO CityDO = (CityDO) cityDAO.findByPk(cityId);
        if (CityDO == null) {
            logger.error("查询城市为空，cityId:" + CityDO.getId());
            throw new BusinessException("T706", "");
        }
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = loadFeatureCityDayHisDAO
            .getLoadFeatureCityDayHisDOs(cityId, dateList, caliberId);
        if (CollectionUtils.isEmpty(loadFeatureCityDayHisDOS)) {
            logger.error("该城市的出力气象数据不存在,cityId:" + cityId);
            throw new BusinessException("T706", "");
        }
        cityId = cityService.findWeatherCityId(cityId);
        List<WeatherFeatureCityDayHisDO> WeatherFeatureCityDayHisDOS = weatherFeatureCityDayHisService
            .listWeatherFeatureCityDayHisDO(dateList, cityId);
        if (CollectionUtils.isEmpty(WeatherFeatureCityDayHisDOS)) {
            logger.info("该城市的气象信息不存在,cityId为:" + cityId);
            throw new BusinessException("T706", "");
        }
        wearherAndLoadIsEqual(WeatherFeatureCityDayHisDOS, loadFeatureCityDayHisDOS);
        //最大负荷
        double[] maxL = new double[loadFeatureCityDayHisDOS.size()];
        //最小负荷
        double[] minL = new double[loadFeatureCityDayHisDOS.size()];
        //平均负荷
        double[] aveL = new double[loadFeatureCityDayHisDOS.size()];
        //峰谷差
        double[] difL = new double[loadFeatureCityDayHisDOS.size()];
        //峰谷差率
        double[] graL = new double[loadFeatureCityDayHisDOS.size()];
        completeArrs(loadFeatureCityDayHisDOS, maxL, minL, aveL, difL, graL);
        results = getPeason(maxL, minL, aveL, difL, graL, WeatherFeatureCityDayHisDOS);
        return results;
    }


    /**
     * 功能描述: 根据orderNo和 belongId查询城市<br>
     * 〈〉
     *
     * @param orderNo
     * @param belongId
     * @return:com.load.base.persistent.CityDO
     * @since: 1.0.0
     * @Author:yangjin
     * @Date: 2019/6/28 17:36
     */
    public CityDO findCityByBelongIdAndOrderNo(Integer orderNo, String belongId) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create()
            .where(QueryOp.StringEqualTo, "belongId", belongId)
            .where(QueryOp.NumberEqualTo, "orderNo", orderNo)
            .build();
        return (CityDO) cityDAO.query(param).getDatas().get(0);
    }


    /**
     * 功能描述: 根据时间和城市查询台风影响日期（从开始时间到消亡时间）<br>
     * 〈〉
     *
     * @param startDate
     * @param endDate
     * @param cityId
     * @return:java.util.List<java.lang.String>
     * @since: 1.0.0
     * @Author:yangjin
     * @Date: 2019/6/27 17:23
     */
    @Override
    public List<String> findTyphoonDate(Date startDate, Date endDate, String cityId) throws Exception {


        List<String> dateList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        List<Date> startTimeAndEndTimeList = DateUtil.getListBetweenDay(startDate, endDate);
        DataPackage dataPackage = getTyphoonByStartAndEnd(startDate, endDate, cityId);
        List<TyphoonArchiveDO> datas = (List<TyphoonArchiveDO>) dataPackage.getDatas();
        Set<Date> typhoonDates = new HashSet<>();


        //查出台风影响日期集合
        datas.forEach((TyphoonArchiveDO t) -> {
            List<Date> betweenDate = DateUtil.getListBetweenDay(t.getLoginTime(), t.getEndTime());
            typhoonDates.addAll(betweenDate);
        });

        //比较台风影响日期和当前查询日期
        List<Date> dates = new ArrayList<>();
        startTimeAndEndTimeList.forEach(t -> {
            if (typhoonDates.contains(t)) {
                dateList.add(sdf.format(t));

            }
        });

        return dateList;
    }


    /**
     * 功能描述:根据时间段,城市id查询该时间段里有哪些台风 <br>
     * 〈〉
     *
     * @param startDate
     * @param endDate
     * @param cityId
     * @return:com.tsie.core.infrastructure.db.DataPackage
     * @since: 1.0.0
     * @Author:yangjin
     * @Date: 2019/6/27 17:30
     */
    @Override
    public DataPackage getTyphoonByStartAndEnd(Date startDate, Date endDate, String cityId) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create()
            .where(QueryOp.NumberEqualTo, "flag", true)
            .where(QueryOp.DateNoLessThan, "endTime", new java.sql.Date(startDate.getTime()))
            .where(QueryOp.DateNoMoreThan, "loginTime", new java.sql.Date(endDate.getTime()))
            .where(QueryOp.StringEqualTo, "cityId", cityId)
            .build();
        return typhoonHisDao.query(param);

    }


    /**
     * 功能描述:比较天气和出力特征的数据数量是否相同，不同需要做出处理 <br>
     * 〈〉
     *
     * @param WeatherFeatureCityDayHisDOS
     * @param loadFeatureCityDayHisDOS
     * @return:void
     * @since: 1.0.0
     * @Author:yangjin
     * @Date: 2019/6/28 17:35
     */
    public void wearherAndLoadIsEqual(List<WeatherFeatureCityDayHisDO> WeatherFeatureCityDayHisDOS,
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS) {
        SimpleDateFormat formate = new SimpleDateFormat(DateUtil.DATE_FORMAT2);


        List<String> wList = new ArrayList<>();
        List<String> lList = new ArrayList<>();

        WeatherFeatureCityDayHisDOS.forEach(t -> {
            wList.add(formate.format(t.getDate()));
        });

        loadFeatureCityDayHisDOS.forEach(t -> {
            lList.add(formate.format(t.getDate()));
        });

        //取两个日期的集合的并集
        wList.retainAll(lList);
        for (Iterator<WeatherFeatureCityDayHisDO> ite = WeatherFeatureCityDayHisDOS.iterator(); ite.hasNext(); ) {
            WeatherFeatureCityDayHisDO next = ite.next();
            if (!wList.contains(formate.format(next.getDate()))) {
                ite.remove();
            }
        }
        for (Iterator<LoadFeatureCityDayHisDO> ite = loadFeatureCityDayHisDOS.iterator(); ite.hasNext(); ) {
            LoadFeatureCityDayHisDO next = ite.next();
            if (!wList.contains(formate.format(next.getDate()))) {
                ite.remove();
            }
        }
        if (WeatherFeatureCityDayHisDOS.size() < 2) {
            logger.error("该城市的天气特征数据少于两天");
            throw new BusinessException("T706", "");
        }

        if (loadFeatureCityDayHisDOS.size() < 2) {
            logger.error("该城市的出力特征数据少于两天");
            throw new BusinessException("T706", "");
        }

    }
}