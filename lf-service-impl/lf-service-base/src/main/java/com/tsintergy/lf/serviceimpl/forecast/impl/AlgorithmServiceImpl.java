
package com.tsintergy.lf.serviceimpl.forecast.impl;


import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.vo.CacheVO;
import com.tsieframework.core.component.cache.business.CacheService;
import com.tsieframework.core.component.cache.entity.CacheRequest;
import com.tsieframework.core.component.cache.entity.CacheResponse;
import com.tsintergy.lf.core.constants.CacheConstants;
import com.tsintergy.lf.serviceapi.base.forecast.api.AlgorithmService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmDO;
import com.tsintergy.lf.serviceimpl.forecast.dao.AlgorithmDAO;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version $Id: AlgorithmServiceImpl.java, v 0.1 2018-01-31 10:23:29 tao Exp $$
 */

@Service("algorithmService")
public class AlgorithmServiceImpl implements AlgorithmService {

    @Autowired
    CacheService cacheService;

    @Autowired
    AlgorithmDAO algorithmDAO;


    @Override
    public String getAlgorithmCn(String algorithmId) {
        CacheRequest request = new CacheRequest();
        request.setCacheKey(CacheConstants.CACHE_ID_ALGORITHM_PREFIX + algorithmId);
        CacheResponse response = cacheService.queryCacheItemList(request);
        if (response.getData() != null && response.getData().size() > 0) {
            return ((AlgorithmDO) response.getData().get(0)).getAlgorithmCn();
        }
        return null;
    }

    @Override
    public AlgorithmDO findAlgorithmVOByPk(Serializable pk) throws Exception {
        try {
            return algorithmDAO.findVOByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public AlgorithmDO getAlgorithmDOById(String algorithmId) {
        CacheRequest request = new CacheRequest();
        request.setCacheKey(CacheConstants.CACHE_ID_ALGORITHM_PREFIX + algorithmId);
        CacheResponse response = cacheService.queryCacheItemList(request);
        if (response.getData() != null && response.getData().size() > 0) {
            return (AlgorithmDO) response.getData().get(0);
        }
        return null;
    }

    @Override
    public String getAlgorithmEn(String algorithmId) {
        CacheRequest request = new CacheRequest();
        request.setCacheKey(CacheConstants.CACHE_ID_ALGORITHM_PREFIX + algorithmId);
        CacheResponse response = cacheService.queryCacheItemList(request);
        if (response.getData() != null && response.getData().size() > 0) {
            return ((AlgorithmDO) response.getData().get(0)).getAlgorithmEn();
        }
        return null;
    }

    @Override
    public List<AlgorithmDO> getAllAlgorithms() {
        CacheRequest request = new CacheRequest();
        request.setCacheKey(CacheConstants.CACHE_ID_ALGORITHM_PREFIX + "*");
        CacheResponse response = cacheService.queryCacheItemList(request);
        List<CacheVO> list = response.getData();
        List<AlgorithmDO> algorithmVOS = new ArrayList<>();
        for (CacheVO cacheVO : list) {
            algorithmVOS.add((AlgorithmDO) cacheVO);
        }
        Collections.sort(algorithmVOS, new Comparator() {
            @Override
            public int compare(Object o1, Object o2) {
                AlgorithmDO obj1 = (AlgorithmDO) o1;
                AlgorithmDO obj2 = (AlgorithmDO) o2;
                return obj1.getOrderNo().compareTo(obj2.getOrderNo());
            }
        });
        return algorithmVOS;
    }

    @Override
    public AlgorithmDO getAlgorithmVOByCode(String code) {
        List<AlgorithmDO> algorithmVOS = this.getAllAlgorithms();
        for (AlgorithmDO algorithmVO : algorithmVOS
        ) {
            if (code.equals(algorithmVO.getCode())) {
                return algorithmVO;
            }
        }
        return null;
    }

    @Override
    public List<AlgorithmDO> getAllAlgorithmsNotCache() throws Exception {
        return algorithmDAO.findAll().stream().filter(AlgorithmDO::getValid).collect(Collectors.toList());
    }

}
