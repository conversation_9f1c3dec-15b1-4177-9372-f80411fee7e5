package com.tsintergy.lf.serviceimpl.evalucation.dao;


import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.serviceapi.base.base.pojo.BaseLoadFcCityDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.DispersionLoadCityFcDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @version $Id: DispersionLoadCityFcDAO.java, v 0.1 2018-01-31 10:15:56 tao Exp $$
 */
@Slf4j
@Component
public class DispersionLoadCityFcDAO extends BaseAbstractDAO<DispersionLoadCityFcDO> {


    /**
     * 计算预测离散率
     *
     * @param accuracyLoadCityFcVO 准确率
     */
    public DispersionLoadCityFcDO calculateDispersion(BaseLoadFcCityDO accuracyLoadCityFcVO) throws Exception {

        // 平均准确率
        BigDecimal avgAccuracy = BigDecimalUtils.avgList(BasePeriodUtils
                        .toList(accuracyLoadCityFcVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO),
                4, false);
        // 准确率map
        Map<String, BigDecimal> accuracyMap = BasePeriodUtils
                .toMap(accuracyLoadCityFcVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        // 离散率map
        Map<String, BigDecimal> dispersionMap = new HashMap<String, BigDecimal>();

        for (String column : accuracyMap.keySet()) {
            dispersionMap.put(column, LoadCalUtil.getVariance(accuracyMap.get(column), avgAccuracy));
        }

        DispersionLoadCityFcDO dispersionLoadCityFcVO = new DispersionLoadCityFcDO();
        dispersionLoadCityFcVO.setAlgorithmId(accuracyLoadCityFcVO.getAlgorithmId());
        dispersionLoadCityFcVO.setCityId(accuracyLoadCityFcVO.getCityId());
        dispersionLoadCityFcVO.setDate(accuracyLoadCityFcVO.getDate());
        dispersionLoadCityFcVO.setCaliberId(accuracyLoadCityFcVO.getCaliberId());
        BasePeriodUtils.setAllFiled(dispersionLoadCityFcVO, dispersionMap);
        dispersionLoadCityFcVO.setReport(accuracyLoadCityFcVO.getReport());

        return dispersionLoadCityFcVO;
    }

    /**
     *      功能描述: <br>  获取地区负荷预测离散率
     *
     * @param cityId      地区ID
     * @param caliberId   口径ID
     * @param algorithmId 算法
     * @param startDate   开始时间
     * @param endDate     结束时间
     * @return 地区负荷预测准确率列表  @since: 1.0.0     @Author:zhaoml     @Date: 2018/4/24 11:26   
     */
    public List<DispersionLoadCityFcDO> getDispersionLoadCityFcDO(String cityId, String caliberId, String algorithmId,
                                                                  Date startDate, Date endDate, Boolean isReport) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (StringUtils.isNotBlank(algorithmId)) {
            param.getQueryConditions().put("_ne_algorithmId", algorithmId);
        }
        if (StringUtils.isNotBlank(cityId)) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        if (null != isReport) {
            param.getQueryConditions().put("_ne_report", isReport);
        }
        if (StringUtils.isNotBlank(caliberId)) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        param.setDesc("date");
        return query(param).getDatas();
    }

    /**
     * 计算指定时段内的平均离散率
     *
     * @param dispersionLoadCityFcVOS 离散率列表
     * @param startPeriod             开始时段
     * @param endPeriod               结束时段
     */
    public BigDecimal calculateAvgDispersion(List<DispersionLoadCityFcDO> dispersionLoadCityFcVOS, String startPeriod,
                                             String endPeriod) {

        List<BigDecimal> datas = new ArrayList<BigDecimal>();

        if (dispersionLoadCityFcVOS != null) {
            for (DispersionLoadCityFcDO dispersionLoadCityFcVO : dispersionLoadCityFcVOS) {
                Map<String, BigDecimal> map = BasePeriodUtils
                        .toMap(dispersionLoadCityFcVO, Constants.LOAD_CURVE_POINT_NUM,
                                Constants.LOAD_CURVE_START_WITH_ZERO);
                for (String column : map.keySet()) {
                    if (column.replace("t", "").compareTo(startPeriod) > -1
                            && column.replace("t", "").compareTo(endPeriod) < 1) {
                        datas.add(map.get(column));
                    }
                }
            }
        }

        BigDecimal dispersion = BigDecimalUtils.avgList(datas, 4, false);
        // 离散率=标准差
        dispersion = LoadCalUtil.getStandardDeviation(dispersion);
        return dispersion;
    }

    /**
     *      功能描述: <br>  获取地区负荷预测离散率
     *
     * @param cityIds     地区ID
     * @param caliberId   口径ID
     * @param algorithmId 算法
     * @param startDate   开始时间
     * @param endDate     结束时间
     * @return 地区负荷预测准确率列表  @since: 1.0.0     @Author:zhaoml     @Date: 2018/4/24 13:16   
     */
    public List<DispersionLoadCityFcDO> getDispersionLoadCityFcDO(List<String> cityIds, String caliberId,
                                                                  String algorithmId, Date startDate, Date endDate, Boolean isReport) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (StringUtils.isNotBlank(algorithmId)) {
            param.getQueryConditions().put("_ne_algorithmId", algorithmId);
        }
        if (null != cityIds && cityIds.size() > 0) {
            param.getQueryConditions().put("_sin_cityId", cityIds);
        }
        if (null != isReport) {
            param.getQueryConditions().put("_ne_report", isReport);
        }
        if (StringUtils.isNotBlank(caliberId)) {
            param.getQueryConditions().put("_se_caliberId", caliberId);
        }
        param.setDesc("date");
        return query(param).getDatas();
    }

    /**
     * 计算预测离散率
     *
     * @param accuracyLoadCityFcDOS 准确率
     */
    public List<DispersionLoadCityFcDO> calculateDispersion(List<? extends BaseLoadFcCityDO> accuracyLoadCityFcDOS)
            throws Exception {

        List<DispersionLoadCityFcDO> dispersionLoadCityFcDOS = new ArrayList<DispersionLoadCityFcDO>();

        for (BaseLoadFcCityDO accuracyLoadCityFcDO : accuracyLoadCityFcDOS) {
            try {
                DispersionLoadCityFcDO dispersionLoadCityFcDO = calculateDispersion(accuracyLoadCityFcDO);
                dispersionLoadCityFcDOS.add(dispersionLoadCityFcDO);
            } catch (Exception e) {
                log.error("计算离散了出错了", e);
            }
        }

        return dispersionLoadCityFcDOS;
    }


    /**
     *      功能描述: <br>  获取离散率
     *
     * @param cityId      城市ID
     * @param caliberId   口径ID
     * @param algorithmId 算法ID
     * @param date        日期
     * @return  @since: 1.0.0     @Author:zhaoml     @Date: 2018/4/24 11:30   
     */
    public DispersionLoadCityFcDO getDispersionLoadCityFcDO(String cityId, String caliberId, String algorithmId,
                                                            Date date) throws Exception {

        if (StringUtils.isBlank(cityId)) {
            throw new BusinessException("T706", "城市ID不可为空");
        }
        if (StringUtils.isBlank(caliberId)) {
            throw new BusinessException("T706", "口径ID不可为空");
        }
        if (StringUtils.isBlank(algorithmId)) {
            throw new BusinessException("T706", "算法ID不可为空");
        }
        if (date == null) {
            throw new BusinessException("T706", "日期不可为空");
        }

        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        param.getQueryConditions().put("_de_date", new java.sql.Date(date.getTime()));
        param.getQueryConditions().put("_ne_cityId", cityId);
        param.getQueryConditions().put("_ne_algorithmId", algorithmId);
        param.getQueryConditions().put("_ne_caliberId", caliberId);

        List<DispersionLoadCityFcDO> dispersionLoadCityFcDOS = this.query(param).getDatas();
        if (dispersionLoadCityFcDOS.size() > 0) {
            return dispersionLoadCityFcDOS.get(0);
        }
        return null;
    }

    /**
     * 保存或更新
     */
    public DispersionLoadCityFcDO doSaveOrUpdateDispersionLoadCityFcVO(DispersionLoadCityFcDO dispersionLoadCityFcDO)
            throws Exception {
        DispersionLoadCityFcDO oldVO = getDispersionLoadCityFcDO(dispersionLoadCityFcDO.getCityId(),
                dispersionLoadCityFcDO
                        .getCaliberId(), dispersionLoadCityFcDO.getAlgorithmId(), dispersionLoadCityFcDO.getDate());
        if (oldVO != null) {
            String id = oldVO.getId();
            BeanUtils.copyProperties(dispersionLoadCityFcDO, oldVO);
            oldVO.setId(id);
            return (DispersionLoadCityFcDO) this.updateAndFlush(oldVO);
        } else {
            return (DispersionLoadCityFcDO) this.createAndFlush(dispersionLoadCityFcDO);
        }
    }

    /**
     * 保存或更新
     */
    public List<DispersionLoadCityFcDO> doSaveOrUpdateDispersionLoadCityFcVOs(
            List<DispersionLoadCityFcDO> dispersionLoadCityFcDOS) throws Exception {
        List<DispersionLoadCityFcDO> vos = new ArrayList<DispersionLoadCityFcDO>();
        for (DispersionLoadCityFcDO dispersionLoadCityFcDO : dispersionLoadCityFcDOS) {
            try {
                vos.add(this.doSaveOrUpdateDispersionLoadCityFcVO(dispersionLoadCityFcDO));
            } catch (Exception e) {
                log.error("保存偏差率出错了", e);
            }
        }
        return vos;
    }


}