<?xml version="1.0" encoding="UTF-8"?>

<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0" 
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">

  <localRepository>D:\qnhl\repository</localRepository>
  
  
  
  <!-- 镜像仓库只有一个连接不上时才会使用第二个,不是jar包在第一个找不到会去第二个找-->
  <mirrors>
	  <!-- 镜像链接规则为id的字母排序，BeiJing -> GuangZhou -->
	 <!--  <mirror>
		  <id>BeiJing-Nexus</id>
		  <mirrorOf>*</mirrorOf>
		  <name>all repository</name>
		   <url>http://192.168.3.98:8081/nexus/content/groups/public/</url>		  
	  </mirror>
      -->
  
	 <!--   <mirror>
		  <id>GuangZhou-Nexus</id>
		  <mirrorOf>*</mirrorOf>
		  <name>all repository</name>
		  <url>http://gz.tsintergy.com:28081/nexus/content/groups/public/</url>
	  </mirror> 
	   -->
	  
	  
	   <mirror>
      <id>alimaven</id>
      <mirrorOf>central  </mirrorOf>
      <name>aliyun maven</name>
      <url>http://maven.aliyun.com/nexus/content/groups/public</url>
    </mirror>
  </mirrors>
  
  <servers>
	<server>
		<id>aif-snapshots</id>
		<username>admin</username>
		<password>admin123</password>
	</server>
	<server>
		<id>aif-release</id>
		<username>admin</username>
		<password>admin123</password>
	</server>
	<server>
          <id>tsintergy-release</id>
          <username>devUser</username>
          <password>qinghua123@</password>
      </server>
      <server>
          <id>tsintergy-snapshots</id>
          <username>devUser</username>
          <password>qinghua123@</password>
      </server>
	  
	   <server>
          <id>tsie</id>
          <username>devUser</username>
          <password>qinghua123@</password>
      </server>
	  
	  
	  	 <server>
            <id>public</id>
            <username>admin</username>
            <password>admin123</password>
        </server>
		
		<server>
            <id>beijing</id>
            <username>admin</username>
            <password>admin123</password>
        </server>
		
	
  </servers>



<!--  <snapshots>-->
<!--    <enabled>true</enabled>-->
<!--    <updatePolicy>always</updatePolicy>-->
<!--    <checksumPolicy>wran</checksumPolicy>-->
<!--  </snapshots>-->
  
  <profiles>
  
  <profile>
      <id>aif-snapshots</id>
      <repositories>
        <repository>
          <id>aif-snapshots</id>
          <name>Aif Snapshots</name>
          <url>http://192.168.3.98:8081/nexus/content/repositories/aif-snapshots/</url>
          <releases>
            <enabled>true</enabled>
          </releases>
          <snapshots>
            <enabled>true</enabled>
          </snapshots>
        </repository>
      </repositories>
      <pluginRepositories>
        <pluginRepository>
          <id>aif-snapshots</id>
          <name>Aif Snapshots</name>
          <url>http://192.168.3.98:8081/nexus/content/repositories/aif-snapshots/</url>
          <releases>
            <enabled>true</enabled>
          </releases>
          <snapshots>
            <enabled>true</enabled>
          </snapshots>
        </pluginRepository>
      </pluginRepositories>
    </profile>
	
	
    <profile>
      <id>aif-release</id>
      <repositories>
        <repository>
          <id>aif-release</id>
          <name>Aif Release</name>
          <url>http://192.168.3.98:8081/nexus/content/repositories/aif-release/</url>
          <releases>
            <enabled>true</enabled>
          </releases>
          <snapshots>
            <enabled>true</enabled>
          </snapshots>
        </repository>
      </repositories>
      <pluginRepositories>
        <pluginRepository>
          <id>aif-release</id>
          <name>Aif Release</name>
          <url>http://192.168.3.98:8081/nexus/content/repositories/aif-release/</url>
          <releases>
            <enabled>true</enabled>
          </releases>
          <snapshots>
            <enabled>true</enabled>
          </snapshots>
        </pluginRepository>
      </pluginRepositories>
    </profile>
	
	 <profile>
            <id>nexus</id>
            <repositories>
                <repository>
                    <id>nexus</id>
                    <name>Nexus</name>
                    <!--<url>http://192.168.2.98:8081/nexus/content/groups/public/</url>-->
                    <url>http://nexus3.gzdevops.tsintergy.com//content/groups/public/</url>
                    <releases><enabled>true</enabled></releases>
                    <snapshots><enabled>true</enabled></snapshots>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>nexus</id>
                    <name>Nexus</name>
                    <!--<url>http://192.168.2.98:8081/nexus/content/groups/public/</url>-->
                    <url>http://nexus3.gzdevops.tsintergy.com//content/groups/public/</url>
                    <releases><enabled>true</enabled></releases>
                    <snapshots><enabled>true</enabled> </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>
		
		
		 <profile>
            <id>tsie</id>
            <repositories>
                <repository>
                    <id>tsie</id>
                    <name>TSIE</name>
                        <!-- <url>http://192.168.2.98:8081/nexus/content/repositories/releases/</url> -->
                        <url>http://nexus3.gzdevops.tsintergy.com/content/repositories/releases/</url>
                    <releases><enabled>true</enabled></releases>
                    <snapshots><enabled>true</enabled></snapshots>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>tsie</id>
                    <name>TSIE</name>
                        <!-- <url>http://192.168.2.98:8081/nexus/content/repositories/releases/</url> -->
                        <url>http://nexus3.gzdevops.tsintergy.com/content/repositories/releases/</url>
                    <releases><enabled>true</enabled></releases>
                    <snapshots><enabled>true</enabled> </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>
		
		  <profile>
            <id>beijing</id>
            <repositories>
                <repository>
                    <id>beijing</id>
                    <name>BEIJING-TSINTERGY</name>
                        <url>http://192.168.3.98:8081/nexus/content/groups/public/</url>
                    <releases><enabled>true</enabled></releases>
                    <snapshots><enabled>true</enabled></snapshots>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>beijing</id>
                    <name>BEIJING</name>
                        <url>http://192.168.3.98:8081/nexus/content/groups/public/</url>
                    <releases><enabled>true</enabled></releases>
                    <snapshots><enabled>true</enabled> </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>
		
		
  
 
  </profiles>

 
  <activeProfiles>
	   <activeProfile>aif-snapshots</activeProfile>
	   <activeProfile>aif-release</activeProfile>
	   <activeProfile>nexus</activeProfile>
       <activeProfile>tsie</activeProfile>
	   <activeProfile>beijing</activeProfile>
  </activeProfiles>

</settings>
