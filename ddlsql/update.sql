INSERT INTO `tsie_menu`(`id`, `name`, `type`, `menupath`, `parentId`, `description`,`targetType`,`rel`,`sort`) VALUES (14, '节假日信息管理', 1, 'HolidaySetting', '4028d0816cb8644d016cb86f0585010e','节假日信息管理','navTap','节假日信息管理','1000');
-- 2021-02-05 增加网状及亮度的配置
INSERT INTO `setting_system_init` (`id`, `field`, `value`, `name`, `description`) VALUES ('40', 'color_str', '#5B5B5B', '网格的亮度颜色', '网格的亮度颜色');
INSERT INTO `setting_system_init` (`id`, `field`, `value`, `name`, `description`) VALUES ('41', 'open_reseau', '1', '是否开启页面网格', '是否开启页面网格（0，关闭 1开启）');

-- 2022-08-24 增加针对负荷较小地市计算准确率时分母取固定值
INSERT INTO `setting_system_init` VALUES ('49', 'accuracy_denominator', '{\"4\":\"2000\",\"9\":\"2000\"}', '固定分母值', '针对负荷较小地区在计算准确率时分母取值 城市id:分母值');
﻿

--20210222 增加菜单 批次预测表

INSERT INTO `tsie_menu`(`id`, `name`, `type`, `menupath`, `parentId`, `description`,`targetType`,`rel`,`sort`) VALUES ('4028fa816c8b208b016c93da97f90254','上报结果查询','1','ReportResult','4028fa816c8b208b016c93da97f90a9a','上报结果查询','navTab','上报结果查询','9999');

CREATE TABLE "load_city_fc_batch" (
  "id" varchar(32) NOT NULL COMMENT '96点负荷表id',
  "date" date NOT NULL COMMENT '日期',
  "city_id" varchar(32) NOT NULL COMMENT '城市表id',
  "caliber_id" varchar(32) NOT NULL COMMENT '口径id',
  "algorithm_id" varchar(32) NOT NULL COMMENT '预测算法id',
  "batch_id" varchar(32) NOT NULL COMMENT '批次号',
  "t0000" decimal(32,4) DEFAULT NULL,
  "t0015" decimal(32,4) DEFAULT NULL,
  "t0030" decimal(32,4) DEFAULT NULL,
  "t0045" decimal(32,4) DEFAULT NULL,
  "t0100" decimal(32,4) DEFAULT NULL,
  "t0115" decimal(32,4) DEFAULT NULL,
  "t0130" decimal(32,4) DEFAULT NULL,
  "t0145" decimal(32,4) DEFAULT NULL,
  "t0200" decimal(32,4) DEFAULT NULL,
  "t0215" decimal(32,4) DEFAULT NULL,
  "t0230" decimal(32,4) DEFAULT NULL,
  "t0245" decimal(32,4) DEFAULT NULL,
  "t0300" decimal(32,4) DEFAULT NULL,
  "t0315" decimal(32,4) DEFAULT NULL,
  "t0330" decimal(32,4) DEFAULT NULL,
  "t0345" decimal(32,4) DEFAULT NULL,
  "t0400" decimal(32,4) DEFAULT NULL,
  "t0415" decimal(32,4) DEFAULT NULL,
  "t0430" decimal(32,4) DEFAULT NULL,
  "t0445" decimal(32,4) DEFAULT NULL,
  "t0500" decimal(32,4) DEFAULT NULL,
  "t0515" decimal(32,4) DEFAULT NULL,
  "t0530" decimal(32,4) DEFAULT NULL,
  "t0545" decimal(32,4) DEFAULT NULL,
  "t0600" decimal(32,4) DEFAULT NULL,
  "t0615" decimal(32,4) DEFAULT NULL,
  "t0630" decimal(32,4) DEFAULT NULL,
  "t0645" decimal(32,4) DEFAULT NULL,
  "t0700" decimal(32,4) DEFAULT NULL,
  "t0715" decimal(32,4) DEFAULT NULL,
  "t0730" decimal(32,4) DEFAULT NULL,
  "t0745" decimal(32,4) DEFAULT NULL,
  "t0800" decimal(32,4) DEFAULT NULL,
  "t0815" decimal(32,4) DEFAULT NULL,
  "t0830" decimal(32,4) DEFAULT NULL,
  "t0845" decimal(32,4) DEFAULT NULL,
  "t0900" decimal(32,4) DEFAULT NULL,
  "t0915" decimal(32,4) DEFAULT NULL,
  "t0930" decimal(32,4) DEFAULT NULL,
  "t0945" decimal(32,4) DEFAULT NULL,
  "t1000" decimal(32,4) DEFAULT NULL,
  "t1015" decimal(32,4) DEFAULT NULL,
  "t1030" decimal(32,4) DEFAULT NULL,
  "t1045" decimal(32,4) DEFAULT NULL,
  "t1100" decimal(32,4) DEFAULT NULL,
  "t1115" decimal(32,4) DEFAULT NULL,
  "t1130" decimal(32,4) DEFAULT NULL,
  "t1145" decimal(32,4) DEFAULT NULL,
  "t1200" decimal(32,4) DEFAULT NULL,
  "t1215" decimal(32,4) DEFAULT NULL,
  "t1230" decimal(32,4) DEFAULT NULL,
  "t1245" decimal(32,4) DEFAULT NULL,
  "t1300" decimal(32,4) DEFAULT NULL,
  "t1315" decimal(32,4) DEFAULT NULL,
  "t1330" decimal(32,4) DEFAULT NULL,
  "t1345" decimal(32,4) DEFAULT NULL,
  "t1400" decimal(32,4) DEFAULT NULL,
  "t1415" decimal(32,4) DEFAULT NULL,
  "t1430" decimal(32,4) DEFAULT NULL,
  "t1445" decimal(32,4) DEFAULT NULL,
  "t1500" decimal(32,4) DEFAULT NULL,
  "t1515" decimal(32,4) DEFAULT NULL,
  "t1530" decimal(32,4) DEFAULT NULL,
  "t1545" decimal(32,4) DEFAULT NULL,
  "t1600" decimal(32,4) DEFAULT NULL,
  "t1615" decimal(32,4) DEFAULT NULL,
  "t1630" decimal(32,4) DEFAULT NULL,
  "t1645" decimal(32,4) DEFAULT NULL,
  "t1700" decimal(32,4) DEFAULT NULL,
  "t1715" decimal(32,4) DEFAULT NULL,
  "t1730" decimal(32,4) DEFAULT NULL,
  "t1745" decimal(32,4) DEFAULT NULL,
  "t1800" decimal(32,4) DEFAULT NULL,
  "t1815" decimal(32,4) DEFAULT NULL,
  "t1830" decimal(32,4) DEFAULT NULL,
  "t1845" decimal(32,4) DEFAULT NULL,
  "t1900" decimal(32,4) DEFAULT NULL,
  "t1915" decimal(32,4) DEFAULT NULL,
  "t1930" decimal(32,4) DEFAULT NULL,
  "t1945" decimal(32,4) DEFAULT NULL,
  "t2000" decimal(32,4) DEFAULT NULL,
  "t2015" decimal(32,4) DEFAULT NULL,
  "t2030" decimal(32,4) DEFAULT NULL,
  "t2045" decimal(32,4) DEFAULT NULL,
  "t2100" decimal(32,4) DEFAULT NULL,
  "t2115" decimal(32,4) DEFAULT NULL,
  "t2130" decimal(32,4) DEFAULT NULL,
  "t2145" decimal(32,4) DEFAULT NULL,
  "t2200" decimal(32,4) DEFAULT NULL,
  "t2215" decimal(32,4) DEFAULT NULL,
  "t2230" decimal(32,4) DEFAULT NULL,
  "t2245" decimal(32,4) DEFAULT NULL,
  "t2300" decimal(32,4) DEFAULT NULL,
  "t2315" decimal(32,4) DEFAULT NULL,
  "t2330" decimal(32,4) DEFAULT NULL,
  "t2345" decimal(32,4) DEFAULT NULL,
  "t2400" decimal(32,4) DEFAULT NULL,
  "createtime" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatetime" datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  "recommend" tinyint(1) DEFAULT NULL,
  "report" tinyint(1) DEFAULT '0' COMMENT '是否上报结果（1：是；0：否）',
  "report_time" datetime DEFAULT NULL COMMENT '上报时间',
  "user_id" varchar(32) DEFAULT NULL COMMENT '操作者ID',
  "succeed" tinyint(1) DEFAULT NULL COMMENT '是否上报成功（1：是；0：否）',
  PRIMARY KEY ("id") USING BTREE,
  UNIQUE KEY "load_city_fc_unique" ("date","city_id","algorithm_id","caliber_id","batch_id") USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='地区批次预测负荷表';

--2021/2/25 修改索引
ALTER TABLE `load_city_fc_batch`
DROP INDEX `load_city_fc_unique`,
ADD UNIQUE INDEX `load_city_fc_unique`(`date`, `city_id`, `caliber_id`, `algorithm_id`, `batch_id`) USING BTREE;


-- 2021/4/14 添加月预测准确率表
CREATE TABLE "STLF"."STLF_QC"."LOAD_FEATURE_CITY_MONTH_FC_SERVICE"(
"ID" VARCHAR(32),
"YEAR" VARCHAR(4) NOT NULL,
"MONTH" VARCHAR(2) NOT NULL,
"CITY_ID" VARCHAR(32) NOT NULL,
"CALIBER_ID" VARCHAR(32) NOT NULL,
"ALGORITHM_ID" VARCHAR(32) NOT NULL,
"MAX_LOAD" DEC(32,4),
"MIN_LOAD" DEC(32,4),
"AVE_LOAD" DEC(32,4),
"MAX_DATE" DATE,
"MAX_TIME" VARCHAR(5),
"MIN_TIME" VARCHAR(5),
"PEAK" DEC(32,4),
"TROUGH" DEC(32,4),
"DIFFERENT" DEC(32,4),
"GRADIENT" DEC(32,4),
"LOAD_GRADIENT" DEC(32,4),
"DAY_UNBALANCE" DEC(32,4),
"ENERGY" DEC(32,4),
"UPPER_STABILITY" DEC(32,4),
"LOWER_STABILITY" DEC(32,4),
"CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(),
"UPDATETIME" TIMESTAMP(6),
PRIMARY KEY("ID"),
CONSTRAINT "LOAD_FEATURE_CITY_MONTH_FC_UNIQUE" UNIQUE("YEAR", "MONTH", "CITY_ID", "CALIBER_ID"))
 STORAGE( INITIAL 1 , NEXT 1 , MINEXTENTS 1 , on "PRIMARY", FILLFACTOR 0 ) ;


 -- 2021/4/14 添加周预测准确率表
 CREATE TABLE "STLF"."STLF_QC"."LOAD_FEATURE_CITY_WEEK_FC_SERVICE"(
"ID" VARCHAR(32),
"DATE" DATE NOT NULL,
"CITY_ID" VARCHAR(32) NOT NULL,
"CALIBER_ID" VARCHAR(32) NOT NULL,
"ALGORITHM_ID" VARCHAR(32) NOT NULL,
"MAX_LOAD" DEC(32,4),
"MIN_LOAD" DEC(32,4),
"AVE_LOAD" DEC(32,4),
"MAX_DATE" DATE,
"MAX_TIME" VARCHAR(5),
"MIN_TIME" VARCHAR(5),
"PEAK" DEC(32,4),
"TROUGH" DEC(32,4),
"DIFFERENT" DEC(32,4),
"GRADIENT" DEC(32,4),
"LOAD_GRADIENT" DEC(32,4),
"ENERGY" DEC(32,4),
"CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(),
"UPDATETIME" TIMESTAMP(6),
PRIMARY KEY("ID"),
CONSTRAINT "LOAD_FEATURE_CITY_WEEK_FC_UNIQUE" UNIQUE("DATE", "CITY_ID", "CALIBER_ID"))
 STORAGE( INITIAL 1 , NEXT 1 , MINEXTENTS 1 , on "PRIMARY", FILLFACTOR 0 ) ;

 -- 20210915 添加气象源评估页面
 INSERT INTO "STLF"."STLF_QC"."TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('20','气象源评估',1,'WeathersComparativeEvaluation','402879816e83525a016e8357d5ed0029','气象源评估','navTab','气象源评估',9999);
-- 20210915 添加气象影响分析页面
 INSERT INTO "STLF"."STLF_QC"."TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402863816cf123123','气象影响分析',1,'MeteorologicalImpactAnalysis','402879816e83525a016e8357d53c000b','气象影响分析','navTab','气象影响分析',9999);


-- 2021/10/8 重庆新增限电表
CREATE TABLE "STLF"."STLF_QC"."LOAD_CITY_FC_DFD"(
                                                    "ID" VARCHAR(32),
                                                    "DATE" DATE NOT NULL,
                                                    "CITY_ID" VARCHAR(32) NOT NULL,
                                                    "CALIBER_ID" VARCHAR(32) DEFAULT NULL,
                                                    "ALGORITHM_ID" VARCHAR(32) NOT NULL,
                                                    "T0000" DEC(32,4),
                                                    "T0015" DEC(32,4),
                                                    "T0030" DEC(32,4),
                                                    "T0045" DEC(32,4),
                                                    "T0100" DEC(32,4),
                                                    "T0115" DEC(32,4),
                                                    "T0130" DEC(32,4),
                                                    "T0145" DEC(32,4),
                                                    "T0200" DEC(32,4),
                                                    "T0215" DEC(32,4),
                                                    "T0230" DEC(32,4),
                                                    "T0245" DEC(32,4),
                                                    "T0300" DEC(32,4),
                                                    "T0315" DEC(32,4),
                                                    "T0330" DEC(32,4),
                                                    "T0345" DEC(32,4),
                                                    "T0400" DEC(32,4),
                                                    "T0415" DEC(32,4),
                                                    "T0430" DEC(32,4),
                                                    "T0445" DEC(32,4),
                                                    "T0500" DEC(32,4),
                                                    "T0515" DEC(32,4),
                                                    "T0530" DEC(32,4),
                                                    "T0545" DEC(32,4),
                                                    "T0600" DEC(32,4),
                                                    "T0615" DEC(32,4),
                                                    "T0630" DEC(32,4),
                                                    "T0645" DEC(32,4),
                                                    "T0700" DEC(32,4),
                                                    "T0715" DEC(32,4),
                                                    "T0730" DEC(32,4),
                                                    "T0745" DEC(32,4),
                                                    "T0800" DEC(32,4),
                                                    "T0815" DEC(32,4),
                                                    "T0830" DEC(32,4),
                                                    "T0845" DEC(32,4),
                                                    "T0900" DEC(32,4),
                                                    "T0915" DEC(32,4),
                                                    "T0930" DEC(32,4),
                                                    "T0945" DEC(32,4),
                                                    "T1000" DEC(32,4),
                                                    "T1015" DEC(32,4),
                                                    "T1030" DEC(32,4),
                                                    "T1045" DEC(32,4),
                                                    "T1100" DEC(32,4),
                                                    "T1115" DEC(32,4),
                                                    "T1130" DEC(32,4),
                                                    "T1145" DEC(32,4),
                                                    "T1200" DEC(32,4),
                                                    "T1215" DEC(32,4),
                                                    "T1230" DEC(32,4),
                                                    "T1245" DEC(32,4),
                                                    "T1300" DEC(32,4),
                                                    "T1315" DEC(32,4),
                                                    "T1330" DEC(32,4),
                                                    "T1345" DEC(32,4),
                                                    "T1400" DEC(32,4),
                                                    "T1415" DEC(32,4),
                                                    "T1430" DEC(32,4),
                                                    "T1445" DEC(32,4),
                                                    "T1500" DEC(32,4),
                                                    "T1515" DEC(32,4),
                                                    "T1530" DEC(32,4),
                                                    "T1545" DEC(32,4),
                                                    "T1600" DEC(32,4),
                                                    "T1615" DEC(32,4),
                                                    "T1630" DEC(32,4),
                                                    "T1645" DEC(32,4),
                                                    "T1700" DEC(32,4),
                                                    "T1715" DEC(32,4),
                                                    "T1730" DEC(32,4),
                                                    "T1745" DEC(32,4),
                                                    "T1800" DEC(32,4),
                                                    "T1815" DEC(32,4),
                                                    "T1830" DEC(32,4),
                                                    "T1845" DEC(32,4),
                                                    "T1900" DEC(32,4),
                                                    "T1915" DEC(32,4),
                                                    "T1930" DEC(32,4),
                                                    "T1945" DEC(32,4),
                                                    "T2000" DEC(32,4),
                                                    "T2015" DEC(32,4),
                                                    "T2030" DEC(32,4),
                                                    "T2045" DEC(32,4),
                                                    "T2100" DEC(32,4),
                                                    "T2115" DEC(32,4),
                                                    "T2130" DEC(32,4),
                                                    "T2145" DEC(32,4),
                                                    "T2200" DEC(32,4),
                                                    "T2215" DEC(32,4),
                                                    "T2230" DEC(32,4),
                                                    "T2245" DEC(32,4),
                                                    "T2300" DEC(32,4),
                                                    "T2315" DEC(32,4),
                                                    "T2330" DEC(32,4),
                                                    "T2345" DEC(32,4),
                                                    "T2400" DEC(32,4),
                                                    "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(),
                                                    "UPDATETIME" TIMESTAMP(6),
                                                    "RECOMMEND" TINYINT DEFAULT NULL,
                                                    "REPORT" TINYINT DEFAULT '0',
                                                    "REPORT_TIME" TIMESTAMP(6) DEFAULT NULL,
                                                    "USER_ID" VARCHAR(32) DEFAULT NULL,
                                                    "SUCCEED" TINYINT DEFAULT NULL,
                                                    "TYPE" INTEGER DEFAULT '1',
                                                    PRIMARY KEY("ID"))
    STORAGE( INITIAL 1 , NEXT 1 , MINEXTENTS 1 , on "PRIMARY", FILLFACTOR 0 ) ;

-- 2021/10/8 重庆新增限电页面
INSERT INTO "STLF"."STLF_QC"."TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('748567291738cg5o3','限电日修正上报',1,'OtherRevisedReport','402879816e83525a016e8357d58d0015','限电日修正上报','navTab','限电日修正上报',9999);

-- 20211020 修改城市表的表名为city_base_init
ALTER TABLE BASE_CITY_INIT RENAME TO CITY_BASE_INIT;

-- 删除无用的表 王晨
drop table ALGORITHM_PARAM_INIT;
drop table WEATHER_BASE_INIT;
drop table SETTING_FORECAST_CITY_SERVICE;
drop table SETTING_FORECAST_REPORT;
drop table DICTIONARY_CATEGORY_INIT;
drop table DICTIONARY_ITEM_INIT;
drop table WEATHER_SENSITIVITY_CITY_DAY_SERVICE;
drop table WEATHER_SENSITIVITY_CITY_MONTH_SERVICE;

-- 插入菜单安全审计 20211215 hounz
insert into "STLF_ZY"."TSIE_MENU"("ID", "NAME", "TYPE", "MENUPATH", "PARENTID", "DESCRIPTION", "TARGETTYPE", "REL", "SORT")
VALUES('402879816e83525a016e8357d8e300cf','安全审计','1','SecurityAudit','root','安全审计','navTab','安全审计','9999');
-- 以上update sql为历史遗留，酌情删除



create table WEATHER_FEATURE_CITY_DAY_LONG_FC_SERVICE
(
    id                  VARCHAR(32) not null
        constraint INDEX33556809
            primary key,
    city_id             VARCHAR(32) not null,
    date                DATE(10) not null,
    highest_temperature DECIMAL(32, 2),
    lowest_temperature  DECIMAL(32, 2),
    ave_temperature     DECIMAL(32, 2),
    createtime          TIMESTAMP(26, 6
) default CURRENT_TIMESTAMP(),
	updatetime TIMESTAMP(26,6),
	constraint weather_feature_city_day_long_fc_service
		unique (city_id, date)
);

create table SETTING_LABEL_INIT
(
    id          VARCHAR(32) not null
        constraint INDEX33556807
            primary key,
    year        VARCHAR(32) not null,
    startDate   DATE(10) not null,
    endDate     DATE(10) not null,
    labelType   VARCHAR(32) not null,
    description VARCHAR(255),
    createtime  TIMESTAMP(26, 6
) default CURRENT_TIMESTAMP(),
	updatetime TIMESTAMP(26,6)
);

create table LOAD_CITY_FC_LONG
(
    id                    VARCHAR(32) not null
        constraint INDEX33556812
            primary key,
    city_id               VARCHAR(32) not null,
    algorithm_id          VARCHAR(32) not null,
    caliber_id            VARCHAR(32) not null,
    date                  DATE(10) not null,
    max_load              DECIMAL(32),
    max_load_lower_limit  DECIMAL(32),
    max_load_upper_limit  DECIMAL(32),
    min_load              DECIMAL(32),
    min_load_lower_limit  DECIMAL(32),
    min_load_upper_limit  DECIMAL(32),
    mean_load             DECIMAL(32),
    mean_load_lower_limit DECIMAL(32),
    mean_load_upper_limit DECIMAL(32),
    ele_load              DECIMAL(32),
    ele_load_lower_limit  DECIMAL(32),
    ele_load_upper_limit  DECIMAL(32),
    createtime            TIMESTAMP(26, 6
) default CURRENT_TIMESTAMP(),
	updatetime TIMESTAMP(26,6)
);


insert into LF_SHOW.TSIE_MENU (ID, NAME, TYPE, MENUPATH, PARENTID, DESCRIPTION, TARGETTYPE, REL, SORT, PLATFORM) values ('11000000', '中长期预测', 1, '/medium', 'root', '中长期预测', 'navTab', '中长期预测', 13, null);
insert into LF_SHOW.TSIE_MENU (ID, NAME, TYPE, MENUPATH, PARENTID, DESCRIPTION, TARGETTYPE, REL, SORT, PLATFORM) values ('11010000', '预测查询', 1, '/query', '11000000', '预测查询', 'navTab', '预测查询', 9999, null);
insert into LF_SHOW.TSIE_MENU (ID, NAME, TYPE, MENUPATH, PARENTID, DESCRIPTION, TARGETTYPE, REL, SORT, PLATFORM) values ('11010100', '查询预测数据Restful', 0, 'fcData', '11010000', '查询预测数据Restful', 'navTab', '查询预测数据Restful', 9999, null);
insert into LF_SHOW.TSIE_MENU (ID, NAME, TYPE, MENUPATH, PARENTID, DESCRIPTION, TARGETTYPE, REL, SORT, PLATFORM) values ('11010200', '保存预测数据Restful', 0, 'delete', '11010000', '保存预测数据Restful', 'navTab', '保存预测数据Restful', 9999, null);
insert into LF_SHOW.TSIE_MENU (ID, NAME, TYPE, MENUPATH, PARENTID, DESCRIPTION, TARGETTYPE, REL, SORT, PLATFORM) values ('11010300', '手动预测Restful', 0, 'manualPrediction', '11010000', '手动预测Restful', 'navTab', '手动预测Restful', 9999, null);
insert into LF_SHOW.TSIE_MENU (ID, NAME, TYPE, MENUPATH, PARENTID, DESCRIPTION, TARGETTYPE, REL, SORT, PLATFORM) values ('11010400', '轮询查询算法结果Restful', 0, 'polling', '11010000', '轮询查询算法结果Restful', 'navTab', '轮询查询算法结果Restful', 9999, null);
insert into LF_SHOW.TSIE_MENU (ID, NAME, TYPE, MENUPATH, PARENTID, DESCRIPTION, TARGETTYPE, REL, SORT, PLATFORM) values ('11020000', '标签管理', 1, '/label', '11000000', '标签管理', 'navTab', '标签管理', 9999, null);
insert into LF_SHOW.TSIE_MENU (ID, NAME, TYPE, MENUPATH, PARENTID, DESCRIPTION, TARGETTYPE, REL, SORT, PLATFORM) values ('11020100', '标签详情Restful', 0, 'getLabelInfo', '11020000', '标签详情Restful', 'navTab', '标签详情Restful', 9999, null);
insert into LF_SHOW.TSIE_MENU (ID, NAME, TYPE, MENUPATH, PARENTID, DESCRIPTION, TARGETTYPE, REL, SORT, PLATFORM) values ('11020200', '删除标签Restful', 0, 'delete', '11020000', '删除标签Restful', 'navTab', '删除标签Restful', 9999, null);
insert into LF_SHOW.TSIE_MENU (ID, NAME, TYPE, MENUPATH, PARENTID, DESCRIPTION, TARGETTYPE, REL, SORT, PLATFORM) values ('11020300', '编辑添加Restful', 0, 'saveOrUpdate', '11020000', '编辑添加Restful', 'navTab', '编辑添加Restful', 9999, null);
insert into LF_SHOW.TSIE_MENU (ID, NAME, TYPE, MENUPATH, PARENTID, DESCRIPTION, TARGETTYPE, REL, SORT, PLATFORM) values ('11020400', '导入Restful', 0, 'import', '11020000', '导入Restful', 'navTab', '导入Restful', 9999, null);
insert into LF_SHOW.TSIE_MENU (ID, NAME, TYPE, MENUPATH, PARENTID, DESCRIPTION, TARGETTYPE, REL, SORT, PLATFORM) values ('11020500', '影响标签下拉框查询Restful', 0, 'getLabelType', '11020000', '影响标签下拉框查询Restful', 'navTab', '影响标签下拉框查询Restful', 9999, null);
insert into LF_SHOW.TSIE_MENU (ID, NAME, TYPE, MENUPATH, PARENTID, DESCRIPTION, TARGETTYPE, REL, SORT, PLATFORM) values ('11010101', '查询预测数据', 2, 'requestMethod:get,/web/mediumAndLong/fcData', '11010100', '查询预测数据', 'navTab', '查询预测数据', 9999, null);
insert into LF_SHOW.TSIE_MENU (ID, NAME, TYPE, MENUPATH, PARENTID, DESCRIPTION, TARGETTYPE, REL, SORT, PLATFORM) values ('11010201', '保存预测数据', 2, 'requestMethod:post,/web/mediumAndLong/fcData', '11020200', '保存预测数据', 'navTab', '保存预测数据', 9999, null);
insert into LF_SHOW.TSIE_MENU (ID, NAME, TYPE, MENUPATH, PARENTID, DESCRIPTION, TARGETTYPE, REL, SORT, PLATFORM) values ('11010301', '手动预', 2, 'requestMethod:post,/web/mediumAndLong/manualPrediction', '11010300', '手动预', 'navTab', '手动预', 9999, null);
insert into LF_SHOW.TSIE_MENU (ID, NAME, TYPE, MENUPATH, PARENTID, DESCRIPTION, TARGETTYPE, REL, SORT, PLATFORM) values ('11010401', '轮询查询算法结果', 2, 'requestMethod:post,/web/mediumAndLong/polling', '11010400', '轮询查询算法结果', 'navTab', '轮询查询算法结果', 9999, null);
insert into LF_SHOW.TSIE_MENU (ID, NAME, TYPE, MENUPATH, PARENTID, DESCRIPTION, TARGETTYPE, REL, SORT, PLATFORM) values ('11020101', '标签详情', 2, 'requestMethod:get,/web/longForecast/label/getLabelInfo', '11020100', '标签详情', 'navTab', '标签详情', 9999, null);
insert into LF_SHOW.TSIE_MENU (ID, NAME, TYPE, MENUPATH, PARENTID, DESCRIPTION, TARGETTYPE, REL, SORT, PLATFORM) values ('11020201', '删除标签', 2, 'requestMethod:post,/web/longForecast/label/delete', '11020200', '删除标签', 'navTab', '删除标签', 9999, null);
insert into LF_SHOW.TSIE_MENU (ID, NAME, TYPE, MENUPATH, PARENTID, DESCRIPTION, TARGETTYPE, REL, SORT, PLATFORM) values ('11020301', '编辑添加标签', 2, 'requestMethod:post,/web/longForecast/label/saveOrUpdate', '11020300', '编辑添加标签', 'navTab', '编辑添加标签', 9999, null);
insert into LF_SHOW.TSIE_MENU (ID, NAME, TYPE, MENUPATH, PARENTID, DESCRIPTION, TARGETTYPE, REL, SORT, PLATFORM) values ('11020401', '导入标签', 2, 'requestMethod:post,/web/longForecast/label/import', '11020400', '导入标签', 'navTab', '导入标签', 9999, null);
insert into LF_SHOW.TSIE_MENU (ID, NAME, TYPE, MENUPATH, PARENTID, DESCRIPTION, TARGETTYPE, REL, SORT, PLATFORM) values ('11020501', '影响标签下拉框查询', 2, 'requestMethod:get,/web/longForecast/label/getLabelType', '11020500', '影响标签下拉框查询', 'navTab', '影响标签下拉框查询', 9999, null);


# 中长期算法 月度预测；
CREATE TABLE "load_city_fc_long_month" (
     "ID" varchar(32) NOT NULL,
     "YEAR" varchar(4) NOT NULL,
     "MONTH" varchar(2) NOT NULL,
     "CITY_ID" varchar(32) NOT NULL,
     "CALIBER_ID" varchar(32) NOT NULL,
     "ALGORITHM_ID" varchar(32) NOT NULL,
     "MAX_LOAD" decimal(32,4) DEFAULT NULL,
     "MIN_LOAD" decimal(32,4) DEFAULT NULL,
     "ENERGY" decimal(32,4) DEFAULT NULL,
     "CREATETIME" datetime DEFAULT NULL,
     "UPDATETIME" datetime DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

# 中长期算法 年度预测表；
CREATE TABLE "load_city_fc_long_year" (
   "ID" varchar(32) NOT NULL,
   "YEAR" varchar(4) NOT NULL,
   "MONTH" varchar(2) NOT NULL,
   "CITY_ID" varchar(32) NOT NULL,
   "CALIBER_ID" varchar(32) NOT NULL,
   "ALGORITHM_ID" varchar(32) NOT NULL,
   "MAX_LOAD" decimal(32,4) DEFAULT NULL,
   "MIN_LOAD" decimal(32,4) DEFAULT NULL,
   "ENERGY" decimal(32,4) DEFAULT NULL,
   "CREATETIME" datetime DEFAULT NULL,
   "UPDATETIME" datetime DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

# 中长期算法 月度准确率表；
CREATE TABLE "statistics_accuracy_long_city_month" (
      "ID" varchar(32) NOT NULL,
      "CITY_ID" varchar(32) NOT NULL,
      "CALIBER_ID" varchar(32) NOT NULL,
      "ALGORITHM_ID" varchar(32) NOT NULL,
      "YEAR" varchar(4) NOT NULL,
      "MONTH" varchar(2) NOT NULL,
      "MAX_LOAD_ACCURACY" decimal(10,4) DEFAULT NULL,
      "MIN_LOAD_ACCURACY" decimal(10,4) DEFAULT NULL,
      "ENERGY_ACCURACY" decimal(10,4) DEFAULT NULL,
      "CREATETIME" datetime DEFAULT NULL,
      "UPDATETIME" datetime DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

# 中长期算法 月度气象设置数据表；
CREATE TABLE "weather_feature_city_month_long_fc" (
       "id" varchar(32) NOT NULL,
       "city_id" varchar(32) NOT NULL,
       "year" varchar(4) NOT NULL,
       "month" varchar(2) NOT NULL,
       "highest_temperature" decimal(32,2) DEFAULT NULL,
       "lowest_temperature" decimal(32,2) DEFAULT NULL,
       "ave_temperature" decimal(32,2) DEFAULT NULL,
       "createtime" datetime DEFAULT NULL,
       "updatetime" datetime DEFAULT NULL,
       PRIMARY KEY ("id") USING BTREE,
       UNIQUE KEY "weather_feature_city_month_long_fc" ("city_id","year","month") USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

# 中长期算法 日度气象设置数据表；
CREATE TABLE "weather_feature_city_day_long_fc" (
      "id" varchar(32) NOT NULL,
      "city_id" varchar(32) NOT NULL,
      "date" date NOT NULL,
      "highest_temperature" decimal(32,2) DEFAULT NULL,
      "lowest_temperature" decimal(32,2) DEFAULT NULL,
      "ave_temperature" decimal(32,2) DEFAULT NULL,
      "createtime" datetime DEFAULT NULL,
      "updatetime" datetime DEFAULT NULL,
      PRIMARY KEY ("id") USING BTREE,
      UNIQUE KEY "weather_feature_city_day_long_fc" ("city_id","date") USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

#中长期算法月度&年度查询页面
INSERT INTO `tsie_menu` (`ID`, `NAME`, `TYPE`, `MENUPATH`, `PARENTID`, `DESCRIPTION`, `TARGETTYPE`, `REL`, `SORT`, `PLATFORM`) VALUES ('16030000', '中长期预测查询', '1', '/long-query', '16000000', '中长期预测查询', 'navTab', '中长期预测查询', '9999', NULL);


DELETE from setting_system_init where id = '43';
INSERT INTO `qnhl_lf_fj`.`setting_system_init` (`ID`, `FIELD`, `VALUE`, `NAME`, `DESCRIPTION`) VALUES ('43', 'province_run_algorithm', '800,109,107,106,200,113,111,105,201', '省调可运行算法', '省调可运行算法');


## 添加中长期算法气象设置参数表；
CREATE TABLE "weather_long_setting" (
    "ID" varchar(32) NOT NULL,
    "CITY_ID" varchar(32) NOT NULL,
    "YEAR" varchar(4) NOT NULL,
    "MONTH" varchar(2) NOT NULL,
    "TYPE" varchar(2) NOT NULL,
    "CALIBER_ID" varchar(32) NOT NULL,
    "PLAN" varchar(2) NOT NULL,
    "PLAN_REPLENISH" varchar(32) DEFAULT NULL,
    "CREATETIME" datetime DEFAULT NULL,
    "UPDATETIME" datetime DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- 新增行业关系表
create table industry_relation
(
    id        int         not null
        primary key,
    name      varchar(32) null comment '名称',
    parent_id int         null comment '父id',
    level     int         null comment '等级',
    order_no  int         null comment '排序'
)
    charset = utf8;

alter table energy_industry
    add industry_id varchar(32) null;


#2024/04/19 添加批次时间设置表
CREATE TABLE SETTING_BATCH_INIT(
ID VARCHAR(32),
NAME VARCHAR(32) NOT NULL,
START_TIME VARCHAR(32) NOT NULL,
END_TIME VARCHAR(32) NOT NULL,
PRIMARY KEY(ID),
CONSTRAINT SETTING_BATCH_INIT UNIQUE(NAME));

INSERT INTO SETTING_BATCH_INIT(ID,NAME,START_TIME, END_TIME) VALUES('1','第一批次','00:00','12:00');
INSERT INTO SETTING_BATCH_INIT(ID,NAME,START_TIME, END_TIME) VALUES('2','第二批次','12:01','23:59');


#2024/04/19 新增批次准确率表
CREATE TABLE STATISTICS_CITY_DAY_FC_SERVICE_BATCH(
 ID VARCHAR(32),
 CITY_ID VARCHAR(32) NOT NULL,
 CALIBER_ID VARCHAR(32) NOT NULL,
 DATE DATE,
 ALGORITHM_ID VARCHAR(32),
 BATCH_ID VARCHAR(4),
 ALGO_FORE_TIME VARCHAR(20),
 ACCURACY DEC(10,4),
 DEVIATION DEC(10,4),
 PASS DEC(10,4),
 DISPERSION DEC(10,4),
 STANDARD_ACCURACY DEC(10,4),
 REPORT BIT DEFAULT 0,
 CREATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP(),
 UPDATETIME TIMESTAMP,
 PRIMARY KEY(ID),
 CONSTRAINT STATISTICS_BATCH UNIQUE(DATE, CITY_ID, ALGORITHM_ID, CALIBER_ID, BATCH_ID));

-- 2024/04/19 重庆新增多批次准确率页面
INSERT INTO TSIE_MENU(ID,NAME,TYPE,MENUPATH,PARENTID,DESCRIPTION,TARGETTYPE,REL,SORT) VALUES('05010500','多批次预测数据',1,'/mb','05010000','多批次预测数据','navTab','多批次预测数据',5105);
INSERT INTO TSIE_MENU(ID,NAME,TYPE,MENUPATH,PARENTID,DESCRIPTION,TARGETTYPE,REL,SORT) VALUES('05010600','多批次准确率',1,'/mba','05010000','多批次准确率','navTab','多批次准确率',5106);
ALTER TABLE weather_city_fc_load_forecast ADD BATCH_ID varchar(32) NULL;


-- 考核点&综合准确率相关表
CREATE TABLE `accuracy_assess_service`  (
                                            `ID` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                            `DATE` date NOT NULL,
                                            `CITY_ID` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                            `CALIBER_ID` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                            `ALGORITHM_ID` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                            `ASSESS_NAME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                            `ASSESS_TYPE` tinyint(4) DEFAULT NULL,
                                            `ACCURACY` decimal(32, 4) DEFAULT NULL,
                                            `CREATETIME` timestamp(0) DEFAULT NULL,
                                            `UPDATETIME` timestamp(0) DEFAULT NULL,
                                            `REPORT` tinyint(4) DEFAULT NULL,
                                            `BATCH_ID` int(11) DEFAULT NULL,
                                            UNIQUE INDEX `INDEX33565853`(`DATE`, `CITY_ID`, `CALIBER_ID`, `ALGORITHM_ID`, `ASSESS_NAME`, `BATCH_ID`, `CREATETIME`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE `accuracy_composite_service`  (
                                               `ID` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                               `DATE` date NOT NULL,
                                               `CITY_ID` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                               `CALIBER_ID` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                               `ALGORITHM_ID` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                               `ACCURACY_NAME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                               `ACCURACY` decimal(32, 4) DEFAULT NULL,
                                               `CREATETIME` timestamp(0) DEFAULT NULL,
                                               `UPDATETIME` timestamp(0) DEFAULT NULL,
                                               `REPORT` tinyint(4) DEFAULT NULL,
                                               `BATCH_ID` int(11) DEFAULT NULL,
                                               PRIMARY KEY (`ID`) USING BTREE,
                                               INDEX `index`(`DATE`, `CITY_ID`, `CALIBER_ID`, `ALGORITHM_ID`, `ACCURACY_NAME`, `CREATETIME`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE `setting_assess`  (
                                   `ID` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                   `ASSESS_NAME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                   `YEAR` varchar(4) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                   `CALIBER_ID` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                   `MONTH` varchar(4) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                   `START_TIME` varchar(6) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                   `END_TIME` varchar(6) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                   `TYPE` tinyint(4) DEFAULT NULL,
                                   `VALID` tinyint(4) DEFAULT NULL,
                                   `CREATETIME` timestamp(0) DEFAULT NULL,
                                   `UPDATETIME` timestamp(0) DEFAULT NULL,
                                   `SORT` int(11) DEFAULT 1,
                                   PRIMARY KEY (`ID`) USING BTREE,
                                   UNIQUE INDEX `INDEX33565858`(`YEAR`, `MONTH`, `CALIBER_ID`, `ASSESS_NAME`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE `setting_composite_accuracy`  (
                                               `ID` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                               `YEAR` varchar(4) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                               `CALIBER_ID` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                               `ACCURACY_NAME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                               `ASSESS_LIST` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                               `WEIGHT_LIST` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                               `VALID` tinyint(4) DEFAULT NULL,
                                               `CREATETIME` timestamp(0) DEFAULT NULL,
                                               PRIMARY KEY (`ID`) USING BTREE,
                                               UNIQUE INDEX `INDEX33565861`(`YEAR`, `CALIBER_ID`, `ACCURACY_NAME`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

INSERT INTO `tsie_menu`(`ID`, `NAME`, `TYPE`, `MENUPATH`, `PARENTID`, `DESCRIPTION`, `TARGETTYPE`, `REL`, `SORT`, `PLATFORM`) VALUES ('05020500', '考核点配置', 1, '/apc', '05020000', '考核点配置', 'navTab', '考核点配置', 5201, NULL);
INSERT INTO `tsie_menu`(`ID`, `NAME`, `TYPE`, `MENUPATH`, `PARENTID`, `DESCRIPTION`, `TARGETTYPE`, `REL`, `SORT`, `PLATFORM`) VALUES ('05020600', '考核点预测概况', 1, '/forecast', '05020000', '考核点预测概况', 'navTab', '考核点预测概况', 5201, NULL);
INSERT INTO `tsie_menu`(`ID`, `NAME`, `TYPE`, `MENUPATH`, `PARENTID`, `DESCRIPTION`, `TARGETTYPE`, `REL`, `SORT`, `PLATFORM`) VALUES ('05020700', '考核点评估', 1, '/evaluate', '05020000', '考核点评估', 'navTab', '考核点评估', 5201, NULL);


CREATE TABLE `accuracy_assess_service_recall`  (
                                                   `ID` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                                   `DATE` date NOT NULL,
                                                   `CITY_ID` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                                   `CALIBER_ID` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                                   `ALGORITHM_ID` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                                   `ASSESS_NAME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                                   `ASSESS_ID` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                                   `ASSESS_TYPE` tinyint(4) DEFAULT NULL,
                                                   `ACCURACY` decimal(32, 4) DEFAULT NULL,
                                                   `CREATETIME` timestamp(0) DEFAULT NULL,
                                                   `UPDATETIME` timestamp(0) DEFAULT NULL,
                                                   `REPORT` tinyint(4) DEFAULT NULL,
                                                   PRIMARY KEY (`ID`) USING BTREE,
                                                   INDEX `index`(`DATE`, `CITY_ID`, `CALIBER_ID`, `ALGORITHM_ID`, `ASSESS_NAME`, `CREATETIME`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;


CREATE TABLE `accuracy_composite_service_recall`  (
                                                      `ID` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                                      `DATE` date NOT NULL,
                                                      `CITY_ID` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                                      `CALIBER_ID` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                                      `ALGORITHM_ID` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                                      `ACCURACY_NAME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                                      `ACCURACY_ID` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                                      `ASSESS_TYPE` tinyint(4) DEFAULT NULL,
                                                      `ACCURACY` decimal(32, 4) DEFAULT NULL,
                                                      `CREATETIME` timestamp(0) DEFAULT NULL,
                                                      `UPDATETIME` timestamp(0) DEFAULT NULL,
                                                      `REPORT` tinyint(4) DEFAULT NULL,
                                                      PRIMARY KEY (`ID`) USING BTREE,
                                                      INDEX `index`(`DATE`, `CITY_ID`, `CALIBER_ID`, `ALGORITHM_ID`, `ACCURACY_NAME`, `CREATETIME`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

ALTER TABLE "qnhl_lf_fj_xianchang".ACCURACY_ASSESS_SERVICE
    ADD ASSESS_ID VARCHAR(32);

ALTER TABLE "qnhl_lf_fj_xianchang".ACCURACY_COMPOSITE_SERVICE
    ADD ACCURACY_ID VARCHAR(32);
INSERT INTO qnhl_lf_fj_xianchang.tsie_menu (ID, NAME, TYPE, MENUPATH, PARENTID, DESCRIPTION, TARGETTYPE, REL, SORT, PLATFORM) VALUES ('05020800', '考核点预测回溯', 1, '/recall', '05020000', '考核点预测回溯', 'navTab', '考核点预测回溯', 5201, null)

CREATE TABLE "weather_city_his_load_forecast" (
    "ID" varchar(32) NOT NULL,
    "DATE" date NOT NULL,
    "WEATHER_DATE" date NOT NULL,
    "CITY_ID" varchar(32) NOT NULL,
    "WEATHER_CITY_ID" varchar(32) NOT NULL,
    "ALGORITHM_ID" varchar(32) NOT NULL,
    "CALIBER_ID" varchar(32) NOT NULL,
    "TYPE" tinyint(4) DEFAULT NULL,
    "BATCH_ID" tinyint(4) DEFAULT NULL,
    "NORMAL" tinyint(4) DEFAULT NULL,
    "T0000" decimal(32,2) DEFAULT NULL,
    "T0015" decimal(32,2) DEFAULT NULL,
    "T0030" decimal(32,2) DEFAULT NULL,
    "T0045" decimal(32,2) DEFAULT NULL,
    "T0100" decimal(32,2) DEFAULT NULL,
    "T0115" decimal(32,2) DEFAULT NULL,
    "T0130" decimal(32,2) DEFAULT NULL,
    "T0145" decimal(32,2) DEFAULT NULL,
    "T0200" decimal(32,2) DEFAULT NULL,
    "T0215" decimal(32,2) DEFAULT NULL,
    "T0230" decimal(32,2) DEFAULT NULL,
    "T0245" decimal(32,2) DEFAULT NULL,
    "T0300" decimal(32,2) DEFAULT NULL,
    "T0315" decimal(32,2) DEFAULT NULL,
    "T0330" decimal(32,2) DEFAULT NULL,
    "T0345" decimal(32,2) DEFAULT NULL,
    "T0400" decimal(32,2) DEFAULT NULL,
    "T0415" decimal(32,2) DEFAULT NULL,
    "T0430" decimal(32,2) DEFAULT NULL,
    "T0445" decimal(32,2) DEFAULT NULL,
    "T0500" decimal(32,2) DEFAULT NULL,
    "T0515" decimal(32,2) DEFAULT NULL,
    "T0530" decimal(32,2) DEFAULT NULL,
    "T0545" decimal(32,2) DEFAULT NULL,
    "T0600" decimal(32,2) DEFAULT NULL,
    "T0615" decimal(32,2) DEFAULT NULL,
    "T0630" decimal(32,2) DEFAULT NULL,
    "T0645" decimal(32,2) DEFAULT NULL,
    "T0700" decimal(32,2) DEFAULT NULL,
    "T0715" decimal(32,2) DEFAULT NULL,
    "T0730" decimal(32,2) DEFAULT NULL,
    "T0745" decimal(32,2) DEFAULT NULL,
    "T0800" decimal(32,2) DEFAULT NULL,
    "T0815" decimal(32,2) DEFAULT NULL,
    "T0830" decimal(32,2) DEFAULT NULL,
    "T0845" decimal(32,2) DEFAULT NULL,
    "T0900" decimal(32,2) DEFAULT NULL,
    "T0915" decimal(32,2) DEFAULT NULL,
    "T0930" decimal(32,2) DEFAULT NULL,
    "T0945" decimal(32,2) DEFAULT NULL,
    "T1000" decimal(32,2) DEFAULT NULL,
    "T1015" decimal(32,2) DEFAULT NULL,
    "T1030" decimal(32,2) DEFAULT NULL,
    "T1045" decimal(32,2) DEFAULT NULL,
    "T1100" decimal(32,2) DEFAULT NULL,
    "T1115" decimal(32,2) DEFAULT NULL,
    "T1130" decimal(32,2) DEFAULT NULL,
    "T1145" decimal(32,2) DEFAULT NULL,
    "T1200" decimal(32,2) DEFAULT NULL,
    "T1215" decimal(32,2) DEFAULT NULL,
    "T1230" decimal(32,2) DEFAULT NULL,
    "T1245" decimal(32,2) DEFAULT NULL,
    "T1300" decimal(32,2) DEFAULT NULL,
    "T1315" decimal(32,2) DEFAULT NULL,
    "T1330" decimal(32,2) DEFAULT NULL,
    "T1345" decimal(32,2) DEFAULT NULL,
    "T1400" decimal(32,2) DEFAULT NULL,
    "T1415" decimal(32,2) DEFAULT NULL,
    "T1430" decimal(32,2) DEFAULT NULL,
    "T1445" decimal(32,2) DEFAULT NULL,
    "T1500" decimal(32,2) DEFAULT NULL,
    "T1515" decimal(32,2) DEFAULT NULL,
    "T1530" decimal(32,2) DEFAULT NULL,
    "T1545" decimal(32,2) DEFAULT NULL,
    "T1600" decimal(32,2) DEFAULT NULL,
    "T1615" decimal(32,2) DEFAULT NULL,
    "T1630" decimal(32,2) DEFAULT NULL,
    "T1645" decimal(32,2) DEFAULT NULL,
    "T1700" decimal(32,2) DEFAULT NULL,
    "T1715" decimal(32,2) DEFAULT NULL,
    "T1730" decimal(32,2) DEFAULT NULL,
    "T1745" decimal(32,2) DEFAULT NULL,
    "T1800" decimal(32,2) DEFAULT NULL,
    "T1815" decimal(32,2) DEFAULT NULL,
    "T1830" decimal(32,2) DEFAULT NULL,
    "T1845" decimal(32,2) DEFAULT NULL,
    "T1900" decimal(32,2) DEFAULT NULL,
    "T1915" decimal(32,2) DEFAULT NULL,
    "T1930" decimal(32,2) DEFAULT NULL,
    "T1945" decimal(32,2) DEFAULT NULL,
    "T2000" decimal(32,2) DEFAULT NULL,
    "T2015" decimal(32,2) DEFAULT NULL,
    "T2030" decimal(32,2) DEFAULT NULL,
    "T2045" decimal(32,2) DEFAULT NULL,
    "T2100" decimal(32,2) DEFAULT NULL,
    "T2115" decimal(32,2) DEFAULT NULL,
    "T2130" decimal(32,2) DEFAULT NULL,
    "T2145" decimal(32,2) DEFAULT NULL,
    "T2200" decimal(32,2) DEFAULT NULL,
    "T2215" decimal(32,2) DEFAULT NULL,
    "T2230" decimal(32,2) DEFAULT NULL,
    "T2245" decimal(32,2) DEFAULT NULL,
    "T2300" decimal(32,2) DEFAULT NULL,
    "T2315" decimal(32,2) DEFAULT NULL,
    "T2330" decimal(32,2) DEFAULT NULL,
    "T2345" decimal(32,2) DEFAULT NULL,
    "T2400" decimal(32,2) DEFAULT NULL,
    "CREATETIME" datetime DEFAULT NULL,
    "UPDATETIME" datetime DEFAULT NULL,
    "WEATHER_CREATE_TIME" datetime DEFAULT NULL,
    UNIQUE KEY "DATE" ("DATE","CITY_ID","TYPE","WEATHER_CITY_ID","CALIBER_ID","ALGORITHM_ID","BATCH_ID","WEATHER_DATE") USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

ALTER TABLE weather_city_fc_load_forecast ADD "WEATHER_DATE" date NOT NULL;
ALTER TABLE weather_city_fc_load_forecast ADD "WEATHER_CITY_ID" varchar(32) NOT NULL;
ALTER TABLE weather_city_fc_load_forecast ADD "WEATHER_CREATE_TIME" datetime DEFAULT NULL;
ALTER TABLE weather_city_fc_load_forecast ADD "NORMAL" tinyint(4) DEFAULT NULL;


CREATE TABLE "weather_city_his_load_forecast_recall" (
   "ID" varchar(32) NOT NULL,
   "DATE" date NOT NULL,
   "WEATHER_DATE" date NOT NULL,
   "CITY_ID" varchar(32) NOT NULL,
   "WEATHER_CITY_ID" varchar(32) NOT NULL,
   "ALGORITHM_ID" varchar(32) NOT NULL,
   "CALIBER_ID" varchar(32) NOT NULL,
   "TYPE" tinyint(4) DEFAULT NULL,
   "NORMAL" tinyint(4) DEFAULT NULL,
   "T0000" decimal(32,2) DEFAULT NULL,
   "T0015" decimal(32,2) DEFAULT NULL,
   "T0030" decimal(32,2) DEFAULT NULL,
   "T0045" decimal(32,2) DEFAULT NULL,
   "T0100" decimal(32,2) DEFAULT NULL,
   "T0115" decimal(32,2) DEFAULT NULL,
   "T0130" decimal(32,2) DEFAULT NULL,
   "T0145" decimal(32,2) DEFAULT NULL,
   "T0200" decimal(32,2) DEFAULT NULL,
   "T0215" decimal(32,2) DEFAULT NULL,
   "T0230" decimal(32,2) DEFAULT NULL,
   "T0245" decimal(32,2) DEFAULT NULL,
   "T0300" decimal(32,2) DEFAULT NULL,
   "T0315" decimal(32,2) DEFAULT NULL,
   "T0330" decimal(32,2) DEFAULT NULL,
   "T0345" decimal(32,2) DEFAULT NULL,
   "T0400" decimal(32,2) DEFAULT NULL,
   "T0415" decimal(32,2) DEFAULT NULL,
   "T0430" decimal(32,2) DEFAULT NULL,
   "T0445" decimal(32,2) DEFAULT NULL,
   "T0500" decimal(32,2) DEFAULT NULL,
   "T0515" decimal(32,2) DEFAULT NULL,
   "T0530" decimal(32,2) DEFAULT NULL,
   "T0545" decimal(32,2) DEFAULT NULL,
   "T0600" decimal(32,2) DEFAULT NULL,
   "T0615" decimal(32,2) DEFAULT NULL,
   "T0630" decimal(32,2) DEFAULT NULL,
   "T0645" decimal(32,2) DEFAULT NULL,
   "T0700" decimal(32,2) DEFAULT NULL,
   "T0715" decimal(32,2) DEFAULT NULL,
   "T0730" decimal(32,2) DEFAULT NULL,
   "T0745" decimal(32,2) DEFAULT NULL,
   "T0800" decimal(32,2) DEFAULT NULL,
   "T0815" decimal(32,2) DEFAULT NULL,
   "T0830" decimal(32,2) DEFAULT NULL,
   "T0845" decimal(32,2) DEFAULT NULL,
   "T0900" decimal(32,2) DEFAULT NULL,
   "T0915" decimal(32,2) DEFAULT NULL,
   "T0930" decimal(32,2) DEFAULT NULL,
   "T0945" decimal(32,2) DEFAULT NULL,
   "T1000" decimal(32,2) DEFAULT NULL,
   "T1015" decimal(32,2) DEFAULT NULL,
   "T1030" decimal(32,2) DEFAULT NULL,
   "T1045" decimal(32,2) DEFAULT NULL,
   "T1100" decimal(32,2) DEFAULT NULL,
   "T1115" decimal(32,2) DEFAULT NULL,
   "T1130" decimal(32,2) DEFAULT NULL,
   "T1145" decimal(32,2) DEFAULT NULL,
   "T1200" decimal(32,2) DEFAULT NULL,
   "T1215" decimal(32,2) DEFAULT NULL,
   "T1230" decimal(32,2) DEFAULT NULL,
   "T1245" decimal(32,2) DEFAULT NULL,
   "T1300" decimal(32,2) DEFAULT NULL,
   "T1315" decimal(32,2) DEFAULT NULL,
   "T1330" decimal(32,2) DEFAULT NULL,
   "T1345" decimal(32,2) DEFAULT NULL,
   "T1400" decimal(32,2) DEFAULT NULL,
   "T1415" decimal(32,2) DEFAULT NULL,
   "T1430" decimal(32,2) DEFAULT NULL,
   "T1445" decimal(32,2) DEFAULT NULL,
   "T1500" decimal(32,2) DEFAULT NULL,
   "T1515" decimal(32,2) DEFAULT NULL,
   "T1530" decimal(32,2) DEFAULT NULL,
   "T1545" decimal(32,2) DEFAULT NULL,
   "T1600" decimal(32,2) DEFAULT NULL,
   "T1615" decimal(32,2) DEFAULT NULL,
   "T1630" decimal(32,2) DEFAULT NULL,
   "T1645" decimal(32,2) DEFAULT NULL,
   "T1700" decimal(32,2) DEFAULT NULL,
   "T1715" decimal(32,2) DEFAULT NULL,
   "T1730" decimal(32,2) DEFAULT NULL,
   "T1745" decimal(32,2) DEFAULT NULL,
   "T1800" decimal(32,2) DEFAULT NULL,
   "T1815" decimal(32,2) DEFAULT NULL,
   "T1830" decimal(32,2) DEFAULT NULL,
   "T1845" decimal(32,2) DEFAULT NULL,
   "T1900" decimal(32,2) DEFAULT NULL,
   "T1915" decimal(32,2) DEFAULT NULL,
   "T1930" decimal(32,2) DEFAULT NULL,
   "T1945" decimal(32,2) DEFAULT NULL,
   "T2000" decimal(32,2) DEFAULT NULL,
   "T2015" decimal(32,2) DEFAULT NULL,
   "T2030" decimal(32,2) DEFAULT NULL,
   "T2045" decimal(32,2) DEFAULT NULL,
   "T2100" decimal(32,2) DEFAULT NULL,
   "T2115" decimal(32,2) DEFAULT NULL,
   "T2130" decimal(32,2) DEFAULT NULL,
   "T2145" decimal(32,2) DEFAULT NULL,
   "T2200" decimal(32,2) DEFAULT NULL,
   "T2215" decimal(32,2) DEFAULT NULL,
   "T2230" decimal(32,2) DEFAULT NULL,
   "T2245" decimal(32,2) DEFAULT NULL,
   "T2300" decimal(32,2) DEFAULT NULL,
   "T2315" decimal(32,2) DEFAULT NULL,
   "T2330" decimal(32,2) DEFAULT NULL,
   "T2345" decimal(32,2) DEFAULT NULL,
   "T2400" decimal(32,2) DEFAULT NULL,
   "CREATETIME" datetime DEFAULT NULL,
   "UPDATETIME" datetime DEFAULT NULL,
   "WEATHER_CREATE_TIME" datetime DEFAULT NULL,
   UNIQUE KEY "DATE" ("DATE","CITY_ID","TYPE","WEATHER_CITY_ID","CALIBER_ID","ALGORITHM_ID","WEATHER_DATE") USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8;


--
ALTER TABLE load_solution_manage ADD season varchar(1) NULL;
