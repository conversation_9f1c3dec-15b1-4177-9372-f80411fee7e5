/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  wangchen
 * Date:  2019/6/5 11:17
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.load.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @create 2020/4/15
 * @since 1.0.0
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class BaseLoadAnalyseRequest implements Serializable {

    private String cityId;

    private String countyId;

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getCountyId() {
        return countyId;
    }

    public void setCountyId(String countyId) {
        this.countyId = countyId;
    }
}
