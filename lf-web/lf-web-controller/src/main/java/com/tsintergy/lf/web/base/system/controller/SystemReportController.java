package com.tsintergy.lf.web.base.system.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.SystemConstant;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.dto.SaveSystemStepDTO;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemData;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemStepDTO;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemWeatherDTO;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingSystemDO;
import com.tsintergy.lf.web.base.common.request.SaveDayAccuracyWeightRequest;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import com.tsintergy.lf.web.base.system.request.SystemComprehensiveModelRequest;
import com.tsintergy.lf.web.base.system.request.SystemReportRequest;
import com.tsintergy.lf.web.base.system.request.SystemWeatherRequest;
import com.tsintergy.lf.web.base.system.response.IndexInfoResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 系统设置 预测上报 web接口
 *
 * <AUTHOR>
 **/

@RequestMapping("/system")
@RestController
@Slf4j
@Api(tags = "预测上报")
public class SystemReportController extends CommonBaseController {

    @Autowired
    private SettingSystemService settingSystemService;


    @GetMapping("/baseInfo")
    @ApiOperation("查询登录页名称")
    public BaseResp<String> getIndexName () throws Exception{
        SettingSystemDO byFieldId = settingSystemService.findByFieldId(SystemConstant.LOGIN_INFO);
        String setting = byFieldId.getValue();
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(IndexInfoResp.builderBySetting(setting));
        return baseResp;
    }






    /**
     * 系统设置-短期设置 and 超短期预测设置 保存接口
     *
     * <AUTHOR>
     */
    @ApiOperation("短期设置与超短期预测设置")
    @RequestMapping(value = "/setting", method = RequestMethod.POST)
    public BaseResp updateWeatherTyphoon(@RequestBody SystemReportRequest request)
        throws Exception {
        SystemData data = new SystemData();
        BeanUtils.copyProperties(request, data);
        settingSystemService.doUpdate(data);
        BaseResp resp = BaseResp.succResp();
        return resp;
    }


    /**
     * 系统设置-短期设置 and 超短期预测设置 查询接口
     */
    @ApiOperation("短期设置 and 超短期预测设置")
    @RequestMapping(value = "/setting", method = RequestMethod.GET)
    public BaseResp<SystemData> updateWeatherTyphoon()
        throws Exception {
        SystemData systemSetting = settingSystemService.getSystemSetting();
        BaseResp resp = BaseResp.succResp();
        if (systemSetting == null) {
            resp.setRetCode("T706");
            return resp;
        }
        resp.setData(systemSetting);
        return resp;
    }


    /**
     * 系统设置-气象预测偏差率设置查询接口
     */
    @ApiOperation("气象预测偏差率设置查询接口")
    @RequestMapping(value = "/weather", method = RequestMethod.GET)
    public BaseResp<List<SystemWeatherDTO>> findWeatherSwitchAndValue()
            throws Exception {
        List<SystemWeatherDTO> weatherSwitchListOnOpen = this.settingSystemService.getWeatherSwitchList(false);
        BaseResp resp = BaseResp.succResp();
        if (weatherSwitchListOnOpen == null) {
            resp.setRetCode("T706");
            return resp;
        }
        resp.setData(weatherSwitchListOnOpen);
        return resp;
    }

    /**
     * 系统设置-气象预测偏差率设置 接口
     */
    @ApiOperation("气象预测偏差率设置")
    @RequestMapping(value = "/weather", method = RequestMethod.POST)
    public BaseResp findWeatherSwitchAndValue(@RequestBody SystemWeatherRequest request)
            throws Exception {
        this.settingSystemService.updateWeatherSwitch(request.getData());
        return BaseResp.succResp();
    }

    /*
     * 设置综合模型比例
     * */
    @ApiOperation("设置综合模型比例")
    @RequestMapping(value = "setComprehensiveModel",method = RequestMethod.POST)
    public BaseResp setComprehensiveModel(@RequestBody  List<SystemComprehensiveModelRequest> request ) throws Exception{
        String value = JSON.toJSONString(request);
        settingSystemService.doUpdateOrSaveValueByKey(SystemConstant.SCALE_COMPREHENSIVE_MODEL,value);
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }

    @ApiOperation("获取综合模型")
    @RequestMapping(value = "getComprehensiveModel",method = RequestMethod.GET)
    public BaseResp<JSONObject> getComprehensiveModel() throws Exception{
        SettingSystemDO byFieldId = settingSystemService.findByFieldId(SystemConstant.SCALE_COMPREHENSIVE_MODEL);
        String value = byFieldId.getValue();
        JSONObject jsonObject = JSONObject.parseObject(value);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(jsonObject);
        return baseResp;
    }

    @ApiOperation("系统设置-步长设置查询接口")
    @RequestMapping(value = "/findStepSetting", method = RequestMethod.GET)
    public BaseResp<SystemStepDTO> findStepSetting()throws Exception {
        SystemStepDTO stepSetting = this.settingSystemService.findStepSetting();
        BaseResp resp = BaseResp.succResp();
        if (stepSetting == null) {
            resp.setRetCode("T706");
            return resp;
        }
        resp.setData(stepSetting);
        return resp;
    }

    @ApiOperation("系统设置-步长设置保存接口")
    @RequestMapping(value = "/updateStepSetting", method = RequestMethod.POST)
    public BaseResp updateStepSetting(@RequestBody SaveSystemStepDTO request) throws Exception {
        this.settingSystemService.updateStepSetting(request);
        return BaseResp.succResp();
    }



}
