/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.acload.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * Description:<br>
 *
 * @Author:<EMAIL>
 * @Date: 2022/5/23 14:43
 * @Version:1.0.0
 */
@ApiModel
@Data
public class AcLoadAnalyseRequest {

    @ApiModelProperty(value = "城市id")
    private String cityId;

    @ApiModelProperty(value = "开始时间")
    private Date startDate;

    @ApiModelProperty(value = "结束时间")
    private Date endDate;

    @ApiModelProperty(value = "时间")
    private Date date;

    private String weatherType;

    private String weatherId;

    private String loadType;

}