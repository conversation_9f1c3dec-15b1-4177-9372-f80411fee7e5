/**
 * Copyright(C),2015‐2019,北京清能互联科技有限公司
 * Author:yangjin
 * Date:2019/9/2210:13
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.index.controller;

import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.enums.LoadFeatureEnum;
import com.tsintergy.lf.serviceapi.algorithm.dto.SimilarDateBean;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsCityDayFcService;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.ForecastService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.api.SimilarDayService;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.*;
import com.tsintergy.lf.serviceapi.base.load.dto.AreaLoadFeatureDTOS;
import com.tsintergy.lf.serviceapi.base.load.dto.IndexLoadFeatureDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureCityMonthDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemData;
import com.tsintergy.lf.serviceapi.base.weather.api.*;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherNameDTO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.*;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import com.tsintergy.lf.web.base.index.response.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 *Description:首页 <br>
 *
 *<AUTHOR>
 *@create 2019/9/22
 *@since 1.0.0
 */


@RequestMapping("/index")
@RestController
@Api(tags = "首页")
public class IndexLfController extends CommonBaseController {

    private static final Logger logger = LoggerFactory.getLogger(IndexLfController.class);

    @Autowired
    LoadFeatureStatService loadFeatureStatService;

    @Autowired
    LoadFeatureCityMonthHisService loadFeatureCityMonthHisService;

    @Autowired
    LoadFeatureCityDayHisService loadFeatureCityDayHisService;

    @Autowired
    CityService cityService;

    @Autowired
    LoadCityFcService loadCityFcService;

    @Autowired
    LoadCityHisService loadCityHisService;

    @Autowired
    StatisticsCityDayFcService statisticsCityDayFcService;

    @Autowired
    SimilarDayService similarDayService;

    @Autowired
    ForecastService forecastService;

    @Autowired
    WeatherCityFcService weatherCityFcService;

    @Autowired
    WeatherCityHisService weatherCityHisService;


    @Autowired
    WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    WeatherFeatureCityDayFcService weatherFeatureCityDayFcService;

    @Autowired
    WeatherStationFcBasicWgService weatherStationFcBasicWgService;

    @Autowired
    WeatherStationHisBasicWgService weatherStationHisBasicWgService;

    private static final String FC = "2";

    private static final String HIS = "1";


    @Autowired
    SettingSystemService settingSystemService;

    @Autowired
    LoadFeatureCityDayFcService loadFeatureCityDayFcService;


    @ApiOperation("首页获取预测日期")
    @RequestMapping(value = "/fcDateList",method = RequestMethod.GET)
    public BaseResp fcDateList() throws Exception{
        SystemData systemSetting = settingSystemService.getSystemSetting();
        String forecastDay = systemSetting.getForecastDay();
        Date systemDate = this.getSystemDate();
        if(StringUtils.isEmpty(forecastDay)){
            forecastDay = "3";
        }
        List<String> dateList = new ArrayList<>();
        for(int i=0;i<=Integer.valueOf(forecastDay);i++){
            Date date = DateUtils.addDays(systemDate, i);
            dateList.add(DateUtils.date2String(date,DateFormatType.SIMPLE_DATE_FORMAT_STR));
        }

        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(dateList);
        return baseResp;
    }


    @ApiOperation("获取所有地市气象特性")
    @RequestMapping("/getCityWeatherFeature")
    public BaseResp<List<CityWeatherFeatureResponse>> getCityWeatherFeature(@ApiParam("日期") String date, @ApiParam("数据类型") String dataType) throws Exception {
        //获取当前系统日期
        Date systemDate = this.getSystemDate();
        //把小时分钟秒同步为000000
        String sysDateStr = DateUtils.date2String(systemDate, DateFormatType.SIMPLE_DATE_FORMAT_STR);
        Date paramDate = DateUtils.string2Date(date, DateFormatType.SIMPLE_DATE_FORMAT_STR);
        List<CityDO> allCitys = cityService.findAllCitys();

        List<CityWeatherFeatureResponse> cityWeatherFeatureResponses = new ArrayList<>();

        //如果dataType为1,那么地市气象特性由地市标准站点气象特性代替
        if ("1".equals(dataType)) {
            List<CityWeatherFeatureResponse> responses = this.getStandardStationWeatherFeature(allCitys, paramDate);
            BaseResp baseResp = BaseResp.succResp();
            baseResp.setData(responses);
            return baseResp;
        }
        //日期和系统日期一致，保证为当天,当天得要要查询实际和历史
        if (date.equals(sysDateStr)) {
            CityWeatherFeatureResponse hisCityWeatherFeatureResponse = new CityWeatherFeatureResponse();
            hisCityWeatherFeatureResponse.setType(HIS);
            List<WeatherFeatureResponse> weatherFeatureResponses = new ArrayList<>();
            for (CityDO cityDO : allCitys) {
                if (cityDO.getType().equals(CityConstants.CITY_TYPE)) {
                    WeatherFeatureResponse todayHisWeather = new WeatherFeatureResponse();
                    String weatherCityId = cityDO.getWeatherCityId();
                    if (!StringUtils.isEmpty(weatherCityId)) {
                        List<WeatherCityHisDO> weatherCityHisDOs = weatherCityHisService
                                .findWeatherCityHisDOs(weatherCityId, null, paramDate, paramDate);

                        todayHisWeather.setCityName(cityDO.getCity());
                        todayHisWeather.setDate(paramDate);
                        if (!CollectionUtils.isEmpty(weatherCityHisDOs)) {
                            for (WeatherCityHisDO weatherCityHisDO : weatherCityHisDOs) {
                                Map<String, BigDecimal> decimalMap = statisticsWeatherFeature(weatherCityHisDO);
                                if (WeatherEnum.TEMPERATURE.getType().equals(weatherCityHisDO.getType())) {
                                    todayHisWeather.setMaxTem(decimalMap.get(BasePeriodUtils.LOAD_MAX));
                                    todayHisWeather.setMinTem(decimalMap.get(BasePeriodUtils.LOAD_MIN));
                                } else if (WeatherEnum.HUMIDITY.getType().equals(weatherCityHisDO.getType())) {
                                    todayHisWeather.setHum(decimalMap.get(BasePeriodUtils.LOAD_AVG));
                                } else if (WeatherEnum.RAINFALL.getType().equals(weatherCityHisDO.getType())) {
                                    todayHisWeather.setRainfull(decimalMap.get("sum"));
                                }
                                if (WeatherEnum.WINDSPEED.getType().equals(weatherCityHisDO.getType())) {
                                    todayHisWeather.setWindSpedd(decimalMap.get(BasePeriodUtils.LOAD_AVG));
                                }
                            }
                        }
                        weatherFeatureResponses.add(todayHisWeather);
                    }
                }
            }
            hisCityWeatherFeatureResponse.setTodayWeatherFeature(weatherFeatureResponses);
            cityWeatherFeatureResponses.add(hisCityWeatherFeatureResponse);
        }
        //不是当天只需要查询对应日期的预测气象特性

        CityWeatherFeatureResponse fcCityWeatherFeatureResponse = new CityWeatherFeatureResponse();
        fcCityWeatherFeatureResponse.setType(FC);
        List<WeatherFeatureResponse> WeatherFeatureResponses = new ArrayList<>();
        for(CityDO cityDO:allCitys){
            if(cityDO.getType().equals(CityConstants.CITY_TYPE)) {
                WeatherFeatureResponse weatherFeatureResponse = new WeatherFeatureResponse();
                weatherFeatureResponse.setDate(paramDate);
                weatherFeatureResponse.setCityName(cityDO.getCity());

                String weatherCityId = cityDO.getWeatherCityId();
                WeatherFeatureCityDayFcDO todayFcDO = weatherFeatureCityDayFcService
                    .findWeatherFeatureCityDayFcVO(paramDate, weatherCityId);
                if(todayFcDO != null) {
                    weatherFeatureResponse.setHum(todayFcDO.getAveHumidity());
                    weatherFeatureResponse.setMaxTem(todayFcDO.getHighestTemperature());
                    weatherFeatureResponse.setMinTem(todayFcDO.getLowestTemperature());
                    weatherFeatureResponse.setRainfull(todayFcDO.getRainfall());
                    weatherFeatureResponse.setWindSpedd(todayFcDO.getAveWinds());
                }
                WeatherFeatureResponses.add(weatherFeatureResponse);
            }
        }
        fcCityWeatherFeatureResponse.setTodayWeatherFeature(WeatherFeatureResponses);
        cityWeatherFeatureResponses.add(fcCityWeatherFeatureResponse);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(cityWeatherFeatureResponses);
        return baseResp;

    }

    private List<CityWeatherFeatureResponse> getStandardStationWeatherFeature(List<CityDO> allCitys, Date date) {

        List<CityWeatherFeatureResponse> result = new ArrayList<>();

        CityWeatherFeatureResponse hisCityWeatherFeatureResponse = new CityWeatherFeatureResponse();
        hisCityWeatherFeatureResponse.setType(HIS);
        List<WeatherFeatureResponse> hisWeatherFeatureResponses = new ArrayList<>();

        CityWeatherFeatureResponse fcCityWeatherFeatureResponse = new CityWeatherFeatureResponse();
        fcCityWeatherFeatureResponse.setType(FC);
        List<WeatherFeatureResponse> fcWeatherFeatureResponses = new ArrayList<>();


        List<WeatherStationHisBasicWgDO> stationHisBasicWgDOS = weatherStationHisBasicWgService.getStandardStationHisWeatherBasic(null, null, date, date);
        Map<String, List<WeatherStationHisBasicWgDO>> hisWeatherGroupedByStationWgId = stationHisBasicWgDOS.stream()
                .collect(Collectors.groupingBy(WeatherStationHisBasicWgDO::getStationWgId));

        List<WeatherStationFcBasicWgDO> stationFcBasicWgDOS = weatherStationFcBasicWgService.getStandardStationFcWeatherBasic(null, null, date, date);
        Map<String, List<WeatherStationFcBasicWgDO>> fcWeatherGroupedByStationWgId = stationFcBasicWgDOS.stream()
                .collect(Collectors.groupingBy(WeatherStationFcBasicWgDO::getStationWgId));

        for (CityDO cityDO : allCitys) {
            //地市标准站点历史气象特性
            WeatherFeatureResponse hisWeatherFeature = new WeatherFeatureResponse();
            hisWeatherFeature.setCityName(cityDO.getCity());
            hisWeatherFeature.setDate(date);
            String hisStationId = CityConstants.standardWgStationMap.get(cityDO.getId());
            List<WeatherStationHisBasicWgDO> weatherStationHisBasicWgDOS = hisWeatherGroupedByStationWgId.get(hisStationId);
            if (!CollectionUtils.isEmpty(weatherStationHisBasicWgDOS)) {
                for (WeatherStationHisBasicWgDO hisBasicWgDO : weatherStationHisBasicWgDOS) {
                    WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                    BeanUtils.copyProperties(hisBasicWgDO, weatherCityHisDO);
                    Map<String, BigDecimal> decimalMap = statisticsWeatherFeature(weatherCityHisDO);
                    if (WeatherEnum.TEMPERATURE.getType().equals(weatherCityHisDO.getType())) {
                        hisWeatherFeature.setMaxTem(decimalMap.get(BasePeriodUtils.LOAD_MAX));
                        hisWeatherFeature.setMinTem(decimalMap.get(BasePeriodUtils.LOAD_MIN));
                    } else if (WeatherEnum.HUMIDITY.getType().equals(weatherCityHisDO.getType())) {
                        hisWeatherFeature.setHum(decimalMap.get(BasePeriodUtils.LOAD_AVG));
                    } else if (WeatherEnum.RAINFALL.getType().equals(weatherCityHisDO.getType())) {
                        hisWeatherFeature.setRainfull(decimalMap.get("sum"));
                    }
                    if (WeatherEnum.WINDSPEED.getType().equals(weatherCityHisDO.getType())) {
                        hisWeatherFeature.setWindSpedd(decimalMap.get(BasePeriodUtils.LOAD_AVG));
                    }
                }
            }
            hisWeatherFeatureResponses.add(hisWeatherFeature);

            //地市标准站点预测气象特性
            WeatherFeatureResponse fcWeatherFeature = new WeatherFeatureResponse();
            fcWeatherFeature.setCityName(cityDO.getCity());
            fcWeatherFeature.setDate(date);
            String fcStationId = CityConstants.standardWgStationMap.get(cityDO.getId());
            List<WeatherStationFcBasicWgDO> weatherStationFcBasicWgDOS = fcWeatherGroupedByStationWgId.get(fcStationId);
            if (!CollectionUtils.isEmpty(weatherStationFcBasicWgDOS)) {
                for (WeatherStationFcBasicWgDO fcBasicWgDO : weatherStationFcBasicWgDOS) {
                    WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                    BeanUtils.copyProperties(fcBasicWgDO, weatherCityHisDO);
                    Map<String, BigDecimal> decimalMap = statisticsWeatherFeature(weatherCityHisDO);
                    if (WeatherEnum.TEMPERATURE.getType().equals(weatherCityHisDO.getType())) {
                        fcWeatherFeature.setMaxTem(decimalMap.get(BasePeriodUtils.LOAD_MAX));
                        fcWeatherFeature.setMinTem(decimalMap.get(BasePeriodUtils.LOAD_MIN));
                    } else if (WeatherEnum.HUMIDITY.getType().equals(weatherCityHisDO.getType())) {
                        fcWeatherFeature.setHum(decimalMap.get(BasePeriodUtils.LOAD_AVG));
                    } else if (WeatherEnum.RAINFALL.getType().equals(weatherCityHisDO.getType())) {
                        fcWeatherFeature.setRainfull(decimalMap.get("sum"));
                    }
                    if (WeatherEnum.WINDSPEED.getType().equals(weatherCityHisDO.getType())) {
                        fcWeatherFeature.setWindSpedd(decimalMap.get(BasePeriodUtils.LOAD_AVG));
                    }
                }
            }
            fcWeatherFeatureResponses.add(fcWeatherFeature);
        }
        hisCityWeatherFeatureResponse.setTodayWeatherFeature(hisWeatherFeatureResponses);
        fcCityWeatherFeatureResponse.setTodayWeatherFeature(fcWeatherFeatureResponses);
        result.add(hisCityWeatherFeatureResponse);
        result.add(fcCityWeatherFeatureResponse);
        return result;
    }


    @ApiOperation("通过类型获取角色")
    @RequestMapping("/getPowerCharacterByType")
    public BaseResp<IndexLoadFeatureDTO> getPowerCharacter(@ApiParam("日期类型") String dateType) throws Exception {

        String endStr = DateUtils.date2String(this.getSystemDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR);
        String startStr = getStartStr(dateType, this.getSystemDate(), false);
        //只查询省的数据，所以cityId固定为1
        IndexLoadFeatureDTO statLoadFeatureCityMonth = loadFeatureStatService.findStatLoadFeatureCityMonth(
                CityConstants.PROVINCE_ID, startStr, endStr, this.getCaliberId());
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(statLoadFeatureCityMonth);
        return baseResp;
    }


    @RequestMapping("/getFeatureByType")
    @ApiOperation("通过类型获取特征")
    public BaseResp<List<LoadFeatureCityMonthDTO>> getFeatureByType(@ApiParam("日期类型")String dateType,@ApiParam("数据类型")String dataType) throws Exception{

        Date systemDate = this.getSystemDate();
        Calendar endTime = Calendar.getInstance();
        endTime.setTime(systemDate);
        endTime.add(Calendar.MONTH,-1);
        SimpleDateFormat format= new SimpleDateFormat("yyyy-MM-dd");
        String endStr = format.format(endTime.getTime());
        String startStr = getStartStr(dateType, systemDate , true);
        List<LoadFeatureCityMonthDTO> typePowerFeatureDate = loadFeatureStatService.findTypePowerFeatureDate(dataType, format.parse(startStr), format.parse(endStr), CityConstants.PROVINCE_ID, this.getCaliberId());

        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(typePowerFeatureDate);
        return baseResp;
    }



    /**
     * 功能描述:查询日期内该省最大负荷以及其地市最大负荷占比 <br>
     * 〈〉
     *
     * @param date
     * @return:com.tsie.core.common.base.BaseResp
     * @since: 1.0.0
     * @Author:yangjin
     * @Date: 2019/9/27 13:16
     */

    @ApiOperation("查询日期内该省最大负荷以及其地市最大负荷占比")
    @RequestMapping("getCityFeatureProportion")
    public BaseResp<HashMap<Object, Object>> getCityFeatureProportion(@ApiParam("日期")Date date) throws Exception{
        if(date == null){
            date = DateUtils.addDays(new Date(),-1);
        }
        List<CityFeatureProportionResponse> resultLsit = new ArrayList();

        //查询省最大负荷
        List<LoadFeatureCityDayHisDO> provinceLoadFeature = loadFeatureCityDayHisService.findLoadFeatureCityDayHisDOS("1", date, date, "1");
        if(provinceLoadFeature == null||provinceLoadFeature.size() ==0){
            throw TsieExceptionUtils.newBusinessException("T706");
        }
        LoadFeatureCityDayHisDO loadFeatureCityDayHisVO = provinceLoadFeature.stream().max(Comparator.comparing(LoadFeatureCityDayHisDO::getMaxLoad)).get();
        BigDecimal maxLoadVo = loadFeatureCityDayHisVO.getMaxLoad();

        //查询该省所有地市的负荷特性
        List<LoadFeatureCityDayHisDO> findLoadFeatureCityDayHisDOS = loadFeatureCityDayHisService.findLoadFeatureCityDayHisDOS(null, date, date, "1");
        //计算占比
        for(LoadFeatureCityDayHisDO vo : findLoadFeatureCityDayHisDOS){
            if(loadFeatureCityDayHisVO.getCityId().equals(vo.getCityId())){continue;}
            BigDecimal maxLoad = vo.getMaxLoad();
            String cityid = vo.getCityId();
            CityDO city = cityService.findCityById(cityid);
            CityFeatureProportionResponse response = new CityFeatureProportionResponse();
            response.setCityName(city.getCity());
            response.setMaxLoad(maxLoad);
            BigDecimal divide = maxLoad.divide(maxLoadVo, 4, BigDecimal.ROUND_HALF_UP);
            response.setProportion(Double.valueOf(divide.multiply(new BigDecimal(100)).setScale(2,BigDecimal.ROUND_HALF_UP).toString()));
            resultLsit.add(response);
        }
        //排序
        List<CityFeatureProportionResponse> collect = resultLsit.stream().sorted(Comparator.comparing(CityFeatureProportionResponse::getProportion).reversed()).collect(Collectors.toList());


        HashMap<Object, Object> resultMap = new HashMap<>();
        resultMap.put("data",collect);
        resultMap.put("maxLoad",maxLoadVo);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(resultMap);
        return baseResp;
    }


    /**
     * 功能描述:查询当前时间出力特性 <br>
     * 〈〉
     *
     * @param date
     * @param hour
     * @param minute
     * @return:com.tsie.core.common.base.BaseResp
     * @since: 1.0.0
     * @Author:yangjin
     * @Date: 2019/9/27 13:21
     */
    @ApiOperation("查询当前时间出力特性")
    @RequestMapping("/getRealTimePowerFeature")
    public BaseResp<IndexLfReponse> getRealTimePowerFeature(@ApiParam("日期")Date date, @ApiParam("小时")String hour, @ApiParam("分钟")String minute) throws Exception {

        String cityId = "1";

        String  caliberId = getCaliberId();

        if(date == null){
            date = getSystemDate();
        }

        //查询昨日负荷特性
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisVOS = loadFeatureCityDayHisService.findLoadFeatureCityDayHisDOS(cityId, DateUtils.addDays(date, -1), DateUtils.addDays(date, -1), caliberId);
        BigDecimal yesterdayMaxPower = null;
        if (loadFeatureCityDayHisVOS != null && loadFeatureCityDayHisVOS.size() == 1) {
            yesterdayMaxPower = loadFeatureCityDayHisVOS.get(0).getMaxLoad();
        } else {
            //不能因为一个数据有问题就抛异常，这里可以写日志记录
            logger.error(DateUtils.addDays(date, -1) + "城市id为" + cityId + "负荷特性统计有误");
        }

        //查询今日预测数据
        LoadCityFcDO reportLoadCityFcDO = loadCityFcService.getReportLoadCityFcDOWithOutNull(date, cityId, caliberId);
        List<BigDecimal> bigDecimals = null;
        BigDecimal max = null;
        if (reportLoadCityFcDO != null) {
            bigDecimals = BasePeriodUtils.toList(reportLoadCityFcDO, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
            max = Collections.max(bigDecimals.stream().filter(Objects::nonNull).collect(Collectors.toList()));
        }
        //查询今天负荷
        List<LoadCityHisDO> loadCityHisVO = loadCityHisService.find24LoadCityHisDO(date, cityId, caliberId);
        IndexLfReponse indexLfReponse=new IndexLfReponse();
        indexLfReponse.setYesterdayMaxPower(yesterdayMaxPower);
        indexLfReponse.setTodayFcMaxPower(max);

        if(loadCityHisVO!=null&&loadCityHisVO.size()>0) {
            List<BigDecimal> load = BasePeriodUtils.toList(loadCityHisVO.get(0), 24, Constants.LOAD_CURVE_START_WITH_ZERO);
            indexLfReponse.setRealTime(load);

        }else {
            indexLfReponse.setRealTime(null);
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(indexLfReponse);
        return baseResp;
    }


    @ApiOperation("获取报告准确性统计")
    @RequestMapping("/getReportAccuracyStat")
    public BaseResp<HashMap<String, Double>> getReportAccuracyStat(@ApiParam("日期")Date date) throws Exception{

        String cityId ="1";

        String  caliberId = getCaliberId();
        //设置当前日期
        if(date == null){
            date = getSystemDate();
        }

        //昨天准确率查询
        List<StatisticsCityDayFcDO> yesterday = statisticsCityDayFcService.getReportAccuracy(cityId, caliberId, DateUtils.addDays(date, -1), DateUtils.addDays(date, -1));

        //基准日准确率查询
        List<StatisticsCityDayFcDO> baseDay = statisticsCityDayFcService.getReportAccuracy(cityId, caliberId, DateUtils.addDays(date, -2), DateUtils.addDays(date, -2));


        //70天内相似日查找
        Date start = DateUtils.addDays(date, -70);
        Date end = DateUtils.addDays(date, -1);
        List<SimilarDateBean> similarDateBeans = null;
        try {
             similarDateBeans = similarDayService.listSimilarDayWithFcWeather(cityId, caliberId, date, start, end, 1, super.getLoginUserId());
        }catch (Exception e){

        }
        List<StatisticsCityDayFcDO> similday = null;
        if(similarDateBeans !=null&&similarDateBeans.size()>0) {
            Date simildate = similarDateBeans.get(0).getDate();
            //相似日准确率查询
             similday = statisticsCityDayFcService.getReportAccuracy(cityId, caliberId, simildate, simildate);
        }
        HashMap<String, Double> result = new HashMap<>();
        if(yesterday!=null&&yesterday.size()>0){
            result.put("yesterday",yesterday.get(0).getAccuracy().multiply(new BigDecimal(100)).setScale(4,BigDecimal.ROUND_HALF_UP).doubleValue());
        }


        if(baseDay!=null&&baseDay.size()>0) {
            result.put("baseDay", baseDay.get(0).getAccuracy().multiply(new BigDecimal(100)).setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue());
        }


        if(similday!=null&&similday.size()>0) {
            result.put("similday", similday.get(0).getAccuracy().multiply(new BigDecimal(100)).setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue());
        }

        if(result.get("yesterday") == null&&result.get("baseDay")==null&&result.get("similday") == null){
            throw TsieExceptionUtils.newBusinessException("T706");
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(result);
        return  baseResp;
    }

    @ApiOperation("获取负载数据")
    @RequestMapping(value = "/getLoadData")
    public BaseResp<HashMap<String, Object>> getLoadData(@ApiParam("日期")Date date) throws Exception {

        //省
        String cityId = "1";
        String caliberId = getCaliberId();
        if (date == null) {
            date = getSystemDate();
        }

        //查询预测曲线
        List<BigDecimal> reportFcLoad = null;
        LoadCityFcDO reportLoadCityFcDO = null;
        try {
             reportLoadCityFcDO = loadCityFcService.getReportLoadCityFcDO(date, cityId, caliberId);
             reportFcLoad = BasePeriodUtils.toList(reportLoadCityFcDO, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
        }catch (Exception e){
            logger.error("查询预测负荷失败");
            e.printStackTrace();
        }
        //查询历史曲线
        List<BigDecimal> loadCityHisVO = null;
        try {
             loadCityHisVO = loadCityHisService.findLoadCityHisDO(date, cityId, caliberId);
        }catch (Exception e){
            logger.error("查询历史负荷失败");
            e.printStackTrace();
        }
        //判断是否为空
       if(reportLoadCityFcDO == null&&(loadCityHisVO==null||loadCityHisVO.size()==0)){
            throw TsieExceptionUtils.newBusinessException("T706");
       }

        HashMap<String, Object> result = new HashMap<>();
        List<SimilarDateBean> similarDateBeans = null;
        Date simildate = null;

        //查询相似日
        try {
            Date start = DateUtils.addDays(date, -70);
            Date end = DateUtils.addDays(date, -1);
            similarDateBeans = similarDayService.listSimilarDayWithFcWeather(cityId, caliberId, date, start, end, 1, super.getLoginUserId());
            simildate = similarDateBeans.get(0).getDate();
        } catch (BusinessException e) {
            logger.error("相似日查询出现错误:" + e.getMessage());
        }

        // 1 设置相似日
        if (simildate != null) {
            List<BigDecimal> similLoad = loadCityHisService.findLoadCityHisDO(simildate, cityId, caliberId);
            result.put("similayLoad", similLoad);
        }
        result.put("similday", simildate);
        //2 设置基准日(直接当前日期减2，其实应该取有完整负荷的最近的一天)
        try {
            List<BigDecimal> baseDayLoad = loadCityHisService.findLoadCityHisDO(DateUtils.addDays(date, -2), cityId, caliberId);
            result.put("baseDayLoad", baseDayLoad);
            result.put("baseday", DateUtils.addDays(date, -2));
        }catch (Exception e){
            logger.error("基准日负荷查询失败");
            e.printStackTrace();
        }

        //置信上限
        if(reportLoadCityFcDO!=null) {
            Map<String, List<BigDecimal>> maxMinConfidence = forecastService.getMaxMinConfidence(cityId, caliberId, reportLoadCityFcDO.getAlgorithmId(), date);
            if (!CollectionUtils.isEmpty(maxMinConfidence) || maxMinConfidence != null) {
                result.put("max", maxMinConfidence.get("max"));
                //设置置信下线
                result.put("min", maxMinConfidence.get("min"));
            }
        }
        result.put("reportFcLoad", reportFcLoad);
        result.put("todayHisLoad", loadCityHisVO);

        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(result);
        return baseResp;
    }

    @ApiOperation("获取天气数据")
    @RequestMapping(value = "/getWeatherData")
    public BaseResp<HashMap<String, Object>> getWeatherData(@ApiParam("日期")Date date) throws Exception {

        //没有省气象用省会市气象代替
        String cityId = "2";

        String caliberId = getCaliberId();

        if (date == null) {
            date = getSystemDate();
        }
        HashMap<String, Object> result = new HashMap<>();
        List<WeatherNameDTO> allFcData = null;
        try {
             allFcData = weatherCityFcService.findAllByDateAndCityId(cityId, date);

        }catch (Exception e){
            logger.error("预测气象数据查询失败");
            e.printStackTrace();
        }


        List<WeatherNameDTO> allHisData = null;
        try {
             allHisData = weatherCityHisService.findAllByDateAndCityId(cityId, date);
        }catch (Exception e){
            logger.error("历史气象数据查询失败");
            e.printStackTrace();
        }


        if(allFcData ==null&&allHisData==null){
            throw TsieExceptionUtils.newBusinessException("T706");
        }


        List<SimilarDateBean> similarDateBeans = null;
        Date simildate = null;

        try {
            //查询相似日
            Date start = DateUtils.addDays(date, -70);
            Date end = DateUtils.addDays(date, -1);
            similarDateBeans = similarDayService.listSimilarDayWithFcWeather(cityId, caliberId, date, start, end, 1, super.getLoginUserId());
            simildate = similarDateBeans.get(0).getDate();
        } catch (BusinessException e) {
            logger.error("相似日查询出现错误:" + e.getMessage());
        }


        // 1 设置相似日
        if (simildate != null) {
            List<WeatherNameDTO> similarWeatherData = weatherCityHisService.findAllByDateAndCityId(cityId, simildate);
            result.put("similayWeather", similarWeatherData);
        }
        result.put("similday", simildate);

        //2 设置基准日
        try {
            List<WeatherNameDTO> baseDayWeather = weatherCityHisService.findAllByDateAndCityId(cityId, DateUtils.addDays(date, -2));
            result.put("baseDayWeather", baseDayWeather);
        }catch (Exception e){
            logger.error("基准日查询气象数据失败");
            e.printStackTrace();
        }

        result.put("fcWearher", allFcData);
        result.put("realWearher", allHisData);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(result);
        return baseResp;

    }


    String getStartStr(String dateType,Date systemDate , Boolean beginOfMonth){
        SimpleDateFormat format= new SimpleDateFormat("yyyy-MM-dd");
        String startStr = null;
        Calendar startTime = Calendar.getInstance();
        startTime.setTime(systemDate);
        if ("0".equals(dateType)) {
            //近3个月的负荷统计
            startTime.add(Calendar.MONTH,-3);
        } else if("1".equals(dateType)){
            //近6个月
            startTime.add(Calendar.MONTH,-6);
        }else if("2".equals(dateType)){
            //近12个月的负荷统计
            startTime.add(Calendar.MONTH,-12);
        }
        if(beginOfMonth){
            startTime.set(Calendar.DATE,1);
        }
        startStr = format.format(startTime.getTime());
        return startStr;
    }
    /**
     * 各个地市最大负荷占比
     * @param caliberId
     * @param date
     * @param type
     * @return
     */
    @ApiOperation("各个地市最大负荷占比")
    @RequestMapping("/getAreaLoadFeature")
    public BaseResp<AreaLoadFeatureDTOS> getAreaLoadFeature(@ApiParam("口径ID")String caliberId,@ApiParam("日期") Date date,@ApiParam("类型") String type) throws Exception {
        if (StringUtils.isBlank(caliberId)) {
            caliberId = super.getCaliberId();
        }
        if (date == null) {
            date = super.getSystemDate();
        }
        Integer loadType =  LoadFeatureEnum.Max.getType();
        if (StringUtils.isNotBlank(type) && type.equals("2")) {
            //最大负荷
            loadType = LoadFeatureEnum.Min.getType();
        }
        AreaLoadFeatureDTOS areaLoadFeatureDTOS = loadFeatureCityDayHisService.findAreaLoadFeatureDTO(caliberId, date, loadType);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(areaLoadFeatureDTOS);
        return baseResp;
    }

    @RequestMapping(value = "/findProvinceWeather", method = RequestMethod.GET)
    public BaseResp findProvinceWeather(Date date, String dataType) throws Exception {
        CityDO city = cityService.findCityByType(CityConstants.PROVINCE_TYPE);
        //使用福州站点平均气象数据代替
        if ("3".equals(dataType)) {
            city = cityService.findCityById("2");
        }
        Date beforeYesterday = DateUtils.addDays(date, -2);
        Date yesterday = DateUtils.addDays(date, -1);
        Date tomrrowFc = DateUtils.addDays(date, 1);

        ProvinceWeatherFeatureResponse response = new ProvinceWeatherFeatureResponse();
        String caliberId = this.getCaliberId();
        //前天历史气象特性
        List<WeatherFeatureCityDayHisDO> beforeYesterdayHisDOS =
                weatherFeatureCityDayHisService.listWeatherFeatureCityDayHisDO(city.getId(), beforeYesterday, beforeYesterday);

        if(caliberId.equals("10")){
            beforeYesterdayHisDOS = weatherFeatureCityDayHisService.findWeatherFeatureCityDayHisDO(Arrays.asList(city.getId()),beforeYesterday,beforeYesterday);
        }



        WeatherFeatureResponse beforeYesterdayHisWeather = new WeatherFeatureResponse();
        beforeYesterdayHisWeather.setDate(beforeYesterday);
        List<LoadFeatureCityDayHisDO> beforeYesterdayLoadFeatureCityDayHisDOS = loadFeatureCityDayHisService.findLoadFeatureCityDayHisDOS(
            city.getId(), beforeYesterday, beforeYesterday, caliberId);
        if(beforeYesterdayLoadFeatureCityDayHisDOS != null){
            LoadFeatureCityDayHisDO loadFeatureCityDayHisDO = beforeYesterdayLoadFeatureCityDayHisDOS.get(0);
            beforeYesterdayHisWeather.setMaxLoad(loadFeatureCityDayHisDO.getMaxLoad());
            beforeYesterdayHisWeather.setMinLoad(loadFeatureCityDayHisDO.getMinLoad());
        }

        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(beforeYesterdayHisDOS)) {
            beforeYesterdayHisWeather.setHum(beforeYesterdayHisDOS.get(0).getAveHumidity());
            beforeYesterdayHisWeather.setMaxTem(beforeYesterdayHisDOS.get(0).getHighestTemperature());
            beforeYesterdayHisWeather.setMinTem(beforeYesterdayHisDOS.get(0).getLowestTemperature());
            beforeYesterdayHisWeather.setRainfull(beforeYesterdayHisDOS.get(0).getRainfall());
            beforeYesterdayHisWeather.setWindSpedd(beforeYesterdayHisDOS.get(0).getAveWinds());
        }
        response.setBeforeYesterdayHisWeather(beforeYesterdayHisWeather);

        //昨天历史气象特性
        List<WeatherFeatureCityDayHisDO> yesterdayHisDOS =
            weatherFeatureCityDayHisService.listWeatherFeatureCityDayHisDO(city.getId(), yesterday, yesterday);

        if(caliberId.equals("10")){
            yesterdayHisDOS = weatherFeatureCityDayHisService.findWeatherFeatureCityDayHisDO(Arrays.asList(city.getId()),yesterday,yesterday);
        }


        WeatherFeatureResponse yesterdayHisWeather = new WeatherFeatureResponse();
        yesterdayHisWeather.setDate(yesterday);

        List<LoadFeatureCityDayHisDO> yesterdayLoadFeatureCityDayHisDOS = loadFeatureCityDayHisService.findLoadFeatureCityDayHisDOS(
            city.getId(), yesterday, yesterday, caliberId);
        if(yesterdayLoadFeatureCityDayHisDOS != null){
            LoadFeatureCityDayHisDO loadFeatureCityDayHisDO = yesterdayLoadFeatureCityDayHisDOS.get(0);
            yesterdayHisWeather.setMaxLoad(loadFeatureCityDayHisDO.getMaxLoad());
            yesterdayHisWeather.setMinLoad(loadFeatureCityDayHisDO.getMinLoad());
        }

        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(yesterdayHisDOS)){
            yesterdayHisWeather.setHum(yesterdayHisDOS.get(0).getAveHumidity());
            yesterdayHisWeather.setMaxTem(yesterdayHisDOS.get(0).getHighestTemperature());
            yesterdayHisWeather.setMinTem(yesterdayHisDOS.get(0).getLowestTemperature());
            yesterdayHisWeather.setRainfull(yesterdayHisDOS.get(0).getRainfall());
            yesterdayHisWeather.setWindSpedd(yesterdayHisDOS.get(0).getAveWinds());
        }
        response.setYesterdayHisWeather(yesterdayHisWeather);

        //今天预测
        WeatherFeatureCityDayFcDO todayFcDO = weatherFeatureCityDayFcService.findWeatherFeatureCityDayFcVO(date, city.getId());
        if(caliberId.equals("10")){
            todayFcDO = weatherFeatureCityDayFcService
                .findWeatherFeatureCityFcVOByDate(city.getId(), date);
        }

        WeatherFeatureResponse todayFcWeather = new WeatherFeatureResponse();
        todayFcWeather.setDate(date);

        LoadFeatureCityDayFcDO todayLoadFeatureCityDayFcReport = loadFeatureCityDayFcService.findLoadFeatureCityDayFcReport(
            city.getId(), caliberId, new java.sql.Date(date.getTime()));
        if(todayLoadFeatureCityDayFcReport != null){
            todayFcWeather.setMinLoad(todayLoadFeatureCityDayFcReport.getMinLoad());
            todayFcWeather.setMaxLoad(todayLoadFeatureCityDayFcReport.getMaxLoad());
        }

        if(todayFcDO != null) {
            todayFcWeather.setHum(todayFcDO.getAveHumidity());
            todayFcWeather.setMaxTem(todayFcDO.getHighestTemperature());
            todayFcWeather.setMinTem(todayFcDO.getLowestTemperature());
            todayFcWeather.setRainfull(todayFcDO.getRainfall());
            todayFcWeather.setWindSpedd(todayFcDO.getAveWinds());
        }
        response.setTodayFcWeather(todayFcWeather);





        //明天预测
        WeatherFeatureCityDayFcDO tomrrowFcDOS = weatherFeatureCityDayFcService.findWeatherFeatureCityDayFcVO(tomrrowFc, city.getId());
        if(caliberId.equals("10")){
            tomrrowFcDOS = weatherFeatureCityDayFcService
                .findWeatherFeatureCityFcVOByDate(city.getId(), tomrrowFc);
        }

        WeatherFeatureResponse afterFcWeather = new WeatherFeatureResponse();
        afterFcWeather.setDate(tomrrowFc);
        LoadFeatureCityDayFcDO tomrrowLoadFeatureCityDayFcReport = loadFeatureCityDayFcService.findLoadFeatureCityDayFcReport(
            city.getId(), caliberId, new java.sql.Date(tomrrowFc.getTime()));
        if(tomrrowLoadFeatureCityDayFcReport != null){
            afterFcWeather.setMinLoad(tomrrowLoadFeatureCityDayFcReport.getMinLoad());
            afterFcWeather.setMaxLoad(tomrrowLoadFeatureCityDayFcReport.getMaxLoad());
        }

        if(tomrrowFcDOS != null) {
            afterFcWeather.setHum(tomrrowFcDOS.getAveHumidity());
            afterFcWeather.setMaxTem(tomrrowFcDOS.getHighestTemperature());
            afterFcWeather.setMinTem(tomrrowFcDOS.getLowestTemperature());
            afterFcWeather.setRainfull(tomrrowFcDOS.getRainfall());
            afterFcWeather.setWindSpedd(tomrrowFcDOS.getAveWinds());
        }
        response.setAfterFcWeather(afterFcWeather);





        //今天实际气象
        List<WeatherCityHisDO> cityHisDOS = weatherCityHisService.findWeatherCityHisDOs(cityService.findWeatherCityId(city.getId()), null, date, date);
        if(caliberId.equals("10")){
             cityHisDOS = weatherCityHisService.findWeatherCityHisDOs(city.getId(), null, date, date);
        }

        WeatherFeatureResponse todayHisWeather = new WeatherFeatureResponse();
        todayHisWeather.setDate(date);
        List<LoadCityHisDO> loadCityHisDOS = loadCityHisService.findLoadCityHisDOS(city.getId(),date,date , caliberId);
        if(!CollectionUtils.isEmpty(loadCityHisDOS)){
            LoadCityHisDO loadCityHis = loadCityHisDOS.get(0);
            List<BigDecimal> bigDecimals = BasePeriodUtils.toList(loadCityHis, Constants.LOAD_CURVE_POINT_NUM,
                Constants.LOAD_CURVE_START_WITH_ZERO).stream().filter(t->t!=null).collect(Collectors.toList());
            if(bigDecimals.size()!=0) {
                BigDecimal maxLoad = Collections.max(bigDecimals);
                BigDecimal minLoad = Collections.min(bigDecimals);
                todayHisWeather.setMaxLoad(maxLoad);
                todayHisWeather.setMinLoad(minLoad);
            }
        }

        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(cityHisDOS)) {
            if(cityHisDOS.get(0).getUpdatetime() == null){
                todayHisWeather.setUpdatetime(cityHisDOS.get(0).getCreatetime());
            }else {
                todayHisWeather.setUpdatetime(cityHisDOS.get(0).getUpdatetime());
            }
        }

        for(WeatherCityHisDO weatherCityHisDO:cityHisDOS){
            Map<String, BigDecimal> decimalMap = statisticsWeatherFeature(weatherCityHisDO);
            if (WeatherEnum.TEMPERATURE.getType().equals(weatherCityHisDO.getType())) {
                todayHisWeather.setMaxTem(decimalMap.get(BasePeriodUtils.LOAD_MAX));
                todayHisWeather.setMinTem(decimalMap.get(BasePeriodUtils.LOAD_MIN));
            } else if (WeatherEnum.HUMIDITY.getType().equals(weatherCityHisDO.getType())) {
                todayHisWeather.setHum(decimalMap.get(BasePeriodUtils.LOAD_AVG));
            } else if (WeatherEnum.RAINFALL.getType().equals(weatherCityHisDO.getType())) {
                todayHisWeather.setRainfull(decimalMap.get("sum"));
            }
            if (WeatherEnum.WINDSPEED.getType().equals(weatherCityHisDO.getType())) {
                todayHisWeather.setWindSpedd(decimalMap.get(BasePeriodUtils.LOAD_AVG));
            }
        }
        response.setTodayHisWeather(todayHisWeather);

        //使用标准站点气象特性数据代替
        if ("1".equals(dataType)) {
            //预测气象
            List<WeatherStationFcBasicWgDO> stationFcWeatherBasic =
                    weatherStationFcBasicWgService.getStandardStationFcWeatherBasic(city.getId(), null, date, tomrrowFc);
            if (!CollectionUtils.isEmpty(stationFcWeatherBasic)) {
                //今日预测气象特性
                List<WeatherCityHisDO> todayWeatherFc = stationFcWeatherBasic.stream()
                        .filter(fcBasic -> fcBasic.getDate().equals(date))
                        .map(fcBasic -> {
                            WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                            BeanUtils.copyProperties(fcBasic, weatherCityHisDO);
                            return weatherCityHisDO;
                        }).collect(Collectors.toList());
                setStandardStationWeatherFeature(response.getTodayFcWeather(), todayWeatherFc);

                //明日预测气象特性
                List<WeatherCityHisDO> tomrrowWeatherFc = stationFcWeatherBasic.stream()
                        .filter(fcBasic -> fcBasic.getDate().equals(tomrrowFc))
                        .map(fcBasic -> {
                            WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                            BeanUtils.copyProperties(fcBasic, weatherCityHisDO);
                            return weatherCityHisDO;
                        }).collect(Collectors.toList());
                setStandardStationWeatherFeature(response.getAfterFcWeather(), tomrrowWeatherFc);
            }

            //历史气象
            List<WeatherStationHisBasicWgDO> stationHisWeatherBasic =
                    weatherStationHisBasicWgService.getStandardStationHisWeatherBasic(city.getId(), null, beforeYesterday, date);
            if (!CollectionUtils.isEmpty(stationHisWeatherBasic)) {
                //前天实际气象特性
                List<WeatherCityHisDO> beforeYesterdayHis = stationHisWeatherBasic.stream()
                        .filter(fcBasic -> fcBasic.getDate().equals(beforeYesterday))
                        .map(fcBasic -> {
                            WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                            BeanUtils.copyProperties(fcBasic, weatherCityHisDO);
                            return weatherCityHisDO;
                        }).collect(Collectors.toList());
                setStandardStationWeatherFeature(response.getBeforeYesterdayHisWeather(), beforeYesterdayHis);

                //昨天实际气象特性
                List<WeatherCityHisDO> yesterdayWeatherFc = stationHisWeatherBasic.stream()
                        .filter(fcBasic -> fcBasic.getDate().equals(yesterday))
                        .map(fcBasic -> {
                            WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                            BeanUtils.copyProperties(fcBasic, weatherCityHisDO);
                            return weatherCityHisDO;
                        }).collect(Collectors.toList());
                setStandardStationWeatherFeature(response.getYesterdayHisWeather(), yesterdayWeatherFc);

                //今天实际气象特性
                List<WeatherCityHisDO> todayWeatherHis = stationHisWeatherBasic.stream()
                        .filter(fcBasic -> fcBasic.getDate().equals(date))
                        .map(fcBasic -> {
                            WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                            BeanUtils.copyProperties(fcBasic, weatherCityHisDO);
                            return weatherCityHisDO;
                        }).collect(Collectors.toList());
                setStandardStationWeatherFeature(response.getTodayHisWeather(), todayWeatherHis);
            }
        }

        //使用地市标准站点加权平均数据代替(仅福建)
        if ("2".equals(dataType)) {
            //预测气象
            List<WeatherCityFcDO> stationFcWeatherBasic =
                    weatherCityFcService.getProvinceWeightAvgFcWeatherData(null, date, tomrrowFc);
            if (!CollectionUtils.isEmpty(stationFcWeatherBasic)) {
                //今日预测气象特性
                List<WeatherCityHisDO> todayWeatherFc = stationFcWeatherBasic.stream()
                        .filter(fcBasic -> fcBasic.getDate().equals(date))
                        .map(fcBasic -> {
                            WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                            BeanUtils.copyProperties(fcBasic, weatherCityHisDO);
                            return weatherCityHisDO;
                        }).collect(Collectors.toList());
                setStandardStationWeatherFeature(response.getTodayFcWeather(), todayWeatherFc);

                //明日预测气象特性
                List<WeatherCityHisDO> tomrrowWeatherFc = stationFcWeatherBasic.stream()
                        .filter(fcBasic -> fcBasic.getDate().equals(tomrrowFc))
                        .map(fcBasic -> {
                            WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                            BeanUtils.copyProperties(fcBasic, weatherCityHisDO);
                            return weatherCityHisDO;
                        }).collect(Collectors.toList());
                setStandardStationWeatherFeature(response.getAfterFcWeather(), tomrrowWeatherFc);
            }

            //历史气象
            List<WeatherCityHisDO> stationHisWeatherBasic =
                    weatherCityHisService.getProvinceWeightAvgHisWeatherData(null, beforeYesterday, date);
            if (!CollectionUtils.isEmpty(stationHisWeatherBasic)) {
                //前天实际气象特性
                List<WeatherCityHisDO> beforeYesterdayHis = stationHisWeatherBasic.stream()
                        .filter(fcBasic -> fcBasic.getDate().equals(beforeYesterday))
                        .map(fcBasic -> {
                            WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                            BeanUtils.copyProperties(fcBasic, weatherCityHisDO);
                            return weatherCityHisDO;
                        }).collect(Collectors.toList());
                setStandardStationWeatherFeature(response.getBeforeYesterdayHisWeather(), beforeYesterdayHis);

                //昨天实际气象特性
                List<WeatherCityHisDO> yesterdayWeatherFc = stationHisWeatherBasic.stream()
                        .filter(fcBasic -> fcBasic.getDate().equals(yesterday))
                        .map(fcBasic -> {
                            WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                            BeanUtils.copyProperties(fcBasic, weatherCityHisDO);
                            return weatherCityHisDO;
                        }).collect(Collectors.toList());
                setStandardStationWeatherFeature(response.getYesterdayHisWeather(), yesterdayWeatherFc);

                //今天实际气象特性
                List<WeatherCityHisDO> todayWeatherHis = stationHisWeatherBasic.stream()
                        .filter(fcBasic -> fcBasic.getDate().equals(date))
                        .map(fcBasic -> {
                            WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                            BeanUtils.copyProperties(fcBasic, weatherCityHisDO);
                            return weatherCityHisDO;
                        }).collect(Collectors.toList());
                setStandardStationWeatherFeature(response.getTodayHisWeather(), todayWeatherHis);
            }
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(response);
        return baseResp;
    }

    private void setStandardStationWeatherFeature(WeatherFeatureResponse response,
                                                  List<WeatherCityHisDO> weatherCityHisDOS) {
        for (WeatherCityHisDO weatherCityHisDO : weatherCityHisDOS) {
            Map<String, BigDecimal> decimalMap = statisticsWeatherFeature(weatherCityHisDO);
            if (WeatherEnum.TEMPERATURE.getType().equals(weatherCityHisDO.getType())) {
                response.setMaxTem(decimalMap.get(BasePeriodUtils.LOAD_MAX));
                response.setMinTem(decimalMap.get(BasePeriodUtils.LOAD_MIN));
            } else if (WeatherEnum.HUMIDITY.getType().equals(weatherCityHisDO.getType())) {
                response.setHum(decimalMap.get(BasePeriodUtils.LOAD_AVG));
            } else if (WeatherEnum.RAINFALL.getType().equals(weatherCityHisDO.getType())) {
                response.setRainfull(decimalMap.get("sum"));
            }
            if (WeatherEnum.WINDSPEED.getType().equals(weatherCityHisDO.getType())) {
                response.setWindSpedd(decimalMap.get(BasePeriodUtils.LOAD_AVG));
            }
        }
    }

    public Map<String, BigDecimal> statisticsWeatherFeature(WeatherCityHisDO weatherCityHisDO){
        List<BigDecimal> bigDecimals =
            BasePeriodUtils.toList(weatherCityHisDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        List<BigDecimal> bigDecimalsWithOutNull =
            bigDecimals.stream().filter(t -> t != null).collect(Collectors.toList());

        Map<String, BigDecimal> map = new HashMap();
        //风向需要特殊处理
        if(WeatherEnum.WINDDIRECTION.getType().equals(weatherCityHisDO.getType())){
            Map<Integer, Integer> windDirectionTimes = new HashMap<>();
            for(BigDecimal winddirection:bigDecimalsWithOutNull){

                //北
                if(winddirection.compareTo(new BigDecimal(22.5))<=0 || winddirection.compareTo(new BigDecimal(337.5))>0){
                    if(windDirectionTimes.get(0) == null){
                        windDirectionTimes.put(0,1);
                    }else {
                        windDirectionTimes.put(0,windDirectionTimes.get(0)+1);
                    }
                    //东北
                }else if(winddirection.compareTo(new BigDecimal(22.5))>0 && winddirection.compareTo(new BigDecimal(67.5))<=0 ){
                    if(windDirectionTimes.get(45) == null){
                        windDirectionTimes.put(45,1);
                    }else {
                        windDirectionTimes.put(45,windDirectionTimes.get(45)+1);
                    }
                    //东
                }else if(winddirection.compareTo(new BigDecimal(67.5))>0 && winddirection.compareTo(new BigDecimal(112.5))<=0 ){
                    if(windDirectionTimes.get(90) == null){
                        windDirectionTimes.put(90,1);
                    }else {
                        windDirectionTimes.put(90,windDirectionTimes.get(90)+1);
                    }
                    //东南
                }else if(winddirection.compareTo(new BigDecimal(112.5))>0 && winddirection.compareTo(new BigDecimal(157.5))<=0 ){
                    if(windDirectionTimes.get(135) == null){
                        windDirectionTimes.put(135,1);
                    }else {
                        windDirectionTimes.put(135,windDirectionTimes.get(135)+1);
                    }
                    //南
                }else if(winddirection.compareTo(new BigDecimal(157.5))>0 && winddirection.compareTo(new BigDecimal(202.5))<=0 ){
                    if(windDirectionTimes.get(180) == null){
                        windDirectionTimes.put(180,1);
                    }else {
                        windDirectionTimes.put(180,windDirectionTimes.get(180)+1);
                    }
                    //西南
                }else if(winddirection.compareTo(new BigDecimal(202.5))>0 && winddirection.compareTo(new BigDecimal(247.5))<=0 ){
                    if(windDirectionTimes.get(225) == null){
                        windDirectionTimes.put(225,1);
                    }else {
                        windDirectionTimes.put(225,windDirectionTimes.get(225)+1);
                    }
                    //西
                }else if(winddirection.compareTo(new BigDecimal(247.5))>0 && winddirection.compareTo(new BigDecimal(292.5))<=0 ){
                    if(windDirectionTimes.get(270) == null){
                        windDirectionTimes.put(270,1);
                    }else {
                        windDirectionTimes.put(270,windDirectionTimes.get(270)+1);
                    }
                    //西北
                }else if(winddirection.compareTo(new BigDecimal(292.5))>0 && winddirection.compareTo(new BigDecimal(337.5))<=0 ){
                    if(windDirectionTimes.get(315) == null){
                        windDirectionTimes.put(315,1);
                    }else {
                        windDirectionTimes.put(315,windDirectionTimes.get(315)+1);
                    }
                }
            }
            List<Map.Entry<Integer,Integer>> list = new ArrayList(windDirectionTimes.entrySet());
            Collections.sort(list, (o1, o2) -> (o1.getValue() - o2.getValue()));
            Integer key = list.get(list.size() - 1).getKey();
            map.put(BasePeriodUtils.LOAD_AVG,new BigDecimal(key));
        }else {
            if (!bigDecimalsWithOutNull.isEmpty()) {
                BigDecimal max = Collections.max(bigDecimalsWithOutNull);
                BigDecimal min = Collections.min(bigDecimalsWithOutNull);

                //求和
                BigDecimal sum = bigDecimalsWithOutNull.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                //求平均值
                BigDecimal average = sum.divide(BigDecimal.valueOf(bigDecimalsWithOutNull.size()), 2, BigDecimal.ROUND_HALF_UP);
                map.put(BasePeriodUtils.LOAD_AVG, average);
                map.put(BasePeriodUtils.LOAD_MAX,max);
                map.put(BasePeriodUtils.LOAD_MIN,min);
                map.put("sum",sum);
            }
        }
        return map;
    }
}