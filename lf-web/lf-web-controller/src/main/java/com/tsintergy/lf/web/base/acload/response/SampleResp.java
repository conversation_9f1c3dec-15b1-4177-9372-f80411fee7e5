/**
 * Copyright (C), 2015‐2022, 北京清能互联科技有限公司 Author:  <EMAIL> Date:  2022/5/24 1:24 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.acload.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * Description: 样本数据 <br>
 *
 * <AUTHOR>
 * @create 2022/5/24
 * @since 1.0.0
 */
@ApiModel
@Data
public class SampleResp {

    @ApiModelProperty(value = "日期", example = "2022-03-01")
    Date date;

    @ApiModelProperty(value = "温度(℃)", example = "20")
    BigDecimal Temperature;

    BigDecimal highestTemperature;

    BigDecimal lowestTemperature;

    String cityName;

    @ApiModelProperty(value = "降水量（mm）", example = "0.5")
    BigDecimal rainfall;
}  
