/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司 Author:  wangchen Date:  2018/8/6 15:08 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.analyze.controller;


import static com.tsieframework.core.base.exception.TsieExceptionUtils.newBusinessException;

import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.analyze.api.DeviationAnalyzeCityDayFcService;
import com.tsintergy.lf.serviceapi.base.analyze.dto.DeviationAnalyzeDTO;
import com.tsintergy.lf.serviceapi.base.analyze.dto.LoadDataDTO;
import com.tsintergy.lf.serviceapi.base.analyze.dto.ReportAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.analyze.dto.StatisticsDeviationAnalyzeDTO;
import com.tsintergy.lf.serviceapi.base.analyze.pojo.DeviationAnalyzeCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.check.api.SettingReportService;
import com.tsintergy.lf.serviceapi.base.check.pojo.SettingReportDO;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsCityDayFcService;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemData;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.web.base.analyze.request.DeviationAnalyzeRequest;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import com.tsintergy.lf.web.base.weather.request.WeatherHisFcRequest;
import com.tsintergy.lf.web.base.weather.response.WeatherDataResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 误差分析 <br>
 *
 * <AUTHOR>
 * @create 2018/8/6
 * @since 1.0.0
 */
@Api(tags = "误差分析")
@RequestMapping("/analyze")
@RestController
public class DeviationAnalyzeController extends CommonBaseController {

    private static final Logger logger = LogManager.getLogger(DeviationAnalyzeController.class);

    private static BigDecimal QUALIFIED = BigDecimal.valueOf(0.97);

    @Autowired
    private SettingReportService settingReportService;

    @Autowired
    private DeviationAnalyzeCityDayFcService deviationAnalyzeCityDayFcService;

    @Autowired
    private LoadCityFcService loadCityFcService;

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private StatisticsCityDayFcService statisticsCityDayFcService;

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Autowired
    private WeatherCityFcService weatherCityFcService;

    @Autowired
    private CityService cityService;

    @Autowired
    private Environment environment;

    @Autowired
    private SettingSystemService settingSystemService;


    /**
     * 功能描述: <br>
     * <p>
     * 误差统计——准确率、合格率、自动预测天数（柱状图）
     *
     * @return:
     * @since: 1.0.0
     * @Author:wangchen
     * @Date: 2018/8/8 13:12
     */
    @RequestMapping(value = "/accuracy-pass", method = RequestMethod.GET)
    @ApiOperation(value = "误差统计——准确率、合格率、自动预测天数（柱状图）")
    public BaseResp<StatisticsDeviationAnalyzeDTO> getAccuracyPass(DeviationAnalyzeRequest request) {
        String cityId = request.getCityId();
        String caliberId = request.getCaliberId();
        Date startTime = request.getStartTime();
        Date endTime = request.getEndTime();
        if (StringUtils.isBlank(cityId)) {
            cityId = getLoginCityId();
        }
        if (StringUtils.isBlank(caliberId)) {
            caliberId = getCaliberId();
        }
        if (startTime == null) {
            startTime = DateUtils.addDays(getSystemDate(), -30);
        }
        if (endTime == null) {
            endTime = getSystemDate();
        }
        StatisticsDeviationAnalyzeDTO statisticsDeviationAnalyzeDTO = null;
        try {
            statisticsDeviationAnalyzeDTO = statisticsCityDayFcService.findStatisticsDeviationAnalyzeDTO(cityId,
                caliberId, startTime, endTime);
            BaseResp baseResp = BaseResp.succResp();
            if (statisticsDeviationAnalyzeDTO != null) {
                baseResp.setData(statisticsDeviationAnalyzeDTO);
                return baseResp;
            } else {
                return new BaseResp("T706");
            }

        } catch (Exception e) {
            logger.error("02I20180001", e);
            throw newBusinessException("02I20180001");
        }
    }

    /**
     * 功能描述: <br>
     * <p>
     * 误差分析统计——合格天数、人为因素、气象因素、模型算法(饼图)
     *
     * @return:
     * @since: 1.0.0
     * @Author:wangchen
     * @Date: 2018/8/7 21:16
     */
    @RequestMapping(value = "/human-weather-algorithm", method = RequestMethod.GET)
    @ApiOperation("统计误差因素")
    public BaseResp<StatisticsDeviationAnalyzeDTO> overview(DeviationAnalyzeRequest request) {
        String cityId = request.getCityId();
        String caliberId = request.getCaliberId();
        Date startTime = request.getStartTime();
        Date endTime = request.getEndTime();
        if (StringUtils.isBlank(cityId)) {
            cityId = getLoginCityId();
        }
        if (StringUtils.isBlank(caliberId)) {
            caliberId = getCaliberId();
        }
        if (startTime == null) {
            startTime = DateUtils.addDays(getSystemDate(), -30);
        }
        if (endTime == null) {
            endTime = getSystemDate();
        }
        StatisticsDeviationAnalyzeDTO statisticsDeviationAnalyzeDTO = null;
        try {
            statisticsDeviationAnalyzeDTO = deviationAnalyzeCityDayFcService.findStatisticsDeviationAnalyzeDTO(null,
                cityId, caliberId, startTime, endTime);
            if (null != statisticsDeviationAnalyzeDTO) {
                BaseResp baseResp = BaseResp.succResp();
                baseResp.setData(statisticsDeviationAnalyzeDTO);
                return baseResp;
            } else {
                return new BaseResp("T706");
            }

        } catch (Exception e) {
            e.printStackTrace();
            logger.error("02I20180001", e);
            throw newBusinessException("02I20180001");
        }

    }

    /**
     * 功能描述: <br> 准确率速查
     *
     * @return:
     * @since: 1.0.0
     * @Author:wangfeng
     * @Date: 2018/8/16 13:55
     */
    @ApiOperation("准确率速查")
    @RequestMapping(value = "/accuracy", method = RequestMethod.GET)
    public BaseResp<List<ReportAccuracyDTO>> accuracy(DeviationAnalyzeRequest request) {

        try {
            String cityId = request.getCityId();
            String caliberId = request.getCaliberId();
            Date startTime = request.getStartTime();
            Date endTime = request.getEndTime();

            if (StringUtils.isBlank(cityId)) {
                cityId = getLoginCityId();
            }
            if (StringUtils.isBlank(caliberId)) {
                caliberId = getCaliberId();
            }
            if (startTime == null) {

                startTime = DateUtils.addDays(getSystemDate(), -30);
            }
            if (endTime == null) {
                endTime = getSystemDate();
            }
            BaseResp baseResp = new BaseResp();
            List<ReportAccuracyDTO> reportAccuracyDTOS =
                deviationAnalyzeCityDayFcService.findReportAccuracyDTO(cityId, caliberId, startTime, endTime);
            if (!CollectionUtils.isEmpty(reportAccuracyDTOS)) {
                baseResp.setData(reportAccuracyDTOS);
                baseResp.setRetCode("T200");
                baseResp.setRetMsg("查询准确率成功");
                return baseResp;
            } else {
                return new BaseResp("T706");
            }

        } catch (Exception e) {
            logger.error("02I20180001", e);
            throw newBusinessException("02I20180001");
        }
    }


    /**
     * 功能描述: <br>
     * <p>
     * 负荷预测比对
     *
     * @return:
     * @since: 1.0.0
     * @Author:wangchen
     * @Date: 2018/8/7 20:57
     */
    @ApiOperation("负荷预测比对")
    @RequestMapping(value = "/loadForecastCompare", method = RequestMethod.GET)
    public BaseResp<LoadDataDTO> loadForecastCompare(DeviationAnalyzeRequest request) {
        String cityId = request.getCityId();
        String caliberId = request.getCaliberId();
        Date date = request.getDate();
        if (StringUtils.isBlank(cityId)) {
            cityId = getLoginCityId();
        }
        if (StringUtils.isBlank(caliberId)) {
            caliberId = getCaliberId();
        }
        if (request.getDate() == null) {
            date = getSystemDate();
        }

        LoadDataDTO loadDataDTO = new LoadDataDTO();
        try {
            String algorithmId = loadCityFcService.getReportAlgorithmId(cityId, date, caliberId);
            /*
             *  上报的算法id是人工决策算法，则三条曲线（自动预测曲线，人工修正后的最终上报曲线，真实负荷曲线）
             *  否则两条曲线，自动预测曲线即就是最终上报曲线+真实负荷曲线
             */
            if (algorithmId.equals(AlgorithmConstants.MD_ALGORITHM_ID)) {
                //人工修正后上报的曲线
                List<BigDecimal> reportData = loadCityFcService.findLoadCityFcDO(date, cityId, caliberId, algorithmId);
                loadDataDTO.setRepairLoad(reportData);
                //自动预测的曲线
                LoadCityFcDO loadCityFcVO = loadCityFcService.getRecommendLoadCityFcDO(cityId, caliberId, date);
                String autoAlrgotithmId = "";
                if (loadCityFcVO != null) {
                    autoAlrgotithmId = loadCityFcVO.getAlgorithmId();
                } else {
                    SystemData systemSetting = this.settingSystemService.getSystemSetting();
                    if (cityId.equals(CityConstants.PROVINCE_ID)) {
                        autoAlrgotithmId = systemSetting.getProvinceNormalAlgorithm();
                    } else {
                        autoAlrgotithmId = systemSetting.getCityNormalAlgorithm();
                    }
                }
                loadDataDTO.setAutoLoad(loadCityFcService.findLoadCityFcDO(date, cityId, caliberId, autoAlrgotithmId));
            } else {
                //自动预测的曲线
                List<BigDecimal> autoLoad = loadCityFcService.findLoadCityFcDO(date, cityId, caliberId, algorithmId);
                loadDataDTO.setAutoLoad(autoLoad);
            }
            // 真实负荷的曲线
            List<BigDecimal> realData = loadCityHisService.findLoadCityHisDO(date, cityId, caliberId);
            loadDataDTO.setRealLoad(realData);
            BaseResp baseResp = BaseResp.succResp();
            baseResp.setData(loadDataDTO);
            baseResp.setRetCode("T200");
            return baseResp;
        } catch (Exception e) {
            e.printStackTrace();
            throw newBusinessException("T706");
        }
    }


    /**
     * 功能描述: <br>
     * <p>
     * 误差分析记录(表格)
     *
     * @return:
     * @since: 1.0.0
     * @Author:wangchen
     * @Date: 2018/8/9 20:30
     */
    @ApiOperation("误差分析记录(表格)")
    @RequestMapping(value = "/deviationAnalyze", method = RequestMethod.GET)
    public BaseResp<List<DeviationAnalyzeDTO>> deviationAnalyze(DeviationAnalyzeRequest request) {
        String cityId = request.getCityId();
        String caliberId = request.getCaliberId();
        Date startTime = request.getStartTime();
        Date endTime = request.getEndTime();
        if (StringUtils.isBlank(cityId)) {
            cityId = getLoginCityId();
        }
        if (StringUtils.isBlank(caliberId)) {
            caliberId = getCaliberId();
        }
        if (startTime == null) {
            startTime = DateUtils.addDays(getSystemDate(), -30);
        }
        if (endTime == null) {
            endTime = getSystemDate();
        }

        if (request.getDate() != null) {
            startTime = request.getDate();
            endTime = request.getDate();
        }
        List<DeviationAnalyzeDTO> deviationAnalyzeDTOS = null;
        try {
            BaseResp baseResp = BaseResp.succResp();
            deviationAnalyzeDTOS = deviationAnalyzeCityDayFcService.findDeviationAnalyzeDTO(null, cityId, caliberId,
                startTime, endTime);
            if (!CollectionUtils.isEmpty(deviationAnalyzeDTOS) && deviationAnalyzeDTOS.size() > 0) {
                baseResp.setData(deviationAnalyzeDTOS);
                baseResp.setRetCode("T200");
                return baseResp;
            } else {
                return new BaseResp("T706");
            }

        } catch (Exception e) {
            logger.error("02I20180001", e);
            throw newBusinessException("02I20180001");
        }
    }

    /**
     * 功能描述: <br>
     * <p>
     * 气象预测比对
     *
     * @return:
     * @since: 1.0.0
     * @Author:wangchen
     * @Date: 2018/8/10 8:51
     */
    @ApiOperation("气象预测比对")
    @RequestMapping(value = "/weather-his-fc", method = RequestMethod.GET)
    public BaseResp<WeatherDataResp> getWeatherHisAndFcData(WeatherHisFcRequest weatherHisFcRequest) {

        if (weatherHisFcRequest.getCityId() == null) {
            weatherHisFcRequest.setCityId(this.getLoginCityId());
        }
        if (weatherHisFcRequest.getDate() == null) {
            weatherHisFcRequest.setDate(this.getSystemDate());
        }
        if (weatherHisFcRequest.getType() == null) {
            weatherHisFcRequest.setType(WeatherEnum.TEMPERATURE.value());
        }
        WeatherDataResp weatherDataResp = new WeatherDataResp();
        try {
            String cityId = cityService.findWeatherCityId(weatherHisFcRequest.getCityId());
            weatherHisFcRequest.setCityId(cityId);

            // 历史数据
            List<WeatherCityHisDO> weatherCityHisVOs =
                weatherCityHisService.findWeatherCityHisDOs(weatherHisFcRequest.getCityId(),
                    weatherHisFcRequest.getType(), weatherHisFcRequest.getDate(),
                    weatherHisFcRequest.getDate());
            if (weatherCityHisVOs != null && weatherCityHisVOs.size() > 0) {
                weatherDataResp.setReal(BasePeriodUtils.toList(weatherCityHisVOs.get(0),
                    Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO));
            }
            // 预测数据
            List<WeatherCityFcDO> weatherCityFcVOs = weatherCityFcService.findWeatherCityFcDOs(cityId,
                weatherHisFcRequest.getType(), weatherHisFcRequest.getDate(), weatherHisFcRequest.getDate());
            if (weatherCityFcVOs != null && weatherCityFcVOs.size() > 0) {
                weatherDataResp.setForecast(BasePeriodUtils.toList(weatherCityFcVOs.get(0),
                    Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO));
            }
        } catch (Exception e) {
            logger.error("获取气象的历史和预报数据失败", e);
            throw newBusinessException("02I20180001");
        }

        if (weatherDataResp.getReal() == null && weatherDataResp.getForecast() == null) {
            throw newBusinessException("T706");
        }
        BaseResp resp = BaseResp.succResp();
        resp.setData(weatherDataResp);
        return resp;
    }


    /**
     * 误差分析统计(实施调用）
     */
    @ApiOperation("误差分析统计(实施调用）")
    @RequestMapping(value = "/allAnalyze", method = RequestMethod.GET)
    public BaseResp allAnalyze(@ApiParam(value = "开始日期") Date startDate,@ApiParam(value = "结束日期")  Date endDate) {
//        Date startDate = DateUtils.string2Date("2019-01-01",DateFormatType.SIMPLE_DATE_FORMAT_STR);
//        Date endDate = DateUtils.string2Date("2019-03-21",DateFormatType.SIMPLE_DATE_FORMAT_STR);
        BaseResp baseResp = BaseResp.succResp();
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        for (Date date : dateList) {
            deviationAnalyzeCityDayFcService.doInitDeviationAnalyze(date,date,null);
        }
        return baseResp;
    }


    /**
     * 校验是否是算法导致的误差
     */
    private void checkAlgorithmReson(StatisticsCityDayFcDO reportStatisticsCityDayFcVO,
        DeviationAnalyzeCityDayFcDO deviationAnalyzeCityDayFcVO) throws Exception {
        List<StatisticsCityDayFcDO> otherStatisticsCityDayFcVOs =
            statisticsCityDayFcService
                .queryStatisticsCity(deviationAnalyzeCityDayFcVO.getCityId(), null,
                    deviationAnalyzeCityDayFcVO.getCaliberId(), deviationAnalyzeCityDayFcVO.getDate(),
                    false);
        int count = 0;
        for (StatisticsCityDayFcDO otherStatisticsCityDayFcVo : otherStatisticsCityDayFcVOs) {
            if (otherStatisticsCityDayFcVo.getAccuracy().compareTo(QUALIFIED) > -1) {
                //情况5 准确率未达标，系统推荐算法非最优
                deviationAnalyzeCityDayFcVO.setDeviationAlgorithm(new BigDecimal(1));
                deviationAnalyzeCityDayFcVO.setDeviationHuman(new BigDecimal(0));
                deviationAnalyzeCityDayFcVO.setDeviationWeather(new BigDecimal(0));
                deviationAnalyzeCityDayFcVO.setDescription(environment.getProperty("5"));
                break;
            }
            count++;
        }
        Boolean isWeather = null;
        try {
            isWeather =
                deviationAnalyzeCityDayFcService
                    .isWeatherDeviations(reportStatisticsCityDayFcVO.getCityId(),
                        reportStatisticsCityDayFcVO.getDate());
        } catch (Exception e) {
            logger.info("气象数据特性数据为空。");
        }
        if (isWeather) {
            //情况6  准确率未达标  湿度预测偏差比较大
            deviationAnalyzeCityDayFcVO.setDeviationHuman(new BigDecimal(0));
            deviationAnalyzeCityDayFcVO.setDeviationAlgorithm(new BigDecimal(0));
            deviationAnalyzeCityDayFcVO.setDeviationWeather(new BigDecimal(1));
            deviationAnalyzeCityDayFcVO.setDescription(environment.getProperty("6"));
        } else {
            //情况10 准确率未达标，自动预测算法欠佳
            deviationAnalyzeCityDayFcVO.setDeviationAlgorithm(new BigDecimal(1));
            deviationAnalyzeCityDayFcVO.setDeviationHuman(new BigDecimal(0));
            deviationAnalyzeCityDayFcVO.setDeviationWeather(new BigDecimal(0));
            deviationAnalyzeCityDayFcVO.setDescription(environment.getProperty("7"));
        }
    }

    public DeviationAnalyzeCityDayFcService getDeviationAnalyzeCityDayFcService() {
        return deviationAnalyzeCityDayFcService;
    }

    public void setDeviationAnalyzeCityDayFcService(DeviationAnalyzeCityDayFcService deviationAnalyzeCityDayFcService) {
        this.deviationAnalyzeCityDayFcService = deviationAnalyzeCityDayFcService;
    }

    public LoadCityFcService getLoadCityFcService() {
        return loadCityFcService;
    }

    public void setLoadCityFcService(LoadCityFcService loadCityFcService) {
        this.loadCityFcService = loadCityFcService;
    }

    public LoadCityHisService getLoadCityHisService() {
        return loadCityHisService;
    }

    public void setLoadCityHisService(LoadCityHisService loadCityHisService) {
        this.loadCityHisService = loadCityHisService;
    }

    public StatisticsCityDayFcService getStatisticsCityDayFcService() {
        return statisticsCityDayFcService;
    }

    public void setStatisticsCityDayFcService(StatisticsCityDayFcService statisticsCityDayFcService) {
        this.statisticsCityDayFcService = statisticsCityDayFcService;
    }

    public WeatherCityHisService getWeatherCityHisService() {
        return weatherCityHisService;
    }

    public void setWeatherCityHisService(WeatherCityHisService weatherCityHisService) {
        this.weatherCityHisService = weatherCityHisService;
    }

    public WeatherCityFcService getWeatherCityFcService() {
        return weatherCityFcService;
    }

    public void setWeatherCityFcService(WeatherCityFcService weatherCityFcService) {
        this.weatherCityFcService = weatherCityFcService;
    }

}