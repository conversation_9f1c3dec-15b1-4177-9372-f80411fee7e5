package com.tsintergy.lf.web.base.security.controller;


import com.tsieframework.cloud.security.web.system.request.sysusercontroller.WebTsieUserRequest;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;

/**
 * @date: 6/14/18 11:01 AM
 * @author: angel
 **/
public class UserRequest extends WebTsieUserRequest {

    //扩展的
    @NotNull(message = "城市不能为空")
    @ApiModelProperty(value = "城市ID")
    private String cityId;

    @ApiModelProperty(value = "城市名称")
    private String city;

    @ApiModelProperty(value = "uid")
    private String tsieUId;

    /**
     * 调控云用户ID
     */
    private String cloudUserId;

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getTsieUId() {
        return tsieUId;
    }

    public void setTsieUId(String tsieUId) {
        this.tsieUId = tsieUId;
    }

    public String getCloudUserId() {
        return cloudUserId;
    }

    public void setCloudUserId(String cloudUserId) {
        this.cloudUserId = cloudUserId;
    }
}
