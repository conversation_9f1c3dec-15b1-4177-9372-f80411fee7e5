/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangchen Date:  2019/6/5 16:31 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.load.response;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 月/年 特性分析对象
 *
 * <AUTHOR>
 * @create 2020/4/15
 * @since 1.0.0
 */
@Data
@ApiModel
public class YearMonthFeatureAnalyseResp implements Serializable {

    @ApiModelProperty(value = "日期",example = "2021-03-03")
    private String date;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最大负荷",example = "123121")
    private BigDecimal maxLoad;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最小负荷",example = "123121")
    private BigDecimal minLoad;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "平均负荷",example = "123121")
    private BigDecimal avgLoad;
    @BigdecimalJsonFormat(percentConvert = 100)
    @ApiModelProperty(value = "负荷峰谷差率",example = "0.93")
    private BigDecimal loadGradient;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "电量",example = "123121")
    private BigDecimal energy;

    @ApiModelProperty(value = "雨量",example = "231")
    private BigDecimal rain;

    /**
     * 周期均匀曲线 前三年/月(包含本年/月)的平均出力的平均值
     */
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "周期均匀曲线",example = "231")
    private BigDecimal avgPeriod;

    @ApiModelProperty(value = "最高温度",example = "36")
    private BigDecimal highestTemperature;


}
