/**
 * Copyright (C), 2015‐2022, 北京清能互联科技有限公司 Author:  <EMAIL> Date:  2022/5/23 19:54 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.acload.controller;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.BasePeriod96VO;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsieframework.core.base.web.BaseRespBuilder;
import com.tsieframework.util.BeanCopierUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.ResponseCodeConstants;
import com.tsintergy.lf.core.constants.SystemConstant;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.AcLoadReportD5000Service;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.AirConditionerLoadBaseManageService;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.LoadSolutionManageDetailService;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.LoadSolutionManageEntityService;
import com.tsintergy.lf.serviceapi.base.airconditioner.dto.*;
import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.AcLoadReportD5000DO;
import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.LoadSolutionManageDO;
import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.LoadSolutionManageDetailDO;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.common.enumeration.DateType2;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingSystemDO;
import com.tsintergy.lf.web.base.acload.request.AcLoadManagePostDataRequest;
import com.tsintergy.lf.web.base.acload.request.LoadSolutionManageRequest;
import com.tsintergy.lf.web.base.acload.request.SaveLoadSolutionAcManageRequest;
import com.tsintergy.lf.web.base.acload.request.SaveLoadSolutionManageRequest;
import com.tsintergy.lf.web.base.acload.response.*;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * Description: 空调负荷基础负荷管理 <br>
 *
 * <AUTHOR>
 * @create 2022/5/23
 * @since 1.0.0
 */
@Api(tags = "空调负荷基础负荷管理")
@RequestMapping("/airConditionerLoad")
@RestController
public class AirConditionerLoadBaseManageController extends CommonBaseController {

    @Autowired
    private AirConditionerLoadBaseManageService airConditionerLoadBaseManageService;

    @Autowired
    AcLoadReportD5000Service acLoadReportD5000Service;

    @Autowired
    CityService cityService;

    @Autowired
    LoadSolutionManageDetailService loadSolutionManageDetailService;

    @Autowired
    LoadSolutionManageEntityService loadSolutionManageEntityService;

    @ApiOperation("空调负荷基础负荷管理-获取日期类型")
    @ApiImplicitParam(name = "isAll", value = "是否全部", required = true, example = "true")
    @RequestMapping(value = "/get/getDateType", method = RequestMethod.GET)
    public BaseResp<List<DropDownBoxResp>> getDateType(@RequestParam("isAll") boolean isAll) throws Exception {
        DateType2[] values = DateType2.values();
        List<DropDownBoxResp> dropDownBoxList = new ArrayList<>();
        if (isAll) {
            dropDownBoxList.add(0, new DropDownBoxResp("all", "全部"));
        }
        for (int i = 0; i < values.length; i++) {
            dropDownBoxList.add(new DropDownBoxResp(String.valueOf(values[i].getId()), values[i].getText()));
        }
        return BaseRespBuilder.success().setData(dropDownBoxList).build();
    }

    @ApiOperation("空调负荷基础负荷管理-基础负荷方案管理查询")
    @RequestMapping(value = "/queryLoadSolutionManage", method = RequestMethod.POST)
    public BaseResp<List<LoadSolutionManageResp>> queryLoadSolutionManage(
        @RequestBody LoadSolutionManageRequest request) throws Exception {
        List<LoadSolutionManageRespDTO> result = new ArrayList<>();
        List<LoadSolutionManageRespDTO> loadSolutionManageRespDTOS = airConditionerLoadBaseManageService
            .queryLoadSolutionManage(
                BeanCopierUtils.copyProperties(request, LoadSolutionManageDTO.class));
        BaseResp baseResp = BaseResp.succResp();
        if (CollectionUtils.isEmpty(loadSolutionManageRespDTOS)) {
            baseResp.setRetCode(ResponseCodeConstants.FIND_DATA_NULL_CODE);
            baseResp.setRetMsg(ResponseCodeConstants.FIND_DATA_NULL_VALUE);
            return baseResp;
        }
        BeanCopierUtils.copyPropertiesOfList(loadSolutionManageRespDTOS, LoadSolutionManageResp.class);
        if (!CollectionUtils.isEmpty(loadSolutionManageRespDTOS)) {
            for (LoadSolutionManageRespDTO loadSolutionManageRespDTO : loadSolutionManageRespDTOS) {
                if (loadSolutionManageRespDTO.getWeatherType() != null
                    && loadSolutionManageRespDTO.getWeatherType() == 1) {
                    loadSolutionManageRespDTO.setWeatherName("福建气象");
                    loadSolutionManageRespDTO.setLimitRain(true);
                } else if (loadSolutionManageRespDTO.getWeatherType() != null &&
                        loadSolutionManageRespDTO.getWeatherType() == 2) {
                    loadSolutionManageRespDTO.setWeatherName("福州气象");
                    List<LoadSolutionManageDetailDO> loadDetailData = loadSolutionManageDetailService.getLoadSolutionManageDetailByManageId(loadSolutionManageRespDTO.getId());
                    if (CollectionUtils.isNotEmpty(loadDetailData)) {
                        loadSolutionManageRespDTO.setTemperatureRangeStart(loadDetailData.get(0).getLowestTemperature());
                        loadSolutionManageRespDTO.setTemperatureRangeEnd(loadDetailData.get(0).getHighestTemperature());
                        loadSolutionManageRespDTO.setLimitRain(true);
                        loadSolutionManageRespDTO.setMaxRain(loadDetailData.get(0).getRainfall());
                    }
                }
                result.add(loadSolutionManageRespDTO);
            }
        }
        baseResp.setData(result);
        return baseResp;
    }

    @ApiOperation("空调负荷基础负荷管理-基础负荷方案管理新增或编辑")
    @RequestMapping(value = "/saveOrUpdateLoadSolutionManage", method = RequestMethod.POST)
    public BaseResp<Void> saveOrUpdateLoadSolutionManage(@RequestBody SaveLoadSolutionManageRequest request)
        throws Exception {
        airConditionerLoadBaseManageService.saveOrUpdateLoadSolutionManage(
            BeanCopierUtils.copyProperties(request, SaveLoadSolutionManageDTO.class));
        return BaseResp.succResp();
    }

    @ApiOperation("空调负荷基础负荷管理-基础负荷方案管理新增或编辑新")
    @RequestMapping(value = "/saveOrUpdateAcLoadSolutionManage", method = RequestMethod.POST)
    public BaseResp<Void> saveOrUpdateAcLoadSolutionManage(@RequestBody SaveLoadSolutionAcManageRequest request)
            throws Exception {
        airConditionerLoadBaseManageService.saveOrUpdateLoadSolutionManage(
                BeanCopierUtils.copyProperties(request, SaveLoadSolutionManageDTO.class));
        SaveLoadSolutionCongfigManageDTO dto = new SaveLoadSolutionCongfigManageDTO();
        BeanUtils.copyProperties(request, dto);
        List<AcLoadManagePostDataDTO> dataDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(request.getData())) {
            for (AcLoadManagePostDataRequest dataRequest : request.getData()) {
                AcLoadManagePostDataDTO dataDTO = new AcLoadManagePostDataDTO();
                BeanUtils.copyProperties(dataRequest, dataDTO);
                dataDTOList.add(dataDTO);
            }
        }
        dto.setData(dataDTOList);
        airConditionerLoadBaseManageService.saveOrUpdateAcLoadSolutionManage(dto);
        return BaseResp.succResp();
    }

    @ApiOperation("空调负荷基础负荷管理-删除")
    @RequestMapping(value = "/saveAcLoadSolutionManage", method = RequestMethod.POST)
    public BaseResp<Void> saveAcLoadSolutionManage(@RequestBody List<AcLoadCityConfigDataDTO> acLoadManageDataRespsp)
            throws Exception {
        LoadSolutionManageDO oneByQueryWrapper = loadSolutionManageEntityService.findOneByQueryWrapper(
                JpaWrappers.<LoadSolutionManageDO>lambdaQuery()
                        .eq(LoadSolutionManageDO::getId, acLoadManageDataRespsp.get(0).getId()));
        for (AcLoadCityConfigDataDTO acLoadCityConfigDataDTO : acLoadManageDataRespsp) {
            airConditionerLoadBaseManageService.saveAcLoadSolutionManage(acLoadCityConfigDataDTO, oneByQueryWrapper.getWeatherType());
        }
        return BaseResp.succResp();
    }

    @ApiOperation("空调负荷基础负荷管理-基础负荷方案管理删除")
    @ApiImplicitParam(name = "solutionId", value = "方案id", required = true, example = "440000000")
    @RequestMapping(value = "/deleteLoadSolutionManage", method = RequestMethod.GET)
    public BaseResp<Void> deleteLoadSolutionManage(@RequestParam("solutionId") String solutionId) throws Exception {
        airConditionerLoadBaseManageService.deleteLoadSolutionManage(solutionId);
        return BaseResp.succResp();
    }

    @ApiOperation("空调负荷基础负荷管理-基础负荷曲线查询")
    @ApiImplicitParam(name = "solutionId", value = "方案id", required = true, example = "440000000")
    @RequestMapping(value = "/queryBaseLoadCurve", method = RequestMethod.GET)
    public BaseResp<BaseLoadCurveResp> queryBaseLoadCurve(@RequestParam("solutionId") String solutionId)
        throws Exception {
        // old
        // BaseLoadCurveRespDTO resp = airConditionerLoadBaseManageService.queryBaseLoadCurve(solutionId);
        BaseLoadCurveRespDTO resp = airConditionerLoadBaseManageService.queryBaseNewLoadCurve(solutionId);
        BaseResp baseResp = BaseResp.succResp();
        if (resp == null) {
            baseResp.setRetCode(ResponseCodeConstants.FIND_DATA_NULL_CODE);
            baseResp.setRetMsg(ResponseCodeConstants.FIND_DATA_NULL_VALUE);
            return baseResp;
        }
        baseResp.setData(BeanCopierUtils.copyProperties(resp, BaseLoadCurveResp.class));
        return baseResp;
    }

    @ApiOperation("空调负荷基础负荷管理-样本情况统计查询")
    @ApiImplicitParam(name = "solutionId", value = "方案id", required = true, example = "440000000")
    @RequestMapping(value = "/querySampleStatistics", method = RequestMethod.GET)
    public BaseResp<SampleStatisticsResp> querySampleStatistics(@RequestParam("solutionId") String solutionId)
        throws Exception {
        // old:
        // SampleStatisticsRespDTO resp = airConditionerLoadBaseManageService.querySampleStatistics(solutionId);
        SampleStatisticsRespDTO resp = airConditionerLoadBaseManageService.querySampleNewStatistics(solutionId);
        BaseResp baseResp = BaseResp.succResp();
        if (resp == null) {
            baseResp.setRetCode(ResponseCodeConstants.FIND_DATA_NULL_CODE);
            baseResp.setRetMsg(ResponseCodeConstants.FIND_DATA_NULL_VALUE);
            return baseResp;
        }

        SampleStatisticsResp sampleStatisticsResp = new SampleStatisticsResp();
        sampleStatisticsResp.setSolutionId(resp.getSolutionId());
        if (CollectionUtils.isNotEmpty(resp.getSampleList())) {
            sampleStatisticsResp.setSampleList(
                BeanCopierUtils.copyPropertiesOfList(resp.getSampleList(), SampleResp.class));
        }
        baseResp.setData(sampleStatisticsResp);
        return baseResp;
    }

    @ApiOperation("空调负荷基础负荷管理-获取初始化提取配置")
    @RequestMapping(value = "/manage", method = RequestMethod.GET)
    public BaseResp<List<AcLoadManageDataResp>> queryManageData(Integer weatherType, String id)
            throws Exception {
        List<AcLoadManageDataResp> result = new ArrayList<>();
        if (id != null) {
            // 查数据库配置的
            List<LoadSolutionManageDetailDO> loadSolutionManageDetailByManageId = loadSolutionManageDetailService.getLoadSolutionManageDetailByManageId(id);
            if (CollectionUtils.isNotEmpty(loadSolutionManageDetailByManageId)) {
                loadSolutionManageDetailByManageId.forEach(
                        t -> {
                            result.add(new AcLoadManageDataResp(UUID.randomUUID().toString().toLowerCase().replace("-", ""), t.getCityName(), t.getHighestTemperature(),
                                    t.getLowestTemperature(), t.getRainfall(), t.getManageId(), weatherType));
                        }
                );
            }
        } else {
            if (weatherType == 1) {
                // 福建气象（9地市）
                List<String> collect = cityService.findAllCitys().stream().filter(t -> !Constants.PROVINCE_ID.equals(t.getId()))
                        .sorted(Comparator.comparing(CityDO::getOrderNo)).map(CityDO::getCity).collect(Collectors.toList());
                collect.forEach(
                        t -> {
                            result.add(new AcLoadManageDataResp(UUID.randomUUID().toString().toLowerCase().replace("-", ""), t, BigDecimal.valueOf(23),
                                    BigDecimal.valueOf(12), BigDecimal.valueOf(5), UUID.randomUUID().toString().replace("-","").toLowerCase(), weatherType));
                        }
                );
            } else if (weatherType == 2) {
                // 福州气象
                result.add(new AcLoadManageDataResp(UUID.randomUUID().toString().toLowerCase().replace("-", ""),
                        "福州", BigDecimal.valueOf(23), BigDecimal.valueOf(12), BigDecimal.valueOf(5), UUID.randomUUID().toString().replace("-","").toLowerCase(), weatherType));
            }
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(result);
        return baseResp;
    }

    @ApiOperation("空调负荷基础负荷管理-获取方案日期")
    @RequestMapping(value = "/extract", method = RequestMethod.POST)
    public BaseResp<List<AcLoadManageDataResp>> queryExtractData(@RequestBody List<AcLoadManagePostDataResp> acLoadManageDataRespsp)
            throws Exception {
        List<AcLoadManageDataResp> result = new ArrayList<>();
        List<SaveLoadConfigDataDTO> saveLoadConfigDataDTOList = new ArrayList<>();
        for (AcLoadManagePostDataResp acLoadManagePostDataResp : acLoadManageDataRespsp) {
            try {
                SaveLoadConfigDataDTO saveLoadConfigDataDTO = new SaveLoadConfigDataDTO();
                BeanUtils.copyProperties(acLoadManagePostDataResp, saveLoadConfigDataDTO);
                saveLoadConfigDataDTOList.add(saveLoadConfigDataDTO);
                //List<SampleDateDataDTO> sampleDateDataDTOS = new ArrayList<>();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        List<SampleDateDataDTO> sampleDateDataDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(saveLoadConfigDataDTOList) && saveLoadConfigDataDTOList.get(0).getType() == 1) {
            sampleDateDataDTOS = airConditionerLoadBaseManageService.queryWeatherSampleStatistics(saveLoadConfigDataDTOList);
        } else if (CollectionUtils.isNotEmpty(saveLoadConfigDataDTOList) && saveLoadConfigDataDTOList.get(0).getType() == 2) {
            sampleDateDataDTOS = airConditionerLoadBaseManageService.queryWeatherSampleReturnStatistics(saveLoadConfigDataDTOList);
        }
        result.addAll(BeanCopierUtils.copyPropertiesOfList(sampleDateDataDTOS, AcLoadManageDataResp.class));
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(result);
        return baseResp;
    }

    @ApiOperation("空调负荷基础负荷管理-获取负荷曲线")
    @RequestMapping(value = "/load", method = RequestMethod.GET)
    public BaseResp<List<BigDecimal>> queryLoadData(Date date, String cityName, Integer weatherType) throws Exception {
        List<BigDecimal> bigDecimals = airConditionerLoadBaseManageService.queryFcHisData(date, cityName,
                this.getCaliberId(), weatherType);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(bigDecimals);
        return baseResp;
    }

    @Autowired
    SettingSystemService settingSystemService;



    @ApiOperation("空调负荷基础负荷管理-灵敏度数据导出")
    @RequestMapping(value = "/sensitivity", method = RequestMethod.GET)
    public BaseResp<List<AcFcSensitivityResp>> acLoadImport(String year) throws Exception {

        SettingSystemDO importDataSendSwitch =
            settingSystemService.findByFieldId(SystemConstant.IMPORTDATASHOW_SENDD5000);





        String loginCityId = "1";
        String caliberId = "1";
        List<AcLoadReportD5000DO> acLoadReportD5000DOS = acLoadReportD5000Service
            .getAcLoadReportD5000DO(loginCityId, caliberId, year, null, null, null);

        //人工导入
               List<AcLoadReportD5000DO> report = acLoadReportD5000DOS.stream()
            .filter(t -> t.getType() >= 5 && t.getType() <= 8).collect(Collectors.toList());

        //算法自动生成的
        List<AcLoadReportD5000DO> auto = acLoadReportD5000DOS.stream()
            .filter(t -> t.getType() >= 9 && t.getType() <= 12).collect(Collectors.toList());



        List<AcFcSensitivityResp> resps = new ArrayList<>();
        //通过文件导入的
        if (!CollectionUtils.isEmpty(report) && importDataSendSwitch.getValue().equals("1")) {
            Map<Integer, List<AcLoadReportD5000DO>> listMap = report.stream()
                .collect(Collectors.groupingBy(AcLoadReportD5000DO::getType));
            List<AcLoadReportD5000DO> featureXS = listMap.get(5);
            List<AcLoadReportD5000DO> featureYS = listMap.get(6);
            List<AcLoadReportD5000DO> sensitivityXS = listMap.get(7);
            List<AcLoadReportD5000DO> sensitivityYS = listMap.get(8);

            if (CollectionUtils.isEmpty(sensitivityXS)) {
                BaseResp baseResp = BaseResp.succResp();
                baseResp.setRetCode("T706");
                return baseResp;
            }


            String[] featureYT0000Split = null;
            if (!CollectionUtils.isEmpty(featureYS)) {
                AcLoadReportD5000DO featureY = featureYS.get(0);
                String featureYT0000 = featureY.getData();
                featureYT0000Split = featureYT0000.split(",");
            }


            String[] sensitivityXT0000Split = null;
            if (!CollectionUtils.isEmpty(sensitivityXS)) {
                AcLoadReportD5000DO sensitivityX = sensitivityXS.get(0);
                String sensitivityXT0000 = sensitivityX.getData();
                sensitivityXT0000Split = sensitivityXT0000.split(",");
            }

            String[] sensitivityYT0000Split = null;
            if (!CollectionUtils.isEmpty(sensitivityYS)) {
                AcLoadReportD5000DO sensitivityY = sensitivityYS.get(0);
                String sensitivityYT0000 = sensitivityY.getData();
                sensitivityYT0000Split = sensitivityYT0000.split(",");
            }


            for (int i = 0; i < sensitivityXT0000Split.length; i++) {
                AcFcSensitivityResp acFcSensitivityResp = new AcFcSensitivityResp();
                acFcSensitivityResp.setMaxTemp(new BigDecimal(sensitivityXT0000Split[i]));

                if (featureYT0000Split != null) {
                    acFcSensitivityResp.setMaxLoad(new BigDecimal(featureYT0000Split[i]));
                }

                if (sensitivityYT0000Split != null) {
                    acFcSensitivityResp.setSensitivity(new BigDecimal(sensitivityYT0000Split[i]));
                }
                resps.add(acFcSensitivityResp);
            }
        } else if(!CollectionUtils.isEmpty(auto)  &&  importDataSendSwitch.getValue().equals("0")){
            Map<Integer, List<AcLoadReportD5000DO>> listMap = auto.stream()
                .collect(Collectors.groupingBy(AcLoadReportD5000DO::getType));

            List<AcLoadReportD5000DO> featureXS = listMap.get(9);
            List<AcLoadReportD5000DO> featureYS = listMap.get(10);
            List<AcLoadReportD5000DO> sensitivityXS = listMap.get(11);
            List<AcLoadReportD5000DO> sensitivityYS = listMap.get(12);
            AcLoadReportD5000DO featureX = featureXS.get(0);
            AcLoadReportD5000DO featureY = featureYS.get(0);
            AcLoadReportD5000DO sensitivityX = sensitivityXS.get(0);
            AcLoadReportD5000DO sensitivityY = sensitivityYS.get(0);
            String featureXT0000 = featureX.getData();
            String featureYT0000 = featureY.getData();
            String sensitivityXT0000 = sensitivityX.getData();
            String sensitivityYT0000 = sensitivityY.getData();

            String[] sensitivityXT0000Split = sensitivityXT0000.split(",");
            String[] sensitivityYT0000Split = sensitivityYT0000.split(",");
            String[] featureYT0000Split = featureYT0000.split(",");
            String[] featureXT0000Split = featureXT0000.split(",");
            // [15,15.05,16,16.05,17,17.05,18,18.05]
            // [1,2,3,4,5,6,7]

            //[15.3,15.07,15.8,15.9,16.2,16.3,18.2,18.7]
            //[100,200,300,400,500,600,700,800]

            //计算重新生成的特性值
            List<BigDecimal> featureYList = new ArrayList<>();
            for (int i = 0; i < sensitivityXT0000Split.length; i++) {
                BigDecimal value = new BigDecimal(sensitivityXT0000Split[i]);
                BigDecimal data = getData(featureXT0000Split, featureYT0000Split, value);
                featureYList.add(data);
            }

            for (int i = 0; i < sensitivityXT0000Split.length; i++) {
                AcFcSensitivityResp acFcSensitivityResp = new AcFcSensitivityResp();
                acFcSensitivityResp.setMaxTemp(new BigDecimal(sensitivityXT0000Split[i]));
                acFcSensitivityResp.setMaxLoad(featureYList.get(i));
                acFcSensitivityResp.setSensitivity(new BigDecimal(sensitivityYT0000Split[i]));
                resps.add(acFcSensitivityResp);
            }
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(resps);
        return baseResp;
    }


    public BigDecimal getData(String[] valuex, String[] valuey, BigDecimal value) {
        // [15,15.05,16,16.05,17,17.05,18,18.05]
        // [1,2,3,4,5,6,7]

        //[15.3,15.07,15.8,15.9,16.2,16.3,18.2,18.7]
        //[100,200,300,400,500,600,700,800]

        // value 表示目标的x轴数据
        // 根据 valuex 和valuey   计算  value 对应的valuey
        // 先判断 value  在valuex的中哪个范围内  比如15
        BigDecimal add = null;
        BigDecimal first = new BigDecimal(valuex[0]);
        BigDecimal last = new BigDecimal(valuex[valuex.length - 1]);
        if (value.compareTo(first) <= 0) {
            BigDecimal second = new BigDecimal(valuex[1]);
            BigDecimal firstValue = new BigDecimal(valuey[0]);
            BigDecimal secondValue = new BigDecimal(valuey[1]);
            BigDecimal valueDevation = secondValue.subtract(firstValue);
            BigDecimal xDevation = second.subtract(first);
            BigDecimal avgDevation = valueDevation.divide(xDevation, 2, BigDecimal.ROUND_HALF_UP);
            add = (first.subtract(value).multiply(avgDevation)).add(firstValue);
            return add;

        } else if (value.compareTo(last) >= 0) {
            BigDecimal secondLast = new BigDecimal(valuex[valuex.length - 2]);
            BigDecimal lastValue = new BigDecimal(valuey[valuex.length - 1]);
            BigDecimal secondLastValue = new BigDecimal(valuey[valuex.length - 2]);
            BigDecimal valueDevation = lastValue.subtract(secondLastValue);
            BigDecimal xDevation = last.subtract(secondLast);
            BigDecimal avgDevation = valueDevation.divide(xDevation);
            add = (value.subtract(last).multiply(avgDevation)).add(lastValue);
            return add;
        }

        for (int i = 0; i < valuex.length - 1; i++) {
            BigDecimal var1 = new BigDecimal(valuex[i]);
            BigDecimal var2 = new BigDecimal(valuex[i + 1]);
            if (value.compareTo(var1) > 0 && value.compareTo(var2) <= 0) {
                BigDecimal valueFirst = new BigDecimal(valuey[i]);
                BigDecimal valueSecond = new BigDecimal(valuey[i + 1]);
                BigDecimal yDevation = valueSecond.subtract(valueFirst);
                BigDecimal xDevation = var2.subtract(var1);
                BigDecimal avgDevation = yDevation.divide(xDevation, 2, BigDecimal.ROUND_HALF_UP);
                add = (value.subtract(var1).multiply(avgDevation)).add(valueFirst);
                return add;
            }
        }
        return add;
    }

    //todo wangchen 导入功能暂时屏蔽  开放需要重新根据当前逻辑调整
    @Deprecated
    @ApiOperation("空调负荷基础负荷管理-基础负荷数据导入")
    @RequestMapping(value = "/acLoadImport", method = RequestMethod.POST)
    public BaseResp acLoadImport(MultipartFile uploadFile) throws Exception {
        String caliberId = this.getCaliberId();
        String cityId = this.getLoginCityId();

        XSSFWorkbook xssfWorkbook = new XSSFWorkbook(uploadFile.getInputStream());
        XSSFSheet sheetAt = xssfWorkbook.getSheetAt(0);
        int lastRowNum = sheetAt.getLastRowNum();
        for (int rowNum = 1; rowNum <= lastRowNum; rowNum++) {
            XSSFRow row = sheetAt.getRow(rowNum);
            XSSFCell cell0 = row.getCell(0);
            String yearStr = null;
            String monthStr = null;
            Date date = null;
            String dateStr = null;
            if (cell0 != null) {
                switch (cell0.getCellTypeEnum()) {
                    case BLANK:
                        ;
                    case STRING:
                        dateStr = cell0.getStringCellValue();
                        if (!StringUtils.isEmpty(dateStr)) {
                            yearStr = dateStr.substring(0, 4);
                            monthStr = dateStr.substring(4, 6);
                            date = DateUtils.string2Date(dateStr, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
                        }
                        break;
                    case NUMERIC:
                        cell0.setCellType(CellType.STRING);
                        dateStr = cell0.getStringCellValue();
                        if (!StringUtils.isEmpty(dateStr)) {
                            yearStr = dateStr.substring(0, 4);
                            monthStr = dateStr.substring(4, 6);
                            date = DateUtils.string2Date(dateStr, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
                        }
                        break;
                }
            }
            XSSFCell cell1 = row.getCell(1);
            cell1.setCellType(CellType.STRING);
            String type = cell1.getStringCellValue();
            Map<String, BigDecimal> hashMap = new HashMap<>();
            for (int j = 0; j < 96; j++) {
                int hour = (j + 1) / 4;
                int minute = ((j + 1) % 4) * 15;
                String hourStr = hour < 10 ? "0" + hour : hour + "";
                String minuteStr = minute == 0 ? "0" + minute : minute + "";
                String fileld = "t" + hourStr + minuteStr;
                XSSFCell cell = row.getCell(2 + j);
                if (cell == null) {
                    hashMap.put(fileld, null);
                } else {
                    switch (cell.getCellTypeEnum()) {
                        case BLANK:
                            hashMap.put(fileld, null);
                            break;
                        case STRING:
                            hashMap.put(fileld, new BigDecimal(cell.getStringCellValue()));
                            break;
                        case NUMERIC:
                            hashMap.put(fileld, new BigDecimal(cell.getNumericCellValue()));
                            break;
                        default:
                            ;
                    }
                }
            }
            Integer dateType = null;
            switch (type) {
                //正常日基础负荷
                case "1":
                    dateType = DateType2.WORKDAY.getId();
                    if (StringUtils.isEmpty(yearStr) || StringUtils.isEmpty(monthStr)) {
                        BaseResp baseResp = BaseResp.failResp("基础负荷正常日数据中日期为空");
                        return baseResp;
                    }
                    wrapBasicLoad(cityId, caliberId, yearStr, monthStr, dateType, hashMap);
                    break;

                //节假日基础负荷
                case "2":
                    if (StringUtils.isEmpty(yearStr) || StringUtils.isEmpty(monthStr)) {
                        BaseResp baseResp = BaseResp.failResp("基础负荷节假日数据中日期为空");
                        return baseResp;
                    }
                    dateType = DateType2.SATURDAY.getId();
                    wrapBasicLoad(cityId, caliberId, yearStr, monthStr, dateType, hashMap);
                    break;
                //空调负荷预测数据
                case "3":
                    if (StringUtils.isEmpty(dateStr)) {
                        BaseResp baseResp = BaseResp.failResp("空调负荷预测数据中日期为空");
                        return baseResp;
                    }
                    dateType = 3;
                    wrapAcLoad(cityId, caliberId, date, dateType, hashMap);
                    break;

                //空调负荷实际数据
                case "4":
                    if (StringUtils.isEmpty(dateStr)) {
                        BaseResp baseResp = BaseResp.failResp("空调负荷实际数据中日期为空");
                        return baseResp;
                    }
                    dateType = 4;
                    wrapAcLoad(cityId, caliberId, date, dateType, hashMap);
                    break;

                //灵敏度分析特性 x轴
                case "5":
                    dateType = 5;
                    wrapSensitivity(cityId, caliberId, dateType, hashMap, yearStr);
                    break;
                //灵敏度分析特性 y轴
                case "6":
                    dateType = 6;
                    wrapSensitivity(cityId, caliberId, dateType, hashMap, yearStr);
                    break;
                //灵敏度分析 x轴
                case "7":
                    dateType = 7;
                    wrapSensitivity(cityId, caliberId, dateType, hashMap, yearStr);
                    break;
                //灵敏度分析 y轴
                case "8":
                    dateType = 8;
                    wrapSensitivity(cityId, caliberId, dateType, hashMap, yearStr);
                    break;
            }
        }
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }


    void wrapBasicLoad(String cityId, String caliberId, String yearStr, String monthStr, Integer dateType,
        Map<String, BigDecimal> hashMap) throws Exception {
        List<AcLoadReportD5000DO> acLoadReportD5000DOS = acLoadReportD5000Service
            .getAcLoadReportD5000DO(cityId, caliberId, yearStr, monthStr, dateType, null);
        if (CollectionUtils.isEmpty(acLoadReportD5000DOS)) {
            //创建
            AcLoadReportD5000DO acLoadReportD5000DO = new AcLoadReportD5000DO();
            acLoadReportD5000DO.setId(UUID.randomUUID().toString().replace("-", "").toLowerCase());
            acLoadReportD5000DO.setCaliberId(caliberId);
            acLoadReportD5000DO.setCityId(cityId);
            acLoadReportD5000DO.setMonth(monthStr);
            acLoadReportD5000DO.setType(dateType);
            acLoadReportD5000DO.setYear(yearStr);
            BasePeriodUtils.setAllFiled(acLoadReportD5000DO, hashMap);
            acLoadReportD5000DO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            acLoadReportD5000DO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            acLoadReportD5000Service.doSave(acLoadReportD5000DO);
        } else {
            //更新
            AcLoadReportD5000DO acLoadReportD5000DO = acLoadReportD5000DOS.get(0);
            acLoadReportD5000DO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            BasePeriodUtils.setAllFiled(acLoadReportD5000DO, hashMap);
            acLoadReportD5000Service.doUpdate(acLoadReportD5000DO);
        }
    }

    void wrapAcLoad(String cityId, String caliberId, Date date, Integer dateType, Map<String, BigDecimal> hashMap)
        throws Exception {
        List<AcLoadReportD5000DO> acLoadReportD5000DOS = acLoadReportD5000Service
            .getAcLoadReportD5000DO(cityId, caliberId, null, null, dateType, date);
        if (CollectionUtils.isEmpty(acLoadReportD5000DOS)) {
            //创建
            AcLoadReportD5000DO acLoadReportD5000DO = new AcLoadReportD5000DO();
            acLoadReportD5000DO.setId(UUID.randomUUID().toString().replace("-", "").toLowerCase());
            acLoadReportD5000DO.setCaliberId(caliberId);
            acLoadReportD5000DO.setCityId(cityId);
            acLoadReportD5000DO.setDate(date);
            acLoadReportD5000DO.setType(dateType);
            BasePeriodUtils.setAllFiled(acLoadReportD5000DO, hashMap);
            acLoadReportD5000DO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            acLoadReportD5000DO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            acLoadReportD5000Service.doSave(acLoadReportD5000DO);
        } else {
            //更新
            AcLoadReportD5000DO acLoadReportD5000DO = acLoadReportD5000DOS.get(0);
            acLoadReportD5000DO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            BasePeriodUtils.setAllFiled(acLoadReportD5000DO, hashMap);
            acLoadReportD5000Service.doUpdate(acLoadReportD5000DO);
        }
    }


    void wrapSensitivity(String cityId, String caliberId, Integer dateType, Map<String, BigDecimal> hashMap,
        String yearStr)
        throws Exception {
        List<AcLoadReportD5000DO> acLoadReportD5000DOS = acLoadReportD5000Service
            .getAcLoadReportD5000DO(cityId, caliberId, yearStr, null, dateType, null);
        if (CollectionUtils.isEmpty(acLoadReportD5000DOS)) {
            //创建
            BasePeriod96VO basePeriod96VO = new BasePeriod96VO();
            AcLoadReportD5000DO acLoadReportD5000DO = new AcLoadReportD5000DO();
            acLoadReportD5000DO.setId(UUID.randomUUID().toString().replace("-", "").toLowerCase());
            acLoadReportD5000DO.setCaliberId(caliberId);
            acLoadReportD5000DO.setCityId(cityId);
            acLoadReportD5000DO.setType(dateType);
            acLoadReportD5000DO.setYear(yearStr);
            BasePeriodUtils.setAllFiled(acLoadReportD5000DO, hashMap);
            BasePeriodUtils.setAllFiled(basePeriod96VO, hashMap);
            List<BigDecimal> bigDecimals = BasePeriodUtils
                .toList(basePeriod96VO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < bigDecimals.size(); i++) {
                BigDecimal bigDecimal = bigDecimals.get(i);
                if (bigDecimal == null) {
                    continue;
                } else {
                    String bigDecimalStr = bigDecimal.toString();
                    sb.append(bigDecimalStr).append(",");
                }
            }
            String str = null;
            if (sb.toString().endsWith(",")) {
                String s = sb.toString();
                str = s.substring(0, s.length() - 1);
            } else {
                str = sb.toString();
            }
            acLoadReportD5000DO.setData(str);
            acLoadReportD5000DO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            acLoadReportD5000DO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            acLoadReportD5000Service.doSave(acLoadReportD5000DO);
        } else {
            //更新
            AcLoadReportD5000DO acLoadReportD5000DO = acLoadReportD5000DOS.get(0);
            acLoadReportD5000DO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            BasePeriodUtils.setAllFiled(acLoadReportD5000DO, hashMap);
            BasePeriod96VO basePeriod96VO = new BasePeriod96VO();
            BasePeriodUtils.setAllFiled(basePeriod96VO, hashMap);
            List<BigDecimal> bigDecimals = BasePeriodUtils
                .toList(basePeriod96VO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < bigDecimals.size(); i++) {
                BigDecimal bigDecimal = bigDecimals.get(i);
                if(bigDecimal == null)continue;
                String bigDecimalStr = bigDecimal.toString();
                sb.append(bigDecimalStr).append(",");
            }
            String str = null;
            if (sb.toString().endsWith(",")) {
                String s = sb.toString();
                str = s.substring(0, s.length() - 1);
            } else {
                str = sb.toString();
            }
            acLoadReportD5000DO.setData(str);
            acLoadReportD5000Service.doUpdate(acLoadReportD5000DO);
        }
    }
}  
