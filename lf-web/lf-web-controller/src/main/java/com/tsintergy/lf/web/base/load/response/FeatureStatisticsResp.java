/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangchen Date:  2019/5/19 15:19 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.load.response;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 特性分析-特性统计
 *
 * <AUTHOR>
 * @create 2019/4/15
 * @since 1.0.0
 */
@Data
@ApiModel
public class FeatureStatisticsResp implements Serializable {

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最大电量",example = "123121")
    private BigDecimal maxEnergy;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最大实际电量",example = "123121")
    private BigDecimal hisMaxEnergy;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "平均电量",example = "123121")
    private BigDecimal avgEnergy;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "实际平均电量",example = "123121")
    private BigDecimal hisAvgEnergy;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最小电量",example = "123121")
    private BigDecimal minEnergy;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "实际最小电量",example = "123121")
    private BigDecimal hisMinEnergy;
    @BigdecimalJsonFormat(percentConvert = 100)
    @ApiModelProperty(value = "平均负荷峰谷差率",example = "123121")
    private BigDecimal avgloadGradient;
    @BigdecimalJsonFormat(percentConvert = 100)
    @ApiModelProperty(value = "实际平均负荷峰谷差率",example = "123121")
    private BigDecimal hisAvgLoadGradient;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "总电量",example = "123121")
    private BigDecimal totalEnergy;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "实际总电量",example = "123121")
    private BigDecimal hisTotalEnergy;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最高温度",example = "123121")
    private BigDecimal highestTemperature;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "实际最高温度",example = "123121")
    private BigDecimal hisHighestTemperature;

    /**
     * 峰谷差
     */
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "峰谷差",example = "123121")
    private  BigDecimal different;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "实际峰谷差",example = "123121")
    private  BigDecimal hisDifferent;

    /**
     * 峰谷差率
     */
    @BigdecimalJsonFormat(percentConvert = 100)
    @ApiModelProperty(value = "峰谷差率",example = "0.99")
    private  BigDecimal gradient;
    @BigdecimalJsonFormat(percentConvert = 100)
    @ApiModelProperty(value = "实际峰谷差率",example = "0.98")
    private  BigDecimal hisGradient;



}
