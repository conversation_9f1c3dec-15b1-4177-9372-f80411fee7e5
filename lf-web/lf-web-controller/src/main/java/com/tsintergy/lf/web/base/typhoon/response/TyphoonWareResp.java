/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  wang<PERSON>
 * Date:  2019/6/25 8:05
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.typhoon.response;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/6/25
 * @since 1.0.0
 */
@ApiModel
public class TyphoonWareResp implements Serializable {

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最大",example = "[12,213,2112]")
    private List<BigDecimal> max;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最小值",example = "[12,213,2112]")
    private List<BigDecimal> min;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "平均值",example = "[12,213,2112]")
    private List<BigDecimal> avg;

    @ApiModelProperty(value = "日期",example = "2012-03-12,2012-03-12,2012-03-12")
    private List<String> date;

    public List<BigDecimal> getMax() {
        return max;
    }

    public void setMax(List<BigDecimal> max) {
        this.max = max;
    }

    public List<BigDecimal> getMin() {
        return min;
    }

    public void setMin(List<BigDecimal> min) {
        this.min = min;
    }

    public List<BigDecimal> getAvg() {
        return avg;
    }

    public void setAvg(List<BigDecimal> avg) {
        this.avg = avg;
    }

    public List<String> getDate() {
        return date;
    }

    public void setDate(List<String> date) {
        this.date = date;
    }
}