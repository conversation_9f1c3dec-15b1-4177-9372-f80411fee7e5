package com.tsintergy.lf.web.base.common.controller;


import static com.tsieframework.core.base.exception.TsieExceptionUtils.newBusinessException;

import com.alibaba.fastjson.JSONObject;
import com.tsieframework.cloud.security.web.common.web.controller.BaseController;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.*;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.dto.CaliberDTO;
import com.tsintergy.lf.serviceapi.base.base.dto.CityDTO;
import com.tsintergy.lf.serviceapi.base.base.dto.PeakTimeDTO;
import com.tsintergy.lf.serviceapi.base.base.pojo.CaliberDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.AlgorithmService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.dto.AlgorithmDTO;
import com.tsintergy.lf.serviceapi.base.forecast.dto.ManualAlgorithmDTO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemData;
import com.tsintergy.lf.web.base.check.response.CommonResp;
import com.tsintergy.lf.web.base.common.response.PeriodsResp;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 通用模块 User:taojingui Date:18-2-8 Time:下午8:10
 */
@Api(tags = "通用模块")
@RequestMapping("/common")
@RestController
public class CommonController extends CommonBaseController {

    private final Logger logger = LogManager.getLogger(CommonController.class);

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private AlgorithmService algorithmService;

    @Autowired
    private CityService cityService;

    @Autowired
    private LoadCityFcService loadCityFcService;

    @Autowired
    private SettingSystemService settingSystemService;

    @Autowired
    private CaliberService caliberService;


    /**
     * 获取系统时间
     */
    @ApiOperation("获取系统时间")
    @GetMapping("/system-time")
    public BaseResp<Map<String, Object>> getSystemTime() {
        Date date = this.getSystemDate();
        if (date == null) {
            date = new Date();
        }

        Calendar sysCal = Calendar.getInstance();
        sysCal.setTime(date);

        Calendar nowCal = Calendar.getInstance();
        sysCal.set(Calendar.HOUR_OF_DAY, nowCal.get(Calendar.HOUR_OF_DAY));
        sysCal.set(Calendar.MINUTE, nowCal.get(Calendar.MINUTE));
        sysCal.set(Calendar.SECOND, nowCal.get(Calendar.SECOND));

        BaseResp resp = BaseResp.succResp();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("time", sysCal.getTimeInMillis());
        resp.setData(map);
        return resp;
    }

    /**
     * 设置系统日期
     */
    @ApiOperation("设置系统日期")
    @PostMapping("/system-time/set")
    public BaseResp setSystemDate(@ApiParam(value = "时间")String time) {
        try {
            Date date = DateUtils.string2Date(time, DateFormatType.SIMPLE_DATE_FORMAT_STR);
            request.getSession().setAttribute(CacheConstants.SYSTEM_DATE, DateUtils.date2String(date, DateFormatType.DATE_FORMAT_STR));
            return BaseResp.succResp();
        } catch (Exception e) {
            return BaseResp.failResp("时间格式不对");
        }
    }

    @ApiOperation("获取多算法预测准确率页面算法列表")
    @GetMapping("/accuracy/algorithm/list")
    public BaseResp<CommonResp<AlgorithmDTO>> getAccuracyAlgorithm(@ApiParam(value = "城市id")String cityId) {
        try {
            // 获取算法列表
            List<AlgorithmDO> allAlgorithmsNotCache = algorithmService.getAllAlgorithmsNotCache();
            List<AlgorithmDO> pageAlgorithms = allAlgorithmsNotCache.stream().filter(
                    t -> AlgorithmConstants.COMMON_ALGORITHM_TYPE.equals(t.getType()) || AlgorithmConstants.NORMAL_ALGORITHM_TYPE
                        .equals(t.getType()) || AlgorithmConstants.HOLIDAY_ALGORITHM_TYPE.equals(t.getType())
                        || AlgorithmConstants.SPECIAL_ALGORITHM_TYPE.equals(t.getType()))
                .collect(Collectors.toList());
            // 获取自动预测算法
            String defaultAlgorithm = null;
            SystemData systemSetting = settingSystemService.getSystemSetting();
            List<AlgorithmDO> viewAlgorithms = null;
            if(cityId == null){
                cityId = this.getLoginCityId();
            }
            CityDO cityDO = cityService.findCityById(cityId);
            if(!CollectionUtils.isEmpty(pageAlgorithms)){
                if(CityConstants.PROVINCE_TYPE.equals(cityDO.getType())){
                    defaultAlgorithm = systemSetting.getProvinceNormalAlgorithm();
                    viewAlgorithms = pageAlgorithms.stream().filter(t->t.getProvinceView() == true).collect(Collectors.toList());
                }else if(CityConstants.CITY_TYPE.equals(cityDO.getType())){
                    defaultAlgorithm = systemSetting.getCityNormalAlgorithm();
                    viewAlgorithms = pageAlgorithms.stream().filter(t->t.getCityView() == true).collect(Collectors.toList());
                }
            }
            List<AlgorithmDTO> dtos = new ArrayList<>();
            List<String> cmAccuracyAlgorithmIds = systemSetting.getCmAccuracyAlgorithmIds();
            for (AlgorithmDO algorithmVO : viewAlgorithms) {
                if (cmAccuracyAlgorithmIds.contains(algorithmVO.getId()) && !algorithmVO.getId().equals(AlgorithmEnum.QR.getId())
                        && !algorithmVO.getId().equals(AlgorithmEnum.REPORT_FINAL.getId())) {
                    AlgorithmDTO dto = new AlgorithmDTO();
                    dto.setId(algorithmVO.getId());
                    dto.setAlgorithm(algorithmVO.getAlgorithmCn());
                    if (defaultAlgorithm.equals(algorithmVO.getId())) {
                        dto.setDefault(true);
                    }
                    dtos.add(dto);
                }
            }
            BaseResp resp = BaseResp.succResp();
            CommonResp<AlgorithmDTO> data = new CommonResp<AlgorithmDTO>();
            data.setDataList(dtos);
            resp.setData(data);
            return resp;
        } catch (Exception e) {
            logger.error("02I20180001", e);
            throw newBusinessException("02I20180001");
        }
        /*BaseResp<CommonResp<AlgorithmDTO>> algorithm = this.getAlgorithm(cityId);
        List<AlgorithmDTO> dataList = algorithm.getData().getDataList();
        if (!CollectionUtils.isEmpty(dataList)) {
            for (AlgorithmDTO algorithmDTO : dataList) {
                if (algorithmDTO.getId().equals(AlgorithmEnum.FORECAST_INNOVATION.getId()) ||
                    algorithmDTO.getId().equals(AlgorithmEnum.GRU_FACTORS.getId()) ||
                    algorithmDTO.getId().equals(AlgorithmEnum.FORECAST_XGBOOST.getId())) {

                }
            }
        }
        BaseResp resp = BaseResp.succResp();
        resp.setData(algorithm);
        return resp;*/
    }

    /**
     * 获取算法列表
     */
    @ApiOperation("获取算法列表")
    @GetMapping("/algorithm")
    public BaseResp<CommonResp<AlgorithmDTO>> getAlgorithm(@ApiParam(value = "城市id")String cityId) {
        try {
            // 获取算法列表
            List<AlgorithmDO> allAlgorithmsNotCache = algorithmService.getAllAlgorithmsNotCache();
            List<AlgorithmDO> pageAlgorithms = allAlgorithmsNotCache.stream().filter(
                t -> AlgorithmConstants.COMMON_ALGORITHM_TYPE.equals(t.getType()) || AlgorithmConstants.NORMAL_ALGORITHM_TYPE
                    .equals(t.getType()) || AlgorithmConstants.HOLIDAY_ALGORITHM_TYPE.equals(t.getType())
                    || AlgorithmConstants.SPECIAL_ALGORITHM_TYPE.equals(t.getType()))
                .collect(Collectors.toList());


            // 获取自动预测算法
            String defaultAlgorithm = null;
            SystemData systemSetting = settingSystemService.getSystemSetting();
            List<AlgorithmDO> viewAlgorithms = null;
            if(cityId == null){
                cityId = this.getLoginCityId();
            }
            CityDO cityDO = cityService.findCityById(cityId);
            if(!CollectionUtils.isEmpty(pageAlgorithms)){
                if(CityConstants.PROVINCE_TYPE.equals(cityDO.getType())){
                    defaultAlgorithm = systemSetting.getProvinceNormalAlgorithm();
                    viewAlgorithms = pageAlgorithms.stream().filter(t->t.getProvinceView() == true).collect(Collectors.toList());
                }else if(CityConstants.CITY_TYPE.equals(cityDO.getType())){
                    defaultAlgorithm = systemSetting.getCityNormalAlgorithm();
                    viewAlgorithms = pageAlgorithms.stream().filter(t->t.getCityView() == true).collect(Collectors.toList());
                }
            }

            List<AlgorithmDTO> dtos = new ArrayList<>();
            for (AlgorithmDO algorithmVO : viewAlgorithms) {
                AlgorithmDTO dto = new AlgorithmDTO();
                dto.setId(algorithmVO.getId());
                dto.setAlgorithm(algorithmVO.getAlgorithmCn());
                if (defaultAlgorithm.equals(algorithmVO.getId())) {
                    dto.setDefault(true);
                }
                dtos.add(dto);
            }
            BaseResp resp = BaseResp.succResp();
            CommonResp<AlgorithmDTO> data = new CommonResp<AlgorithmDTO>();
            data.setDataList(dtos);
            resp.setData(data);
            return resp;
        } catch (Exception e) {
            logger.error("02I20180001", e);
            throw newBusinessException("02I20180001");
        }
    }


    @ApiOperation("上报算法")
    @GetMapping("/algorithm-report")
    public BaseResp<CommonResp<AlgorithmDTO>> getAlgorithmReportV2(@ApiParam(value = "时间")Date date,@ApiParam(value = "口径id") String caliberId, @ApiParam(value = "城市")String cityId) throws Exception{
        if(date == null){
            date = this.getSystemDate();
        }

        if(StringUtils.isEmpty(caliberId)){
            caliberId = this.getCaliberId();
        }

        if(StringUtils.isEmpty(cityId)){
            cityId = this.getLoginCityId();
        }


        List<AlgorithmDTO> dtos = new ArrayList<AlgorithmDTO>();
        CityDO cityDO = cityService.findCityById(cityId);

        String reportAlgorithmId = null;
        SystemData systemSetting = settingSystemService.getSystemSetting();
        if (cityId.equals(CityConstants.PROVINCE_ID)) {
            reportAlgorithmId = systemSetting.getProvinceNormalAlgorithm();
        } else {
            reportAlgorithmId = systemSetting.getCityNormalAlgorithm();
        }


        List<AlgorithmDO> allAlgorithmsNotCache = algorithmService.getAllAlgorithmsNotCache();
        if(!CollectionUtils.isEmpty(allAlgorithmsNotCache)){
            List<AlgorithmDO> pageAlgorithms = null;

            //查询正常日算法
            pageAlgorithms = allAlgorithmsNotCache.stream().filter(
                t -> AlgorithmConstants.COMMON_ALGORITHM_TYPE.equals(t.getType()) || AlgorithmConstants.NORMAL_ALGORITHM_TYPE
                    .equals(t.getType()))
                .collect(Collectors.toList());

            //根据城市筛选要展示的算法，排除修正上报
            List<AlgorithmDO> viewAlgorithms = null;
            if(!CollectionUtils.isEmpty(pageAlgorithms)){
                if(CityConstants.PROVINCE_TYPE.equals(cityDO.getType())){
                    viewAlgorithms = pageAlgorithms.stream().filter(t->t.getProvinceView() == true && !t.getId().equals(AlgorithmConstants.MD_ALGORITHM_ID)).collect(Collectors.toList());
                }else if(CityConstants.CITY_TYPE.equals(cityDO.getType())){
                    viewAlgorithms = pageAlgorithms.stream().filter(t->t.getCityView() == true && !t.getId().equals(AlgorithmConstants.MD_ALGORITHM_ID)).collect(Collectors.toList());
                }
            }

            // flag 标识位，用来标识是否有算法设置位true
            Boolean flag = false;
            if(!CollectionUtils.isEmpty(viewAlgorithms)){
                for(AlgorithmDO algorithmDO:viewAlgorithms){
                    AlgorithmDTO dto = new AlgorithmDTO();
                    dto.setAlgorithm(algorithmDO.getAlgorithmCn());
                    dto.setId(algorithmDO.getId());
                    if(algorithmDO.getId().equals(reportAlgorithmId)){
                        dto.setDefault(true);
                        flag = true;
                    }
                    dtos.add(dto);
                }
            }

            if(!flag){
                AlgorithmDTO dto = dtos.get(0);
                dto.setDefault(true);
                dtos.set(0,dto);
            }
        }
        BaseResp resp = BaseResp.succResp();
        CommonResp<AlgorithmDTO> data = new CommonResp<AlgorithmDTO>();
        data.setDataList(dtos);
        resp.setData(data);
        return resp;
    }


    @GetMapping("/manualPredictionAlgorithms")
    @ApiOperation(value = "正常日修正上报手动预测中算法下拉列表接口")
    public BaseResp<List<ManualAlgorithmDTO>> getAlgorithmListManualPrediction(@RequestParam(required = false) @ApiParam(value = "城市id")String cityId) throws Exception{

        if(StringUtils.isEmpty(cityId)){
            cityId = this.getLoginCityId();
        }
        CityDO cityDO = cityService.findCityById(cityId);
        List<AlgorithmDO> allAlgorithmsNotCache = algorithmService.getAllAlgorithmsNotCache();
        List<AlgorithmDO> viewAlgorithms = null;
        if(!CollectionUtils.isEmpty(allAlgorithmsNotCache)){
            List<AlgorithmDO> pageAlgorithms = null;
            pageAlgorithms = allAlgorithmsNotCache.stream().filter(
                t -> AlgorithmConstants.NORMAL_ALGORITHM_TYPE.equals(t.getType()))
                .collect(Collectors.toList());


            if(!CollectionUtils.isEmpty(pageAlgorithms)){
                if(CityConstants.PROVINCE_TYPE.equals(cityDO.getType())){
                    viewAlgorithms = pageAlgorithms.stream().filter(t->t.getProvinceView() == true).collect(Collectors.toList());
                }else if(CityConstants.CITY_TYPE.equals(cityDO.getType())){
                    viewAlgorithms = pageAlgorithms.stream().filter(t->t.getCityView() == true).collect(Collectors.toList());
                }
            }
        }

        List<ManualAlgorithmDTO> dtos = new ArrayList<ManualAlgorithmDTO>();
        if(!CollectionUtils.isEmpty(viewAlgorithms)){
            for(AlgorithmDO algorithmDO:viewAlgorithms){
                ManualAlgorithmDTO dto = new ManualAlgorithmDTO();
                dto.setId(algorithmDO.getId());
                dto.setName(algorithmDO.getAlgorithmCn());
                dto.setCode(algorithmDO.getCode());
                dtos.add(dto);
            }
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(dtos);
        return baseResp;
    }


    @GetMapping("/systemSettingAlgorithms")
    @ApiOperation(value = "系统设置中算法下拉列表接口")
    public BaseResp<CommonResp<AlgorithmDTO>> getAlgorithmListSystemSetting(@ApiParam(value = "城市Type")Integer cityType) throws Exception{

        try {
            // 获取算法列表
            List<AlgorithmDO> allAlgorithmsNotCache = algorithmService.getAllAlgorithmsNotCache();
            List<AlgorithmDO> pageAlgorithms = allAlgorithmsNotCache.stream().filter(
                t ->  AlgorithmConstants.NORMAL_ALGORITHM_TYPE.equals(t.getType())
                    || AlgorithmConstants.HOLIDAY_ALGORITHM_TYPE.equals(t.getType())
                    || AlgorithmConstants.SPECIAL_ALGORITHM_TYPE.equals(t.getType()))
                .collect(Collectors.toList());

            // 获取自动预测算法
            String defaultAlgorithm = null;
            SystemData systemSetting = settingSystemService.getSystemSetting();
            List<AlgorithmDO> viewAlgorithms = null;
            if(!CollectionUtils.isEmpty(pageAlgorithms)){
                if(CityConstants.PROVINCE_TYPE.equals(cityType)){
                    defaultAlgorithm = systemSetting.getProvinceNormalAlgorithm();
                    viewAlgorithms = pageAlgorithms.stream().filter(t->t.getProvinceView() == true).collect(Collectors.toList());
                }else if(CityConstants.CITY_TYPE.equals(cityType)){
                    defaultAlgorithm = systemSetting.getCityNormalAlgorithm();
                    viewAlgorithms = pageAlgorithms.stream().filter(t->t.getCityView() == true).collect(Collectors.toList());
                }
            }

            List<AlgorithmDTO> dtos = new ArrayList<>();
            for (AlgorithmDO algorithmVO : viewAlgorithms) {
                AlgorithmDTO dto = new AlgorithmDTO();
                dto.setId(algorithmVO.getId());
                dto.setAlgorithm(algorithmVO.getAlgorithmCn());
                if (defaultAlgorithm.equals(algorithmVO.getId())) {
                    dto.setDefault(true);
                }
                dtos.add(dto);
            }
            BaseResp resp = BaseResp.succResp();
            CommonResp<AlgorithmDTO> data = new CommonResp<AlgorithmDTO>();
            data.setDataList(dtos);
            resp.setData(data);
            return resp;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("02I20180001", e);
            throw newBusinessException("02I20180001");
        }
    }



    /**
     * 获取算法列表
     */
    public BaseResp getAlgorithmReport(String date, String caliberId, String cityId) {
        try {
            Date d = this.getSystemDate();
            if (date != null) {
                d = DateUtils.string2Date(date, DateFormatType.SIMPLE_DATE_FORMAT_STR);
            }
            if (caliberId == null) {
                caliberId = this.getCaliberId();
            }
            if (StringUtils.isEmpty(cityId)) {
                cityId = this.getLoginCityId();
            }
            String reportAlgorithmId = null;
            LoadCityFcDO fcVO = loadCityFcService.getReport(cityId, caliberId, d);
            if (fcVO != null) {
                reportAlgorithmId = AlgorithmConstants.MD_ALGORITHM_ID;
            } else {
                SystemData systemSetting = settingSystemService.getSystemSetting();
                if (cityId.equals(CityConstants.PROVINCE_ID)) {
                    reportAlgorithmId = systemSetting.getProvinceNormalAlgorithm();
                } else {
                    reportAlgorithmId = systemSetting.getCityNormalAlgorithm();
                }
            }
            //先查人工决策算法，无，再查系统默认算法，无，就找任意一条有预测数据的算法
            List<AlgorithmDTO> dtos = new ArrayList<>();
            // 获取算法列表
            List<AlgorithmDO> algorithmVOS = algorithmService.getAllAlgorithms();
            for (AlgorithmDO algorithmVO : algorithmVOS) {
                String code = algorithmVO.getCode();
                String id = algorithmVO.getId();
                if (id.equals(AlgorithmConstants.MD_ALGORITHM_ID) || code.equals(AlgorithmEnum.FORECAST_XGBOOST.getType())
                    || code.equals(AlgorithmEnum.FORECAST_SVM_LARGE.getType())
                    || code.equals(AlgorithmEnum.FORECAST_INNOVATION.getType()) || code
                    .equals(AlgorithmEnum.FORECAST_SVM.getType()) || code
                    .equals(AlgorithmEnum.COMPREHENSIVE_MODEL.getType())
                    || code.equals(AlgorithmEnum.REPLENISH_LGB.getType())) {
                    AlgorithmDTO algorithmDTO = new AlgorithmDTO();
                    algorithmDTO.setId(algorithmVO.getId());
                    algorithmDTO.setAlgorithm(algorithmVO.getAlgorithmCn());
                    if (id.equals(reportAlgorithmId)) {
                        algorithmDTO.setDefault(true);
                    }
                    dtos.add(algorithmDTO);
                }

            }
            BaseResp resp = BaseResp.succResp();
            CommonResp<AlgorithmDTO> data = new CommonResp<AlgorithmDTO>();
            data.setDataList(dtos);
            resp.setData(data);
            return resp;
        } catch (Exception e) {
            throw newBusinessException("02I20180001");
        }

    }

    /**
     * 获取城市列表
     */
    @ApiOperation("获取城市列表")
    @GetMapping("/city")
    public BaseResp<CommonResp<CityDTO>> getCity() {
        try {
            String cityId = this.getLoginCityId();
            CityDO city = cityService.findCityById(cityId);
            List<CityDO> cityVOS = new ArrayList<>();
            if (city.getType() == 1) {
                cityVOS = cityService.findAllCitys();
            } else {
                cityVOS.add(city);
            }
            List<CityDTO> dtos = new ArrayList<CityDTO>();
            for (CityDO cityVO : cityVOS) {
                CityDTO dto = new CityDTO();
                dto.setId(cityVO.getId());
                dto.setCity(cityVO.getCity());
                dtos.add(dto);
            }
            CommonResp<CityDTO> commonResp = new CommonResp<CityDTO>();
            commonResp.setDataList(dtos);
            BaseResp resp = BaseResp.succResp();
            resp.setData(commonResp);
            return resp;
        } catch (Exception e) {
            logger.error("02I20180001", e);
            throw newBusinessException("02I20180001");
        }
    }

    /**
     * 获取早中晚高峰
     */
    @ApiOperation("获取早中晚高峰")
    @GetMapping("/peak")
    public BaseResp<PeakTimeDTO> getPeak() {
        try {
            // 获取高峰时段
            String peakTime = settingSystemService.getValue(SystemConstant.PEAK_TIME);
            String[] times = peakTime.split(",");
            PeakTimeDTO peakTimeDTO = new PeakTimeDTO();
            peakTimeDTO.setEarlyPeak(Arrays.asList(times[0].split("~")));
            peakTimeDTO.setNoonPeak(Arrays.asList(times[1].split("~")));
            peakTimeDTO.setNightPeak(Arrays.asList(times[2].split("~")));
            BaseResp<PeakTimeDTO> resp = BaseResp.succResp();
            resp.setData(peakTimeDTO);
            return resp;
        } catch (Exception e) {
            throw newBusinessException("02I20180001");
        }
    }

    /**
     * 获取时段列表
     */
    @ApiOperation("获取时段列表")
    @GetMapping("/periods")
    public BaseResp<PeriodsResp> getPeriods(Integer type) {
        try {
            PeriodsResp periodsResp = new PeriodsResp();
            // 负荷时刻点数
            periodsResp.setLoadPeriods(ColumnUtil.getDayTimes(Constants.LOAD_CURVE_POINT_NUM,
                Constants.LOAD_CURVE_START_WITH_ZERO));
            // 气象时刻点数
            periodsResp.setWeatherPeriods(ColumnUtil.getDayTimes(Constants.WEATHER_CURVE_POINT_NUM,
                Constants.WEATHER_CURVE_START_WITH_ZERO));
            BaseResp<PeriodsResp> resp = BaseResp.succResp();
            resp.setData(periodsResp);
            return resp;
        } catch (Exception e) {
            throw newBusinessException("02I20180001");
        }
    }

    /**
     * 获取口径列表
     */
    @ApiOperation("获取口径列表")
    @GetMapping("/caliber")
    public BaseResp<CommonResp<CaliberDTO>> getCaliberList() {
        String loginCityId = this.getLoginCityId();
        try {
            CityDO cityDO = cityService.findCityById(loginCityId);
            List<CaliberDO> caliberVOS = caliberService.findAllCalibers();
            if(CityConstants.PROVINCE_TYPE.equals(cityDO.getType())){
                //省调
                caliberVOS = caliberVOS.stream().filter(t->"1".equals(t.getProvinceView())).collect(Collectors.toList());
            }else if(CityConstants.CITY_TYPE.equals(cityDO.getType())){
                caliberVOS = caliberVOS.stream().filter(t->"1".equals(t.getCityView())).collect(Collectors.toList());
            }

            String defaultCaliberId = this.getCaliberId();
            boolean flag = false;
            List<CaliberDTO> dtos = new ArrayList<CaliberDTO>();
            for (CaliberDO caliberVO : caliberVOS) {
                CaliberDTO dto = new CaliberDTO();
                dto.setId(caliberVO.getId());
                dto.setCaliber(caliberVO.getName());
                if(defaultCaliberId.equals(caliberVO.getId())){
                    flag =true;
                }
                dto.setDefault(defaultCaliberId.equals(caliberVO.getId()));
                dtos.add(dto);
            }
            if(!flag){
                CaliberDTO caliberDTO = dtos.get(0);
                caliberDTO.setDefault(true);
            }

            String caliberId = dtos.stream().filter(t -> t.getDefault() == true).collect(Collectors.toList()).get(0).getId();
            if( request.getSession().getAttribute(CacheConstants.CALIBER_ID) == null) {
                request.getSession().setAttribute(CacheConstants.CALIBER_ID, caliberId);
            }

            CommonResp<CaliberDTO> commonResp = new CommonResp<CaliberDTO>();
            commonResp.setDataList(dtos);
            BaseResp resp = BaseResp.succResp();
            resp.setData(commonResp);
            return resp;
        } catch (Exception e) {
            logger.error("02I20180001", e);
            throw newBusinessException("02I20180001");
        }
    }

    /**
     * 设置默认口径
     */
    @ApiOperation("设置默认口径")
    @RequestMapping(value = "/caliber/default", method = RequestMethod.POST)
    public BaseResp setDefaultCaliber(@RequestBody JSONObject jsonObject) {
        try {
            String caliberId = jsonObject.get("caliberId").toString();
            if (StringUtils.isBlank(caliberId)) {
                return BaseResp.failResp("口径不可为空");
            }
            request.getSession().setAttribute(CacheConstants.CALIBER_ID, caliberId);
            return BaseResp.succResp();
        } catch (Exception e) {
            logger.error("02I20180001", e);
            throw newBusinessException("02I20180001");
        }
    }

    /**
     * 上报结果查询页面 获取算法列表 增加最终上报选项
     */
    @ApiOperation("上报结果查询页面")
    @GetMapping("/reportResult/algorithm")
    public BaseResp<CommonResp<AlgorithmDTO>> geReportResulttAlgorithm() {
        try {
            // 获取算法列表
            List<AlgorithmDO> algorithmVOS = algorithmService.getAllAlgorithms();
            List<AlgorithmDTO> dtos = new ArrayList<AlgorithmDTO>();
            CityDO cityById = cityService.findCityById(getLoginCityId());
            AlgorithmDTO algorithmDTO = new AlgorithmDTO();
            algorithmDTO.setId(AlgorithmEnum.RESULT_REPORT.getId());
            algorithmDTO.setAlgorithm(AlgorithmEnum.RESULT_REPORT.getDescription());
            algorithmDTO.setDefault(true);
            dtos.add(algorithmDTO);
            for (AlgorithmDO algorithmVO : algorithmVOS) {
                if (algorithmVO.getCode().equals(AlgorithmEnum.FORECAST_SIMILAR.getType())) {
                    continue;
                }
                if (cityById.getType().equals(CityConstants.CITY_TYPE)) {
                    if (AlgorithmEnum.COMPREHENSIVE_MODEL.getType().equals(algorithmVO.getCode()) ||
                        AlgorithmEnum.FORECAST_INNOVATION.getType().equals(algorithmVO.getCode())) {
                        continue;
                    }
                }
                AlgorithmDTO dto = new AlgorithmDTO();
                dto.setId(algorithmVO.getId());
                dto.setAlgorithm(algorithmVO.getAlgorithmCn());
                dtos.add(dto);
            }
            BaseResp resp = BaseResp.succResp();
            CommonResp<AlgorithmDTO> data = new CommonResp<AlgorithmDTO>();
            data.setDataList(dtos);
            resp.setData(data);
            return resp;
        } catch (Exception e) {
            logger.error("02I20180001", e);
            throw newBusinessException("02I20180001");
        }
    }
}

