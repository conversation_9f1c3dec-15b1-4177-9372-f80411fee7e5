package com.tsintergy.lf.web.base.configure;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.tsieframework.cloud.security.serviceapi.system.api.RedisService;
import com.tsieframework.cloud.security.serviceapi.system.api.SecurityService;
import com.tsieframework.cloud.security.web.common.security.TokenManager;
import com.tsieframework.cloud.security.web.common.security.account.AccountManager;
import com.tsieframework.core.base.SpringContextManager;
import com.tsieframework.core.base.enums.fastjson.TsieFastJsonConfig;
import com.tsieframework.core.base.enums.fastjson.TsieJsonHttpMessageConverter;
import com.tsintergy.lf.serviceapi.base.security.api.UserService;
import com.tsintergy.lf.web.base.common.aspect.LogBeanPostProcessor;
import com.tsintergy.lf.web.base.common.convert.SmartDateFormatter;
import com.tsintergy.lf.web.base.common.filter.BigDecimalFilter;
import com.tsintergy.lf.web.base.common.interceptor.*;
import com.tsintergy.lf.web.base.security.controller.SsoLoadForecastAccountManager;
import com.tsintergy.lf.web.base.security.properties.EvalutionProperties;
import com.tsintergy.lf.web.base.security.properties.SecurityProperties;
import java.util.ArrayList;
import java.util.EventListener;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import javax.servlet.Filter;
import lombok.extern.slf4j.Slf4j;
import org.jasig.cas.client.authentication.AuthenticationFilter;
import org.jasig.cas.client.session.SingleSignOutFilter;
import org.jasig.cas.client.session.SingleSignOutHttpSessionListener;
import org.jasig.cas.client.util.AssertionThreadLocalFilter;
import org.jasig.cas.client.util.HttpServletRequestWrapperFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.ServletListenerRegistrationBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.http.MediaType;
import org.springframework.security.web.firewall.HttpFirewall;
import org.springframework.security.web.firewall.StrictHttpFirewall;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <p>
 * Web模块的配置，通常这里负责注入非Controller的其他类型的Bean定义，方便统一查找
 * </p>
 *
 * <AUTHOR>
 * @date 2020-02-18 11:00:43
 */
@Configuration
@Slf4j
@EnableConfigurationProperties({SecurityProperties.class, EvalutionProperties.class})
public class LfWebConfig {

    @Value("${tsie.cas.url.prefix}")
    public String casServerUrlPrefix;

    @Value("${tsie.cas.url.login}")
    public String casServerLoginUrl;

    @Value("${tsie.cas.server.name}")
    public String serverName;

    @Value("${tsie.cas.open}")
    public Boolean openSSO;

    @Value("${tsie.cas.server.restfulRedirectUrl}")
    public  String restfulRedirectUrl;

    @Value("${tsie.cas.ifRestful}")
    public Boolean ifRestful;

    @Value("${cas.redirect-url}")
    private String redirectUrl;

    @Value("${cas.server-url-prefix}")
    private String diaokongServerName;

    @Value("${cas.server-login-url}")
    private String diaokongServerLoginUrl;

    @Value("${cas.client-host-url}")
    private String diaokongClientHostUrl;

    @Value("${sso.whiteList}")
    private String whiteList;

    @Autowired
    private SecurityService securityService;

    @Autowired
    private TokenManager tokenManager;

    @Autowired
    private UserService userService;

    public LfWebConfig() {
        log.debug("初始化" + getClass());
    }


    @Bean
    public TokenManager tokenManager() {
        return new TokenManager();
    }

    @Bean
    public SmartDateFormatter smartDateFormatter() {
        return new SmartDateFormatter();
    }

    @Bean
    public AccountManager accountManager() {
        return new SsoLoadForecastAccountManager(securityService, tokenManager);
    }

    @Bean
    public ScheduledThreadPoolExecutor scheduledThreadPoolExecutor() {
        return new ScheduledThreadPoolExecutor(5);
    }


    @Bean
    public TsieJsonHttpMessageConverter messageConverter() {
        TsieJsonHttpMessageConverter converter = new TsieJsonHttpMessageConverter();
        TsieFastJsonConfig fastJsonConfig = new TsieFastJsonConfig();
        fastJsonConfig.setSerializerFeatures(
            SerializerFeature.WriteMapNullValue,
            SerializerFeature.QuoteFieldNames,
            SerializerFeature.WriteNullListAsEmpty,
            SerializerFeature.WriteDateUseDateFormat,
            SerializerFeature.SkipTransientField);
        //日期格式化 如果使用这种格式化会导致@JsonField注解失效
//        fastJsonConfig.setDateFormat("yyyy-MM-dd");
        JSON.DEFFAULT_DATE_FORMAT = "yyyy-MM-dd";
        fastJsonConfig.setSerializeFilters();
        //对BigDecimal类型做统一处理
        fastJsonConfig.setSerializeFilters(new BigDecimalFilter());
        converter.setFastJsonConfig(fastJsonConfig);
        List<MediaType> supportedMediaTypes = new ArrayList<>();
        supportedMediaTypes.add(MediaType.APPLICATION_JSON);
        converter.setSupportedMediaTypes(supportedMediaTypes);
        return converter;
    }


    @Bean
    public FilterRegistrationBean filterSingleRegistration() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(new SingleSignOutFilter());
        // 设定匹配的路径
        registration.addUrlPatterns("/*");
        Map<String, String> initParameters = new HashMap();
        initParameters.put("casServerUrlPrefix", casServerUrlPrefix);
        registration.setInitParameters(initParameters);
        // 设定加载的顺序
        registration.setOrder(1);
        return registration;
    }

    /**
     * SingleSignOutHttpSessionListener 添加监听器 用于单点退出，该过滤器用于实现单点登出功能，可选配置
     */
    @Bean
    public ServletListenerRegistrationBean<EventListener> singleSignOutListenerRegistration() {
        ServletListenerRegistrationBean<EventListener> registrationBean = new ServletListenerRegistrationBean<EventListener>();
        registrationBean.setListener(new SingleSignOutHttpSessionListener());
        registrationBean.setOrder(1);
        return registrationBean;
    }



//    @Bean
//    public FilterRegistrationBean<LoginInitFilter> casLoginInitFilter(){
//        final FilterRegistrationBean registration = new FilterRegistrationBean();
//        registration.setFilter(new LoginInitFilter(securityService, tokenManager));
//        // 设定匹配的路径
//        registration.addUrlPatterns("/*");
//        // 设定加载的顺序
//        registration.setOrder(99);
//        return registration;
//    }

    /**
     * Cas30ProxyReceivingTicketValidationFilter 验证过滤器 该过滤器负责对Ticket的校验工作，必须启用它
     */

    /**
     * AssertionThreadLocalFilter
     * <p>
     * 该过滤器使得开发者可以通过org.jasig.cas.client.util.AssertionHolder来获取用户的登录名。 比如AssertionHolder.getAssertion().getPrincipal().getName()。
     */
    @Bean
    public FilterRegistrationBean filterAssertionThreadLocalRegistration() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(new AssertionThreadLocalFilter());
        // 设定匹配的路径
        registration.addUrlPatterns("/*");
        // 设定加载的顺序
        registration.setOrder(1);
        return registration;
    }

    /**
     * HttpServletRequestWrapperFilter wraper过滤器；该过滤器负责实现HttpServletRequest请求的包裹，比如允许开发者通过HttpServletRequest的getRemoteUser()方法获得SSO登录用户的登录名，可选配置。
     */
    @Bean
    public FilterRegistrationBean filterWrapperRegistration() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(new HttpServletRequestWrapperFilter());
        // 设定匹配的路径
        registration.addUrlPatterns("/*");
        // 设定加载的顺序
        registration.setOrder(1);
        return registration;
    }

    /*************************************   SSO配置-结束   ************************************************/


//    @Configuration
//    static class WebConfig implements WebMvcConfigurer {
//
//        private final TokenManager tokenManager;
//
//        private final com.tsieframework.cloud.security.core.base.properties.SecurityProperties securityProperties;
//
//        private final SecurityProperties customSecurityProperties;
//
//        private final RedisService redisService;
//
//        private final UserService loadUserDetailService;
//
//        public WebConfig(TokenManager tokenManager,
//            com.tsieframework.cloud.security.core.base.properties.SecurityProperties securityProperties,
//            SecurityProperties customSecurityProperties, RedisService redisService,
//            UserService loadUserDetailService) {
//            this.tokenManager = tokenManager;
//            this.securityProperties = securityProperties;
//            this.customSecurityProperties = customSecurityProperties;
//            this.redisService = redisService;
//            this.loadUserDetailService = loadUserDetailService;
//        }
//
//        @Override
//        public void addInterceptors(InterceptorRegistry registry) {
//            boolean openInterceptor = this.customSecurityProperties.isTokenInterceptorOpen();
//            ArrayList excludes = new ArrayList();
//
//            try {
//                List<String> excludesProperty = this.securityProperties.getTokenInterceptor().getExcludes();
//                if (excludesProperty != null) {
//                    excludes.addAll(excludesProperty);
//                }
//            } catch (Exception var5) {
//                log.error("自定义排除项加载失败");
//            }
//
//            excludes.add("/**/oauthController/**");
//            excludes.add("/**/logincontroller/**");
//            excludes.add("/**/sysMenuManage/menu/userMenuTree");
//            excludes.add("/**/systemManageController/menu/initmenulist");
//            excludes.add("/**/systemManageController/menu/initztreenode");
//            excludes.add("/**/system/baseInfo");
//            excludes.add("/**/login.html");
//            excludes.add("/**/*.html");
//            excludes.add("/**/*.htm");
//            excludes.add("/**/*.gif");
//            excludes.add("/**/*.jpg");
//            excludes.add("/**/*.jpeg");
//            excludes.add("/**/*.bmp");
//            excludes.add("/**/*.png");
//            excludes.add("/**/*.ico");
//            excludes.add("/**/*.txt");
//            excludes.add("/**/*.js");
//            excludes.add("/**/*.css");
//            excludes.add("/**/*.map");
//            excludes.add("/**/*.svg");
//            excludes.add("/**/*.woff");
//            excludes.add("/**/*.woff2");
//            excludes.add("/**/*.tff");
//            log.debug("添加默认排除项");
//            if (openInterceptor) {
//                //注入权限校验拦截器
//                CustomTokenInterceptor tokenInterceptor = new CustomTokenInterceptor();
//                tokenInterceptor.setTokenCacheManager(this.tokenManager);
//                registry.addInterceptor(tokenInterceptor).excludePathPatterns(excludes)
//                    .addPathPatterns(new String[]{"/**"});
//
//                //注入密码过期校验拦截器
//                PasswordVerificationInterceptor passwordVerificationInterceptor = new PasswordVerificationInterceptor();
//                passwordVerificationInterceptor.setRedisService(redisService);
//                passwordVerificationInterceptor.setSecurityProperties(customSecurityProperties);
//                passwordVerificationInterceptor.setTokenManager(tokenManager);
//                passwordVerificationInterceptor.setUserService(loadUserDetailService);
//                registry.addInterceptor(passwordVerificationInterceptor).excludePathPatterns(excludes)
//                    .addPathPatterns(new String[]{"/**"});
//            }
//
//        }
//    }


    @Bean
    public BeanPostProcessor myBeanPostProcessor(){
        return new LogBeanPostProcessor();
    }

//    @Bean
//    @Conditional(DiaokongCasLoginCondition.class)
//    @DependsOn("accountManager")
//    public FilterRegistrationBean filterDiaoKongLoginRegistration() {
//
//        FilterRegistrationBean registration = new FilterRegistrationBean();
//        Map<String, String> initParameters = new HashMap();
//        SsoLoadForecastAccountManager ssoLoadForecastAccountManager = SpringContextManager.getApplicationContext().getBean(SsoLoadForecastAccountManager.class);
//        Filter auth = new DiaokongCasAuthenticationFilter(securityService, ssoLoadForecastAccountManager, userService, redirectUrl);
//        registration.setFilter(auth);
//        initParameters.put("casServerLoginUrl", diaokongServerLoginUrl);
//        initParameters.put("serverName", diaokongClientHostUrl);
//        initParameters.put("restfulRedirectUrl",redirectUrl);
//        // 不拦截的请求 .* 有后缀的文件
//        initParameters.put("ignorePattern", whiteList);
//
//        registration.setInitParameters(initParameters);
//        registration.addUrlPatterns("/*");
//        // 设定加载的顺序
//        registration.setOrder(6);
//        return registration;
//    }

    @Bean
//    @Conditional(DiaokongCasLoginCondition.class)
    public HttpFirewall allowSemicolonHttpFirewall() {
        StrictHttpFirewall firewall = new StrictHttpFirewall();
        firewall.setAllowSemicolon(true); // 允许分号
        return firewall;
    }

}
