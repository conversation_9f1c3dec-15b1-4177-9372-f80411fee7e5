package com.tsintergy.lf.web.base.assess.controller;


import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.base.assess.api.SettingAssessService;
import com.tsintergy.lf.serviceapi.base.assess.api.SettingCompositeAccuracyService;
import com.tsintergy.lf.serviceapi.base.assess.dto.EquationDTO;
import com.tsintergy.lf.serviceapi.base.assess.dto.SettingAssessDataDTO;
import com.tsintergy.lf.serviceapi.base.assess.pojo.SettingCompositeAccuracyDO;
import com.tsintergy.lf.web.base.assess.request.AccuracyDataRequest;
import com.tsintergy.lf.web.base.controller.BaseController;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 考核点配置页面
 *
 * <AUTHOR>
 **/
@RequestMapping("/setting")
@RestController
@Slf4j
public class SettingAssessController extends BaseController {


    @Resource
    private SettingAssessService settingAssessService;

    @Resource
    private SettingCompositeAccuracyService settingCompositeAccuracyService;


    /**
     * 考核点配置-查询考核点列表；
     *
     * <AUTHOR>
     */
    @RequestMapping(value = "/assess/list", method = RequestMethod.GET)
    public BaseResp assessList(String year, String caliberId) {
        List<SettingAssessDataDTO> dataList = settingAssessService.findAssessDataList(year, caliberId);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(dataList);
        return baseResp;
    }

    /**
     * 考核点配置-校验名称是否可用；  true 可用； false 不可用
     *
     * <AUTHOR>
     */
    @RequestMapping(value = "/assess/check", method = RequestMethod.GET)
    public BaseResp assessCheck(String year, String caliberId, String assessName) {
        Boolean aBoolean = settingAssessService.checkNameAvailable(year, caliberId, assessName);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(aBoolean);
        return baseResp;
    }

    /**
     * 考核点配置-保存考核点数据
     *
     * <AUTHOR>
     */
    @RequestMapping(value = "/assess/save", method = RequestMethod.POST)
    public BaseResp assessSave(@RequestBody List<SettingAssessDataDTO> dataList) {
        settingAssessService.doSaveOrUpdateAssess(dataList);
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }

    /**
     * 考核点配置-删除考核点
     *
     * <AUTHOR>
     */
    @RequestMapping(value = "/assess/delete", method = RequestMethod.DELETE)
    public BaseResp deleteAssess(@RequestBody AccuracyDataRequest accuracyDataRequest) {
        settingAssessService.doDeleteByAssessName(accuracyDataRequest.getYear(), accuracyDataRequest.getCaliberId(),
            accuracyDataRequest.getAssessName());
        List<SettingCompositeAccuracyDO> byAssessUnitName = settingCompositeAccuracyService
            .findByAssessUnitName(accuracyDataRequest.getYear(), accuracyDataRequest.getCaliberId(),accuracyDataRequest.getAssessName());
        if (!CollectionUtils.isEmpty(byAssessUnitName)){
            for (SettingCompositeAccuracyDO one : byAssessUnitName) {
                settingCompositeAccuracyService.doDelete(one);
            }
        }
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }

    /**
     * 考核点配置-查询所有可用考核点
     *
     * <AUTHOR>
     */
    @RequestMapping(value = "/assess/nameList", method = RequestMethod.GET)
    public BaseResp nameList(String year, String caliberId) {
        List<String> allValidAssess = settingAssessService.findAllValidAssess(year, caliberId);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(allValidAssess);
        return baseResp;
    }

    /**
     * 综合准确率列表查询
     *
     * <AUTHOR>
     */
    @RequestMapping(value = "/compositeAccuracy/list", method = RequestMethod.GET)
    public BaseResp compositeAccuracyList(String year, String caliberId, Boolean valid) {
        List<EquationDTO> allEquation = this.settingCompositeAccuracyService.findAllEquation(year, caliberId, valid);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(allEquation);
        return baseResp;
    }

    /**
     * 综合准确率设置-校验综合准确率名称是否可用；  true 可用； false 不可用
     *
     * <AUTHOR>
     */
    @RequestMapping(value = "/compositeAccuracy/check", method = RequestMethod.GET)
    public BaseResp compositeAccuracyCheck(String year, String caliberId, String accuracyName) {
        Boolean aBoolean = settingCompositeAccuracyService.checkNameAvailable(year, caliberId, accuracyName);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(aBoolean);
        return baseResp;
    }

    /**
     * 综合准确率设置-保存考核点数据
     *
     * <AUTHOR>
     */
    @RequestMapping(value = "/compositeAccuracy/save", method = RequestMethod.POST)
    public BaseResp compositeAccuracySave(@RequestBody List<EquationDTO> dataList) {
        settingCompositeAccuracyService.doSaveOrUpdateAccuracySetting(dataList);
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }

    /**
     * 综合准确率设置-删除考核点
     *
     * <AUTHOR>
     */
    @RequestMapping(value = "/compositeAccuracy/delete", method = RequestMethod.DELETE)
    public BaseResp deleteCompositeAccuracy(String year, String caliberId, String accuracyName) {
        settingCompositeAccuracyService.doDeleteByAccuracyName(year, caliberId, accuracyName);
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }

}
