package com.tsintergy.lf.web.base.weather.request;


import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

/**
 * 气象的历史和预报数据
 **/
public class WeatherHisFcRequest {

    /**
     * 气象类型
     */
    @ApiModelProperty(value = "气象类型",example="4")
    private Integer type;

    /**
     * cityId
     */
    @ApiModelProperty(value = "城市id")
    private String cityId;

    /**
     * date
     */

    @ApiModelProperty(value = "日期")
    private Date date;

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
