package com.tsintergy.lf.web.base.forecast.request;

import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * @date: 2/27/18 2:26 PM
 * @author: angel
 **/
public class ForecastRequest {
    @ApiModelProperty(value = "城市id")
    private String cityId;
    @ApiModelProperty(value = "口径id")
    private String caliberId;

    @ApiModelProperty(value = "算法id")
    private String algorithmId;

    @ApiModelProperty(value = "相似日")
    private Date similarDay;

    @ApiModelProperty(value = "目标日")
    private Date targetDay;

    @ApiModelProperty(value = "开始日")
    private Date startDate;

    @ApiModelProperty(value = "结束日")
    private Date endDate;

    public String getAlgorithmId() {
        return algorithmId;
    }

    public void setAlgorithmId(String algorithmId) {
        this.algorithmId = algorithmId;
    }

    public Date getSimilarDay() {
        return similarDay;
    }

    public void setSimilarDay(Date similarDay) {
        this.similarDay = similarDay;
    }

    public Date getTargetDay() {
        return targetDay;
    }

    public void setTargetDay(Date targetDay) {
        this.targetDay = targetDay;
    }

    public String getCaliberId() {
        return caliberId;
    }

    public void setCaliberId(String caliberId) {
        this.caliberId = caliberId;
    }

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }
}
