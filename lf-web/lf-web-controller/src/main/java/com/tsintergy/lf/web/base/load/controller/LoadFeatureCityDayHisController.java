package com.tsintergy.lf.web.base.load.controller;

import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.web.base.load.request.LoadAnalyseRequest;
import com.tsintergy.lf.web.base.load.response.FeatureAnalyseResp;
import com.tsintergy.lf.web.base.load.response.FeatureStatisticsResp;
import com.tsintergy.lf.web.base.load.response.LoadWeatherHisResp;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


/**
 * 日负荷特性
 *
 * @author: wangh
 **/
@RequestMapping("/character/day")
@RestController
@Api(tags = "城市特征日历史控制器")
public class LoadFeatureCityDayHisController extends BaseLoadFeatureController {

    private Logger logger = LogManager.getLogger(LoadFeatureCityDayHisController.class);

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private LoadFeatureCityDayHisService loadFeatureCityDayHisService;

    @Autowired
    private WeatherCityHisService weatherCityHisService;


    @Autowired
    private WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;


    @Autowired
    CityService cityService;


    /**
     * 日处理变化趋势图
     */
    @RequestMapping(value = "/power_K", method = RequestMethod.POST)
    @ApiOperation("获取日负荷图表")
    public BaseResp<List<FeatureAnalyseResp>> getDayLoadKChart(@RequestBody LoadAnalyseRequest loadAnalyseRequest) throws Exception {
        this.checkParam(loadAnalyseRequest);
        String caliberId = getCaliberId();
        String cityId = loadAnalyseRequest.getCityId();
        String weatherCityId = cityService.findWeatherCityId(cityId);
        Date startDate = DateUtils
                .string2Date(loadAnalyseRequest.getStartDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR);
        Date endDate = DateUtils.string2Date(loadAnalyseRequest.getEndDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR);
        //日特性
        List<LoadFeatureCityDayHisDO> powerFeatureCityDayHisStatDOS = loadFeatureCityDayHisService
                .findLoadFeatureCityDayHisDOS(cityId, startDate, endDate, caliberId);
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisStatDOS = weatherFeatureCityDayHisService
                .listWeatherFeatureCityDayHisDO(weatherCityId, startDate, endDate);
        if (CollectionUtils.isEmpty(powerFeatureCityDayHisStatDOS)) {
            throw TsieExceptionUtils.newBusinessException("T706");
        }
        //将气象的日期和降雨量放到map中
        Map<String, BigDecimal> rainfallMap = new HashMap<>();
        //将气象的日期和最高温放到map中
        Map<String, BigDecimal> highestTemperatureMap = new HashMap<>();
        if (weatherFeatureCityDayHisStatDOS != null && weatherFeatureCityDayHisStatDOS.size() > 0) {
            Map<Date, BigDecimal> weatherFeature = weatherFeatureCityDayHisStatDOS.stream().collect(
                    Collectors.toMap(WeatherFeatureCityDayHisDO::getDate, weather -> weather.getRainfall() == null ? BigDecimal.ZERO : weather.getRainfall()));
            weatherFeature.forEach((k, v) -> {
                rainfallMap.put(DateUtils.date2String(k, DateFormatType.DATE_FORMAT_STR), v);
            });
            Map<Date, BigDecimal> highestTemperatureDateMap = weatherFeatureCityDayHisStatDOS.stream().collect(
                    Collectors.toMap(WeatherFeatureCityDayHisDO::getDate, weatherFeatureCityDayHisDO -> weatherFeatureCityDayHisDO.getHighestTemperature()== null ? new BigDecimal(0):weatherFeatureCityDayHisDO.getHighestTemperature()));
            highestTemperatureDateMap.forEach((k, v) -> {
                highestTemperatureMap.put(DateUtils.date2String(k, DateFormatType.DATE_FORMAT_STR), v);
            });
        }

        Map<Date, List<LoadFeatureCityDayHisDO>> mapByDate = powerFeatureCityDayHisStatDOS.stream()
                .collect(Collectors.groupingBy(dp -> new Date(dp.getDate().getTime())));

        //构建vo
        List<FeatureAnalyseResp> resultList = new ArrayList<>();
        if (powerFeatureCityDayHisStatDOS != null && powerFeatureCityDayHisStatDOS.size() > 0) {
            powerFeatureCityDayHisStatDOS.forEach(powerFeatureCityDayHisStatDO -> {
                FeatureAnalyseResp resp = new FeatureAnalyseResp();
                BeanUtils.copyProperties(powerFeatureCityDayHisStatDO, resp);
                //获取前三天的出力数据
                List<LoadFeatureCityDayHisDO> firstData = mapByDate.get(resp.getDate());
                List<LoadFeatureCityDayHisDO> secondData = mapByDate.get(DateUtil.getMoveDay(resp.getDate(), -1));
                List<LoadFeatureCityDayHisDO> thirdData = mapByDate.get(DateUtil.getMoveDay(resp.getDate(), -2));
                if (firstData != null && secondData != null && thirdData != null) {
                    BigDecimal add = BigDecimalUtils
                            .add(firstData.get(0).getAveLoad(), secondData.get(0).getAveLoad());
                    BigDecimal add1 = BigDecimalUtils
                            .add(add, thirdData.get(0).getAveLoad());
                    BigDecimal divide = BigDecimalUtils
                            .divide(add1, new BigDecimal(3), 4);
                    resp.setAvgPeriod(divide);
                }
                resp.setAvgLoad(powerFeatureCityDayHisStatDO.getAveLoad());
                resp.setRain(rainfallMap.get(
                        DateUtils.date2String(powerFeatureCityDayHisStatDO.getDate(), DateFormatType.DATE_FORMAT_STR)));
                resp.setHighestTemperature(highestTemperatureMap.get(
                        DateUtils.date2String(powerFeatureCityDayHisStatDO.getDate(), DateFormatType.DATE_FORMAT_STR)));

                resultList.add(resp);
            });
        }

        if (resultList.size() < 1) {
            throw TsieExceptionUtils.newBusinessException("T706");
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(resultList);
        return baseResp;
    }

    /**
     * 特性分析-日特性统计查询
     */
    @ApiOperation("日特性统计查询")
    @RequestMapping(value = "/statisticsFeatureCity", method = RequestMethod.POST)
    public BaseResp<FeatureStatisticsResp> statisticsFeatureCity(@RequestBody LoadAnalyseRequest loadAnalyseRequest) throws Exception {
        try {
            checkParam(loadAnalyseRequest);
            String caliberId = getCaliberId();
            Date date = DateUtils.string2Date(loadAnalyseRequest.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR);
            Date lastYear = DateUtils.addYears(date, -1);
            //日特性
            List<LoadFeatureCityDayHisDO> powerFeatureCityDayHisStatDOS = loadFeatureCityDayHisService
                    .findLoadFeatureCityDayHisDOS(loadAnalyseRequest.getCityId(), date,
                            date,
                            caliberId);
            List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisStatDOS = weatherFeatureCityDayHisService
                    .listWeatherFeatureCityDayHisDO(loadAnalyseRequest.getCityId(), date,
                            date);
            List<LoadFeatureCityDayHisDO> lastYearPowerFeatureCityDayHisStatDOS = loadFeatureCityDayHisService
                    .findLoadFeatureCityDayHisDOS(loadAnalyseRequest.getCityId(), lastYear, lastYear, caliberId);
            List<WeatherFeatureCityDayHisDO> lastYearWeatherFeatureCityDayHisStatDOS = weatherFeatureCityDayHisService
                    .listWeatherFeatureCityDayHisDO(loadAnalyseRequest.getCityId(), lastYear, lastYear);

            //最大、最小、平均出力
            Map<String, BigDecimal> maxMinAvgPower = this.getMaxMinAvgPower(powerFeatureCityDayHisStatDOS);
            Map<String, BigDecimal> lastYearMaxMinAvgPower = this
                    .getMaxMinAvgPower(lastYearPowerFeatureCityDayHisStatDOS);

            //日均负荷率
            BigDecimal avgloadGradient = this.getAvgLoadGradient(powerFeatureCityDayHisStatDOS);
            BigDecimal lastYearAvgloadGradient = this.getAvgLoadGradient(lastYearPowerFeatureCityDayHisStatDOS);
//
//            //累计发电量
//            BigDecimal totalEnergy = this.getTotalEnergy(powerFeatureCityDayHisStatDOS);
//            BigDecimal lastYearTotalEnergy = this.getTotalEnergy(lastYearPowerFeatureCityDayHisStatDOS);

            //累计降雨量
/*            BigDecimal totalRain = this.getTotalRain(weatherFeatureCityDayHisStatDOS);
            BigDecimal lastYearTotalRain = this.getTotalRain(lastYearWeatherFeatureCityDayHisStatDOS);*/

            //最高温度
//            BigDecimal highestTemperature = getHisHighestTemperature(weatherFeatureCityDayHisStatDOS);
//            BigDecimal hisHighestTemperature = getHisHighestTemperature(lastYearWeatherFeatureCityDayHisStatDOS);


            //创建视图vo
            FeatureStatisticsResp featureStatisticsVO = new FeatureStatisticsResp();
            if (maxMinAvgPower != null) {
                featureStatisticsVO.setMaxEnergy(maxMinAvgPower.get("max"));
                featureStatisticsVO.setMinEnergy(maxMinAvgPower.get("min"));
                featureStatisticsVO.setAvgEnergy(maxMinAvgPower.get("avg"));
                featureStatisticsVO.setDifferent(maxMinAvgPower.get("different"));
                featureStatisticsVO.setGradient(maxMinAvgPower.get("gradient"));
            }
            if (lastYearMaxMinAvgPower != null) {
                featureStatisticsVO.setHisMaxEnergy(lastYearMaxMinAvgPower.get("max"));
                featureStatisticsVO.setHisMinEnergy(lastYearMaxMinAvgPower.get("min"));
                featureStatisticsVO.setHisAvgEnergy(lastYearMaxMinAvgPower.get("avg"));
                featureStatisticsVO.setHisDifferent(lastYearMaxMinAvgPower.get("different"));
                featureStatisticsVO.setHisGradient(lastYearMaxMinAvgPower.get("gradient"));
            }
            featureStatisticsVO.setAvgloadGradient(avgloadGradient);
            featureStatisticsVO.setHisAvgLoadGradient(lastYearAvgloadGradient);
       /*
            featureStatisticsVO.setTotalEnergy(totalEnergy);
            featureStatisticsVO.setHisTotalEnergy(lastYearTotalEnergy);
          featureStatisticsVO.setTotalRain(totalRain);
            featureStatisticsVO.setHisTotalRain(lastYearTotalRain);
            featureStatisticsVO.setHighestTemperature(highestTemperature);
            featureStatisticsVO.setHisHighestTemperature(hisHighestTemperature);*/
            BaseResp baseResp = BaseResp.succResp();
            baseResp.setData(featureStatisticsVO);
            return baseResp;
        } catch (Exception e) {
            logger.error("特性统计异常...", e);
            throw e;
        }
    }

    private BigDecimal getHisHighestTemperature(List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisStatDOS) {
        List<BigDecimal> highestTemperature = weatherFeatureCityDayHisStatDOS.stream().map(WeatherFeatureCityDayHisDO::getHighestTemperature).collect(Collectors.toList());
        return CollectionUtils.isEmpty(highestTemperature) ? null : highestTemperature.stream().max(Comparator.comparing(
                Function.identity())).get();
    }

    /**
     * 特性分析-日历史曲线
     */
    @ApiOperation("日历史曲线")
    @RequestMapping(value = "/hisPowerAndTemperature", method = RequestMethod.POST)
    public BaseResp<LoadWeatherHisResp> getHisPowerAndRain(@RequestBody LoadAnalyseRequest request) throws Exception {
        this.checkParam(request);
        Date startDate = DateUtils.string2Date(request.getStartDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR);
        Date endDate = DateUtils.string2Date(request.getEndDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR);
        List<BigDecimal> powerList = loadCityHisService
                .findLoadCityHisDO(request.getCityId(), startDate, endDate, super.getCaliberId());
        List<BigDecimal> weatherList = weatherCityHisService
                .findWeatherCityHisValueList(request.getCityId(), startDate,
                        endDate, WeatherEnum.TEMPERATURE.getType());
        LoadWeatherHisResp powerWeatherHisVO = new LoadWeatherHisResp();
        powerWeatherHisVO.setPower(powerList);
        powerWeatherHisVO.setTemperature(weatherList);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(powerWeatherHisVO);
        return baseResp;
    }


    private BigDecimal getAvgLoadGradient(List<LoadFeatureCityDayHisDO> loadList) {
        if (CollectionUtils.isNotEmpty(loadList)) {
            return loadList.stream().map(LoadFeatureCityDayHisDO::getLoadGradient)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .divide(new BigDecimal(loadList.size()), 4, BigDecimal.ROUND_HALF_UP);
        }
        return null;
    }

    private BigDecimal getTotalEnergy(List<LoadFeatureCityDayHisDO> loadList) {
        if (CollectionUtils.isNotEmpty(loadList)) {
            return loadList.stream().map(LoadFeatureCityDayHisDO::getEnergy)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        return null;
    }

    private BigDecimal getTotalRain(List<WeatherFeatureCityDayHisDO> weatherList) {
        if (CollectionUtils.isNotEmpty(weatherList)) {
            return weatherList.stream().map(WeatherFeatureCityDayHisDO::getRainfall)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        return null;
    }


}
