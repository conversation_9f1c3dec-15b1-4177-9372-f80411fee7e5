/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  wa<PERSON><PERSON>
 * Date:  2019/6/24 7:51
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.typhoon.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.enums.FeatureRowEnum;
import com.tsintergy.lf.core.enums.LoadFeatureEnum;
import com.tsintergy.lf.core.enums.WeatherColumnEnum;
import com.tsintergy.lf.core.enums.WeatherFeatureEnum;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.typhoon.api.ExtremeWeatherService;
import com.tsintergy.lf.serviceapi.base.typhoon.dto.MaxMinCorrelationDTO;
import com.tsintergy.lf.serviceapi.base.typhoon.dto.TyphoonCorrelationDTO;
import com.tsintergy.lf.serviceapi.base.typhoon.dto.TyphoonScatterDTO;
import com.tsintergy.lf.serviceapi.base.typhoon.dto.TyphoonScatterRequestv2DTO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import com.tsintergy.lf.web.base.typhoon.request.TyphoonScatterRequest;
import com.tsintergy.lf.web.base.typhoon.request.TyphoonScatterRequestv2;
import com.tsintergy.lf.web.base.typhoon.response.CorrelationDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

;

/**
 * Description: 极端天气分析页面 <br>
 *
 * <AUTHOR> @create 2019/6/24
 * @since 1.0.0
 */
@RequestMapping("/typhoonAnalysis")
@RestController
@Api(value = "极端天气控制器" ,tags = "极端天气控制器")
public class ExtremeWeatherController extends CommonBaseController {

    private final Logger logger = LogManager.getLogger(ExtremeWeatherController.class);

    @Autowired
    private ExtremeWeatherService extremeWeatherService;

    @Autowired
    private WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    private HolidayService holidayService;

    @Autowired
    private CityService cityService;


    @RequestMapping(value = "/correlation", method = RequestMethod.POST)
    @ApiOperation("相关系数分析")
    public BaseResp<CorrelationDTO> getTyphoonCorrelationV2(@RequestBody TyphoonScatterRequestv2 typhoonScatterRequestv2) throws Exception {
        //省份的判断,如果不写，默认查询广东省
        if (StringUtils.isEmpty(typhoonScatterRequestv2.getCityId())) {
            typhoonScatterRequestv2.setCityId(this.getLoginCityId());
        }
        BaseResp resp = BaseResp.succResp();
        TyphoonScatterRequestv2DTO dto = new TyphoonScatterRequestv2DTO();
        BeanUtils.copyProperties(typhoonScatterRequestv2, dto);
        String weatherCityId = cityService.findWeatherCityId(typhoonScatterRequestv2.getCityId());
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisVOS = weatherFeatureCityDayHisService.findWeatherFeatureBySearchData(weatherCityId, typhoonScatterRequestv2.getStartDate(), typhoonScatterRequestv2.getEndDate(), typhoonScatterRequestv2.getSearchDTOS());
        if(CollectionUtils.isEmpty(weatherFeatureCityDayHisVOS)){
            resp.setData(null);
            resp.setRetCode("T706");
            resp.setRetMsg("查询气象特性数据为空");
            return resp;
        }
        List<Date> dateList = weatherFeatureCityDayHisVOS.stream().map(WeatherFeatureCityDayHisDO::getDate).collect(Collectors.toList());
        List<Integer> dayTypes = typhoonScatterRequestv2.getDayType();
        List<Date> dates = getDate(dateList, dayTypes);
        List<TyphoonCorrelationDTO> result = extremeWeatherService.getTyphoonCorrelation(dates, typhoonScatterRequestv2.getCityId(), typhoonScatterRequestv2.getCaliberId());
        List<Integer> columns = typhoonScatterRequestv2.getColumns();
        List<Integer> rows = typhoonScatterRequestv2.getRows();

        result = result.stream().filter(t -> columns.contains(t.getY()) && rows.contains(t.getX())).collect(Collectors.toList());
        CorrelationDTO correlationDTO = new CorrelationDTO();
        correlationDTO.setResult(result);
        result.sort(Comparator.comparing(TyphoonCorrelationDTO::getV, Comparator.nullsFirst(Comparator.reverseOrder())));
        MaxMinCorrelationDTO maxMinCorrelationDTO = new MaxMinCorrelationDTO();
        if(CollectionUtils.isNotEmpty(result)){
            TyphoonCorrelationDTO typhoonCorrelationDTO = result.get(0);
            maxMinCorrelationDTO.setPositiveMax(LoadFeatureEnum.findNameByType(typhoonCorrelationDTO.getX()) + "-" + WeatherFeatureEnum.findNameByType(typhoonCorrelationDTO.getY()));
            maxMinCorrelationDTO.setMaxCoefficient(typhoonCorrelationDTO.getV());
        }

        //最大负相关
        List<TyphoonCorrelationDTO> maxMIn = result.stream().filter(t -> t.getV().compareTo(new BigDecimal(0)) <0).collect(Collectors.toList());
        maxMIn.sort(Comparator.comparing(TyphoonCorrelationDTO::getV, Comparator.nullsFirst(Comparator.naturalOrder())));
        if(CollectionUtils.isNotEmpty(maxMIn)){
            TyphoonCorrelationDTO correlationDTO1 = maxMIn.get(0);
            maxMinCorrelationDTO.setPositiveMinus(LoadFeatureEnum.findNameByType(correlationDTO1.getX()) + "-" + WeatherFeatureEnum.findNameByType(correlationDTO1.getY()));
            maxMinCorrelationDTO.setMinCoefficient(correlationDTO1.getV());
        }
        correlationDTO.setCorrelationDTO(maxMinCorrelationDTO);
        resp.setData(correlationDTO);
        return resp;
    }

    @ApiOperation("气象散点分析")
    @RequestMapping(value = "/weatherScatter", method = RequestMethod.POST)
    public BaseResp<List<TyphoonScatterDTO>> getTyphoonWeatherScatter(@RequestBody TyphoonScatterRequestv2 typhoonScatterRequestv3) throws Exception {
        TyphoonScatterRequest scatterRequest = new TyphoonScatterRequest();
        if (typhoonScatterRequestv3.getColumns().size() == 0) {
            //默认maxLoad
            scatterRequest.setColumnName(WeatherColumnEnum.HIGHESTTEMPERATURE.getValue());
        } else {
            scatterRequest.setColumnName(WeatherColumnEnum.getValueById(typhoonScatterRequestv3.getColumns().get(0)));
        }
        if (typhoonScatterRequestv3.getRows().size() == 0) {
            //默认默认highestTemperature
            scatterRequest.setRowName(FeatureRowEnum.MAXLOAD.getValue());
        } else {
            scatterRequest.setRowName(FeatureRowEnum.getValueById(typhoonScatterRequestv3.getRows().get(0)));
        }
        if (StringUtils.isBlank(typhoonScatterRequestv3.getCityId())) {
            scatterRequest.setCityId(this.getLoginCityId());
        } else {
            scatterRequest.setCityId(typhoonScatterRequestv3.getCityId());
        }
        if (StringUtils.isEmpty(typhoonScatterRequestv3.getCaliberId())) {
            scatterRequest.setCaliberId(this.getCaliberId());
        } else {
            scatterRequest.setCaliberId(typhoonScatterRequestv3.getCaliberId());
        }
        TyphoonScatterRequestv2DTO dto = new TyphoonScatterRequestv2DTO();
        BeanUtils.copyProperties(typhoonScatterRequestv3, dto);
        String weatherCityId = cityService.findWeatherCityId(typhoonScatterRequestv3.getCityId());
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisVOS = weatherFeatureCityDayHisService.findWeatherFeatureBySearchData(weatherCityId, typhoonScatterRequestv3.getStartDate(), typhoonScatterRequestv3.getEndDate(), typhoonScatterRequestv3.getSearchDTOS());
        List<Integer> dayTypes = typhoonScatterRequestv3.getDayType();
        List<Date> dateList = weatherFeatureCityDayHisVOS.stream().map(WeatherFeatureCityDayHisDO::getDate).collect(Collectors.toList());
        List<Date> dates = getDate(dateList, dayTypes);
        List<TyphoonScatterDTO> results = extremeWeatherService.getTyphoonWeatherScatter(dates,
                scatterRequest.getRowName(),
                scatterRequest.getColumnName(),
                scatterRequest.getCityId(),
                scatterRequest.getCaliberId());
        BaseResp resp = BaseResp.succResp();
        resp.setData(results);
        return resp;
    }


    private List<Date> getDate(List<Date> dateList, List<Integer> dayTypes) throws Exception {
        List<Date> holidayS = holidayService.getAllHolidays();
        List<Date> normal = new ArrayList<>();
        List<Date> weekend = new ArrayList<>();
        List<Date> holiday = new ArrayList<>();
        for (Date date : dateList) {
            Boolean week = DateUtil.isWeekend(date);
            if (week) {
                weekend.add(date);
            } else {
                normal.add(date);
            }
            if (holidayS.contains(date)) {
                holiday.add(date);
            }
        }
        List<Date> analyze = new ArrayList<>();
        for (Integer type : dayTypes) {
            if (type == 1) {
                analyze.addAll(normal);
            } else if (type == 2) {
                analyze.addAll(weekend);
            } else if (type == 3) {
                analyze.addAll(holiday);
            }
        }
        return analyze;
    }
}