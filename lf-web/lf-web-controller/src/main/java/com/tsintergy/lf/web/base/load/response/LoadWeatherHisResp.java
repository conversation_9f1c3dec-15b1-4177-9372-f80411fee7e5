/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangchen Date:  2019/5/17 18:36 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.load.response;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 气象/处理对象
 *
 * <AUTHOR>
 * @create 2020/4/15
 * @since 1.0.0
 */
@ApiModel
public class LoadWeatherHisResp implements Serializable {
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "负荷集")
    List<BigDecimal> power;

    @ApiModelProperty(value = "温度集")
    List<BigDecimal> temperature;

    public List<BigDecimal> getPower() {
        return power;
    }

    public void setPower(List<BigDecimal> power) {
        this.power = power;
    }

    public List<BigDecimal> getTemperature() {
        return temperature;
    }

    public void setTemperature(List<BigDecimal> temperature) {
        this.temperature = temperature;
    }
}
