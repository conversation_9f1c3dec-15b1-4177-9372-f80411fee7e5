package com.tsintergy.lf.web.base.forecast.controller;


import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.api.ManualForecastService;
import com.tsintergy.lf.serviceapi.base.forecast.bean.DuplicateForecastException;
import com.tsintergy.lf.serviceapi.base.forecast.dto.LoadCityFcDTO;
import com.tsintergy.lf.serviceapi.base.forecast.dto.ManualAlgorithmDTO;
import com.tsintergy.lf.serviceapi.base.forecast.dto.ManualForecastCurveDTO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcLoadForecastService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcModifyService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayFcService;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherCityFcModifyDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherDeatilDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherFeatureDTO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayFcDO;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import com.tsintergy.lf.web.base.forecast.request.ExistRequest;
import com.tsintergy.lf.web.base.forecast.request.ManualAlgorihtmCurveRequest;
import com.tsintergy.lf.web.base.forecast.request.ManualRequest2;
import com.tsintergy.lf.web.base.forecast.request.ModifyWeatherRequest;
import com.tsintergy.lf.web.base.forecast.response.ReportResp;
import com.tsintergy.lf.web.base.forecast.response.WeatherFcResp;
import com.tsintergy.lf.web.base.forecast.response.WeatherFcTimeResp;
import com.tsintergy.lf.web.base.forecast.response.WeatherResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 手动预测模块
 *
 * <AUTHOR>
 * @date 2019-11-04
 */
@Api(tags = "手动预测模块")
@RequestMapping("/manual")
@RestController
public class ManualForecastController extends CommonBaseController {

    @Autowired
    private ManualForecastService manualForecastService;

    @Autowired
    private LoadCityFcService loadCityFcService;

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Autowired
    private WeatherCityFcService weatherCityFcService;

    @Autowired
    private ForecastManager forecastManager;

    @Autowired
    private WeatherCityFcModifyService weatherCityFcModifyService;

    @Autowired
    private CityService cityService;

    @Autowired
    private WeatherCityFcLoadForecastService weatherCityFcLoadForecastService;

    @Autowired
    private WeatherFeatureCityDayFcService weatherFeatureCityDayFcService;


    /**
     * 气象预测查询接口
     */
    @ApiOperation("气象预测查询接口")
    @RequestMapping(value = "/weather", method = RequestMethod.GET)
    public BaseResp<WeatherResp> manualWeather(Date startDate, Date endDate, Integer type) throws Exception {
        List<WeatherDTO> weatherHis = this.weatherCityHisService
            .findWeatherCityHisDTOs(super.getLoginCityId(), type, startDate, endDate);
        List<WeatherDTO> weatherFc = this.weatherCityFcService
            .findWeatherCityFcDTOs(super.getLoginCityId(), type, startDate, endDate);
        WeatherResp result = new WeatherResp();
        result.setFcWeather(weatherFc);
        result.setHisWeather(weatherHis);
        BaseResp<WeatherResp> resp = new BaseResp<>();
        resp.setData(result);
        return resp;
    }

    /**
     * 判断上报数据是否存在
     *
     * <AUTHOR>
     */
    @ApiOperation("判断上报数据是否存在")
    @RequestMapping(value = "/exist/fc", method = RequestMethod.POST)
    public BaseResp<List<LoadCityFcDO>> manualExistData(@RequestBody ExistRequest request) throws Exception {
        List<LoadCityFcDO> result = new ArrayList<>();
        for (LoadCityFcDTO vo : request.getLoadCityFcDTOS()) {
            List<LoadCityFcDO> loadCityFc = this.loadCityFcService
                .listReportLoadCityFc(super.getLoginCityId(), null, vo.getDate(), vo.getDate(), vo.getAlgorithmId());
            result.addAll(loadCityFc);
        }
        BaseResp<List<LoadCityFcDO>> resp = new BaseResp<>();
        if (result.size() == 0) {
            return new BaseResp<>("T706");
        }
        resp.setData(result);
        return resp;
    }

    /**
     * 批量上报接口
     *
     * <AUTHOR>
     */
    @ApiOperation("批量上报接口")
    @RequestMapping(value = "/report", method = RequestMethod.POST)
    public BaseResp report(@RequestBody ExistRequest request) throws Exception {
        for (LoadCityFcDTO DTO : request.getLoadCityFcDTOS()) {
            LoadCityFcDO fcVO = new LoadCityFcDO();
            BeanUtils.copyProperties(DTO, fcVO);
            fcVO.setReport(true);
            fcVO.setReportTime(new Timestamp(System.currentTimeMillis()));
            fcVO.setCityId(super.getLoginCityId());
            fcVO.setRecommend(false);
            fcVO.setCaliberId("1");
            BasePeriodUtils.setAllFiled(fcVO,
                ColumnUtil.listToMap(DTO.getData(), Constants.LOAD_CURVE_START_WITH_ZERO));
            LoadCityFcDO exist = loadCityFcService
                .getLoadCityFc(super.getLoginCityId(), null, DTO.getDate(), DTO.getDate(), DTO.getAlgorithmId());
            if (exist == null) {
                this.loadCityFcService
                    .doSaveOrUpdateLoadCityFcDO96(fcVO);
            } else {
                this.loadCityFcService.doRemoveLoadCityFcDO(exist);
                this.loadCityFcService
                    .doSaveOrUpdateLoadCityFcDO96(fcVO);
            }
        }
        return BaseResp.succResp();
    }

    /**
     * 判断手动预测算法是否可以预测
     */
    @ApiOperation("判断手动预测算法是否可以预测")
    @RequestMapping(value = "/algorithm/isforecast", method = RequestMethod.GET)
    public BaseResp<Integer> isForecast() {
        boolean isForecast = ForecastManager.isForecast(super.getLoginUserId());
        BaseResp baseResp = BaseResp.succResp();
        if (isForecast) {
            baseResp.setData(1);
        } else {
            baseResp.setData(0);
        }
        return baseResp;
    }

    /**
     * 调用预测算法
     */
    @ApiOperation("调用预测算法")
    @RequestMapping(value = "/algorithm/forecast", method = RequestMethod.POST)
    public BaseResp<Boolean> forecast(@RequestBody ManualRequest2 manualRequest2) {
        List<String> algorithmIds = manualRequest2.getAlgorithmIds();
        List<AlgorithmEnum> algorithmEnums = new ArrayList<>();
        for (AlgorithmEnum algorithmEnum : AlgorithmEnum.values()) {
            if (algorithmIds.contains(algorithmEnum.getId())) {
                algorithmEnums.add(algorithmEnum);
            }
        }


        BaseResp baseResp = BaseResp.succResp();
        try {
            forecastManager.forecast(super.getLoginUserId(), super.getLoginCityId(), super.getCaliberId(),
                manualRequest2.getTargetDate(), manualRequest2.getTargetDate(), algorithmEnums,
                manualRequest2.getWeatherCode());
            baseResp.setData(true);
        } catch (DuplicateForecastException e) {
            baseResp.setData(false);
        } catch (Exception e) {
            baseResp = BaseResp.failResp("调用预测算法失败...");
        }
        return baseResp;
    }


    /**
     * 手动预测提供算法
     */
    @ApiOperation("手动预测提供算法")
    @RequestMapping(value = "/algorithm/name", method = RequestMethod.GET)
    public BaseResp<List<ManualAlgorithmDTO>> algorithmName() throws Exception{
        List<ManualAlgorithmDTO> algorithmRespList = manualForecastService.findForecastAlgorithm(this.getLoginCityId());
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(algorithmRespList);
        return baseResp;
    }

    /**
     * 查询算法预测曲线
     */
    @ApiOperation("查询算法预测曲线")
    @RequestMapping(value = "/algorithm/curve", method = RequestMethod.POST)
    public BaseResp<List<ManualForecastCurveDTO>> algorithmCurve(@RequestBody ManualAlgorihtmCurveRequest manualAlgorihtmCurveRequest)
        throws Exception {
        List<ManualForecastCurveDTO> manualForecastCurveDTOS = manualForecastService
            .forecastCurve(manualAlgorihtmCurveRequest.getDate(), super.getLoginCityId(), super.getCaliberId(),
                manualAlgorihtmCurveRequest.getIds());
        if (CollectionUtils.isEmpty(manualForecastCurveDTOS)) {
            throw TsieExceptionUtils.newBusinessException("T706");
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(manualForecastCurveDTOS);
        return baseResp;
    }

    /**
     * 气象曲线展示
     */
    @ApiOperation("气象曲线展示")
    @RequestMapping(value = "/weather/curve", method = RequestMethod.GET)
    public BaseResp<WeatherDeatilDTO> weatherCurve(Date date) throws Exception {
        WeatherDeatilDTO weatherDeatilDTO = weatherCityFcService.findWeatherDeatilDTO(date, super.getLoginCityId());
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(weatherDeatilDTO);
        return baseResp;
    }


    /**
     * 查看上报状态
     */
    @ApiOperation("查看上报状态")
    @RequestMapping(value = "/report/status", method = RequestMethod.GET)
    public BaseResp<ReportResp> manualReport(Date date) throws Exception {
        LoadCityFcDO report = loadCityFcService.getReport(super.getLoginCityId(), super.getCaliberId(), date);
        BaseResp baseResp = BaseResp.succResp();
        ReportResp reportResp = new ReportResp(false, null);
        if (report != null) {
            reportResp = new ReportResp(true, report.getReportTime());
        }
        baseResp.setData(reportResp);
        return baseResp;
    }


    /**
     * 功能描述: 保存修正气象<br> 〈〉
     *
     * @return:com.tsie.core.common.base.BaseResp
     * @since: 1.0.0
     * @Author:yangjin
     * @Date: 2020/6/16 14:51
     */

    @ApiOperation("保存修正气象")
    @RequestMapping(value = "/weather/modify", method = RequestMethod.POST)
    public BaseResp modifyWeather(@RequestBody ModifyWeatherRequest modifyWeatherRequest) throws Exception {

        BaseResp baseResp = BaseResp.succResp();
        if (StringUtils.isEmpty(modifyWeatherRequest.getType()) || CollectionUtils
            .isEmpty(modifyWeatherRequest.getValues())
            || StringUtils.isEmpty(modifyWeatherRequest.getDate())) {
            baseResp.setRetMsg("参数不能为空");
        }
        WeatherCityFcModifyDTO modifyDTO = new WeatherCityFcModifyDTO();
        BeanUtils.copyProperties(modifyWeatherRequest, modifyDTO);
        String weatherCityId = cityService.findWeatherCityId(this.getLoginCityId());
        modifyDTO.setCityId(weatherCityId);
        weatherCityFcModifyService.doInsertOrUpdateData(modifyDTO);
        return baseResp;
    }



    @RequestMapping(value = "/weather/time", method = RequestMethod.POST)
    public BaseResp<WeatherFcTimeResp> manualWeatherFcTime(@RequestBody ManualRequest2 manualRequest2) throws Exception {
        WeatherFcTimeResp weatherFcTimeResp = new WeatherFcTimeResp();
        BaseResp<WeatherFcTimeResp> resp = BaseResp.succResp();
        String caliberId = this.getCaliberId();
        List<String> algorithmIds = manualRequest2.getAlgorithmIds();
        if(CollectionUtils.isEmpty(algorithmIds)){
            algorithmIds = new ArrayList<String>();
            for (AlgorithmEnum algorithmEnum : AlgorithmEnum.values()) {
                if(!StringUtils.equals("3",algorithmEnum.getId())){
                    algorithmIds.add(algorithmEnum.getId());
                }
            }
        }
        resp.setData(weatherFcTimeResp);

        List<WeatherCityFcDO> weatherCityFcDOs = weatherCityFcService.findWeatherCityFcDOs(manualRequest2.getCityId(), null, manualRequest2.getDate(), manualRequest2.getDate());
        //取最新气象预测时间，如果updatetime不为null则取最新更新时间，为null则取创建时间
        if(!CollectionUtils.isEmpty(weatherCityFcDOs)){
            if(null != weatherCityFcDOs.get(0).getUpdatetime()){
                weatherFcTimeResp.setNewWeatherFcTime(
                    DateUtil.getStrDate(weatherCityFcDOs.get(0).getUpdatetime(),DateUtil.DATE_FORMAT1));
            }else{
                weatherFcTimeResp.setNewWeatherFcTime(DateUtil.getStrDate(weatherCityFcDOs.get(0).getCreatetime(),DateUtil.DATE_FORMAT1));
            }
        }
        //取算法预测所用气象时间
        Date weatherFcTime = weatherCityFcLoadForecastService.findWeatherFcTime(manualRequest2.getCityId(), null,  algorithmIds, manualRequest2.getDate(),caliberId);
        if(weatherFcTime == null){
            return resp;
        }
        weatherFcTimeResp.setAlgorithmWeatherFcTime(DateUtil.getStrDate(weatherFcTime,DateUtil.DATE_FORMAT1));
        return resp;
    }


    @RequestMapping(value = "/weatherFc", method = RequestMethod.POST)
    public BaseResp<WeatherFcResp> manualWeatherFc(@RequestBody ManualRequest2 manualRequest2) throws Exception {
        WeatherFcResp weatherFcResp = new WeatherFcResp();
        BaseResp<WeatherFcResp> resp = BaseResp.succResp();
        String caliberId = this.getCaliberId();
        String cityId = manualRequest2.getCityId();
        Date date =  manualRequest2.getTargetDate();
        List<String> algorithmIds = manualRequest2.getAlgorithmIds();
        if(CollectionUtils.isEmpty(algorithmIds)){
            for (AlgorithmEnum algorithmEnum : AlgorithmEnum.values()) {
                if(!StringUtils.equals("3",algorithmEnum.getId())){
                    algorithmIds.add(algorithmEnum.getId());
                }
            }
        }
        resp.setData(weatherFcResp);

        //取最新气象数据
        List<WeatherCityFcDO> weatherCityFcDOs = weatherCityFcService.findWeatherCityFcDOs(cityId, null, date, date);
        //取算法预测所用气象数据
        List<WeatherDTO> weatherFc = weatherCityFcLoadForecastService.findWeatherFc(cityId, null, manualRequest2.getAlgorithmIds(), date,caliberId );

        List<WeatherDTO> weatherCityFcDOlist = new ArrayList<>();
        if(!CollectionUtils.isEmpty(weatherCityFcDOs)){
            for(WeatherCityFcDO weatherCityFcDO : weatherCityFcDOs){
                WeatherDTO weatherDTO = new WeatherDTO();
                weatherDTO.setCity(weatherCityFcDO.getCityId());
                weatherDTO.setType(weatherCityFcDO.getType());
                weatherDTO.setDate(weatherCityFcDO.getDate());
                weatherDTO.setData(BasePeriodUtils
                    .toList(weatherCityFcDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
                weatherCityFcDOlist.add(weatherDTO);
            }
        }
        weatherFcResp.setAlgorithmWeatherFcList(weatherFc);
        weatherFcResp.setNewWeatherFcList(weatherCityFcDOlist);
        //取最新采集气象特征
        List<WeatherFeatureDTO> weatherFeatureList = new ArrayList<>();
        weatherFcResp.setWeatherFeature(weatherFeatureList);
        WeatherFeatureCityDayFcDO weatherFeatureCityDayFcDO = weatherFeatureCityDayFcService.findWeatherFeatureCityFcVOByDate(cityService.findWeatherCityId(cityId), date);
        if(null != weatherFeatureCityDayFcDO){
            WeatherFeatureDTO weatherFeatureDTO = new WeatherFeatureDTO("");
            weatherFeatureDTO.setAveHumidity(weatherFeatureCityDayFcDO.getAveHumidity());
            weatherFeatureDTO.setHighestTemperature(weatherFeatureCityDayFcDO.getHighestTemperature());
            weatherFeatureDTO.setLowestTemperature(weatherFeatureCityDayFcDO.getLowestTemperature());
            weatherFeatureDTO.setRainfall(weatherFeatureCityDayFcDO.getRainfall());
            weatherFeatureDTO.setWeatherName("最新采集");
            weatherFeatureList.add(weatherFeatureDTO);
        }
        weatherCityFcLoadForecastService.findWeatherFeature(cityService.findWeatherCityId(cityId), null, algorithmIds, date, weatherFeatureList,caliberId);

        return resp;
    }
}

