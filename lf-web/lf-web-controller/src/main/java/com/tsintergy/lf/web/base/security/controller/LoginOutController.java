/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司
 * Author:  wang<PERSON>
 * Date:  2020/11/11 10:54
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.security.controller;


import com.tsieframework.cloud.security.serviceapi.system.pojo.TsieUserVO;
import com.tsieframework.cloud.security.web.businesslog.annotation.OperateLog;
import com.tsieframework.cloud.security.web.common.security.TokenManager;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.web.base.controller.BaseController;
import com.tsintergy.lf.web.base.security.response.BusInfoResp;
import com.tsintergy.lf.web.base.security.response.LoginResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpSession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/11/11
 * @since 2.0.0
 */
@RestController
@RequestMapping({"/logincontroller"})
@Slf4j
@Api(tags = "退出Controller")
public class LoginOutController {

    @Autowired
    private SsoLoadForecastAccountManager ssoLoadForecastAccountManager;

    @Autowired
    protected TokenManager tokenManager;

    @Value("${tsie.cas.url.logout}")
    public String casServerLogoutUrl;

    @Value("${tsie.cas.server.frontViewUrl}")
    public  String frontViewUrl;

    @Value("${tsie.cas.server.restfulRedirectUrl}")
    public  String restfulRedirectUrl;

    @Value("${tsie.cas.server.lfChooseUrl}")
    public  String lfChooseUrl;

//    @Value("${cas.server.restfulLfChooseUrl}")
//    public  String restfulLfChooseUrl;

    @Value("${tsie.cas.server.buslfChooseUrl}")
    public  String buslfChooseUrl;

    @Value("${tsie.cas.server.redirectUrl}")
    public  String redirectUrl;


    @GetMapping({"/cas/logout"})
    @OperateLog(operate = "登出")
    @ApiOperation("登出")
    public BaseResp<String> casLogout(HttpSession session) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        try {
            TsieUserVO userVO = tokenManager.getLoginUser();
            ssoLoadForecastAccountManager.logout(userVO);
            session.invalidate();
        } catch (BusinessException e) {
            if (e.getErrorCode().equals("T0000")) {
                baseResp.setData(casServerLogoutUrl + "?service=" + restfulRedirectUrl);
            }
        }
        baseResp.setData(casServerLogoutUrl + "?service=" + restfulRedirectUrl);
        return baseResp;
    }

    @ApiOperation("获取设置信息")
    @GetMapping({"/getSystemInfo"})
    public BaseResp<LoginResp> getSystemInfo() {
        try {
            BaseResp<LoginResp> systemInfo = ssoLoadForecastAccountManager.getSystemInfo();
            return systemInfo;
        } catch (Exception e) {
            BaseResp baseResp = BaseResp.failResp();
            baseResp.setRetCode("T100");
            baseResp.setRetMsg("登录超时");
            baseResp.setData(restfulRedirectUrl);
            return baseResp;
        }
    }

    /**
     * 获取多个系统的访问信息
     *
     * @return
     * @throws Exception
     */
    @ApiOperation("获取多个系统的访问信息")
    @GetMapping({"/path"})
    public BaseResp<List<BusInfoResp>> getBusInfo() throws Exception {
        BaseResp resp = BaseResp.succResp();
        List<BusInfoResp> lfInfoResps=new ArrayList<>();
        BusInfoResp lfInfoResp = new BusInfoResp();
        lfInfoResp.setName("短期");
        lfInfoResp.setPath(lfChooseUrl);
        lfInfoResps.add(lfInfoResp);
        BusInfoResp busInfo = new BusInfoResp();
        busInfo.setName("母线");
        busInfo.setPath(buslfChooseUrl);
        lfInfoResps.add(busInfo);
        resp.setData(lfInfoResps);
        return resp;
    }


    /**
     * 重定向请求
     * @return
     */
    @ApiOperation("重定向请求")
    @RequestMapping(value = "/forward", method = RequestMethod.GET)
    public ModelAndView forward() throws Exception {
        return new ModelAndView(new RedirectView(redirectUrl));
    }

    private ModelAndView getModelAndView() throws Exception {
        BaseResp<LoginResp> systemInfo = ssoLoadForecastAccountManager.getSystemInfo();
        String defaultMenuPath = systemInfo.getData().getDefaultMenuPath();
        if (defaultMenuPath.endsWith("/")) {
            return new ModelAndView(new RedirectView(frontViewUrl + "#" + defaultMenuPath));
        } else {
            return new ModelAndView(new RedirectView(frontViewUrl + "/#" + defaultMenuPath));
        }
    }

    @ApiOperation("视图选择")
    @RequestMapping(value = "/choose", method = RequestMethod.GET)
    public ModelAndView choose() throws Exception {
        return new ModelAndView(new RedirectView(redirectUrl));
    }

    @ApiOperation(value = "重定向")
    @RequestMapping(value = "/redirect", method = RequestMethod.GET)
    public ModelAndView redirect() throws Exception {
        return getModelAndView();
    }
}