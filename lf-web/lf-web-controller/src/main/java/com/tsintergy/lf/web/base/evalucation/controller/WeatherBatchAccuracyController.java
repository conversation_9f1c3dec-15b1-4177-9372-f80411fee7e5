package com.tsintergy.lf.web.base.evalucation.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.AssessAccuracyDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/** @Description
 * 气象批次准确率
 * <AUTHOR>
 * @Date 2025/8/27 13:36
 **/
@RequestMapping("/weatherBatch")
@RestController
public class WeatherBatchAccuracyController {



    @ApiOperation("获取气象准确率")
    @RequestMapping(value = "/accuracy", method = RequestMethod.GET)
    public BaseResp getAssessAccuracy(String cityId, Date startDate, Date endDate, String weatherSource, String batchId) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<AssessAccuracyDTO> assessAccuracy = accuracyCompositeService.getAssessAccuracy(cityId, this.getCaliberId(), defaultAlgorithmIds, batchId, startDate, endDate, days);
        baseResp.setData(assessAccuracy);
        return baseResp;
    }
}
