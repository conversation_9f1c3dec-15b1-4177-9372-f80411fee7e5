package com.tsintergy.lf.web.base.evalucation.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.base.evalucation.api.WeatherAccuracyAssessmentService;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.WeatherAccuracyAssessmentDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.WeatherAccuracyTableDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.WeatherDeviationDetailDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.WeatherForecastComparisonDTO;
import com.tsintergy.lf.web.base.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * 气象批次准确率控制器
 * <AUTHOR>
 * @Date 2025/8/27 13:36
 **/
@Api(tags = "气象批次准确率")
@RequestMapping("/weatherBatch")
@RestController
public class WeatherBatchAccuracyController extends BaseController {

    @Autowired
    private WeatherAccuracyAssessmentService weatherAccuracyAssessmentService;

    @ApiOperation("获取气象预测准确率评估数据")
    @RequestMapping(value = "/accuracy", method = RequestMethod.GET)
    public BaseResp getAssessAccuracy(
            @ApiParam("城市ID") @RequestParam String cityId,
            @ApiParam("开始日期") @RequestParam Date startDate,
            @ApiParam("结束日期") @RequestParam Date endDate,
            @ApiParam("气象源") @RequestParam(required = false) String weatherSource,
            @ApiParam("批次ID") @RequestParam(required = false) String batchId,
            @ApiParam("气象指标类型（0-最高温度，1-最低温度，2-平均温度，3-相对湿度，4-累计降雨量，5-最大风速）") @RequestParam(required = false, defaultValue = "0") Integer weatherIndicatorType,
            @ApiParam("选中的日期（用于偏差详情和预测对比）") @RequestParam(required = false) Date selectedDate,
            @ApiParam("选中的预测天数（D-1到D-10）") @RequestParam(required = false) Integer selectedDay
    ) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        WeatherAccuracyAssessmentDTO result = weatherAccuracyAssessmentService.getWeatherAccuracyAssessment(
                cityId, startDate, endDate, weatherSource, batchId, weatherIndicatorType, selectedDate, selectedDay);
        baseResp.setData(result);
        return baseResp;
    }

    @ApiOperation("获取气象预测准确率表格数据")
    @RequestMapping(value = "/accuracy/table", method = RequestMethod.GET)
    public BaseResp getAccuracyTable(
            @ApiParam("城市ID") @RequestParam String cityId,
            @ApiParam("开始日期") @RequestParam Date startDate,
            @ApiParam("结束日期") @RequestParam Date endDate,
            @ApiParam("气象源") @RequestParam(required = false) String weatherSource,
            @ApiParam("批次ID") @RequestParam(required = false) String batchId,
            @ApiParam("气象指标类型") @RequestParam(required = false, defaultValue = "0") Integer weatherIndicatorType
    ) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        WeatherAccuracyTableDTO result = weatherAccuracyAssessmentService.getWeatherAccuracyTable(
                cityId, startDate, endDate, weatherSource, batchId, weatherIndicatorType);
        baseResp.setData(result);
        return baseResp;
    }

    @ApiOperation("获取气象预测偏差详情")
    @RequestMapping(value = "/accuracy/deviation", method = RequestMethod.GET)
    public BaseResp getDeviationDetail(
            @ApiParam("城市ID") @RequestParam String cityId,
            @ApiParam("选中的日期") @RequestParam Date selectedDate,
            @ApiParam("选中的预测天数（D-1到D-10）") @RequestParam Integer selectedDay,
            @ApiParam("气象源") @RequestParam(required = false) String weatherSource,
            @ApiParam("批次ID") @RequestParam(required = false) String batchId
    ) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        WeatherDeviationDetailDTO result = weatherAccuracyAssessmentService.getWeatherDeviationDetail(
                cityId, selectedDate, selectedDay, weatherSource, batchId);
        baseResp.setData(result);
        return baseResp;
    }

    @ApiOperation("获取气象预测对比数据")
    @RequestMapping(value = "/accuracy/comparison", method = RequestMethod.GET)
    public BaseResp getForecastComparison(
            @ApiParam("城市ID") @RequestParam String cityId,
            @ApiParam("选中的日期") @RequestParam Date selectedDate,
            @ApiParam("选中的预测天数（D-1到D-10）") @RequestParam Integer selectedDay,
            @ApiParam("气象源") @RequestParam(required = false) String weatherSource,
            @ApiParam("批次ID") @RequestParam(required = false) String batchId,
            @ApiParam("气象类型（1-湿度，2-温度，3-降雨量，4-风速）") @RequestParam(required = false, defaultValue = "2") Integer weatherType
    ) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        WeatherForecastComparisonDTO result = weatherAccuracyAssessmentService.getWeatherForecastComparison(
                cityId, selectedDate, selectedDay, weatherSource, batchId, weatherType);
        baseResp.setData(result);
        return baseResp;
    }
}
