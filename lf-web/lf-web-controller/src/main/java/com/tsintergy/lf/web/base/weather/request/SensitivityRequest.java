/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangfeng Date:  2019/8/14 3:28 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.weather.request;


import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 灵敏度分析页面请求参数对象
 *
 * <AUTHOR>
 * @create 2022/3/8
 * @since 1.0.0
 */
@Data
public class SensitivityRequest implements Serializable {

    @ApiModelProperty(value = "城市ID")
    private String cityId;

    @ApiModelProperty(value = "开始日期")
    private Date startDate;

    @ApiModelProperty(value = "结束日期")
    private Date endDate;

    @ApiModelProperty(value = "因变量类型 负荷 1 最大 2 平均 3 最小")
    private String loadType;

    @ApiModelProperty(value = "自变量类型 温度：21最大  22 平均 23最小）（实感 51最大 52平均,53最小")
    private String weatherType;

    @ApiModelProperty(value = "最小值")
    private String min;

    @ApiModelProperty(value = "最大值")
    private String max;

    @ApiModelProperty(value = "step")
    private String step;

    @ApiModelProperty(value = "异常干扰日期列表")
    private List<String> dateNotIncluded;

}