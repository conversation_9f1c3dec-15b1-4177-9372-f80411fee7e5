/**
 * Copyright (C), 2015‐2022, 北京清能互联科技有限公司 Author:  <EMAIL> Date:  2022/5/24 1:32 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.acload.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Description: 下拉选 <br>
 *
 * <AUTHOR>
 * @create 2022/5/24
 * @since 1.0.0
 */
@Data
@ApiModel(description = "下拉框")
@AllArgsConstructor
@NoArgsConstructor
public class DropDownBoxResp {
    @ApiModelProperty(value = "下拉选id", example = "1")
    private String id;

    @ApiModelProperty(value = "下拉选名称", example = "选项1")
    private String name;
}  
