/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.acload.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsieframework.core.base.web.BaseRespBuilder;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.sensitivity.SensitivityResult;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.api.AcSensitivityForecastService;
import com.tsintergy.lf.serviceapi.base.forecast.dto.AcSensitivityAlgorithmDTO;
import com.tsintergy.lf.web.base.acload.request.AcSensitivityRequest;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description:<br>
 *
 * @Author:<EMAIL>
 * @Date: 2022/5/24 11:49
 * @Version:1.0.0
 */
@RestController
@RequestMapping(value = "/ac/sensitivity")
@Api(value = "空调灵敏度分析", tags = "空调灵敏度分析")
public class AcSensitivityController extends CommonBaseController {

    @Autowired
    AcSensitivityForecastService acSensitivityForecastService;

    @RequestMapping(value = "/analyze", method = RequestMethod.POST)
    @ApiOperation("灵敏度分析计算查询接口")
    public BaseResp<SensitivityResult> doSensitivity(@RequestBody AcSensitivityRequest request) {
        AcSensitivityAlgorithmDTO algorithmDTO = new AcSensitivityAlgorithmDTO();
        BeanUtils.copyProperties(request, algorithmDTO);
        algorithmDTO.setCaliberId(this.getCaliberId());
        algorithmDTO.setUserId(getLoginUserId());
        //调整排除时间字段数据
        if (CollectionUtils.isNotEmpty(request.getDateNotIncluded())) {
            algorithmDTO.setDateNotIncluded(processWebDateList(request.getDateNotIncluded()));
        }
        SensitivityResult result = acSensitivityForecastService.doAcSensitivityAlgorithm(algorithmDTO);
        return BaseRespBuilder.success().setData(result, SensitivityResult.class).build();
    }

    /**
     * 清洗前段传输的时间列表
     *
     * @param dateList 元素示例： 2021-01-01~2021-01-02
     */
    private List<String> processWebDateList(List<String> dateList) {
        List<String> result = new ArrayList<>();
        for (String date : dateList) {
            String[] split = date.split(Constants.DATE_SEPARATOR_PUNCTUATION);
            List<String> listBetweenDay = DateUtil.getListBetweenDay(split[0], split[1]);
            result.addAll(listBetweenDay);
        }
        Set<String> resultList = new TreeSet<>(result);
        dateList.clear();
        dateList.addAll(resultList);
        List<String> collect = dateList.stream().map(src -> src.replace("-", "")).collect(Collectors.toList());
        return collect;
    }
}