package com.tsintergy.lf.web.base.forecast.controller;

import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.BaseLoadFeatureCityDayDO;
import com.tsintergy.lf.serviceapi.base.check.api.SettingReportService;
import com.tsintergy.lf.serviceapi.base.check.pojo.SettingReportDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.AlgorithmService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcBatchService;
import com.tsintergy.lf.serviceapi.base.forecast.dto.LoadCityFcBatchAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.forecast.dto.LoadCityFcBatchDTO;
import com.tsintergy.lf.serviceapi.base.forecast.dto.LoadCityFcBatchFeatureDTO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcBatchDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureStatService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import com.tsintergy.lf.web.base.forecast.response.LoadCityFcBatchResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 上报结果查询页面
 * @date 2021/2/3 15:49
 */
@Api(tags = "上报结果查询页面")
@RestController
@RequestMapping("/loadFc/batch")
@Slf4j
public class LoadCityFcBatchController extends CommonBaseController {

    @Autowired
    LoadCityFcBatchService loadCityFcBatchService;

    @Autowired
    LoadCityHisService loadCityHisService;

    @Autowired
    LoadFeatureStatService featureStatService;

    @Autowired
    CityService cityService;

    @Autowired
    AlgorithmService algorithmService;

    @Autowired
    SettingReportService settingReportService;

    @ApiOperation("获取城市负荷")
    @GetMapping("/statistics")
    public BaseResp<LoadCityFcBatchResponse> getLoadCityFcBatch(String cityId, Date date, String caliberId, String algorithmId) throws Exception{
        cityId = cityId == null ? getLoginCityId() : cityId;
        caliberId = caliberId == null ? getCaliberId() : caliberId;
        List<LoadCityFcBatchDO> loadCityFcBatchDOS = loadCityFcBatchService.findByCondition(cityId ,date ,caliberId, algorithmId);
        List<LoadCityHisDO> hisLoads = loadCityHisService.getLoadCityHisDOS(cityId , caliberId , date ,date);
        SettingReportDO settingReportDO = settingReportService.findSettingReportByCityId(cityId);
        LoadCityHisDO hisLoad = CollectionUtils.isEmpty(hisLoads) ? null : hisLoads.get(0);
        //组装数据
        LoadCityFcBatchResponse response = getLoadCityFcBatchResponse(loadCityFcBatchDOS , hisLoad);
        response.setCityId(cityId);
        response.setStandardAccuracy(settingReportDO.getStandardAccuracy());
        response.setCityName(cityService.findCityById(cityId).getCity());
        response.setAlgorithmName(AlgorithmEnum.RESULT_REPORT.getId().equals(algorithmId) ? AlgorithmEnum.RESULT_REPORT.getDescription() : algorithmService.getAlgorithmCn(algorithmId));
        response.setDate(date);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(response);
        return baseResp;
    }

    private LoadCityFcBatchResponse getLoadCityFcBatchResponse(List<LoadCityFcBatchDO> loadCityFcBatchDOS, LoadCityHisDO hisLoad) {
        LoadCityFcBatchResponse response = new LoadCityFcBatchResponse();
        if(CollectionUtils.isNotEmpty(loadCityFcBatchDOS)){
            List<LoadCityFcBatchDTO> loadCityFcBatchDTOS = new ArrayList<>();
            List<LoadCityFcBatchFeatureDTO> loadCityFcBatchFeatureDTOS = new ArrayList<>();
            List<LoadCityFcBatchAccuracyDTO> loadCityFcBatchAccuracyDTOS = new ArrayList<>();
            loadCityFcBatchDOS.forEach(loadCityFcBatchDO -> {
                LoadCityFcBatchDTO loadCityFcBatchDTO = new LoadCityFcBatchDTO();
                loadCityFcBatchDTO.setBatchId(loadCityFcBatchDO.getBatchId());
                Timestamp timestamp;
                if(loadCityFcBatchDO.getReport() == true){
                    timestamp = loadCityFcBatchDO.getReportTime();
                }else {
                    timestamp = new Timestamp(loadCityFcBatchDO.getUpdatetime()== null
                        ? loadCityFcBatchDO.getCreatetime().getTime() : loadCityFcBatchDO.getUpdatetime().getTime());
                }
                loadCityFcBatchDTO.setDateTime(DateUtils.date2String(new Date(timestamp.getTime()), DateFormatType.DATE_FORMAT_STR));
                loadCityFcBatchDTO.setFcLoad(BasePeriodUtils.toList(loadCityFcBatchDO, Constants.LOAD_CURVE_POINT_NUM,
                        Constants.LOAD_CURVE_START_WITH_ZERO));
                loadCityFcBatchDTOS.add(loadCityFcBatchDTO);
                BaseLoadFeatureCityDayDO loadFeatureCityDayFcDO = featureStatService.doStatLoadFeatureCityDay(loadCityFcBatchDO);
                LoadCityFcBatchFeatureDTO featureDTO = new LoadCityFcBatchFeatureDTO();
                featureDTO.setBatchId(loadCityFcBatchDO.getBatchId());
                featureDTO.of(loadFeatureCityDayFcDO);
                loadCityFcBatchFeatureDTOS.add(featureDTO);
                if(hisLoad != null){
                    LoadCityFcBatchAccuracyDTO accuracyDTO = new LoadCityFcBatchAccuracyDTO();
                    accuracyDTO.setBatchId(loadCityFcBatchDO.getBatchId());
                    try {
                        BigDecimal accuracy = LoadCalUtil.getDayAccuracy(hisLoad, loadCityFcBatchDO, Constants.LOAD_CURVE_POINT_NUM,null);
                        accuracyDTO.setAccuracy(accuracy);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    loadCityFcBatchAccuracyDTOS.add(accuracyDTO);
                }

            });
            response.setLoadCityFcBatchDTOS(loadCityFcBatchDTOS);
            response.setLoadCityFcBatchFeatureDTOS(loadCityFcBatchFeatureDTOS);
            response.setLoadCityFcBatchAccuracyDTOS(loadCityFcBatchAccuracyDTOS);
        }
        if(hisLoad != null){
            BaseLoadFeatureCityDayDO hisLoadFeature = featureStatService.doStatLoadFeatureCityDay(hisLoad);
            LoadCityFcBatchFeatureDTO hisLoadFeatureDTO = new LoadCityFcBatchFeatureDTO();
            hisLoadFeatureDTO.of(hisLoadFeature);
            response.setHisLoadFeature(hisLoadFeatureDTO);
            response.setHisLoad(BasePeriodUtils.toList(hisLoad, Constants.LOAD_CURVE_POINT_NUM,
                    Constants.LOAD_CURVE_START_WITH_ZERO));
        }
        return response;
    }


}
