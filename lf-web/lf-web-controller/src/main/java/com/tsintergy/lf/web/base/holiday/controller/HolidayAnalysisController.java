/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司 Author: wangfeng Date: 2018/4/23 13:03 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.holiday.controller;

import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.bus.api.BaseStationService;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.holiday.ao.HolidayAnalysisAO;
import com.tsintergy.lf.serviceapi.base.holiday.ao.HolidaySettingAO;
import com.tsintergy.lf.serviceapi.base.holiday.api.HolidayAnalysisService;
import com.tsintergy.lf.serviceapi.base.holiday.dto.HolidayAnalysisDTO;
import com.tsintergy.lf.serviceapi.base.holiday.dto.HolidayData;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherStationFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherStationInfoClctService;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherFcClctDTO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationInfoClctDO;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import com.tsintergy.lf.web.base.holiday.request.HolidayAnalysisRequest;
import com.tsintergy.lf.web.base.holiday.request.HolidaySettingRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 节假日分析
 *
 * <AUTHOR>
 * @create 2019/6/26
 * @since 1.0.0
 */
@RequestMapping("/holiday")
@RestController
@Slf4j
@Api(tags = "节假日分析")
public class HolidayAnalysisController extends CommonBaseController {

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Autowired
    BaseStationService baseStationService;

    @Autowired
    private HolidayAnalysisService holidayAnalysisService;

    @Autowired
    WeatherStationInfoClctService weatherStationInfoClctService;

    @Autowired
    WeatherStationFcService weatherStationFcService;

    /**
     * 节假日特性分析 生成基准值
     */
    @ApiOperation("节假日特性分析生成基准值")
    @RequestMapping(value = "/analysis-setting", method = RequestMethod.POST)
    public BaseResp<List<HolidayData>> holidayAnalysisSetting(@RequestBody HolidaySettingRequest request)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        HolidaySettingAO ao = new HolidaySettingAO();
        ao.setCaliberId(super.getCaliberId());
        BeanUtils.copyProperties(request, ao);
        List<HolidayData> holidayData = holidayAnalysisService.findHolidayAnalysisSetting(ao);
        if (CollectionUtils.isEmpty(holidayData) || holidayData.size() == 0) {
            baseResp.setRetCode("T706");
            return baseResp;
        }
        baseResp.setData(holidayData);
        return baseResp;
    }

    /**
     * 节假日特性分析 分析特性波动
     */
    @ApiOperation("节假日特性分析")
    @RequestMapping(value = "/analysis", method = RequestMethod.POST)
    public BaseResp<HolidayAnalysisDTO> holidayAnalysis(@RequestBody HolidayAnalysisRequest request) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        HolidayAnalysisAO ao = new HolidayAnalysisAO();
        BeanUtils.copyProperties(request, ao);
        ao.setCaliberId(super.getCaliberId());
        HolidayAnalysisDTO holidayAnalysisDTO = this.holidayAnalysisService.findListFinalAnalysisResult(ao);
        if (Objects.isNull(holidayAnalysisDTO)) {
            baseResp.setRetCode("T706");
            return baseResp;
        }
        baseResp.setData(holidayAnalysisDTO);
        return baseResp;
    }

    @ApiOperation("测试")
    @RequestMapping(value = "/test", method = RequestMethod.GET)
    public BaseResp<BigDecimal> test(Date startDate, Date endDate) throws Exception {
        List<Date> listBetweenDay = DateUtil.getListBetweenDay(startDate, endDate);
        List<String> dataList = new ArrayList<>();
        for (Date day : listBetweenDay) {
            dataList.add(DateUtils.date2String(day, DateFormatType.SIMPLE_DATE_FORMAT_STR));
        }
        Map<String, String> cityAndStationMap = new HashMap();

        cityAndStationMap.put("4223532027237892100", "2");
        cityAndStationMap.put("4223532027254669314", "3");
        cityAndStationMap.put("4223532026130595842", "4");
        cityAndStationMap.put("4223532027355332615", "5");
        cityAndStationMap.put("4223532027305000968", "6");
        cityAndStationMap.put("4223532027321778180", "7");
        cityAndStationMap.put("4223532027372109833", "8");
        cityAndStationMap.put("4223532027338555394", "9");
        cityAndStationMap.put("4223532027271446536", "10");
        weatherCityHisService.doClctWeatherStationToCity(dataList, cityAndStationMap);


        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(new BigDecimal(0.11));
        return baseResp;
    }


    public void doParseFile(List<String> files) {
        for (String file : files) {
            List<WeatherFcClctDTO> weatherFcClctDTOS = new ArrayList<>();
            BufferedReader br = null;
            try {
                br = new BufferedReader(new FileReader(file));
                String line;
                while ((line = br.readLine()) != null) {
                    if (line.startsWith("#") && !(line.equals("#@@@@"))) {
                        // 处理以 # 开头的数据行
                        String[] data = line.split("\\s+");
                        // 输出数据内容
                        WeatherFcClctDTO weatherFcClctDTO = new WeatherFcClctDTO();
                        weatherFcClctDTO.setID(data[0].replace("#", ""));
                        weatherFcClctDTO.setFORCAST_TIME(data[2]);
                        weatherFcClctDTO.setWIND_SPEED(data[3]);
                        weatherFcClctDTO.setRAINFALL(data[15]);
                        weatherFcClctDTO.setTEMPERATURE(data[17]);
                        weatherFcClctDTO.setHUMIDITY(data[19]);
                        weatherFcClctDTOS.add(weatherFcClctDTO);
                    }
                }
            } catch (IOException e) {
            } finally {
                if (br != null) {
                    try {
                        br.close();
                    } catch (IOException e) {
                    }
                }
            }
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(weatherFcClctDTOS)) {
                Map<String, List<WeatherFcClctDTO>> map = weatherFcClctDTOS.stream().collect(
                    Collectors.groupingBy(dto -> dto.getFORCAST_TIME().substring(0, 10)));
                for (Map.Entry<String, List<WeatherFcClctDTO>> entry : map.entrySet()) {
                    List<WeatherStationFcDO> weatherStationFcDOS = new ArrayList<>();
                    String key = entry.getKey();
                    Date day = DateUtil.getDateFromString(key, DateUtil.DATE_FORMAT2);
                    List<WeatherFcClctDTO> value = entry.getValue();
                    String stationId = value.get(0).getID();
                    Map<String, BigDecimal> windMap = new HashMap<>();
                    Map<String, BigDecimal> rainMap = new HashMap<>();
                    Map<String, BigDecimal> temMap = new HashMap<>();
                    Map<String, BigDecimal> humMap = new HashMap<>();
                    for (WeatherFcClctDTO weatherFcClctDTO : value) {
                        String timeStr = weatherFcClctDTO.getFORCAST_TIME().substring(11, 16);
                        String field = "t" + timeStr.replace(":", "");
                        windMap.put(field, BigDecimal.valueOf(Double.parseDouble(weatherFcClctDTO.getWIND_SPEED())));
                        rainMap.put(field, BigDecimal.valueOf(Double.parseDouble(weatherFcClctDTO.getRAINFALL())));
                        temMap.put(field, BigDecimal.valueOf(Double.parseDouble(weatherFcClctDTO.getTEMPERATURE())));
                        humMap.put(field, BigDecimal.valueOf(Double.parseDouble(weatherFcClctDTO.getHUMIDITY())));
                    }
                    wrapperWeatherCityFcData(stationId, day, windMap, WeatherEnum.WINDSPEED.getType(),
                        weatherStationFcDOS);
                    wrapperWeatherCityFcData(stationId, day, rainMap, WeatherEnum.RAINFALL.getType(),
                        weatherStationFcDOS);
                    wrapperWeatherCityFcData(stationId, day, temMap, WeatherEnum.TEMPERATURE.getType(),
                        weatherStationFcDOS);
                    wrapperWeatherCityFcData(stationId, day, humMap, WeatherEnum.HUMIDITY.getType(),
                        weatherStationFcDOS);
                    saveOrUpdateWeatherCityFc(weatherStationFcDOS);
                }
            }
        }

    }

    @SneakyThrows
    public void wrapperWeatherCityFcData(String stationId, Date date, Map<String, BigDecimal> map, Integer type
        , List<WeatherStationFcDO> weatherStationFcDOS) {
        WeatherStationFcDO weatherStationFcDO = new WeatherStationFcDO();
        //查询城市
        WeatherStationInfoClctDO weatherStationInfoClctDO = weatherStationInfoClctService.findByStationId(stationId);
        if (weatherStationInfoClctDO != null) {
            weatherStationFcDO.setCityId(weatherStationInfoClctDO.getCityId());
        }
        weatherStationFcDO.setStationId(stationId);
        weatherStationFcDO.setType(type);
        weatherStationFcDO.setDate(new java.sql.Date(date.getTime()));
        BasePeriodUtils.setAllFiled(weatherStationFcDO, map);
        weatherStationFcDOS.add(weatherStationFcDO);
    }


    public void saveOrUpdateWeatherCityFc(List<WeatherStationFcDO> weatherStationFcDOS) {
        for (WeatherStationFcDO weatherStationFcDO : weatherStationFcDOS) {
            List<WeatherStationFcDO> weatherStationFcDOList = weatherStationFcService
                .findByDateAndType(weatherStationFcDO.getStationId(), weatherStationFcDO.getType(),
                    weatherStationFcDO.getDate());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(weatherStationFcDOList)) {
                WeatherStationFcDO weatherStation = weatherStationFcDOList.get(0);
                weatherStationFcDO.setId(weatherStation.getId());
                weatherStationFcService.saveOrUpdate(weatherStationFcDO);
                if (weatherStationFcDO.getT0000() != null) {
                    List<WeatherStationFcDO> yesterdayList = weatherStationFcService
                        .findByDateAndType(weatherStationFcDO.getStationId(), weatherStationFcDO.getType(),
                            DateUtils.addDays(weatherStationFcDO.getDate(), -1));
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(yesterdayList)) {
                        WeatherStationFcDO yesterday = yesterdayList.get(0);
                        WeatherStationFcDO weatherStationFc = new WeatherStationFcDO();
                        BeanUtils.copyProperties(yesterday, weatherStationFc);
                        weatherStationFc.setT2400(weatherStationFcDO.getT0000());
                        weatherStationFcService.saveOrUpdate(weatherStationFc);
                    }
                }
            } else {
                weatherStationFcService.saveOrUpdate(weatherStationFcDO);
                if (weatherStationFcDO.getT0000() != null) {
                    List<WeatherStationFcDO> yesterdayList = weatherStationFcService
                        .findByDateAndType(weatherStationFcDO.getStationId(), weatherStationFcDO.getType(),
                            DateUtils.addDays(weatherStationFcDO.getDate(), -1));
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(yesterdayList)) {
                        WeatherStationFcDO yesterday = yesterdayList.get(0);
                        WeatherStationFcDO weatherStationFc = new WeatherStationFcDO();
                        BeanUtils.copyProperties(yesterday, weatherStationFc);
                        weatherStationFc.setT2400(weatherStationFcDO.getT0000());
                        weatherStationFcService.saveOrUpdate(weatherStationFc);
                    }
                }
            }
        }

    }

}