/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangfeng Date:  2019/6/29 12:37 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.holiday.controller;


import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.enums.HolidayEnum;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceapi.base.common.enumeration.CommonSetting;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcBatchService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.holiday.api.HolidayModifyService;
import com.tsintergy.lf.serviceapi.base.holiday.dto.HolidayCurveDTO;
import com.tsintergy.lf.serviceapi.base.holiday.dto.HolidayDTO;
import com.tsintergy.lf.serviceapi.base.holiday.dto.HolidayModifyDataDTO;
import com.tsintergy.lf.serviceapi.base.holiday.dto.HolidayReportDTO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureStatService;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import com.tsintergy.lf.web.base.holiday.request.AnalyzeRequest;
import com.tsintergy.lf.web.base.holiday.request.HolidayPushRequest;
import com.tsintergy.lf.web.base.holiday.request.PushData;
import com.tsintergy.lf.web.base.holiday.response.HolidayCodeDTO;
import com.tsintergy.lf.web.base.holiday.response.HolidayInfoResponse;
import com.tsintergy.lf.web.base.holiday.response.HolidayResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/6/29
 * @since 1.0.0
 */
@RequestMapping(value = "/holiday/modify")
@RestController
@Api(tags = "假日修改控制器")
public class HolidayModifyController extends CommonBaseController {

    @Autowired
    private HolidayService holidayService;

    @Autowired
    private HolidayModifyService holidayModifyService;

    @Autowired
    private LoadCityFcService loadCityFcService;

    @Autowired
    private LoadCityFcBatchService loadCityFcBatchService;

    @Autowired
    private LoadFeatureStatService loadFeatureStatService;

    /**
     * 功能描述: <br> 获取节假日code
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    @ApiOperation("获取节假日code")
    @GetMapping(value = "/code")
    public BaseResp<List<HolidayCodeDTO>> getHolidayCode() throws Exception {
        List<HolidayCodeDTO> holidayCodeDTOS = new ArrayList<>();
        for (HolidayEnum holidayEnum : HolidayEnum.values()) {
            if (holidayEnum.getName().equals("自定义")) {
                continue;
            }
            HolidayCodeDTO holidayCodeDTO = new HolidayCodeDTO();
            holidayCodeDTO.setCode(holidayEnum.getCode());
            holidayCodeDTO.setHoliday(holidayEnum.getName());
            holidayCodeDTOS.add(holidayCodeDTO);
        }

        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(holidayCodeDTOS);
        return baseResp;
    }

    /**
     * 功能描述: <br> 获取节假日列表
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    @ApiOperation("获取节假日列表")
    @GetMapping(value = "/info")
    public BaseResp<HolidayInfoResponse> getHolidayList() throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        Date startDate = this.getSystemDate();
        String year = DateUtil.getYearByDate(startDate);
        Date endDate = DateUtil.getYearLast(Integer.parseInt(year));
        List<Date> dateList = holidayService.getAllHolidays();
        List<HolidayDO> holidayVOS = holidayService.getListHoliday(startDate, endDate);
        holidayVOS.sort(
                Comparator.comparing(HolidayDO::getDate, Comparator.nullsFirst(Comparator.naturalOrder())));
        if (dateList.contains(startDate)) {
            List<HolidayDO> vos = holidayService.getAllHolidayVOS();
            Map<Date, HolidayDO> map = new HashMap<>();
            for (HolidayDO vo : vos) {
                List<Date> dates = DateUtil.getListBetweenDay(vo.getStartDate(), vo.getEndDate());
                for (Date date : dates) {
                    map.put(date, vo);
                }
            }
            HolidayDO holidayVO = map.get(startDate);
            if (!holidayVOS.contains(holidayVO)) {
                if (holidayVOS.size() == 0) {
                    holidayVOS.add(holidayVO);
                    year = DateUtil.getYearByDate(holidayVO.getDate());
                } else {
                    holidayVOS.add(holidayVO);
                    //排序
                    holidayVOS.sort(
                        Comparator.comparing(HolidayDO::getDate, Comparator.nullsFirst(Comparator.naturalOrder())));
                }

            }
        }
        if (holidayVOS.size() < 1) {//则本年是没有数据的 需要查找明年的
            year = String.valueOf(Integer.valueOf(year) + 1);
            startDate = DateUtil.getYearFirst(Integer.parseInt(year));
            endDate = DateUtil.getYearLast(Integer.parseInt(year));
            List<HolidayDO> tomorrow = holidayService.getListHoliday(startDate, endDate);
            holidayVOS.addAll(tomorrow);
        }
        HolidayInfoResponse response = new HolidayInfoResponse();
        response.setYear(year);
        response.setHolidayDOS(holidayVOS);
        baseResp.setData(response);
        return baseResp;
    }


    /**
     * 功能描述: <br> 算法列表及上报信息
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    @ApiOperation("算法列表及上报信息")
    @GetMapping(value = "/algorithm")
    public BaseResp<HolidayReportDTO> getAlgorithmList(String cityId, String holidayId) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        if (StringUtils.isEmpty(cityId)) {
            cityId = this.getLoginCityId();
        }
        HolidayReportDTO dto = holidayModifyService.getReportInfo(cityId, getCaliberId(), holidayId);
        baseResp.setData(dto);
        return baseResp;
    }


    /**
     * 功能描述: <br> 节假日预测曲线
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    @ApiOperation("节假日预测曲线")
    @GetMapping(value = "/forecast")
    public BaseResp<List<HolidayCurveDTO>> getForecastCurve(String cityId, String holidayId, String algorithmId) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<HolidayCurveDTO> holidayCurveDTOList = holidayModifyService
            .findHolidayCurve(cityId, getCaliberId(), holidayId, algorithmId);
        baseResp.setData(holidayCurveDTOList);
        return baseResp;
    }


    /**
     * 功能描述: <br> 节假日历史降幅分析
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    @ApiOperation("节假日历史降幅分析")
    @RequestMapping(value = "/anlys", method = RequestMethod.POST)
    public BaseResp<HolidayResponse> getHolidayAnalyze(@RequestBody AnalyzeRequest analyzeRequest) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        HolidayDO holidayVO = holidayService.findHolidayVOByPk(analyzeRequest.getHolidayId());
        Map<String, Integer> map = getDays(holidayVO);
        Integer beforeDay = map.get("before");
        Integer afterDay = map.get("after");
        if (analyzeRequest.getNumber() == null) {
            analyzeRequest.setNumber(1);
        }
        String startYear = String.valueOf(Integer.parseInt(analyzeRequest.getYear()) - analyzeRequest.getNumber());
        String endYear = String.valueOf(Integer.valueOf(analyzeRequest.getYear()) - 1);
        List<HolidayDO> voList = holidayService.getListHolidayByCode(startYear, endYear, holidayVO.getCode());
        if (analyzeRequest.getDtoList() == null) {
            List<HolidayDTO> dtoList = holidayModifyService
                .findHolidayData(analyzeRequest.getCityId(), getCaliberId(), beforeDay, afterDay, voList);
            analyzeRequest.setDtoList(dtoList);
        }
        HolidayModifyDataDTO dataDTO = holidayModifyService
            .getHolidayAnalyse(analyzeRequest.getCityId(), getCaliberId(), analyzeRequest.getDtoList(), beforeDay,
                afterDay, voList);
        HolidayResponse holidayResponse = new HolidayResponse();
        holidayResponse.setReference(analyzeRequest.getDtoList());
        holidayResponse.setCalculate(dataDTO.getHolidayModifyDTOS());
        holidayResponse.setMaxAvg(dataDTO.getMax());
        holidayResponse.setMinAvg(dataDTO.getMin());
        baseResp.setData(holidayResponse);
        return baseResp;
    }


    /**
     * 功能描述: <br> 节假日结果上报
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    @ApiOperation("节假日结果上报")
    @RequestMapping(value = "/push", method = RequestMethod.POST)
    public BaseResp<String> saveOrPush(@RequestBody HolidayPushRequest holidayPushRequest) throws Exception {
        BaseResp baseResp = new BaseResp();
        List<PushData> pushData = holidayPushRequest.getPushData();
        StringBuilder string = new StringBuilder();
        int flag = 0;
        for (PushData data : pushData) {
            Date date = data.getDate();
            BaseResp checkResp = checkReportDeadlineTime(holidayPushRequest.getCityId(), CommonSetting.ORDINARY_DAY,
                date, date, null);
            if (checkResp.getRetCode().equals("T0")) {
                string.append(DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_STR))
                    .append("日数据已过上报日期，未上报;");
                flag++;
                continue;
            }
            LoadCityFcDO loadCityFcVO = new LoadCityFcDO();
            loadCityFcVO.setCityId(holidayPushRequest.getCityId());
            // 人工决策算法
            loadCityFcVO.setAlgorithmId(AlgorithmConstants.MD_ALGORITHM_ID);
            loadCityFcVO.setDate(new java.sql.Date(data.getDate().getTime()));
            loadCityFcVO.setUserId(this.getLoginUserId());
            Map<String, BigDecimal> dataMap = ColumnUtil
                .listToMap(data.getModifyLoad(), Constants.LOAD_CURVE_START_WITH_ZERO);
            BasePeriodUtils.setAllFiled(loadCityFcVO, dataMap);
            loadCityFcVO.setCaliberId(getCaliberId());
            loadCityFcVO.setReport(true);
            loadCityFcVO.setSucceed(true);
            loadCityFcVO.setRecommend(false);
            loadCityFcVO.setReportTime(new Timestamp(System.currentTimeMillis()));
            loadCityFcVO.setCreatetime(new Timestamp(System.currentTimeMillis()));

            try {
                loadCityFcService.doReport(loadCityFcVO);
                loadCityFcVO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                loadCityFcBatchService.doSave(loadCityFcVO);
                string.append(DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_STR)).append("日数据上报成功");
            } catch (BusinessException e) {
                baseResp.setRetCode("T0");
                baseResp.setRetMsg("上报失败，已超过上报时间");
                return baseResp;
            }
            //统计预测负荷特性
            loadFeatureStatService.doStatLoadFeatureCityDayFc(Arrays.asList(holidayPushRequest.getCityId()),date, date,
                getCaliberId());
        }
        if (flag == pushData.size()) {
            BaseResp resp = BaseResp.failResp("该节假日所有假期均超过上报截止时间。");
            return resp;
        }
        BaseResp resp = BaseResp.succResp();
        resp.setRetMsg(string.toString());
        return resp;
    }


    private Map<String, Integer> getDays(HolidayDO holidayVO) {
        List<Date> dateList = DateUtil.getListBetweenDay(holidayVO.getStartDate(), holidayVO.getEndDate());
        Integer beforeDay = 0;
        Integer afterDay = 0;
        if (dateList.size() <= 4) {//1-4天前后各推1
            beforeDay = 2;
            afterDay = 2;
        }
        if (holidayVO.getCode() == 102 || dateList.size() >= 7) {//春节或者中秋节前后各推10
            beforeDay = 2;
            afterDay = 8;
        }
        Map map = new HashMap();
        map.put("before", beforeDay);
        map.put("after", afterDay);
        return map;
    }
}