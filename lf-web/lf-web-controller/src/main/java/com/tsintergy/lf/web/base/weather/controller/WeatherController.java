package com.tsintergy.lf.web.base.weather.controller;


import com.tsieframework.cloud.security.web.businesslog.annotation.OperateLog;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.aif.tool.core.utils.file.weather.Diamond4GridWeatherResolver;
import com.tsintergy.aif.tool.core.utils.file.weather.GridWeatherResolver;
import com.tsintergy.aif.tool.core.utils.file.weather.pojo.GridWeather;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.ParamConstants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.core.util.PeriodDataUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.base.api.WeatherTyphoonDefinitionService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.WeatherTyphoonDefinitionDO;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherNewEnum;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.weather.api.*;
import com.tsintergy.lf.serviceapi.base.weather.dto.*;
import com.tsintergy.lf.serviceapi.base.weather.pojo.*;
import com.tsintergy.lf.web.base.common.response.CommonResp;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import com.tsintergy.lf.web.base.weather.request.*;
import com.tsintergy.lf.web.base.weather.response.WeatherDataResp;
import com.tsintergy.lf.web.base.weather.response.WeatherSimilar;
import com.tsintergy.lf.web.base.weather.response.WeatherSimilarResp;
import com.tsintergy.lf.web.base.weather.support.MyMock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.tsintergy.lf.core.constants.CityConstants.standardWgStationMap;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;

/**
 * @Todo: 气象
 * @date:17-12-18 上午9:25
 * @author:taojingui
 **/

@Slf4j
@RequestMapping("/weather")
@RestController
@Api(value = "天气控制器", tags = "天气控制器")
public class WeatherController extends CommonBaseController {

    private static final Integer DIANKEYUAN_SOURCE = new Integer(1);
    private final Logger logger = LogManager.getLogger(WeatherController.class);
    @Autowired
    WeatherSourceFcService weatherSourceFcService;
    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Autowired
    private WeatherCityFcService weatherCityFcService;

    @Autowired
    private WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    private WeatherFeatureCityMonthHisService weatherFeatureCityMonthHisService;

    @Autowired
    private WeatherFeatureCityQuarterHisService weatherFeatureCityQuarterHisService;

    @Autowired
    private HolidayService holidayService;


    @Autowired
    private WeatherTyphoonDefinitionService weatherTyphoonDefinitionService;

    @Autowired
    private LoadFeatureCityDayHisService loadFeatureCityDayHisService;

    @Autowired
    private WeatherTyphoonRateService weatherTyphoonRateService;

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private CityService cityService;

    @Autowired
    private WeatherCityFcLoadForecastService weatherCityFcLoadForecastService;

    @Autowired
    private BaseWeatherStationWgService baseWeatherStationWgService;

    @Autowired
    private WeatherStationHisClctWgService weatherStationHisClctWgService;

    @Autowired
    private WeatherCityBasedStationFcService weatherCityBasedStationFcService;

    @Autowired
    private WeatherStationHisBasicWgService weatherStationHisBasicWgService;

    @Autowired
    private WeatherStationFcBasicWgService weatherStationFcBasicWgService;


    /**
     * 获取气象的历史和预报数据
     */
    @ApiOperation("获取气象的历史和预报数据")
    @GetMapping("/his-fc")
    public BaseResp<WeatherDataResp> getHisAndFcData(WeatherHisFcRequest weatherHisFcRequest) {

        if (weatherHisFcRequest.getCityId() == null) {
            weatherHisFcRequest.setCityId(this.getLoginCityId());
        }
        if (weatherHisFcRequest.getDate() == null) {
            weatherHisFcRequest.setDate(this.getSystemDate());
        }
        if (weatherHisFcRequest.getType() == null) {
            weatherHisFcRequest.setType(WeatherEnum.TEMPERATURE.value());
        }

        WeatherDataResp weatherDataResp = new WeatherDataResp();
        try {
            CityDO cityVO = cityService.findCityById(weatherHisFcRequest.getCityId());
            // String cityId = weatherHisFcRequest.getCityId();
            String cityId = cityService.findWeatherCityId(cityVO.getId());
            weatherHisFcRequest.setCityId(cityId);

            // 历史数据
            List<WeatherCityHisDO> weatherCityHisVOs = weatherCityHisService
                    .findWeatherCityHisDOs(weatherHisFcRequest.getCityId(), weatherHisFcRequest.getType(),
                            weatherHisFcRequest.getDate(), weatherHisFcRequest.getDate());
            if (weatherCityHisVOs != null && weatherCityHisVOs.size() > 0) {
                weatherDataResp.setReal(BasePeriodUtils
                        .toList(weatherCityHisVOs.get(0), Constants.WEATHER_CURVE_POINT_NUM,
                                Constants.WEATHER_CURVE_START_WITH_ZERO));
            }
            // 预测数据
            List<WeatherCityFcDO> weatherCityFcVOs = weatherCityFcService
                    .findWeatherCityFcDOs(cityId, weatherHisFcRequest.getType(), weatherHisFcRequest.getDate(),
                            weatherHisFcRequest.getDate());
            if (weatherCityFcVOs != null && weatherCityFcVOs.size() > 0) {
                weatherDataResp.setForecast(BasePeriodUtils
                        .toList(weatherCityFcVOs.get(0), Constants.WEATHER_CURVE_POINT_NUM,
                                Constants.WEATHER_CURVE_START_WITH_ZERO));
            }
        } catch (Exception e) {
            logger.error("获取气象的历史和预报数据失败", e);
            return new BaseResp("T706");
        }

        BaseResp resp = BaseResp.succResp();
        if (weatherDataResp.getForecast() == null && weatherDataResp.getReal() == null) {
            return new BaseResp("T706");
        }
        resp.setData(weatherDataResp);
        return resp;
    }

    /**
     * 获取气象历史数据
     */
    @ApiOperation("获取气象历史数据")
    @RequestMapping(value = "/history", method = RequestMethod.GET)
    public BaseResp<CommonResp<WeatherDTO>> getWeatherHistory(@Validated WeatherHisRequest weatherHisRequest,
                                                              BindingResult result)
            throws Exception {
        validateFormValue(result);
        String cityId = weatherHisRequest.getCityId();
        if (StringUtils.isEmpty(cityId)) {
            cityId = super.getLoginCityId();
        }
        //如果是省调用户查询全部
        if (cityService.findCityById(cityId).getType().equals(Constants.PROVINCE_TYPE)) {
            cityId = null;
        }
        BaseResp<CommonResp<WeatherDTO>> response = BaseResp.succResp("查询成功");
        //电科院目前没有历史气象数据
        if (DIANKEYUAN_SOURCE.equals(weatherHisRequest.getSource())) {
            return response;
        }
        List<WeatherDTO> weatherDTOS = weatherCityHisService
                .findWeatherCityHisDTOs(cityId, weatherHisRequest.getType(), weatherHisRequest.getStartDate(),
                        weatherHisRequest.getEndDate());

        //如果dataType:1, 那么地市气象用地市标准站点气象代替   2:地市标准站点加权平均（仅福建）  3:福州站点平均（仅福建）
        if ("1".equals(weatherHisRequest.getDataType())) {
            List<WeatherStationHisBasicWgDO> stationHisBasicWgDOS = weatherStationHisBasicWgService.getStandardStationHisWeatherBasic(
                    null, weatherHisRequest.getType(), weatherHisRequest.getStartDate(), weatherHisRequest.getEndDate());
            Map<String, WeatherStationHisBasicWgDO> basicWgDOMap = stationHisBasicWgDOS.stream().collect(Collectors.toMap(
                    stationDO -> stationDO.getStationWgId() + stationDO.getDate().getTime(), Function.identity()));
            for (WeatherDTO weatherDTO : weatherDTOS) {
                String stationId = standardWgStationMap.get(weatherDTO.getCityId());
                WeatherStationHisBasicWgDO stationHisBasicWgDO = basicWgDOMap.get(stationId + weatherDTO.getDate().getTime());
                weatherDTO.setData(BasePeriodUtils.toList(stationHisBasicWgDO, Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO));
            }
        } else if ("2".equals(weatherHisRequest.getDataType())) {
            List<WeatherCityHisDO> hisWeatherData = weatherCityHisService.getProvinceWeightAvgHisWeatherData(weatherHisRequest.getType(),
                    weatherHisRequest.getStartDate(), weatherHisRequest.getEndDate());
            Map<Date, WeatherCityHisDO> cityHisDOMap = hisWeatherData.stream().collect(Collectors.toMap(WeatherCityHisDO::getDate, Function.identity()));
            for (WeatherDTO weatherDTO : weatherDTOS) {
                if (Constants.PROVINCE_ID.equals(weatherDTO.getCityId())) {
                    WeatherCityHisDO cityHisDO = cityHisDOMap.get(weatherDTO.getDate());
                    weatherDTO.setData(BasePeriodUtils.toList(cityHisDO, Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO));
                }
            }
        } else if ("3".equals(weatherHisRequest.getDataType())) {
            List<WeatherCityHisDO> cityHisDOs =
                    weatherCityHisService.getWeatherCityHisDOs(Collections.singletonList("2"), weatherHisRequest.getType(), weatherHisRequest.getStartDate(), weatherHisRequest.getEndDate());
            Map<Date, WeatherCityHisDO> cityHisDOMap = cityHisDOs.stream().collect(Collectors.toMap(WeatherCityHisDO::getDate, Function.identity()));
            for (WeatherDTO weatherDTO : weatherDTOS) {
                if (Constants.PROVINCE_ID.equals(weatherDTO.getCityId())) {
                    WeatherCityHisDO cityHisDO = cityHisDOMap.get(weatherDTO.getDate());
                    weatherDTO.setData(BasePeriodUtils.toList(cityHisDO, Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO));
                }
            }
        }
        CommonResp<WeatherDTO> commonResp = new CommonResp<WeatherDTO>();
        commonResp.setDataList(weatherDTOS);
        response.setData(commonResp);
        return response;
    }


    /**
     * 获取气象预测数据
     */
    @ApiOperation("获取气象预测数据")
    @RequestMapping(value = "/fc", method = RequestMethod.GET)
    public BaseResp<CommonResp<WeatherDTO>> getWeatherFc(@Validated WeatherHisRequest weatherHisRequest,
                                                         BindingResult result) throws Exception {
        validateFormValue(result);
        if (!StringUtils.isNotBlank(weatherHisRequest.getCityId())) {
            weatherHisRequest.setCityId(null);
        }
        String cityId = weatherHisRequest.getCityId();
        BaseResp<CommonResp<WeatherDTO>> response = BaseResp.succResp("查询成功");
        List<WeatherDTO> weatherDTOS = new ArrayList<>();
        //电科院预测气象
        if (DIANKEYUAN_SOURCE.equals(weatherHisRequest.getSource())) {
            List<WeatherCityBasedStationFcDO> weatherCityBasedStationFcDOs =
                    weatherCityBasedStationFcService.findWeatherCityBasedStationFcDOs(cityId, weatherHisRequest.getType(),
                            weatherHisRequest.getStartDate(), weatherHisRequest.getEndDate());
            if (isNotEmpty(weatherCityBasedStationFcDOs)) {
                for (WeatherCityBasedStationFcDO fcDO : weatherCityBasedStationFcDOs) {
                    WeatherDTO weatherDTO = new WeatherDTO();
                    weatherDTO.setId(fcDO.getId());
                    weatherDTO.setDate(fcDO.getDate());
                    weatherDTO.setWeek(DateUtil.getWeek(fcDO.getDate()));
                    weatherDTO.setCity(this.cityService.findCityById(fcDO.getCityId()).getCity());
                    weatherDTO.setData(BasePeriodUtils.toList(fcDO, Constants.WEATHER_CURVE_POINT_NUM,
                            Constants.WEATHER_CURVE_START_WITH_ZERO));
                    weatherDTOS.add(weatherDTO);
                }
            }
        } else {
            weatherDTOS = weatherCityFcService
                    .findWeatherCityFcDTOs(cityId, weatherHisRequest.getType(), weatherHisRequest.getStartDate(),
                            weatherHisRequest.getEndDate());
        }

        //地市气象用地市标准站点气象代替
        if ("1".equals(weatherHisRequest.getDataType()) && !DIANKEYUAN_SOURCE.equals(weatherHisRequest.getSource())) {
            List<WeatherStationFcBasicWgDO> stationFcBasicWgDOS = weatherStationFcBasicWgService.getStandardStationFcWeatherBasic(
                    null, weatherHisRequest.getType(), weatherHisRequest.getStartDate(), weatherHisRequest.getEndDate());
            Map<String, WeatherStationFcBasicWgDO> basicWgDOMap = stationFcBasicWgDOS.stream().collect(Collectors.toMap(
                    stationDO -> stationDO.getStationWgId() + stationDO.getDate().getTime(), Function.identity()));
            for (WeatherDTO weatherDTO : weatherDTOS) {
                String stationId = standardWgStationMap.get(weatherDTO.getCityId());
                WeatherStationFcBasicWgDO stationFcBasicWgDO = basicWgDOMap.get(stationId + weatherDTO.getDate().getTime());
                weatherDTO.setData(BasePeriodUtils.toList(stationFcBasicWgDO, Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO));
            }
        }
        CommonResp<WeatherDTO> commonResp = new CommonResp<WeatherDTO>();
        commonResp.setDataList(weatherDTOS);
        response.setData(commonResp);
        return response;
    }


    /**
     * 气温敏感度
     */
    @ApiOperation("气温敏感度")
    @RequestMapping(value = "/sensitivity", method = RequestMethod.GET)
    public BaseResp<CommonResp<WeatherSensitivityDTO>> getWeatherSensivity(
            @Validated WeatherSensitivityRequest weatherSensivityRequest,
            BindingResult result) throws Exception {
        validateFormValue(result);
        if (StringUtils.isEmpty(weatherSensivityRequest.getCityId())) {
            weatherSensivityRequest.setCityId(this.getLoginCityId());
        }
        if (StringUtils.isBlank(weatherSensivityRequest.getCaliberId())) {
            weatherSensivityRequest.setCaliberId(this.getCaliberId());
        }
        BaseResp<CommonResp<WeatherSensitivityDTO>> resp = BaseResp.succResp("查询成功");
        CommonResp<WeatherSensitivityDTO> commonResp = new CommonResp<WeatherSensitivityDTO>();
//        List<WeatherSensitivityDTO> weatherSensitivityDTOS=weatherSensitivityDayService.findWeatherSensitivity(weatherSensivityRequest.getCityId(), weatherSensivityRequest.getTargetDay(), weatherSensivityRequest.getEndDate(), weatherSensivityRequest.getLoadType(), weatherSensivityRequest.getWeatherEnum());
        /** 应该是通过算法得出结果，暂时跳过 **///todo wangchen   这里居然是生成随机数？？？
        List<WeatherSensitivityDTO> weatherSensitivityDTOS = new ArrayList<WeatherSensitivityDTO>(10);
        for (int i = 0; i < 10; i++) {
            weatherSensitivityDTOS.add(MyMock.mock(WeatherSensitivityDTO.class));
        }
        // 全部节假日
        List<Date> holidays = holidayService.getAllHolidays();
        if (weatherSensivityRequest.getHoliday()) {
            Iterator<WeatherSensitivityDTO> it = weatherSensitivityDTOS.iterator();
            while (it.hasNext()) {
                WeatherSensitivityDTO weatherSensitivityDTO = it.next();
                if (holidays.contains(weatherSensitivityDTO.getDate())) {
                    it.remove();
                }
            }
        }
        if (weatherSensivityRequest.getWeek()) {
            Iterator<WeatherSensitivityDTO> it = weatherSensitivityDTOS.iterator();
            while (it.hasNext()) {
                if (DateUtil.isWeekend(it.next().getDate())) {
                    it.remove();
                }
            }
        }
        commonResp.setDataList(weatherSensitivityDTOS);
        resp.setData(commonResp);
        return resp;
    }

    /**
     * 获取气象的特征和综合指标
     */
    @ApiOperation("获取气象的特征和综合指标")
    @GetMapping("/feature")
    public BaseResp getWeatherFeature(@Validated WeatherSensitivityRequest weatherSensitivityRequest,
                                      BindingResult result) throws Exception {
        validateFormValue(result);
        if (StringUtils.isEmpty(weatherSensitivityRequest.getCityId())) {
            weatherSensitivityRequest.setCityId(this.getLoginCityId());
        }
        if (StringUtils.isBlank(weatherSensitivityRequest.getCaliberId())) {
            weatherSensitivityRequest.setCaliberId(this.getCaliberId());
        }
        BaseResp response = BaseResp.succResp("查询成功");
//        WeatherFeatureCityDayHisDO weatherFeatureCityDayHisVO = weatherFeatureCityDayHisService.findWeatherFeatureCityHisVOByDate(WeatherSensitivityRequest.getCityId(), DateUtils.string2Date(WeatherSensitivityRequest.getTargetDay(), DateFormatType.SIMPLE_DATE_FORMAT_STR));
        /** 应该是通过算法得出结果，暂时跳过 **/  //todo wangchen ???
        WeatherFeatureCityDayHisDO weatherFeatureCityDayHisVO = new WeatherFeatureCityDayHisDO();
        weatherFeatureCityDayHisVO.setMaxWinds(new BigDecimal(60));
        weatherFeatureCityDayHisVO.setLowestTemperature(new BigDecimal(23));
        weatherFeatureCityDayHisVO.setHighestTemperature(new BigDecimal(30));
        weatherFeatureCityDayHisVO.setAveTemperature(new BigDecimal(25));
        weatherFeatureCityDayHisVO.setRainfall(new BigDecimal(30));
        weatherFeatureCityDayHisVO.setHighestEffectiveTemperature(new BigDecimal(23.21));
        weatherFeatureCityDayHisVO.setHighestComfort(new BigDecimal(66.98));
        weatherFeatureCityDayHisVO.setMaxColdness(new BigDecimal(812.18));
        weatherFeatureCityDayHisVO.setHighestHumidity(new BigDecimal(98));
        weatherFeatureCityDayHisVO.setAveTemperatureHumidity(new BigDecimal(30));

        response.setData(weatherFeatureCityDayHisVO);

        if (response.getData() == null) {
            return new BaseResp("T706");
        } else {
            return response;
        }

    }


    /**
     * 极端天气数据更新
     */
    @ApiOperation("极端天气数据更新")
    @RequestMapping(value = "/updateWeatherTyphoon", method = RequestMethod.POST)
    @OperateLog(operate = "更新极端天气数据更新")
    public BaseResp<WeatherTyphoonDefinitionDO> updateWeatherTyphoon(
            @Validated @RequestBody WeatherTyphoonDefinitionDO weatherTyphoonDefinitionVO,
            BindingResult result) throws Exception {
        validateFormValue(result);

        BaseResp resp = BaseResp.succResp();

        if (StringUtils.isEmpty(weatherTyphoonDefinitionVO.getId())) {
            WeatherTyphoonDefinitionDO vo;
            List<WeatherTyphoonDefinitionDO> vos = weatherTyphoonDefinitionService.getWeatherTyphoonVO();
            vo = vos.get(0);
            weatherTyphoonDefinitionVO.setId(vo.getId());
        }
        weatherTyphoonDefinitionVO.setUpdateAt(new Timestamp(System.currentTimeMillis()));

        weatherTyphoonDefinitionVO = weatherTyphoonDefinitionService.doUpdate(weatherTyphoonDefinitionVO);

        resp.setData(weatherTyphoonDefinitionVO);

        return resp;
    }

    /**
     * 极端天气数据查询
     */
    @ApiOperation("极端天气数据查询")
    @RequestMapping(value = "/getWeatherTyphoon", method = RequestMethod.GET)
    public BaseResp<WeatherTyphoonDefinitionDO> getWeatherTyphoon() throws Exception {
        BaseResp resp = BaseResp.succResp();
        WeatherTyphoonDefinitionDO wvo;
        List<WeatherTyphoonDefinitionDO> list = weatherTyphoonDefinitionService.getWeatherTyphoonVO();
        if (CollectionUtils.isEmpty(list)) {
            return new BaseResp("T706");
        }
        wvo = list.get(0);
        resp.setData(wvo);
        return resp;
    }

//    public Object updateCityRate(@RequestBody CityRateRequest cityRateRequest){
//
//        return null;
//    }

    /**
     * 更新全省参照气象(地市占全省的比例)
     */
    @ApiOperation("更新全省参照气象(地市占全省的比例)")
    @RequestMapping(value = "/updateTyphoonRate", method = RequestMethod.POST)
    @OperateLog(operate = "更新全省参照气象")
    public BaseResp<Map> updateTyphoonRate(@RequestBody CityRateRequest request) throws Exception {
        List<RateVO> list = request.getList();
        Map map = new HashMap<>();
        List<String> keyList = new ArrayList();
        List valueList = new ArrayList();
        //更新之前先清空数据库
        List<WeatherTyphoonRateDO> weatherList = weatherTyphoonRateService.getAllTyphoonRate();
        if (weatherList.size() != 0) {
            weatherTyphoonRateService.delete(weatherList);
        }
        Date date1 = DateUtil.getCureDate();
        for (RateVO rateVO : list) {
            WeatherTyphoonRateDO weatherTyphoonRate = new WeatherTyphoonRateDO();
            weatherTyphoonRate.setUpdateAt(new Timestamp(date1.getTime()));
            weatherTyphoonRate.setCityId(rateVO.getCityId());
            BigDecimal rateB = rateVO.getRate();
            BigDecimal rate = rateB.divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP);
            weatherTyphoonRate.setRate(rate);
            weatherTyphoonRate = weatherTyphoonRateService.creat(weatherTyphoonRate);

            keyList.add(weatherTyphoonRate.getCityId());
            String rateS = String.valueOf(rate);
            valueList.add(rateS);

        }
        map.put("cityId", keyList);
        map.put("rate", valueList);

        BaseResp resp = BaseResp.succResp();

        resp.setData(map);

        return resp;

    }


    /**
     * 查询全省参照气象(地市占全省的比例)
     */
    @ApiOperation("查询全省参照气象(地市占全省的比例)")
    @RequestMapping(value = "/fingWeatherTyphoonRate", method = RequestMethod.GET)
    public BaseResp<Map> fingWeatherTyphoonRate() throws Exception {
        BaseResp resp = BaseResp.succResp();
        Map map = weatherTyphoonRateService.findTyphoonRateMap();
        if (map == null || CollectionUtils.isEmpty(map)) {
            return new BaseResp("T706");
        }
        resp.setData(map);
        return resp;
    }


    /**
     * 加载全省参照气象(地市占全省的比例)
     */
    @ApiOperation("加载全省参照气象(地市占全省的比例)")
    @RequestMapping(value = "/getWeatherTyphoonRate", method = RequestMethod.GET)
    public BaseResp<Map> getWeatherTyphoonRate() throws Exception {

        String caliberId = this.getCaliberId();
        //省份id
        String cityId = CityConstants.PROVINCE_ID;
        //String caliberId = paramRequest.getCaliberId();
        BaseResp resp = BaseResp.succResp();
        //获取当前日期的前一天
        String date = DateUtil.getPreDay("yyyy-MM-dd");
        // String date = DateUtil.getCureDateStr("yyyy-MM-dd");

        //先查询查找前一日的日负荷特性最大值发生的时间
        DBQueryParam param = DBQueryParamBuilder.create()
                .where(QueryOp.NumberEqualTo, "cityId", cityId)
                .where(QueryOp.NumberEqualTo, "caliberId", caliberId)
                .where(QueryOp.DateEqualTo, "date", date)
                .build();

        List<LoadFeatureCityDayHisDO> list = loadFeatureCityDayHisService.queryLoadFeatureCityDayHisDO(param)
                .getDatas();
        if (list == null || list.size() < 1) {
            return BaseResp.failResp("日负荷特性数据异常");
        }
        LoadFeatureCityDayHisDO loadFeatureCityDayHisVO = list.get(0);
        //获得最大负荷出现的时间
        String maxTime = loadFeatureCityDayHisVO.getMaxTime();

        //获得最大负荷数值
        BigDecimal maxLoad = new BigDecimal(0);

        //根据时间查找地市历史负荷数据

        String str1 = maxTime.substring(0, 2);
        String str2 = maxTime.substring(3);

        maxTime = "t" + str1 + str2;

        //转换成Int
        //Integer type = Integer.parseInt(maxTime);

        //查询同一时间 各地市的历史负荷对象
        DBQueryParam param1 = DBQueryParamBuilder.create()
                //.queryDataOnly()
                // .selectFields(maxTime)
                .where(QueryOp.DateEqualTo, "date", date)
                .where(QueryOp.NumberEqualTo, "caliberId", caliberId)
                .build();
        List<LoadCityHisDO> list1 = loadCityHisService.queryLoadCityHisDO(param1).getDatas();

        Map loadMap = new HashMap();
        for (LoadCityHisDO loadCityHisVO : list1) {
            if (!loadCityHisVO.getCityId().equals(CityConstants.PROVINCE_ID)) {
                System.out.println(loadCityHisVO);
                Map map = BasePeriodUtils.toMap(loadCityHisVO, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
                Set keySet = map.keySet();
                Iterator it = keySet.iterator();
                while (it.hasNext()) {
                    String time = (String) it.next();
                    if (time.equals(maxTime)) {
                        BigDecimal load = (BigDecimal) map.get(time);
                        maxLoad = maxLoad.add(load);
                        loadMap.put(loadCityHisVO.getCityId(), load);
                    }
                }
            }


        }
        Map result = weatherTyphoonRateService.addTyphoonRateMap(loadMap, maxLoad);
        resp.setData(result);
        return resp;

    }

    @ApiOperation("相似日实际气象")
    @RequestMapping(value = "/similar/weather", method = RequestMethod.GET)
    @OperateLog(operate = "相似日实际气象")
    public BaseResp<WeatherSimilarResp> queryWeather(WeatherSimilarRequest weatherSimilarRequest) throws Exception {
        String cityId = weatherSimilarRequest.getCityId();
        Integer type = weatherSimilarRequest.getType();
        List<Date> dateList = weatherSimilarRequest.getDateList();
        BaseResp baseResp = BaseResp.succResp();
        WeatherSimilarResp weatherSimilarResp = new WeatherSimilarResp();
        List<WeatherCityFcDO> weatherCityFcVOS = weatherCityFcService
                .findWeatherCityFcDOs(cityId, type, weatherSimilarRequest.getTargetDate(),
                        weatherSimilarRequest.getTargetDate());
        if (!CollectionUtils.isEmpty(weatherCityFcVOS)) {
            WeatherCityFcDO weatherCityFcVO = weatherCityFcVOS.get(0);
            weatherSimilarResp.setFcWeatherLoad(BasePeriodUtils
                    .toList(weatherCityFcVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
        }
        if (CollectionUtils.isEmpty(dateList) && dateList.size() < 1) {
            if (weatherSimilarResp.getFcWeatherLoad() == null) {
                return new BaseResp("T706");
            }
            return baseResp;
        }
        //如果是省 则用省会城市的气象
        cityId = cityService.findWeatherCityId(cityId);
//        cityId = cityVO.getId();
        List<WeatherCityHisDO> weatherCityHisVOS = weatherCityHisService.findWeatherByDates(cityId, type, dateList);
        if (CollectionUtils.isEmpty(weatherCityHisVOS)) {
            return new BaseResp("T706");
        }
        List<WeatherSimilar> similarArrayList = new ArrayList<>();
        for (WeatherCityHisDO weatherCityHisVO : weatherCityHisVOS) {
            WeatherSimilar weatherSimilar = new WeatherSimilar();
            weatherSimilar.setDate(weatherCityHisVO.getDate());
            weatherSimilar.setWeatherLoads(BasePeriodUtils.toList(weatherCityHisVO, Constants.LOAD_CURVE_POINT_NUM,
                    Constants.LOAD_CURVE_START_WITH_ZERO));
            similarArrayList.add(weatherSimilar);
        }
        weatherSimilarResp.setWeatherSimilars(similarArrayList);
        baseResp.setData(weatherSimilarResp);
        return baseResp;
    }

    /**
     *
     */
    @ApiOperation("获取天气")
    @GetMapping(value = "/days")
    public BaseResp<WeatherDataResp> findWeather(@ApiParam("城市ID") String cityId, @ApiParam("开始日期") Date startDate,
                                                 @ApiParam("结束日期") Date endDate,
                                                 @ApiParam("气象类型") Integer type,
                                                 @ApiParam("数据类型") String dataType) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        cityId = cityService.findWeatherCityId(cityId);
        List<WeatherCityHisDO> weatherCityHisVOS = weatherCityHisService
                .findWeatherCityHisDOs(cityId, type, startDate, endDate);
        List<WeatherCityFcDO> fcVOS = weatherCityFcService.findWeatherCityFcDOs(cityId, type, startDate, endDate);

        //dataType:1,地市标准气象用地市代表站点气象代替     dataType:2,地市标准站点气象加权平均(仅福建)
        if ("1".equals(dataType)) {
            List<WeatherStationHisBasicWgDO> stationHisBasicWgDOS = weatherStationHisBasicWgService.getStandardStationHisWeatherBasic(cityId, type, startDate, endDate);
            weatherCityHisVOS = PeriodDataUtil.convertList(stationHisBasicWgDOS, WeatherCityHisDO.class);

            List<WeatherStationFcBasicWgDO> stationFcBasicWgDOS = weatherStationFcBasicWgService.getStandardStationFcWeatherBasic(cityId, type, startDate, endDate);
            fcVOS = PeriodDataUtil.convertList(stationFcBasicWgDOS, WeatherCityFcDO.class);
        } else if ("2".equals(dataType) && Constants.PROVINCE_ID.equals(cityId)) {
            weatherCityHisVOS = weatherCityHisService.getProvinceWeightAvgHisWeatherData(type, startDate, endDate);
            fcVOS = weatherCityFcService.getProvinceWeightAvgFcWeatherData(type, startDate, endDate);
        }

        if (weatherCityHisVOS.size() < 1 && fcVOS.size() < 1) {
            return new BaseResp("T706");
        }
        //转map
        Map<Date, WeatherCityHisDO> realMap = weatherCityHisVOS.stream()
                .collect(Collectors.toMap(WeatherCityHisDO::getDate, Function.identity(), (key1, key2) -> key2));
        Map<Date, WeatherCityFcDO> fcMap = fcVOS.stream()
                .collect(Collectors.toMap(WeatherCityFcDO::getDate, Function.identity(), (key1, key2) -> key2));
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        List<BigDecimal> his = new ArrayList<>();
        List<BigDecimal> fc = new ArrayList<>();
        WeatherDataResp dataResp = new WeatherDataResp();
        for (Date date : dateList) {
            WeatherCityHisDO hisVO = realMap.get(date);
            if (hisVO != null) {
                his.addAll(BasePeriodUtils
                        .toList(hisVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            } else {
                his.addAll(LoadCalUtil.getNullList(Constants.LOAD_CURVE_POINT_NUM));
            }
            WeatherCityFcDO fcVO = fcMap.get(date);
            if (fcVO != null) {
                fc.addAll(
                        BasePeriodUtils.toList(fcVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            } else {
                fc.addAll(LoadCalUtil.getNullList(Constants.LOAD_CURVE_POINT_NUM));
            }
        }
        dataResp.setYesterday(this.getYesterdayDataWhenSingleDayQuery(cityId, startDate, endDate, type, dataType));
        dataResp.setForecast(fc);
        dataResp.setReal(his);
        baseResp.setData(dataResp);
        return baseResp;
    }

    @SneakyThrows
    private List<BigDecimal> getYesterdayDataWhenSingleDayQuery(String cityId, Date startDate, Date endDate, Integer type, String dataType) {
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        //只有查询一天数据时才展示昨日实际气象,查询多天时不展示
        if (dateList.size() != 1) {
            return Collections.emptyList();
        }
        Date yesterday = DateUtils.addDays(startDate, -1);
        WeatherCityHisDO yesterdayWeather = new WeatherCityHisDO();
        //查看地市代表站点气象
        if ("1".equals(dataType)) {
            List<WeatherStationHisBasicWgDO> stationHisBasicWgDOS = weatherStationHisBasicWgService.getStandardStationHisWeatherBasic(cityId, type, yesterday, yesterday);
            if (!CollectionUtils.isEmpty(stationHisBasicWgDOS)) {
                WeatherStationHisBasicWgDO weatherStationHisBasicWgDO = stationHisBasicWgDOS.get(0);
                BeanUtils.copyProperties(weatherStationHisBasicWgDO, yesterdayWeather);
            }
        } else {
            yesterdayWeather = weatherCityHisService.findWeatherCityHisDO(cityId, type, yesterday);
        }

        List<BigDecimal> yesterdayLoads = new ArrayList<>();
        if (yesterdayWeather == null) {
            yesterdayLoads.addAll(LoadCalUtil.getNullList(Constants.LOAD_CURVE_POINT_NUM));
        } else {
            yesterdayLoads.addAll(BasePeriodUtils.toList(yesterdayWeather, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
        }
        return yesterdayLoads;
    }

    @PostMapping(value = "/source/fcPoint")
    @ApiOperation(value = "多日不同数据源气象曲线")
    public BaseResp<List<WeatherFcPointDTO>> findSourceWeatherListPoint(
            @RequestBody WeatherSourceFcRequest weatherSourceFcRequest)
            throws Exception {
        String cityId = weatherSourceFcRequest.getCityId().get(0);
        List<WeatherFcPointDTO> weatherFcPointDTOS = weatherSourceFcService
                .findSourceWeatherPointList(cityId, weatherSourceFcRequest.getStartDate(),
                        weatherSourceFcRequest.getEndDate());
        return this.baseResp(weatherFcPointDTOS);
    }

    @PostMapping(value = "/source/fc")
    @ApiOperation(value = "多日不同数据源气象曲线")
    public BaseResp<List<WeatherFcDTO>> findSourceWeatherPoint(
            @RequestBody WeatherSourceFcRequest weatherSourceFcRequest)
            throws Exception {
        List<WeatherFcDTO> sourceWeatherPoint = weatherSourceFcService
                .findSourceWeatherPoint(weatherSourceFcRequest.getCityId(), weatherSourceFcRequest.getSource(),
                        weatherSourceFcRequest.getStartDate(), weatherSourceFcRequest.getEndDate(), ParamConstants.HIS);
        return this.baseResp(sourceWeatherPoint);
    }


    @PostMapping(value = "/source/fcFeature")
    @ApiOperation(value = "多日不同数据源气象特性")
    public BaseResp<List<WeatherFcFeatureDTO>> findSourceWeatherFeature(
            @RequestBody WeatherSourceFcRequest weatherSourceFcRequest)
            throws Exception {
        List<WeatherFcFeatureDTO> sourceWeatherFeature = weatherSourceFcService
                .findSourceWeatherFeature(weatherSourceFcRequest.getCityId(), weatherSourceFcRequest.getSource(),
                        weatherSourceFcRequest.getStartDate(), weatherSourceFcRequest.getEndDate());
        return this.baseResp(sourceWeatherFeature);
    }


    @PostMapping(value = "/source/fcAccuracy")
    @ApiOperation(value = "多日不同数据源气象准确率")
    public BaseResp<List<WeatherFcAccuracyDTO>> findSourceWeatherAccuracy(
            @RequestBody WeatherSourceFcAccuracyRequest weatherSourceFcAccuracyRequest)
            throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<WeatherFcAccuracyDTO> sourceWeatherAccuracy = weatherSourceFcService
                .findSourceWeatherAccuracy(weatherSourceFcAccuracyRequest.getCityId(),
                        weatherSourceFcAccuracyRequest.getSource()
                        , weatherSourceFcAccuracyRequest.getStartDate(), weatherSourceFcAccuracyRequest.getEndDate());
        baseResp.setData(sourceWeatherAccuracy);
        return baseResp;
    }

    @PostMapping(value = "/source/fcAccuracyAvg")
    @ApiOperation(value = "多日不同数据源气象平均准确率")
    public BaseResp<List<WeatherFcAccuracyDTO>> findSourceWeatherAccuracyAvg(
            @RequestBody WeatherSourceFcAccuracyRequest weatherSourceFcAccuracyRequest)
            throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<WeatherFcAccuracyDTO> sourceWeatherAccuracy = weatherSourceFcService
                .findSourceWeatherAccuracy(weatherSourceFcAccuracyRequest.getCityId(),
                        weatherSourceFcAccuracyRequest.getSource()
                        , weatherSourceFcAccuracyRequest.getStartDate(), weatherSourceFcAccuracyRequest.getEndDate());
        List<WeatherFcAccuracyDTO> WeatherAccuracyList = new ArrayList<>();
        Map<String, List<WeatherFcAccuracyDTO>> map = sourceWeatherAccuracy.stream()
                .collect(Collectors.groupingBy(WeatherFcAccuracyDTO::getType));
        for (WeatherNewEnum weatherEnum : WeatherNewEnum.values()) {
            List<WeatherFcAccuracyDTO> weatherFcAccuracyDTOS = map.get(String.valueOf(weatherEnum.getType()));
            if (weatherFcAccuracyDTOS != null && weatherFcAccuracyDTOS.size() > 0) {
                Map<String, List<WeatherFcAccuracyDTO>> sourceMap = weatherFcAccuracyDTOS.stream()
                        .collect(Collectors.groupingBy(WeatherFcAccuracyDTO::getSource));
                for (String source : weatherSourceFcAccuracyRequest.getSource()) {
                    List<WeatherFcAccuracyDTO> fcAccuracyDTOS = sourceMap.get(source);
                    List<BigDecimal> list = new ArrayList<>();
                    WeatherFcAccuracyDTO weatherFcAccuracyDTO = new WeatherFcAccuracyDTO();
                    if (fcAccuracyDTOS != null && fcAccuracyDTOS.size() > 0) {
                        for (WeatherFcAccuracyDTO fcAccuracyDTO : fcAccuracyDTOS) {
                            weatherFcAccuracyDTO.setCityId(fcAccuracyDTO.getCityId());
                            weatherFcAccuracyDTO.setCityName(fcAccuracyDTO.getCityName());
                            list.add(fcAccuracyDTO.getAccuracy());
                        }
                        Map<String, BigDecimal> maxMinAvg = BasePeriodUtils.getMaxMinAvg(list, 4);
                        if (maxMinAvg != null && maxMinAvg.size() > 0) {
                            BigDecimal accuracy = maxMinAvg.get("avg");
                            weatherFcAccuracyDTO.setAccuracy(accuracy);
                        }
                    }
                    weatherFcAccuracyDTO.setSource(source);
                    weatherFcAccuracyDTO.setType(String.valueOf(weatherEnum.getType()));
                    WeatherAccuracyList.add(weatherFcAccuracyDTO);
                }
            }

        }
        baseResp.setData(WeatherAccuracyList);
        return baseResp;
    }


    /**
     * 功能描述: <br> 导出全部城市的气象数据
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    @GetMapping(value = "/exportAllPoint")
    @ApiOperation(value = "导出全部城市的气象数据")
    public BaseResp exportAllPoint(HttpServletResponse response, @ApiParam("开始日期") Date startDate,
                                   @ApiParam("结束日期") Date endDate) throws Exception {
        OutputStream outputStream = response.getOutputStream();
        List<CityDO> cityS = cityService.findAllCitys();
        List<CityDO> cityDOS = cityS.stream().filter(s -> !(s.getId().equals("2"))).collect(Collectors.toList());
        List<String> cityIds = new ArrayList<>();
        for (CityDO cityDO : cityDOS) {
            cityIds.add(cityDO.getId());
        }
        List<String> sources = Arrays.asList(new String[]{"1", "2"});
        List<String> list = new ArrayList<>(sources);
        List<WeatherFcDTO> pointS = weatherSourceFcService
                .findSourceWeatherPoint(cityIds, list, startDate, endDate, ParamConstants.HIS);
        //写数据到excel
        XSSFWorkbook wk = new XSSFWorkbook();
        XSSFSheet sheet = wk.createSheet();
        sheet.setDefaultColumnWidth(20);
        XSSFCellStyle cellStyle = wk.createCellStyle();
        XSSFRow row = sheet.createRow(0);
        row.createCell(0).setCellValue("单位");
        row.createCell(1).setCellValue("日期");
        row.createCell(2).setCellValue("气象源");
        row.createCell(3).setCellValue("气象类型");
        List<String> columns = ColumnUtil
                .getColumns(Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO, true);
        for (int j = 0; j < 96; j++) {
            row.createCell(j + 4).setCellValue(columns.get(j));
        }
        int i = 1;
        if (pointS != null && pointS.size() > 0) {
            for (WeatherFcDTO weatherFcDTO : pointS) {
                XSSFRow row1 = sheet.createRow(i);
                if (weatherFcDTO.getPoint() != null && weatherFcDTO.getPoint().size() > 0) {
                    row1.createCell(0).setCellValue(weatherFcDTO.getCityName());
                    row1.createCell(1).setCellValue(DateUtil.getDateToStr(weatherFcDTO.getDate()));
                    row1.createCell(2).setCellValue(weatherFcDTO.getSourceName());
                    row1.createCell(3).setCellValue(WeatherNewEnum.getValueByName(
                            Integer.valueOf(weatherFcDTO.getType())));
                    List<BigDecimal> points = weatherFcDTO.getPoint();
                    for (int n = 0; n < points.size(); n++) {
                        BigDecimal point = points.get(n);
                        if (point != null) {
                            row1.createCell(n + 4).setCellValue(String.valueOf(point));
                        } else {
                            row1.createCell(n + 4).setCellValue("");
                        }

                    }
                    i++;
                }
            }
        }
        String file_name = "城市气象数据.xlsx";
        response.setContentType("application/x-download");
        file_name = new String(file_name.getBytes(), "ISO-8859-1");
        response.setHeader("Content-disposition", "attachment; filename=" + file_name);
        //response.setContentType("application/msexcel");
        wk.write(outputStream);
        outputStream.close();
        return BaseResp.succResp();
    }

    /**
     * 功能描述: <br> 导出全部城市的气象数据
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    @GetMapping(value = "/exportWeatherFeature")
    @ApiOperation(value = "导出全部城市的气象特性")
    public BaseResp exportWeatherFeature(HttpServletResponse response, @ApiParam("开始日期") Date startDate,
                                         @ApiParam("结束日期") Date endDate) throws Exception {
        OutputStream outputStream = response.getOutputStream();
        List<CityDO> cityS = cityService.findAllCitys();
        List<CityDO> cityDOS = cityS.stream().filter(s -> !(s.getId().equals("2"))).collect(Collectors.toList());
        List<String> cityIds = new ArrayList<>();
        for (CityDO cityDO : cityDOS) {
            cityIds.add(cityDO.getId());
        }
        List<String> sources = Arrays.asList(new String[]{"1", "2"});
        List<String> list = new ArrayList<>(sources);
        List<WeatherFcFeatureDTO> weatherFcFeatureDTOS = weatherSourceFcService
                .findSourceWeatherFeature(cityIds, list, startDate, endDate);
        //写数据到excel
        XSSFWorkbook wk = new XSSFWorkbook();
        XSSFSheet sheet = wk.createSheet();
        sheet.setDefaultColumnWidth(20);
        XSSFCellStyle cellStyle = wk.createCellStyle();
        XSSFRow row = sheet.createRow(0);
        row.createCell(0).setCellValue("单位");
        row.createCell(1).setCellValue("日期");
        row.createCell(2).setCellValue("气象源");
        row.createCell(3).setCellValue("最高温度");
        row.createCell(4).setCellValue("最低温度");
        row.createCell(5).setCellValue("平均温度");
        row.createCell(6).setCellValue("最大湿度");
        row.createCell(7).setCellValue("最小湿度");
        row.createCell(8).setCellValue("平均湿度");
        row.createCell(9).setCellValue("累积降水量");
        row.createCell(10).setCellValue("最大风速");
        row.createCell(11).setCellValue("最小风速");
        row.createCell(12).setCellValue("平均风速");
        int i = 1;
        if (weatherFcFeatureDTOS != null && weatherFcFeatureDTOS.size() > 0) {
            for (WeatherFcFeatureDTO weatherFcFeatureDTO : weatherFcFeatureDTOS) {
                XSSFRow row1 = sheet.createRow(i);
                if (weatherFcFeatureDTO.getAccumulatePrecipitation() != null
                        || weatherFcFeatureDTO.getHumidityAvg() != null || weatherFcFeatureDTO.getSpeedMax() != null
                        || weatherFcFeatureDTO.getTemperatureMax() != null) {
                    row1.createCell(0).setCellValue(weatherFcFeatureDTO.getCityName());
                    row1.createCell(1).setCellValue(DateUtil.getDateToStr(weatherFcFeatureDTO.getDate()));
                    row1.createCell(2).setCellValue(weatherFcFeatureDTO.getSourceName());
                    if (weatherFcFeatureDTO.getTemperatureMax() != null) {
                        row1.createCell(3).setCellValue(String.valueOf(weatherFcFeatureDTO.getTemperatureMax()));
                    } else {
                        row1.createCell(3).setCellValue("");
                    }
                    if (weatherFcFeatureDTO.getTemperatureMin() != null) {
                        row1.createCell(4).setCellValue(String.valueOf(weatherFcFeatureDTO.getTemperatureMin()));
                    } else {
                        row1.createCell(4).setCellValue("");
                    }
                    if (weatherFcFeatureDTO.getTemperatureAvg() != null) {
                        row1.createCell(5).setCellValue(String.valueOf(weatherFcFeatureDTO.getTemperatureAvg()));
                    } else {
                        row1.createCell(5).setCellValue("");
                    }

                    if (weatherFcFeatureDTO.getHumidityMax() != null) {
                        row1.createCell(6).setCellValue(String.valueOf(weatherFcFeatureDTO.getHumidityMax()));
                    } else {
                        row1.createCell(6).setCellValue("");
                    }
                    if (weatherFcFeatureDTO.getHumidityMin() != null) {
                        row1.createCell(7).setCellValue(String.valueOf(weatherFcFeatureDTO.getHumidityMin()));
                    } else {
                        row1.createCell(7).setCellValue("");
                    }
                    if (weatherFcFeatureDTO.getHumidityAvg() != null) {
                        row1.createCell(8).setCellValue(String.valueOf(weatherFcFeatureDTO.getHumidityAvg()));
                    } else {
                        row1.createCell(8).setCellValue("");
                    }
                    if (weatherFcFeatureDTO.getAccumulatePrecipitation() != null) {
                        row1.createCell(9)
                                .setCellValue(String.valueOf(weatherFcFeatureDTO.getAccumulatePrecipitation()));
                    } else {
                        row1.createCell(9).setCellValue("");
                    }
                    if (weatherFcFeatureDTO.getSpeedMax() != null) {
                        row1.createCell(10).setCellValue(String.valueOf(weatherFcFeatureDTO.getSpeedMax()));
                    } else {
                        row1.createCell(10).setCellValue("");
                    }
                    if (weatherFcFeatureDTO.getSpeedMin() != null) {
                        row1.createCell(11).setCellValue(String.valueOf(weatherFcFeatureDTO.getSpeedMin()));
                    } else {
                        row1.createCell(11).setCellValue("");
                    }
                    if (weatherFcFeatureDTO.getSpeedAvg() != null) {
                        row1.createCell(12).setCellValue(String.valueOf(weatherFcFeatureDTO.getSpeedAvg()));
                    }
                    i++;
                }
            }
        }
        String file_name = "城市气象特性数据.xlsx";
        response.setContentType("application/x-download");
        file_name = new String(file_name.getBytes(), "ISO-8859-1");
        response.setHeader("Content-disposition", "attachment; filename=" + file_name);
        wk.write(outputStream);
        outputStream.close();
        return BaseResp.succResp();
    }

    /**
     * @param cityId
     * @param startDate
     * @param endDate
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/algorithm/days")
    public BaseResp findWeather(String cityId, Date startDate, Date endDate, Integer type, String algorithmId, String caliberId) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        cityId = cityService.findWeatherCityId(cityId);
        List<WeatherCityHisDO> weatherCityHisVOS = weatherCityHisService.findWeatherCityHisDOs(cityId, type, startDate, endDate);
        List<WeatherCityFcDO> fcVOS = weatherCityFcService.findWeatherCityHisDOs(Arrays.asList(cityId), type, startDate, endDate);
//        List<WeatherCityFcLoadForecastDO> fcVOS = weatherCityFcLoadForecastService.findWeatherInfoFcLoadForecast(cityId, type, algorithmId,startDate, endDate,caliberId);
        if (weatherCityHisVOS.size() < 1 && fcVOS.size() < 1) {
            return new BaseResp("T706");
        }
        //转map
        Map<Date, WeatherCityHisDO> realMap = weatherCityHisVOS.stream().collect(Collectors.toMap(WeatherCityHisDO::getDate, Function.identity(), (key1, key2) -> key2));
        Map<Date, List<WeatherCityFcDO>> fcMap = fcVOS.stream()
                .collect(Collectors.groupingBy(WeatherCityFcDO::getDate));
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        List<BigDecimal> his = new ArrayList<>();
        List<BigDecimal> fc = new ArrayList<>();
        WeatherDataResp dataResp = new WeatherDataResp();
        for (Date date : dateList) {
            WeatherCityHisDO hisVO = realMap.get(date);
            if (hisVO != null) {
                his.addAll(BasePeriodUtils.toList(hisVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            } else {
                his.addAll(LoadCalUtil.getNullList(Constants.LOAD_CURVE_POINT_NUM));
            }
            List<WeatherCityFcDO> fcVO = fcMap.get(date);
            if (fcVO != null) {
                fc.addAll(BasePeriodUtils.toList(fcVO.get(0), Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            } else {
                fc.addAll(LoadCalUtil.getNullList(Constants.LOAD_CURVE_POINT_NUM));
            }
        }
        dataResp.setForecast(fc);
        dataResp.setReal(his);
        baseResp.setData(dataResp);
        return baseResp;
    }

    @PostMapping(value = "/parseGridWeatherData")
    @ApiOperation(value = "解析网格气象数据")
    public BaseResp parseGridWeatherData() throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        //读取文件
        GridWeatherResolver gridWeatherResolver = new Diamond4GridWeatherResolver();
        String filePath = "C:\\Users\\<USER>\\Desktop\\福建08019点格点数据.txt";
        File file = new File(filePath);
        if (!gridWeatherResolver.isMatch(file)) {
            throw TsieExceptionUtils.newBusinessException("文件格式不匹配" + file.getName());
        }
        //解析文件
        GridWeather gridWeather = gridWeatherResolver.resolveFile(file);
        //处理数据
        weatherStationHisClctWgService.doParseGridHisWeatherData(gridWeather, 2);
        return baseResp;
    }

    @ApiOperation("气象从采集表到展示表")
    @RequestMapping(value = "/clctToBasic", method = RequestMethod.GET)
    public BaseResp clctToBasic(Date startDate, Date endDate, String stationWgId) {

        List<String> stationWgIdList = new ArrayList<>();
        stationWgIdList.addAll(standardWgStationMap.values());
//        if (StringUtils.isBlank(stationWgId)) {
//            LambdaQueryWrapper<BaseWeatherStationWgDO> queryWrapper = new LambdaQueryWrapper<>();
//            List<BaseWeatherStationWgDO> baseWeatherStationWgDOS = baseWeatherStationWgService.list(queryWrapper);
//            stationWgIdList = baseWeatherStationWgDOS.stream().map(BaseWeatherStationWgDO::getId).collect(Collectors.toList());
//        } else {
//            stationWgIdList.add(stationWgId);
//        }

        List<Date[]> dates = DateUtil.splitDateRange(startDate, endDate, 10);
        for (Date[] dateArr : dates) {
            weatherStationHisClctWgService.clctToBasic(dateArr[0], dateArr[1], stationWgIdList);
        }
        return BaseResp.succResp();
    }

    @ApiOperation("测试-通过格点气象站数据计算城市气象数据")
    @RequestMapping(value = "/doWgStationWeatherToCity", method = RequestMethod.GET)
    public BaseResp test(String cityId, Integer weatherType, Date startDate, Date endDate) throws Exception {
        weatherCityHisService.wgStationHisWeatherToCity(cityId, weatherType, startDate, endDate);
        return BaseResp.succResp();
    }

    @ApiOperation("测试-使用电科院气象补充网格气象")
    @RequestMapping(value = "/fillWgWeatherFcByDky", method = RequestMethod.GET)
    public BaseResp fillWgWeatherFcByDky(WeatherSourceFcRequest weatherSourceFcRequest) throws Exception {
        weatherStationFcBasicWgService.fillMissingFcDataWithDiankeyuan(weatherSourceFcRequest.getCityId(), weatherSourceFcRequest.getStartDate(), weatherSourceFcRequest.getEndDate());
        return BaseResp.succResp();
    }

}
