/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  wangchen Date:  2020/6/11 9:52 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.forecast.controller;


import com.tsintergy.lf.serviceapi.algorithm.api.DayMaxLoadForecastService;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.base.dto.SkipHolidayDTO;
import com.tsintergy.lf.serviceapi.base.forecast.api.AlgorithmService;
import com.tsintergy.lf.serviceapi.base.forecast.api.ForecastService;
import com.tsintergy.lf.serviceapi.base.forecast.bean.DuplicateForecastException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmDO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/6/11 
 * @since 1.0.0
 */
@Component
public class ForecastManager {

    private static final Logger logger = LoggerFactory.getLogger(ForecastManager.class);

    /**
     * 是否可以预测(true:可以预测；false:不能预测）
     */
    private static final Map<String, Boolean> forecastMap = new ConcurrentHashMap<>();

    @Autowired
    private ForecastService forecastService;

    @Autowired
    private HolidayService holidayService;

    @Autowired
    private DayMaxLoadForecastService dayMaxLoadForecastService;

    @Autowired
    private AlgorithmService algorithmService;

    /**
     * 查看是否可以预测
     * @return
     */
    public static boolean isForecast(String userId) {
        Boolean isForecast = forecastMap.get(userId);
        if (isForecast == null || isForecast) {
            return true;
        }
        return false;
    }

    /**
     * 修改用户对应为预测状态
     * @param userId
     */
    public void forecast(String userId, String cityId, String caliberId, Date startDate, Date endDate,
        List<AlgorithmEnum> algorithmEnums, String weatherCode) throws Exception {
        if (isForecast(userId)) {
            forecastMap.put(userId, false);
            Thread thread = new Thread(new Runnable() {
                @Override
                public void run() {
                    try {
//                        SkipHolidayDTO skipHolidayDTO = holidayService.findSkipHolidays(startDate, endDate);
//                        forecastService
//                            .doForecastNormal(cityId, caliberId, skipHolidayDTO.getNormalDays(), algorithmEnums,
//                                weatherCode);
                        for (AlgorithmEnum algorithmEnum : algorithmEnums) {
                            AlgorithmDO algorithmVOByPk = algorithmService.findAlgorithmVOByPk(algorithmEnum.getId());
                            dayMaxLoadForecastService.do96LoadForecast(startDate, endDate,cityId,caliberId,algorithmVOByPk,true,null,null,false);
                        }

                    } catch (Exception e) {
                        logger.error("预测失败", e);
                    } finally {
                        forecastMap.put(userId, true);
                    }
                }
            });
            thread.start();
        } else {
            throw new DuplicateForecastException();
        }
    }


}
