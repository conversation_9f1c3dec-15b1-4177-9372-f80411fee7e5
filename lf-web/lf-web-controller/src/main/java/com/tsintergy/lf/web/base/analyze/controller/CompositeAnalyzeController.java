/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司 Author:  wangchen Date:  2018/8/6 15:08 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.analyze.controller;


import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.enums.AnalyzeEnum;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.analyze.dto.CompositePowerDTO;
import com.tsintergy.lf.serviceapi.base.analyze.dto.CompositeRequestDTO;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.common.enumeration.CityEnum;
import com.tsintergy.lf.serviceapi.base.forecast.api.SimilarDayService;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.api.RollingLoadService;
import com.tsintergy.lf.serviceapi.base.load.pojo.RollingLoadFeatureDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.web.base.analyze.request.CompositeRequest;
import com.tsintergy.lf.web.base.analyze.response.GDLoadResponse;
import com.tsintergy.lf.web.base.analyze.response.GDLoadVO;
import com.tsintergy.lf.web.base.analyze.response.SimilarComparisonResp;
import com.tsintergy.lf.web.base.analyze.response.SimilarPowerResp;
import com.tsintergy.lf.web.base.analyze.response.SimilarRainfallResp;
import com.tsintergy.lf.web.base.analyze.response.SimilarTemperatureResp;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 特性分析----综合相似分析
 *
 * <AUTHOR>
 * @create 2020/4/18
 * @since 1.0.0
 */
@Api(tags = "特性分析")
@RequestMapping("/analyze")
@RestController
public class CompositeAnalyzeController extends CommonBaseController {

    @Autowired
    private SimilarDayService similarDayService;

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Autowired
    private WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    private RollingLoadService rollingLoadService;

    @Autowired
    private CityService cityService;


    /**
     * 特性分析-综合相似分析
     *
     * <AUTHOR>
     */
    @ApiOperation(value = "特性分析-综合相似分析")
    @RequestMapping(value = "/composite", method = RequestMethod.POST)
    public BaseResp<List<CompositePowerDTO>> getCompositeSimilar(@RequestBody CompositeRequest request)
        throws Exception {
        BaseResp resp = BaseResp.succResp();
        CompositeRequestDTO requestDTO = new CompositeRequestDTO();
        String cityId = request.getCityId();
        String caliberId = request.getCaliberId();
        if (StringUtils.isBlank(caliberId)) {
            caliberId = getCaliberId();
        }
        BeanUtils.copyProperties(request, requestDTO);
        Date startDate = DateUtils.string2Date(request.getStartDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR);
        Date endDate = DateUtils.string2Date(request.getEndDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR);
        Date basicDate = DateUtils.string2Date(request.getBasicDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR);
        if (endDate.after(basicDate) || endDate.equals(basicDate)) {
            resp.setRetCode("T0");
            resp.setRetMsg("搜索结束日必须要在查找日前一天或者更早！");
            return resp;
        }

        List<Date> listBetweenDay = DateUtil.getListBetweenDay(startDate, endDate);
        if (listBetweenDay.size() < request.getPageSize()) {
            resp.setRetCode("T0");
            resp.setRetMsg("搜索日期过少，最少选择10天");
            return resp;
        }
        requestDTO.setBasicDate(basicDate);
        requestDTO.setCompute(AnalyzeEnum.getValue(request.getCompute()));
        requestDTO.setCityId(cityId);
        requestDTO.setCaliberId(caliberId);
        requestDTO.setEndDate(endDate);
        requestDTO.setStartDate(startDate);
        requestDTO.setUserId(super.getLoginUserId());
        List<CompositePowerDTO> result = null;
        try {
            result = this.similarDayService.listSimilarComposite(requestDTO);
        } catch (Exception e) {
            resp.setRetCode("T0");
            resp.setRetMsg("目标日期数据不足，详情请关注算法输出文件");
            return resp;
        }
        if (CollectionUtils.isEmpty(result)) {
            resp.setRetCode("T706");
            resp.setRetMsg("查询数据为空");
            return resp;
        }
        resp.setData(result);
        return resp;
    }

    /**
     * 特性分析-对比展示
     */
    @ApiOperation(value = "特性分析-对比展示")
    @RequestMapping(value = "/comparison", method = RequestMethod.GET)
    public BaseResp<SimilarComparisonResp> getComparison(@RequestParam @ApiParam(value = "基本日") Date basicDate, @RequestParam @ApiParam(value = "相似日") Date similarDate,
        @RequestParam(required = false)@ApiParam(value = "口径id") String caliberId) throws Exception {
        String cityId = super.getLoginCityId();
        String weatherCityId = this.cityService.findWeatherCityId(cityId);
        if (caliberId == null) {
            caliberId = super.getCaliberId();
        }
        SimilarComparisonResp result = new SimilarComparisonResp();
        List<SimilarPowerResp> similarPower = this.getSimilarPower(cityId, caliberId, basicDate, similarDate);
        List<SimilarRainfallResp> similarRainFall = this.getSimilarRainFall(weatherCityId, basicDate, similarDate);
        List<SimilarTemperatureResp> similarTemperature = this
            .getSimilarTemperature(weatherCityId, basicDate, similarDate);
        result.setLoad(similarPower);
        result.setRainfall(similarRainFall);
        result.setTemperature(similarTemperature);
        BaseResp resp = BaseResp.succResp();
        if (CollectionUtils.isEmpty(similarPower)) {
            resp.setRetCode("T706");
            resp.setRetMsg("查询数据为空");
            return resp;
        }
        resp.setData(result);
        return resp;
    }

    @GetMapping("/getGDLoad")
    @ApiOperation(value = "获取负荷")
    public  BaseResp<GDLoadVO>  getRollingLoadDetail(@ApiParam(value = "日期") String date) throws Exception {
        BaseResp baseResp = null;
        if (StringUtils.isEmpty(date)) {
            return BaseResp.failResp("日期不能为空");
        }
        Date startDate = DateUtil.getFirstDayDateOfMonth(DateUtils.string2Date(date, DateFormatType.YEAR_MONTH_STR));
        Date endDate = DateUtil.getLastDayOfMonth(DateUtils.string2Date(date, DateFormatType.YEAR_MONTH_STR));
        List<RollingLoadFeatureDO> lastYearGDLoad = rollingLoadService
            .findByDate(DateUtils.addYears(startDate, -1), DateUtils.addYears(endDate, -1));
        Map<Date, List<RollingLoadFeatureDO>> lastYearGDCollect = lastYearGDLoad.stream()
            .collect(Collectors.groupingBy(RollingLoadFeatureDO::getDate));
        List<RollingLoadFeatureDO> byDate = rollingLoadService.findByDate(startDate, endDate);
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisVO = weatherFeatureCityDayHisService
            .findWeatherFeatureCityDayHisDO(
                Arrays.asList(CityEnum.henan.getId()), startDate, endDate);
        List<WeatherFeatureCityDayHisDO> lastYearWeatherFeatureCityDayHisVO = weatherFeatureCityDayHisService
            .findWeatherFeatureCityDayHisDO(Arrays.asList(CityEnum.henan.getId()),
                DateUtils.addYears(startDate, -1), DateUtils.addYears(endDate, -1));
        Map<Date, List<RollingLoadFeatureDO>> load = byDate.stream()
            .collect(Collectors.groupingBy(RollingLoadFeatureDO::getDate));
        Map<java.sql.Date, List<WeatherFeatureCityDayHisDO>> weather = weatherFeatureCityDayHisVO.stream()
            .collect(Collectors.groupingBy(WeatherFeatureCityDayHisDO::getDate));
        List<GDLoadResponse> result = new ArrayList<>();
        while (startDate.before(DateUtils.addDays(endDate, 1))) {
            //保存去年今天滚动负荷数据
            RollingLoadFeatureDO lastYearGDLoadVO = null;
            List<RollingLoadFeatureDO> lastYearGDData = lastYearGDCollect.get(DateUtils.addYears(startDate, -1));
            if (CollectionUtils.isNotEmpty(lastYearGDData)) {
                lastYearGDLoadVO = lastYearGDData.get(0);
            }
            GDLoadResponse gdLoadResponse = new GDLoadResponse();
            List<RollingLoadFeatureDO> gunDongLoadFeatureVOS = load.get(startDate);
            List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisVOS = weather.get(startDate);
            if (CollectionUtils.isNotEmpty(gunDongLoadFeatureVOS)) {
                RollingLoadFeatureDO gunDongLoadFeatureVO = gunDongLoadFeatureVOS.get(0);
                if (gunDongLoadFeatureVO != null) {
                    gdLoadResponse.setCityFireLoad20PointLoad(gunDongLoadFeatureVO.getFire20PointLoad());
                    gdLoadResponse.setCityWaterLoad20PointLoad(gunDongLoadFeatureVO.getWater20PointLoad());
                    gdLoadResponse.setCityWindload20PointLoad(gunDongLoadFeatureVO.getWind20PointLoad());
                    gdLoadResponse.setTongdiaoBuyElectric(gunDongLoadFeatureVO.getProvinceLoadEnergy());
                    gdLoadResponse.setTongdiaoMax(gunDongLoadFeatureVO.getTongDiaoMax());
                    gdLoadResponse.setTongdiaoMin(gunDongLoadFeatureVO.getTongDiaoMin());
                    gdLoadResponse.setZhongdiaoMax(gunDongLoadFeatureVO.getZhongDiaoMax());
                    gdLoadResponse.setZhongdiaoMin(gunDongLoadFeatureVO.getZhongDiaoMin());
                    gdLoadResponse.setXidianLoadMax(gunDongLoadFeatureVO.getXiLiSanLoadMax());
                    gdLoadResponse.setXidianLoadMin(gunDongLoadFeatureVO.getXiLiSanLoadMin());

                    //计算中调峰谷和峰谷比
                    if (gdLoadResponse.getZhongdiaoMax() != null && gdLoadResponse.getZhongdiaoMin() != null) {
                        gdLoadResponse.setZhongdiaoPeak(
                            gdLoadResponse.getZhongdiaoMax().subtract(gdLoadResponse.getZhongdiaoMin()));
                        gdLoadResponse.setZhongdiaoPeakRate(gdLoadResponse.getZhongdiaoMin()
                            .divide(gdLoadResponse.getZhongdiaoMax(), 3, BigDecimal.ROUND_HALF_UP));
                    }

                    //计算统调峰谷和峰谷比
                    if (gdLoadResponse.getTongdiaoMax() != null && gdLoadResponse.getTongdiaoMin() != null) {
                        gdLoadResponse
                            .setTongdiaoPeak(gdLoadResponse.getTongdiaoMax().subtract(gdLoadResponse.getTongdiaoMin()));
                        gdLoadResponse.setTongdiaoPeakRate(gdLoadResponse.getTongdiaoMin()
                            .divide(gdLoadResponse.getTongdiaoMax(), 2, BigDecimal.ROUND_HALF_UP));
                    }

                    //计算电量同比
                    if (lastYearGDLoadVO != null && lastYearGDLoadVO.getProvinceLoadEnergy() != null) {
                        BigDecimal lastYearBuyElectric = lastYearGDLoadVO.getProvinceLoadEnergy();
                        BigDecimal tongdiaoBuyElectric = gdLoadResponse.getTongdiaoBuyElectric();
                        BigDecimal multiply = (tongdiaoBuyElectric.subtract(lastYearBuyElectric))
                            .divide(lastYearBuyElectric, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
                        gdLoadResponse.setElectricOfYear(multiply);
                    }

                    //合计
                    gdLoadResponse.setUseHour(null);
                    if (gdLoadResponse.getCityFireLoad20PointLoad() == null
                        && gdLoadResponse.getCityWaterLoad20PointLoad() == null
                        && gdLoadResponse.getCityWindload20PointLoad() == null) {
                        gdLoadResponse.setTotalCityLoad20PointLoad(null);
                    } else {
                        BigDecimal a = gdLoadResponse.getCityWindload20PointLoad() == null ? BigDecimal.ZERO
                            : gdLoadResponse.getCityWindload20PointLoad();
                        BigDecimal b = gdLoadResponse.getCityWaterLoad20PointLoad() == null ? BigDecimal.ZERO
                            : gdLoadResponse.getCityWaterLoad20PointLoad();
                        BigDecimal c = gdLoadResponse.getCityFireLoad20PointLoad() == null ? BigDecimal.ZERO
                            : gdLoadResponse.getCityFireLoad20PointLoad();
                        gdLoadResponse.setTotalCityLoad20PointLoad(a.add(b).add(c));
                    }

                    //西电负荷峰谷比
                    if (gdLoadResponse.getXidianLoadMax() != null && gdLoadResponse.getXidianLoadMin() != null) {
                        gdLoadResponse.setXidianLoadPeakRate(gdLoadResponse.getXidianLoadMin()
                            .divide(gdLoadResponse.getXidianLoadMax(), 3, BigDecimal.ROUND_HALF_UP));
                    }
                    //错峰电量
                    gdLoadResponse.setPeakElectric(BigDecimal.ZERO);
                    gdLoadResponse.setTotalPeak(BigDecimal.ZERO);
                    gdLoadResponse.setUserConscious(BigDecimal.ZERO);
                    gdLoadResponse.setNetLimited(BigDecimal.ZERO);
                    gdLoadResponse.setGap(BigDecimal.ZERO);

                    if (gdLoadResponse.getTongdiaoMax() != null) {
                        gdLoadResponse.setTongdiaoLoadMax(gdLoadResponse.getTongdiaoMax());
                        if (lastYearGDLoadVO != null && lastYearGDLoadVO != null) {
                            BigDecimal multiply = gdLoadResponse.getTongdiaoMax()
                                .subtract(lastYearGDLoadVO.getTongDiaoMax())
                                .divide(lastYearGDLoadVO.getTongDiaoMax(), 4, BigDecimal.ROUND_HALF_UP)
                                .multiply(new BigDecimal(100));
                            gdLoadResponse.setTongdiaoLoadMaxOfYear(multiply);
                        }
                    }

                    if (gdLoadResponse.getTongdiaoMin() != null) {
                        gdLoadResponse.setTongdiaoLoadMin(gdLoadResponse.getTongdiaoMin());
                        if (lastYearGDLoadVO != null && lastYearGDLoadVO.getTongDiaoMin() != null) {
                            BigDecimal multiply = gdLoadResponse.getTongdiaoMin()
                                .subtract(lastYearGDLoadVO.getTongDiaoMin())
                                .divide(lastYearGDLoadVO.getTongDiaoMin(), 4, BigDecimal.ROUND_HALF_UP)
                                .multiply(new BigDecimal(100));
                            gdLoadResponse.setTongdiaoLoadMinOfYear(multiply);
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(weatherFeatureCityDayHisVOS)) {
                WeatherFeatureCityDayHisDO featureCityDayHisVO = weatherFeatureCityDayHisVOS.get(0);
                if (featureCityDayHisVO != null) {
                    gdLoadResponse.setMaxTemp(featureCityDayHisVO.getHighestTemperature());
                    gdLoadResponse.setRain(featureCityDayHisVO.getRainfall());
                }
            }
            String weekByDate = DateUtil.getDayOfWeekByDate(startDate);
            gdLoadResponse.setWeek(weekByDate);
            gdLoadResponse.setDate(DateUtils.date2String(startDate, DateFormatType.SIMPLE_DATE_CHINESE));
            result.add(gdLoadResponse);
            startDate = DateUtils.addDays(startDate, 1);
        }

        GDLoadResponse gdLoadResponseAvg = new GDLoadResponse();
        gdLoadResponseAvg.setDate("平均");
        gdLoadResponseAvg.setZhongdiaoMax(doubleListAvg(
            result.stream().map(t -> t.getZhongdiaoMax()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseAvg.setZhongdiaoMin(doubleListAvg(
            result.stream().map(t -> t.getZhongdiaoMin()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseAvg.setTongdiaoMax(doubleListAvg(
            result.stream().map(t -> t.getTongdiaoMax()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseAvg.setTongdiaoMin(doubleListAvg(
            result.stream().map(t -> t.getTongdiaoMin()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseAvg.setTongdiaoPeak(doubleListAvg(
            result.stream().map(t -> t.getTongdiaoPeak()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseAvg.setTongdiaoPeakRate(doubleListAvg(
            result.stream().map(t -> t.getTongdiaoPeakRate()).filter(t -> t != null).collect(Collectors.toList())));

        gdLoadResponseAvg.setTongdiaoBuyElectric(doubleListAvg(
            result.stream().map(t -> t.getTongdiaoBuyElectric()).filter(t -> t != null).collect(Collectors.toList())));
        if (result.stream().filter(t -> t.getUseHour() != null).collect(Collectors.toList()).size() > 0) {
            gdLoadResponseAvg.setUseHour(new Double(
                result.stream().filter(t -> t.getUseHour() != null).mapToInt(t -> t.getUseHour()).average()
                    .getAsDouble()).intValue());
        }

        gdLoadResponseAvg.setTotalCityLoad20PointLoad(doubleListAvg(
            result.stream().map(t -> t.getTotalCityLoad20PointLoad()).filter(t -> t != null)
                .collect(Collectors.toList())));
        gdLoadResponseAvg.setCityFireLoad20PointLoad(doubleListAvg(
            result.stream().map(t -> t.getCityFireLoad20PointLoad()).filter(t -> t != null)
                .collect(Collectors.toList())));
        gdLoadResponseAvg.setCityWaterLoad20PointLoad(doubleListAvg(
            result.stream().map(t -> t.getCityWaterLoad20PointLoad()).filter(t -> t != null)
                .collect(Collectors.toList())));
        gdLoadResponseAvg.setCityWindload20PointLoad(doubleListAvg(
            result.stream().map(t -> t.getCityWindload20PointLoad()).filter(t -> t != null)
                .collect(Collectors.toList())));
        gdLoadResponseAvg.setXidianLoadMax(doubleListAvg(
            result.stream().map(t -> t.getXidianLoadMax()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseAvg.setXidianLoadMin(doubleListAvg(
            result.stream().map(t -> t.getXidianLoadMin()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseAvg.setXidianLoadPeakRate(doubleListAvg(
            result.stream().map(t -> t.getXidianLoadPeakRate()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseAvg.setPeakElectric(doubleListAvg(
            result.stream().map(t -> t.getPeakElectric()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseAvg.setTotalPeak(doubleListAvg(
            result.stream().map(t -> t.getTotalPeak()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseAvg.setUserConscious(doubleListAvg(
            result.stream().map(t -> t.getUserConscious()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseAvg.setNetLimited(doubleListAvg(
            result.stream().map(t -> t.getNetLimited()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseAvg.setGap(
            doubleListAvg(result.stream().map(t -> t.getGap()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseAvg.setTongdiaoLoadMax(doubleListAvg(
            result.stream().map(t -> t.getTongdiaoLoadMax()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseAvg.setTongdiaoLoadMin(doubleListAvg(
            result.stream().map(t -> t.getTongdiaoLoadMin()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseAvg.setMaxTemp(doubleListAvg(
            result.stream().map(t -> t.getMaxTemp()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseAvg.setRain(
            doubleListAvg(result.stream().map(t -> t.getRain()).filter(t -> t != null).collect(Collectors.toList())));

        GDLoadResponse gdLoadResponseMax = new GDLoadResponse();
        gdLoadResponseMax.setDate("最大");
        gdLoadResponseMax.setZhongdiaoMax(doubleListMax(
            result.stream().map(t -> t.getZhongdiaoMax()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMax.setZhongdiaoMin(doubleListMax(
            result.stream().map(t -> t.getZhongdiaoMin()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMax.setTongdiaoMax(doubleListMax(
            result.stream().map(t -> t.getTongdiaoMax()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMax.setTongdiaoMin(doubleListMax(
            result.stream().map(t -> t.getTongdiaoMin()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMax.setTongdiaoPeak(doubleListMax(
            result.stream().map(t -> t.getTongdiaoPeak()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMax.setTongdiaoPeakRate(doubleListMax(
            result.stream().map(t -> t.getTongdiaoPeakRate()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMax.setTongdiaoBuyElectric(doubleListMax(
            result.stream().map(t -> t.getTongdiaoBuyElectric()).filter(t -> t != null).collect(Collectors.toList())));
        if (result.stream().filter(t -> t.getUseHour() != null).collect(Collectors.toList()).size() > 0) {
            gdLoadResponseMax.setUseHour(
                result.stream().filter(t -> t.getUseHour() != null).mapToInt(t -> t.getUseHour()).max().getAsInt());
        }
        gdLoadResponseMax.setTotalCityLoad20PointLoad(doubleListMax(
            result.stream().map(t -> t.getTotalCityLoad20PointLoad()).filter(t -> t != null)
                .collect(Collectors.toList())));
        gdLoadResponseMax.setCityFireLoad20PointLoad(doubleListMax(
            result.stream().map(t -> t.getCityFireLoad20PointLoad()).filter(t -> t != null)
                .collect(Collectors.toList())));
        gdLoadResponseMax.setCityWaterLoad20PointLoad(doubleListMax(
            result.stream().map(t -> t.getCityWaterLoad20PointLoad()).filter(t -> t != null)
                .collect(Collectors.toList())));
        gdLoadResponseMax.setCityWindload20PointLoad(doubleListMax(
            result.stream().map(t -> t.getCityWindload20PointLoad()).filter(t -> t != null)
                .collect(Collectors.toList())));
        gdLoadResponseMax.setXidianLoadMax(doubleListMax(
            result.stream().map(t -> t.getXidianLoadMax()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMax.setXidianLoadMin(doubleListMax(
            result.stream().map(t -> t.getXidianLoadMin()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMax.setXidianLoadPeakRate(doubleListMax(
            result.stream().map(t -> t.getXidianLoadPeakRate()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMax.setPeakElectric(doubleListMax(
            result.stream().map(t -> t.getPeakElectric()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMax.setTotalPeak(doubleListMax(
            result.stream().map(t -> t.getTotalPeak()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMax.setUserConscious(doubleListMax(
            result.stream().map(t -> t.getUserConscious()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMax.setNetLimited(doubleListMax(
            result.stream().map(t -> t.getNetLimited()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMax.setGap(
            doubleListMax(result.stream().map(t -> t.getGap()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMax.setTongdiaoLoadMax(doubleListMax(
            result.stream().map(t -> t.getTongdiaoLoadMax()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMax.setTongdiaoLoadMin(doubleListMax(
            result.stream().map(t -> t.getTongdiaoLoadMin()).filter(t -> t != null).collect(Collectors.toList())));

        gdLoadResponseMax.setMaxTemp(doubleListMax(
            result.stream().map(t -> t.getMaxTemp()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMax.setRain(
            doubleListMax(result.stream().map(t -> t.getRain()).filter(t -> t != null).collect(Collectors.toList())));

        GDLoadResponse gdLoadResponseMin = new GDLoadResponse();
        gdLoadResponseMin.setDate("最小");
        gdLoadResponseMin.setZhongdiaoMax(doubleListMin(
            result.stream().map(t -> t.getZhongdiaoMax()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMin.setZhongdiaoMin(doubleListMin(
            result.stream().map(t -> t.getZhongdiaoMin()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMin.setTongdiaoMax(doubleListMin(
            result.stream().map(t -> t.getTongdiaoMax()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMin.setTongdiaoMin(doubleListMin(
            result.stream().map(t -> t.getTongdiaoMin()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMin.setTongdiaoPeak(doubleListMin(
            result.stream().map(t -> t.getTongdiaoPeak()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMin.setTongdiaoPeakRate(doubleListMin(
            result.stream().map(t -> t.getTongdiaoPeakRate()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMin.setTongdiaoBuyElectric(doubleListMin(
            result.stream().map(t -> t.getTongdiaoBuyElectric()).filter(t -> t != null).collect(Collectors.toList())));
        if (result.stream().filter(t -> t.getUseHour() != null).collect(Collectors.toList()).size() > 0) {
            gdLoadResponseMin.setUseHour(
                result.stream().filter(t -> t.getUseHour() != null).mapToInt(t -> t.getUseHour()).min().getAsInt());
        }
        gdLoadResponseMin.setTotalCityLoad20PointLoad(doubleListMin(
            result.stream().map(t -> t.getTotalCityLoad20PointLoad()).filter(t -> t != null)
                .collect(Collectors.toList())));
        gdLoadResponseMin.setCityFireLoad20PointLoad(doubleListMin(
            result.stream().map(t -> t.getCityFireLoad20PointLoad()).filter(t -> t != null)
                .collect(Collectors.toList())));
        gdLoadResponseMin.setCityWaterLoad20PointLoad(doubleListMin(
            result.stream().map(t -> t.getCityWaterLoad20PointLoad()).filter(t -> t != null)
                .collect(Collectors.toList())));
        gdLoadResponseMin.setCityWindload20PointLoad(doubleListMin(
            result.stream().map(t -> t.getCityWindload20PointLoad()).filter(t -> t != null)
                .collect(Collectors.toList())));
        gdLoadResponseMin.setXidianLoadMax(doubleListMin(
            result.stream().map(t -> t.getXidianLoadMax()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMin.setXidianLoadMin(doubleListMin(
            result.stream().map(t -> t.getXidianLoadMin()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMin.setXidianLoadPeakRate(doubleListMin(
            result.stream().map(t -> t.getXidianLoadPeakRate()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMin.setPeakElectric(doubleListMin(
            result.stream().map(t -> t.getPeakElectric()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMin.setTotalPeak(doubleListMin(
            result.stream().map(t -> t.getTotalPeak()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMin.setUserConscious(doubleListMin(
            result.stream().map(t -> t.getUserConscious()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMin.setNetLimited(doubleListMin(
            result.stream().map(t -> t.getNetLimited()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMin.setGap(
            doubleListMin(result.stream().map(t -> t.getGap()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMin.setTongdiaoLoadMin(doubleListMin(
            result.stream().map(t -> t.getTongdiaoLoadMin()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMin.setTongdiaoLoadMax(doubleListMin(
            result.stream().map(t -> t.getTongdiaoLoadMax()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMin.setMaxTemp(doubleListMin(
            result.stream().map(t -> t.getMaxTemp()).filter(t -> t != null).collect(Collectors.toList())));
        gdLoadResponseMin.setRain(
            doubleListMin(result.stream().map(t -> t.getRain()).filter(t -> t != null).collect(Collectors.toList())));
        double lastYearTongdiaoMax = 0;
        if (lastYearGDLoad.stream().filter(t -> t.getTongDiaoMax() != null).collect(Collectors.toList()).size() > 0) {
            lastYearTongdiaoMax = lastYearGDLoad.stream().filter(t -> t.getTongDiaoMax() != null)
                .mapToDouble(t -> t.getTongDiaoMax().doubleValue()).max().getAsDouble();
        }

        double lastYearZhongdiaoMax = 0;
        if (lastYearGDLoad.stream().filter(t -> t.getZhongDiaoMax() != null).collect(Collectors.toList()).size() > 0) {
            lastYearZhongdiaoMax = lastYearGDLoad.stream().filter(t -> t.getZhongDiaoMax() != null)
                .mapToDouble(t -> t.getZhongDiaoMax().doubleValue()).max().getAsDouble();
        }
        double lastYearXidianMax = 0;
        double lastYearTemMax = 0;
        if (lastYearGDLoad.stream().filter(t -> t.getXiLiSanLoadMax() != null).collect(Collectors.toList()).size()
            > 0) {
            lastYearXidianMax = lastYearGDLoad.stream().filter(t -> t.getXiLiSanLoadMax() != null)
                .mapToDouble(t -> t.getXiLiSanLoadMax().doubleValue()).max().getAsDouble();
        }

        if (lastYearWeatherFeatureCityDayHisVO.stream().filter(t -> t.getHighestTemperature() != null)
            .collect(Collectors.toList()).size() > 0) {
            lastYearTemMax = lastYearWeatherFeatureCityDayHisVO.stream().filter(t -> t.getHighestTemperature() != null)
                .mapToDouble(t -> t.getHighestTemperature().doubleValue()).max().getAsDouble();
        }

        result.add(gdLoadResponseAvg);
        result.add(gdLoadResponseMax);
        result.add(gdLoadResponseMin);

        GDLoadVO gdLoadVO = new GDLoadVO();
        gdLoadVO.setGdLoadResponses(result);
        gdLoadVO.setTemMax(gdLoadResponseMax.getMaxTemp());
        if (gdLoadResponseMax.getMaxTemp() != null && lastYearTemMax != 0) {
            BigDecimal bigDecimal = new BigDecimal(
                (gdLoadResponseMax.getMaxTemp().doubleValue() - lastYearTemMax) / lastYearTemMax);
            gdLoadVO.setTemMaxOfYear(
                new BigDecimal((gdLoadResponseMax.getMaxTemp().doubleValue() - lastYearTemMax) / lastYearTemMax)
                    .multiply(new BigDecimal(100)));
        }

        gdLoadVO.setXiLiSanDianMax(gdLoadResponseMax.getXidianLoadMax());
        if (gdLoadResponseMax.getXidianLoadMax() != null && lastYearXidianMax != 0) {
            gdLoadVO.setXiLiSanDianMaxOfYear(new BigDecimal(
                (gdLoadResponseMax.getXidianLoadMax().doubleValue() - lastYearXidianMax) / lastYearXidianMax)
                .multiply(new BigDecimal(100)));
        }

        gdLoadVO.setZhongdiaoMax(gdLoadResponseMax.getZhongdiaoMax());
        if (gdLoadResponseMax.getZhongdiaoMax() != null && lastYearZhongdiaoMax != 0) {
            gdLoadVO.setZhongdiaoMaxOfYear(new BigDecimal(
                (gdLoadResponseMax.getZhongdiaoMax().doubleValue() - lastYearZhongdiaoMax) / lastYearZhongdiaoMax)
                .multiply(new BigDecimal(100)));
        }

        gdLoadVO.setTongdiaoMax(gdLoadResponseMax.getTongdiaoMax());
        if (gdLoadResponseMax.getTongdiaoMax() != null && lastYearTongdiaoMax != 0) {
            gdLoadVO.setTongdiaoMaxOfYear(new BigDecimal(
                (gdLoadResponseMax.getTongdiaoMax().doubleValue() - lastYearTongdiaoMax) / lastYearTongdiaoMax)
                .multiply(new BigDecimal(100)));
        }

        baseResp = BaseResp.succResp();
        baseResp.setData(gdLoadVO);
        return baseResp;
    }


    public BigDecimal doubleListMax(List<BigDecimal> list) {

        if (!CollectionUtils.isEmpty(list)) {
            BigDecimal max = new BigDecimal(
                list.stream().filter(t -> t != null).mapToDouble(BigDecimal::doubleValue).max().getAsDouble())
                .setScale(2, BigDecimal.ROUND_HALF_UP);
            return max;
        }
        return null;
    }


    public BigDecimal doubleListAvg(List<BigDecimal> list) {

        if (!CollectionUtils.isEmpty(list)) {
            BigDecimal avg = new BigDecimal(
                list.stream().filter(t -> t != null).mapToDouble(BigDecimal::doubleValue).average().getAsDouble())
                .setScale(2, BigDecimal.ROUND_HALF_UP);
            return avg.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        return null;
    }

    public BigDecimal doubleListMin(List<BigDecimal> list) {

        if (!CollectionUtils.isEmpty(list)) {
            BigDecimal min = new BigDecimal(
                list.stream().filter(t -> t != null).mapToDouble(BigDecimal::doubleValue).min().getAsDouble())
                .setScale(2, BigDecimal.ROUND_HALF_UP);
            return min;
        }
        return null;
    }


    /**
     * 获取相似出力
     */
    private List<SimilarPowerResp> getSimilarPower(String cityId, String caliberId, Date basicDate,
        Date similarDate) throws Exception {
        //基准日出力96点数据
        List<BigDecimal> basicData = null;
        List<BigDecimal> similarData = null;
        basicData = this.loadCityHisService.findLoadCityHisDO(basicDate, cityId, caliberId);
        similarData = this.loadCityHisService.findLoadCityHisDO(similarDate, cityId, caliberId);
        List<SimilarPowerResp> powerVOS = new ArrayList<>();
        SimilarPowerResp basicPower = new SimilarPowerResp();
        basicPower.setData(basicData);
        basicPower.setDate(basicDate);
        basicPower.setType("1");

        SimilarPowerResp similarPower = new SimilarPowerResp();
        similarPower.setData(similarData);
        similarPower.setDate(similarDate);
        similarPower.setType("2");

        powerVOS.add(basicPower);
        powerVOS.add(similarPower);
        return powerVOS;
    }


    /**
     * 获取相似降雨
     */
    private List<SimilarRainfallResp> getSimilarRainFall(String cityId, Date basicDate, Date similarDate)
        throws Exception {
        List<SimilarRainfallResp> vos = new ArrayList<>();
        SimilarRainfallResp basicVO = new SimilarRainfallResp();
        //基准日天气
        List<BigDecimal> basicWeather = this.weatherCityHisService
            .findWeatherCityHisValueList(cityId, basicDate, WeatherEnum.RAINFALL.getType());
        basicVO.setData(basicWeather);
        basicVO.setDate(basicDate);
        basicVO.setType("1");
        //相似日天气
        List<BigDecimal> similarWeather = this.weatherCityHisService
            .findWeatherCityHisValueList(cityId, similarDate, WeatherEnum.RAINFALL.getType());
        SimilarRainfallResp similarVO = new SimilarRainfallResp();
        similarVO.setData(similarWeather);
        similarVO.setDate(similarDate);
        similarVO.setType("2");
        vos.add(basicVO);
        vos.add(similarVO);
        return vos;
    }

    /**
     * 获取相似温度
     */
    private List<SimilarTemperatureResp> getSimilarTemperature(String cityId, Date basicDate, Date similarDate)
        throws Exception {
        List<SimilarTemperatureResp> vos = new ArrayList<>();
        SimilarTemperatureResp basicVO = new SimilarTemperatureResp();
        //基准日天气
        List<BigDecimal> basicWeather = this.weatherCityHisService
            .findWeatherCityHisValueList(cityId, basicDate, WeatherEnum.TEMPERATURE.getType());
        basicVO.setData(basicWeather);
        basicVO.setDate(basicDate);
        basicVO.setType("1");
        //相似日天气
        List<BigDecimal> similarWeather = this.weatherCityHisService.findWeatherCityHisValueList(cityId, similarDate,
            WeatherEnum.TEMPERATURE.getType());
        SimilarTemperatureResp similarVO = new SimilarTemperatureResp();
        similarVO.setData(similarWeather);
        similarVO.setDate(similarDate);
        similarVO.setType("2");
        vos.add(basicVO);
        vos.add(similarVO);
        return vos;
    }

}