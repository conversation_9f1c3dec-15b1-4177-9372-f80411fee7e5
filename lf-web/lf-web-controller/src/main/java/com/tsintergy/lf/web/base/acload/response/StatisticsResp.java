/**
 * Copyright (C), 2015‐2022, 北京清能互联科技有限公司 Author:  <EMAIL> Date:  2022/5/24 1:29 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.acload.response;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * Description: 样本统计值 <br>
 *
 * <AUTHOR>
 * @create 2022/5/24
 * @since 1.0.0
 */
@ApiModel
@Data
public class StatisticsResp {

    @ApiModelProperty(value = "温度(℃)", example = "20")
    BigDecimal Temperature;

    @BigdecimalJsonFormat(percentConvert = 100)
    @ApiModelProperty(value = "百分比（%）", example = "11.76")
    BigDecimal percentage;
}  
