/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangchen Date:  2019/6/6 15:09 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.load.response;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 年月特性统计通用返回对象
 *
 * <AUTHOR>
 * @create 2020/4/15
 * @since 1.0.0
 */
@Data
@ApiModel
public class YearMonthFeatureStatisticsResp implements Serializable {

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最大负荷",example = "123121")
    private BigDecimal maxLoad;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "平均负荷",example = "123121")
    private BigDecimal avgLoad;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最小负荷",example = "123121")
    private BigDecimal minLoad;
    @BigdecimalJsonFormat(percentConvert = 100)
    @ApiModelProperty(value = "平均负荷峰谷差率",example = "0.93")
    private BigDecimal avgloadGradient;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "总电量",example = "32133")
    private BigDecimal totalEnergy;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "总雨量",example = "1233")
    private BigDecimal totalRain;

    @ApiModelProperty(value = "日期",example = "2021-03-03")
    private String date;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最高温度",example = "36")
    private BigDecimal highestTemperature;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "去年最大负荷",example = "32133")
    private BigDecimal lastMaxLoad;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "去年最小负荷",example = "32133")
    private BigDecimal lastMinLoad;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "去年平均负荷",example = "32133")
    private BigDecimal lastAvgLoad;
    @BigdecimalJsonFormat(percentConvert = 100)
    @ApiModelProperty(value = "去年负荷峰谷差率",example = "0.93")
    private BigDecimal lastLoadGradient;

    /**
     * 峰谷差
     */
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "峰谷差",example = "21")
    private BigDecimal different;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "去年峰谷差",example = "211")
    private BigDecimal lastDifferent;

    /**
     * 峰谷差率
     */
    @BigdecimalJsonFormat(percentConvert = 100)
    @ApiModelProperty(value = "峰谷差率",example = "0.93")
    private BigDecimal gradient;
    @BigdecimalJsonFormat(percentConvert = 100)
    @ApiModelProperty(value = "去年峰谷差率",example = "0.93")
    private BigDecimal lastGradient;


}
