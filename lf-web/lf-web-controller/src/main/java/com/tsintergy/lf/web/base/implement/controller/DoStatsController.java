package com.tsintergy.lf.web.base.implement.controller;


import static com.tsieframework.core.base.exception.TsieExceptionUtils.newBusinessException;

import com.alibaba.fastjson.JSONObject;
import com.tsieframework.cloud.security.web.businesslog.annotation.OperateLog;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.aif.algorithm.serviceapi.base.constants.AlgorithmConstants;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.ShortConstants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.analyze.api.DeviationAnalyzeCityDayFcService;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CaliberDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.TaskInfoDO;
import com.tsintergy.lf.serviceapi.base.evalucation.api.*;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsAccuracyLoadCityMonthHisDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsAccuracyLoadCityYearHisDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.*;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.industry.api.IndustryCityLoadDayHisClctNewService;
import com.tsintergy.lf.serviceapi.base.industry.api.IndustryEnergyService;
import com.tsintergy.lf.serviceapi.base.industry.api.IndustryRelationService;
import com.tsintergy.lf.serviceapi.base.industry.pojo.EnergyIndustryDO;
import com.tsintergy.lf.serviceapi.base.industry.pojo.IndustryCityLoadDayHisClctNewDO;
import com.tsintergy.lf.serviceapi.base.industry.pojo.IndustryRelationDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisClctService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureStatService;
import com.tsintergy.lf.serviceapi.base.load.api.TaskInfoService;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadStatDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.*;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAccuracyDayService;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAccuracyMonthService;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAccuracySynthesizeMonthService;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAccuracyWeekService;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportAccuracyDayDO;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportAccuracyMonthDO;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportAccuracySynthesizeMonthDO;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportAccuracyWeekDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.weather.api.*;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityMonthHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityQuarterHisDO;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import com.tsintergy.lf.web.base.forecast.thread.AutoForecastTask;
import com.tsintergy.lf.web.base.implement.response.LoadAndWeatherResponse;
import com.tsintergy.lf.web.base.implement.response.TaskInfoVO;
import com.tsintergy.lf.web.base.load.request.ForecsatRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.tsieframework.core.base.exception.TsieExceptionUtils.newBusinessException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 统计负荷特性并入库
 */
@Api(tags = "统计负荷特性并入库")
@RestController
@RequestMapping("/stats")
public class DoStatsController extends CommonBaseController {

    private final Logger logger = LogManager.getLogger(DoStatsController.class);

    @Autowired
    private LoadFeatureStatService loadFeatureStatService;

    @Autowired
    private IndustryCityLoadDayHisClctNewService industryCityLoadDayHisClctNewService;

    @Autowired
    private IndustryEnergyService industryEnergyService;

    @Autowired
    private IndustryRelationService industryRelationService;

    @Autowired
    private WeatherFeatureStatService weatherFeatureStatService;

    @Autowired
    private CityService cityService;

    @Autowired
    private LoadCityHisClctService loadCityHisClctService;

    @Autowired
    private LoadCityFcService loadCityFcService;

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Autowired
    private CaliberService caliberService;

    @Autowired
    private ForecastResultStatService forecastResultStatService;

    @Autowired
    private ForecastService forecastService;

    @Autowired
    private AutoForecastService autoForecastService;

    @Autowired
    private AlgorithmService algorithmService;

    @Autowired
    private HolidayService holidayService;

    @Autowired
    private StatisticsWeatherCityDayFcStatService statisticsWeatherCityDayFcStatService;

    @Autowired
    private StatisticsSynthesizeWeatherCityDayHisService statisticsSynthesizeWeatherCityDayHisService;

    @Autowired
    private StatisticsSynthesizeWeatherCityDayFcService statisticsSynthesizeWeatherCityDayFcService;

    @Autowired
    private StatisticsAccuracyWeatherCityDayHisService statisticsAccuracyWeatherCityDayHisService;

    @Autowired
    private StatisticsAccuracyWeatherCityMonthHisService statisticsAccuracyWeatherCityMonthHisService;

    @Autowired
    private StatisticsAccuracyWeatherCityYearHisService statisticsAccuracyWeatherCityYearHisService;

    @Autowired
    private ScheduledThreadPoolExecutor scheduledThreadPoolExecutor;

    @Autowired
    private ReportAccuracyDayService reportAccuracyDayService;

    @Autowired
    private ReportAccuracyWeekService reportAccuracyWeekService;

    @Autowired
    private ReportAccuracyMonthService reportAccuracyMonthService;

    @Autowired
    private ReportAccuracySynthesizeMonthService reportAccuracySynthesizeMonthService;

    @Autowired
    SettingSystemService settingSystemService;

    @Autowired
    StatisticsAccuracyLoadCityMonthHisService statisticsAccuracyLoadCityMonthHisService;

    @Autowired
    StatisticsAccuracyLoadCityYearHisService statisticsAccuracyLoadCityYearHisService;

    @Autowired
    Environment environment;

    @Autowired
    DeviationAnalyzeCityDayFcService deviationAnalyzeCityDayFcService;

    @Autowired
    private AccuracyAssessService accuracyAssessService;

    @Autowired
    private AccuracyCompositeService accuracyCompositeService;

    /**
     * 功能描述: <br> 查看城市表
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    @ApiOperation("查看城市表")
    @OperateLog(operate = "查看城市表")
    @RequestMapping(value = "/allCity", method = RequestMethod.GET)
    public BaseResp<List<CityDO>> initCity() throws Exception {
        BaseResp resp = BaseResp.succResp();
        List<CityDO> cityList = cityService.findAllCitys();
        resp.setData(cityList);
        return resp;
    }

    /**
     * 功能描述: <br> 查看负荷的日期
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    @ApiOperation("查看负荷的日期")
    @OperateLog(operate = "查看负荷的日期")
    @RequestMapping(value = "/load/date", method = RequestMethod.GET)
    public BaseResp<List<LoadStatDTO>> initLoadDate() throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<CityDO> cityList = cityService.findAllCitys();
        List<LoadStatDTO> list = new ArrayList<>();
        for (CityDO cityVO : cityList) {
            List<LoadCityHisClctDO> loadCityHisClctVOS = loadCityHisClctService.findLoadCityHisVOS(cityVO.getId(),
                null, null, null);
            if (CollectionUtils.isEmpty(loadCityHisClctVOS)) {
                LoadStatDTO load = new LoadStatDTO();
                load.setCityId(cityVO.getId());
                load.setCityName(cityVO.getCity());
                load.setCaliberId("ALL");
                load.setDate("空");
                list.add(load);
            } else {
                Map<String, List<LoadCityHisClctDO>> mapData =
                    loadCityHisClctVOS.stream().collect(Collectors.groupingBy(LoadCityHisClctDO::getCaliberId));
                mapData.forEach((k, v) -> {
                    LoadStatDTO load = new LoadStatDTO();
                    load.setCityId(cityVO.getId());
                    load.setCityName(cityVO.getCity());
                    String caliber = caliberService.findCaliberDOByPk(k).getName();
                    load.setCaliberId(caliber);
                    Date startDate = v.get(0).getDate();
                    Date endDate = v.get(v.size() - 1).getDate();
                    String dateStr =
                        DateUtils.date2String(startDate, DateFormatType.SIMPLE_DATE_FORMAT_STR) + "----" + DateUtils
                            .date2String(endDate, DateFormatType.SIMPLE_DATE_FORMAT_STR);
                    load.setDate(dateStr);
                    list.add(load);
                });
            }
        }
        baseResp.setData(list);
        return baseResp;
    }


    /**
     * 功能描述: <br> 查看预测数据的日期
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    @ApiOperation("查看预测数据的日期")
    @OperateLog(operate = "查看预测数据的日期")
    @RequestMapping(value = "/fcLoad/date", method = RequestMethod.GET)
    public BaseResp<List<LoadStatDTO>> initFcDate() throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<CityDO> cityList = cityService.findAllCitys();
        List<LoadStatDTO> list = new ArrayList<>();
        for (CityDO cityVO : cityList) {
            List<LoadCityFcDO> loadCityFcVOS = loadCityFcService.findLoadCityFcDO(cityVO.getId(), null, null);
            if (CollectionUtils.isEmpty(loadCityFcVOS)) {
                LoadStatDTO load = new LoadStatDTO();
                load.setCityId(cityVO.getId());
                load.setCityName(cityVO.getCity());
                load.setDate("空");
                list.add(load);
            } else {
                Map<String, Map<String, List<LoadCityFcDO>>> mapData =
                    loadCityFcVOS.stream().collect(Collectors.groupingBy(LoadCityFcDO::getCaliberId,
                        Collectors.groupingBy(LoadCityFcDO::getAlgorithmId)));
                mapData.forEach((k, v) -> {
                    for (Map.Entry<String, List<LoadCityFcDO>> entry : v.entrySet()) {
                        try {
                            LoadStatDTO load = new LoadStatDTO();
                            load.setCityId(cityVO.getId());
                            load.setCityName(cityVO.getCity());
                            String caliber = caliberService.findCaliberDOByPk(k).getName();
                            load.setCaliberId(caliber);
                            AlgorithmDO algorithmVO = null;
                            algorithmVO = algorithmService.findAlgorithmVOByPk(entry.getKey());
                            load.setAlgorithmName(algorithmVO.getAlgorithmCn());
                            if (entry.getValue() == null) {
                                load.setDate("空");
                            } else {
                                List<LoadCityFcDO> loadCityFcVOS1 = entry.getValue();
//                                loadCityFcVOS1.sort(Comparator.comparing(LoadCityFcDO::getDate));
                                Date startDate = loadCityFcVOS1.get(0).getDate();
                                Date endDate = loadCityFcVOS1.get(loadCityFcVOS1.size() - 1).getDate();
                                String dateStr = DateUtils.date2String(startDate,
                                    DateFormatType.SIMPLE_DATE_FORMAT_STR) + "----" + DateUtils
                                    .date2String(endDate, DateFormatType.SIMPLE_DATE_FORMAT_STR);
                                load.setDate(dateStr);
                            }
                            list.add(load);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
            }
        }
        baseResp.setData(list);
        return baseResp;
    }


    /**
     * 功能描述: <br> 查看气象的日期
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    @ApiOperation("查看气象的日期")
    @OperateLog(operate = "查看气象的日期")
    @RequestMapping(value = "/weather/date", method = RequestMethod.GET)
    public BaseResp<List<LoadAndWeatherResponse>> initWeatherDate() throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<CityDO> cityVOList = cityService.findAllCitys();
        List<LoadAndWeatherResponse> loadAndWeatherResponseList = new ArrayList<>();
        for (CityDO cityVO : cityVOList) {
            List<WeatherCityHisDO> weatherCityHisVOS = weatherCityHisService.findWeatherCityHisDOs(cityVO.getId(),
                null, null, null);
            LoadAndWeatherResponse loadAndWeatherResponse = new LoadAndWeatherResponse();
            loadAndWeatherResponse.setCityId(cityVO.getId());
            loadAndWeatherResponse.setCityName(cityVO.getCity());
            loadAndWeatherResponse.setCaliberId(cityVO.getCity());
            if (CollectionUtils.isEmpty(weatherCityHisVOS)) {
                loadAndWeatherResponse.setDate("空");
            } else {
                Date startDate = weatherCityHisVOS.get(0).getDate();
                Date endDate = weatherCityHisVOS.get(weatherCityHisVOS.size() - 1).getDate();
                String dateStr =
                    DateUtils.date2String(startDate, DateFormatType.SIMPLE_DATE_FORMAT_STR) + "----" + DateUtils
                        .date2String(endDate, DateFormatType.SIMPLE_DATE_FORMAT_STR);
                loadAndWeatherResponse.setDate(dateStr);
            }
            loadAndWeatherResponseList.add(loadAndWeatherResponse);
        }
        baseResp.setData(loadAndWeatherResponseList);
        return baseResp;
    }


    /**
     * 统计地区历史负荷特性并入库 需要参数：startDate 、endDate
     */
    @ApiOperation("统计地区负荷特性并入库")
    @OperateLog(operate = "统计地区负荷特性并入库")
    @RequestMapping(value = "/load", method = RequestMethod.POST)
    public BaseResp saveLoadDay(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        BaseResp resp = BaseResp.succResp();
        List<String> cityIds = forecsatRequest.getCityIds();
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        if (cityIds.size() == 1 && cityIds.get(0).equals("all")) {
            //全部城市 不用城市筛选条件
            cityIds = null;
        }

        //统计日负荷特性
         loadFeatureStatService.doStatLoadFeatureCityDayClctHisSecond(cityIds, startDate,
            endDate, null);
        //统计周负荷特性
        List<LoadFeatureCityWeekHisDO> loadWeek = loadFeatureStatService.doStatLoadFeatureCityWeek(cityIds, startDate,
            endDate, null);
        //统计月负荷特性
        List<LoadFeatureCityMonthHisDO> loadMouth = loadFeatureStatService.doStatLoadFeatureCityMonth(cityIds, startDate
            , endDate, null);
        //统计季负荷特性
        List<LoadFeatureCityQuarterHisDO> loadQuarter = loadFeatureStatService.doStatLoadFeatureCityQuarter(cityIds,
            startDate, endDate, null);
        // 统计年负荷特性
        List<LoadFeatureCityYearHisDO> loadYear = loadFeatureStatService.doStatLoadFeatureCityYear(cityIds, startDate,
            endDate, null);
        return resp;
    }

    /**
     * 统计地区负荷特性并入库 需要参数：startDate 、endDate
     */
    @RequestMapping(value = "/loadByMinute", method = RequestMethod.POST)
    public BaseResp saveLoadBySecond(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        BaseResp resp = BaseResp.succResp();
        List<String> cityIds = forecsatRequest.getCityIds();
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        if (cityIds.size() == 1 && cityIds.get(0).equals("all")) {
            //全部城市 不用城市筛选条件
            cityIds = null;
        }
        //统计日负荷特性
        loadFeatureStatService.doStatLoadFeatureCityDayClctHisSecond(cityIds, startDate,
            endDate, null);

        return resp;
    }

    /**
     * 功能描述: 统计地区预测负荷特性并入库<br>
     *
     * @Return: {@link BaseResp}
     * @Version: 1.0.0
     * @Author:<EMAIL>
     * @Date: 2021/11/8 16:36
     */

    @ApiOperation("负荷预测")
    @RequestMapping(value = "/loadFcFeature", method = RequestMethod.POST)
    public BaseResp statsLoadFcFeature(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        List<String> cityIds = forecsatRequest.getCityIds();
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        if (cityIds.size() == 1 && cityIds.get(0).equals("all")) {
            //全部城市 不用城市筛选条件
            List<CityDO> cityVOS = cityService.findAllCitys();
            cityIds = cityVOS.stream().map(CityDO::getId).collect(Collectors.toList());
        }

        //统计日预测负荷特性
        loadFeatureStatService.doStatLoadFeatureCityDayFc(cityIds, startDate, endDate, null);
        //统计周预测负荷特性
        loadFeatureStatService.doStatLoadFeatureCityWeekFc(cityIds, startDate,
            endDate, "1");
        //统计月预测负荷特性
        loadFeatureStatService.doStatLoadFeatureCityMonthFc(cityIds, startDate
            , endDate, "1");
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;

    }

    /**
     * 统计历史气象特性并入库
     */
    @ApiOperation("统计历史气象特性并入库")
    @OperateLog(operate = "统计历史气象特性并入库")
    @RequestMapping(value = "/weather", method = RequestMethod.POST)
    public BaseResp saveWeatherDay(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        BaseResp resp = BaseResp.succResp();
        List<String> cityIds = forecsatRequest.getCityIds();
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        if (cityIds.size() == 1 && cityIds.get(0).equals("all")) {
            //全部城市 不用城市筛选条件
            List<CityDO> cityVOS = cityService.findAllCitys();
            cityIds = cityVOS.stream().map(CityDO::getId).collect(Collectors.toList());
        }

        try {
            //日气象
            List<WeatherFeatureCityDayHisDO> weatherDay = weatherFeatureStatService.doStatWeatherFeatureCityDay(cityIds,
                startDate, endDate);
            //月气象
            List<WeatherFeatureCityMonthHisDO> weatherMonth =
                weatherFeatureStatService.doStatWeatherFeatureCityMonth(cityIds, startDate, endDate);
            //季气象
            List<WeatherFeatureCityQuarterHisDO> weatherQuarter =
                weatherFeatureStatService.doStatWeatherFeatureCityQuarter(cityIds, startDate, endDate);
        } catch (Exception e) {
            throw newBusinessException(e.getMessage(), e);
        }
        return resp;
    }

    /**
     * 统计预测气象特性并入库
     */
    @ApiOperation("统计预测气象特性并入库")
//    @OperateLog(operate = "统计预测气象特性并入库")
    @RequestMapping(value = "/weatherFcFeature", method = RequestMethod.POST)
    public BaseResp saveWeatherFcFeature(@RequestBody ForecsatRequest forecsatRequest) {
        BaseResp resp = BaseResp.succResp();
        List<String> cityIds = forecsatRequest.getCityIds();
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        try {
            if (cityIds.size() == 1 && cityIds.get(0).equals("all")) {
                List<CityDO> cityVOS = cityService.findAllCitys();
                cityIds = cityVOS.stream().map(CityDO::getId).collect(Collectors.toList());
            }
//            郑州、洛阳、南阳、商丘、周口、驻马店
            //预测日气象
            for (String cityId : cityIds) {
                weatherFeatureStatService.doStatWeatherFeatureCityDayFc(cityId, startDate, endDate);
            }
        } catch (Exception e) {
            throw newBusinessException(e.getMessage(), e);
        }
        return resp;
    }

    /**
     * 功能描述: <br>
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    @ApiOperation("统计预测后评估")
    @OperateLog(operate = "统计预测后评估")
    @RequestMapping(value = "/forecastResult", method = RequestMethod.POST)
    public BaseResp testStatForecastResult(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        List<String> cityIds = forecsatRequest.getCityIds();
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        if (CollectionUtils.isEmpty(forecsatRequest.getCaliberIds())) {
            forecsatRequest.setCaliberIds(
                caliberService.findAllCalibers().stream().map(CaliberDO::getId).collect(Collectors.toList()));
        }
        List<ReportAccuracyDayDO> reportAccuracyDayVOS = new ArrayList<>();
        if (cityIds.size() == 1 && cityIds.get(0).equals("all")) {
            forecastResultStatService.statForecastResult(null, null, null, startDate, endDate);
            deviationAnalyzeCityDayFcService.doInitDeviationAnalyze(startDate, endDate, null);

            List<CityDO> cityVOS = cityService.findAllCitys();
            for (String caliberId : forecsatRequest.getCaliberIds()) {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                String date1 = formatter.format(startDate);
                String date2 = formatter.format(endDate);
                try {
                    reportAccuracyDayVOS = reportAccuracyDayService
                        .doStatSaveReportAccuracy(null, startDate, endDate, caliberId);
                    List<StatisticsAccuracyLoadCityMonthHisDO> monthVOList = statisticsAccuracyLoadCityMonthHisService
                        .getReportMonthAccuracy(cityVOS, caliberId, date1.substring(0, 7), date2.substring(0, 7));
                    statisticsAccuracyLoadCityMonthHisService.doSaveOrUpdate(monthVOList);
                } catch (Exception e) {
                    logger.error("统计月负荷准确率异常...", e);
                    e.printStackTrace();
                }

                try {
                    List<StatisticsAccuracyLoadCityYearHisDO> yearVOList = statisticsAccuracyLoadCityYearHisService
                        .getReportYearAccuracy(cityVOS, caliberId, date1.substring(0, 4), date2.substring(0, 4));
                    statisticsAccuracyLoadCityYearHisService.doSaveOrUpdate(yearVOList);
                } catch (Exception e) {
                    logger.error("统计年负荷准确率异常...", e);
                    e.printStackTrace();
                }
            }
        } else {
            for (String cityId : cityIds) {
                forecastResultStatService.statForecastResult(cityId, null, null, startDate, endDate);
                deviationAnalyzeCityDayFcService.doInitDeviationAnalyze(startDate, endDate, cityId);
            }
            List<CityDO> dos = new ArrayList<>();
            for (String cityId : cityIds) {
                CityDO cityById = cityService.findCityById(cityId);
                dos.add(cityById);

            }
            for (String caliberId : forecsatRequest.getCaliberIds()) {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                String date1 = formatter.format(startDate);
                String date2 = formatter.format(endDate);
                for (String cityId : cityIds) {
                    reportAccuracyDayVOS
                        .addAll(
                            reportAccuracyDayService.doStatSaveReportAccuracy(cityId, startDate, endDate, caliberId));
                }
                try {
                    List<StatisticsAccuracyLoadCityMonthHisDO> monthVOList = statisticsAccuracyLoadCityMonthHisService
                        .getReportMonthAccuracy(dos, caliberId, date1.substring(0, 7), date2.substring(0, 7));
                    statisticsAccuracyLoadCityMonthHisService.doSaveOrUpdate(monthVOList);
                } catch (Exception e) {
                    logger.error("统计月负荷准确率异常...", e);
                    e.printStackTrace();
                }

                try {
                    List<StatisticsAccuracyLoadCityYearHisDO> yearVOList = statisticsAccuracyLoadCityYearHisService
                        .getReportYearAccuracy(dos, caliberId, date1.substring(0, 4), date2.substring(0, 4));
                    statisticsAccuracyLoadCityYearHisService.doSaveOrUpdate(yearVOList);
                } catch (Exception e) {
                    logger.error("统计年负荷准确率异常...", e);
                    e.printStackTrace();
                }

            }
        }
        //统计日负荷填报准确率
        reportAccuracyDayService.doSaveOrUpdate(reportAccuracyDayVOS);

        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }

    /**
     * 实施页面接口
     */
    @RequestMapping(value = "/check", method = RequestMethod.GET)
    public BaseResp check(String start, String end) {
        List<IndustryRelationDO> baseIndustryInfo = industryRelationService
            .findBaseIndustryInfo(null, null, null, null);
        List<EnergyIndustryDO> saveList = new ArrayList<>();
        List<String> collect = baseIndustryInfo.stream().map(IndustryRelationDO::getId).filter(Objects::nonNull)
            .collect(Collectors.toList());
        Map<String, String> collect1 = baseIndustryInfo.stream().collect(
                Collectors.toMap(IndustryRelationDO::getId, IndustryRelationDO::getName));
        List<IndustryCityLoadDayHisClctNewDO> byDateCodes = industryCityLoadDayHisClctNewService
                .findByDateCodes(null, collect, DateUtils.string2Date(start, DateFormatType.SIMPLE_DATE_FORMAT_STR),
                        DateUtils.string2Date(end, DateFormatType.SIMPLE_DATE_FORMAT_STR));
        for (IndustryCityLoadDayHisClctNewDO energyIndustryDO : byDateCodes) {
            EnergyIndustryDO toSave = new EnergyIndustryDO();
            List<BigDecimal> decimals = energyIndustryDO.getloadList();
            BigDecimal divide = BigDecimalFunctions
                    .divide(BigDecimalFunctions.listSum(decimals), new BigDecimal("4"));
            toSave.setDate(energyIndustryDO.getDate());
            toSave.setCreateTime(new Timestamp(System.currentTimeMillis()));
            toSave.setHyName(collect1.get(energyIndustryDO.getTradeCode()));
            toSave.setEnergy(divide);
            saveList.add(toSave);
            industryEnergyService.doSave(toSave);
        }
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }

    /**
     * 实施页面接口
     */
    @ApiOperation("调用修正数据算法")
    @OperateLog(operate = "调用修正数据算法")
    @RequestMapping(value = "/do/dataRepair", method = RequestMethod.POST)
    public BaseResp doAllDataRepair(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<String> cityIds = forecsatRequest.getCityIds();
        if (cityIds.size() == 1 && cityIds.get(0).equals("all")) {
            List<CityDO> cityVOS = cityService.findAllCitys();
            cityIds = cityVOS.stream().map(CityDO::getId).collect(Collectors.toList());
        }
        List<String> caliberIds = forecsatRequest.getCaliberIds();
        if (CollectionUtils.isEmpty(cityIds) || CollectionUtils.isEmpty(caliberIds)) {
            baseResp.setRetCode("T100");
            baseResp.setRetMsg("修正算法失败！ 请选择要预测的城市或口径");
        }
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        for (String cityId : cityIds) {
            for (String caliberId : caliberIds) {
                try {
                    forecastService.doDataRepairAlgorithm(cityId, caliberId, startDate, endDate);
                } catch (Exception e) {
                    continue;
                }
            }
        }
        return baseResp;
    }


    /**
     * 统计综合气象指标
     */
    @ApiOperation("统计综合气象指标")
    @RequestMapping(value = "/statisticsSynthesizeWeather", method = RequestMethod.POST)
    public BaseResp statisticsSynthesizeWeather(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        List<String> cityIds = forecsatRequest.getCityIds();
        if (cityIds.size() == 1 && cityIds.get(0).equals("all")) {
            List<CityDO> cityVOS = cityService.findAllCitys();
            cityIds = cityVOS.stream().map(CityDO::getId).collect(Collectors.toList());
        }
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        for (String cityId : cityIds) {
            statisticsSynthesizeWeatherCityDayHisService.doStatisticsSynthesizeWeatherCityDayHis(cityId, startDate,
                endDate, null);
            statisticsSynthesizeWeatherCityDayFcService
                .doStatisticsSynthesizeWeatherCityDaFc(cityId, startDate, endDate,
                    null);
        }

        BaseResp baseResp = BaseResp.succResp();
        baseResp.setRetMsg("统计综合气象指标完成....");
        return baseResp;
    }

    /**
     * 统计日气象预测准确率
     */
    @ApiOperation("统计日气象预测准确率")
    @RequestMapping(value = "/statisticsAccuracyWeatherCityDay", method = RequestMethod.POST)
    public BaseResp statisticsAccuracyWeatherCityDayHis(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        List<String> cityIds = forecsatRequest.getCityIds();
        if (cityIds.size() == 1 && cityIds.get(0).equals("all")) {
            List<CityDO> cityVOS = cityService.findAllCitys();
            cityIds = cityVOS.stream().map(CityDO::getId).collect(Collectors.toList());
        }
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        for (String cityId : cityIds) {
            try {
                statisticsAccuracyWeatherCityDayHisService
                    .doStatAccuracyWeatherCityDay(cityId, startDate, endDate, null);
            } catch (Exception e) {
                logger.error("统计日气象准确率失败", e);
            }
            try {
                statisticsAccuracyWeatherCityMonthHisService.doStatAccuracyWeatherCityMonth(cityId, startDate, endDate,
                    null);
            } catch (Exception e) {
                logger.error("统计月气象准确率异常...", e);
            }
            try {
                statisticsAccuracyWeatherCityYearHisService
                    .doStatAccuracyWeatherCityYear(cityId, startDate, endDate, null);
            } catch (Exception e) {
                logger.error("统计年气象准确率异常...", e);
            }
        }

        BaseResp baseResp = BaseResp.succResp();
        baseResp.setRetMsg("统计日气象准确率完成");
        return baseResp;
    }

    /**
     * 统计日气象预测准确率- 表statistics_weather_city_day_fc_stat
     */
    @ApiOperation("统计日气象预测准确率")
    @RequestMapping(value = "/doStatisticsWeatherCityDayFcStat", method = RequestMethod.GET)
    public BaseResp doStatisticsWeatherCityDayFcStat(String cityId, Date startDate, Date endDate) {
        try {
            statisticsWeatherCityDayFcStatService.doStatWeatherAccuracy(cityId, startDate, endDate);
        } catch (Exception e) {
            logger.error("统计日气象准确率(doStatisticsWeatherCityDayFcStat)失败", e);
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setRetMsg("统计日气象准确率(doStatisticsWeatherCityDayFcStat)完成");
        return baseResp;
    }

    /**
     * 新息算法调用
     */
    @ApiOperation("新息算法调用")
    @RequestMapping(value = "/doNewAlgorithm", method = RequestMethod.GET)
    public BaseResp doNewAlgorithm(Date startDate, Date endDate) throws Exception {
        List<CityDO> cityVOS = cityService.findAllCitys();
        List<CaliberDO> caliberVOS = caliberService.findAllCalibers();
        logger.info("城市数量为===========" + caliberVOS.size());
        Integer forecastType = Integer.parseInt(environment.getProperty("implement.forecast.type"));
        List<AlgorithmEnum> enums = new ArrayList<>();
        enums.add(AlgorithmEnum.FORECAST_INNOVATION);
        //1 所有城市
        for (CityDO cityVO : cityVOS) {
            if (!cityVO.getId().equals(CityConstants.PROVINCE_ID)) {
                continue;
            }
            //2 所有口径
            for (CaliberDO caliberVO : caliberVOS) {
                logger.info("开始预测：   城市id为=====" + cityVO.getId() + ", 口径id为===========" + caliberVO.getId());
                scheduledThreadPoolExecutor.schedule(new AutoForecastTask(forecastType, getUid(), cityVO.getId(),
                        caliberVO.getId(), autoForecastService, startDate, endDate, enums, null, null, null), 0,
                    TimeUnit.MILLISECONDS);
            }
        }
        logger.info("新息预测算法预测结束");
        BaseResp baseResp = BaseResp.succResp("新息预测算法调用成功，正在后台执行");
        return baseResp;
    }

    /**
     * 节假日算法调用
     */
    @ApiOperation("节假日算法调用")
    @RequestMapping(value = "/doHolidayAlgorithm", method = RequestMethod.POST)
    public BaseResp doHolidayAlgorithm(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        BaseResp baseResp = new BaseResp();
        List<String> cityIds = forecsatRequest.getCityIds();
        List<String> caliberIds = forecsatRequest.getCaliberIds();
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        if (CollectionUtils.isEmpty(cityIds) || CollectionUtils.isEmpty(caliberIds)
            || forecsatRequest.getWeatherType() == null || forecsatRequest.getType() == null) {
            baseResp.setRetCode("T100");
            baseResp.setRetMsg("预测算法失败！ 请选择要预测的城市或口径");
        }
        if (cityIds.size() == 1 && cityIds.get(0).equals("all")) {
            List<CityDO> cityVOS = cityService.findAllCitys();
            cityIds = cityVOS.stream().map(CityDO::getId).collect(Collectors.toList());
        }
        if (caliberIds.size() == 1 && caliberIds.get(0).equals("all")) {
            List<CaliberDO> caliberVOS = caliberService.findAllCalibers();
            caliberIds = caliberVOS.stream().map(CaliberDO::getId).collect(Collectors.toList());
        }
        logger.info("节假日算法开始预测");
        String uid = getUid();
        //1 所有城市
        for (String cityId : cityIds) {
            //2 所有口径
            for (String caliberId : caliberIds) {
                //新版节假日禁止使用多线程调用
                autoForecastService.doForecastHoliday(uid, cityId, caliberId, startDate, endDate);
            }
        }
        baseResp = BaseResp.succResp("节假日算法调用成功，正在后台执行");
        return baseResp;
    }

    /**
     * 统计日，周负荷填报准确率
     */
    @ApiOperation("周负荷填报准确率")
    @RequestMapping(value = "/report/accuracy", method = RequestMethod.POST)
    public BaseResp statReportAccuracy(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        BaseResp baseResp = new BaseResp();
        List<String> cityIds = forecsatRequest.getCityIds();
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        List<ReportAccuracyDayDO> reportAccuracyDayVOS = new ArrayList<>();
        List<ReportAccuracyWeekDO> reportAccuracyWeekVOList = new ArrayList<>();
        List<ReportAccuracyMonthDO> monthVOList = new ArrayList<>();
        List<CaliberDO> allCalibers = caliberService.findAllCalibers();
        for (CaliberDO allCaliber : allCalibers) {
            if (cityIds.size() == 1 && cityIds.get(0).equals("all")) {
                reportAccuracyDayVOS = reportAccuracyDayService
                    .doStatSaveReportAccuracy(null, startDate, endDate, allCaliber.getId());
//            reportAccuracyWeekVOList = reportAccuracyWeekService.doStatWeekAccuracy(null, startDate, endDate);
//            monthVOList = reportAccuracyMonthService.statReportMonthAccuracy(null, startDate, endDate);
            } else {
                for (String cityId : cityIds) {
                    reportAccuracyDayVOS
                        .addAll(reportAccuracyDayService
                            .doStatSaveReportAccuracy(cityId, startDate, endDate, allCaliber.getId()));
//                reportAccuracyWeekVOList
//                    .addAll(reportAccuracyWeekService.doStatWeekAccuracy(cityId, startDate, endDate));
//                monthVOList.addAll(reportAccuracyMonthService.statReportMonthAccuracy(cityId, startDate, endDate));
                }
            }
        }
        //统计日负荷填报准确率
        reportAccuracyDayService.doSaveOrUpdate(reportAccuracyDayVOS);
        //统计周负荷填报准确率
//        reportAccuracyWeekService.doSaveOrUpdate(reportAccuracyWeekVOList);
        //统计月准确率
//        reportAccuracyMonthService.doSaveOrUpdate(monthVOList);
        baseResp = BaseResp.succResp("预测准确率统计成功");
        return baseResp;
    }

    /**
     *       统计月准确率  其中包括 月度最大最小，周度月平均最大最小，日度月平均最大最小准确率
     */
    @ApiOperation("统计月准确率")
    @RequestMapping(value = "/month/accuracy", method = RequestMethod.GET)
    public BaseResp statMonthReportAccuracy(Date startDate, Date endDate) throws Exception {
        List<ReportAccuracyMonthDO> monthVOList = reportAccuracyMonthService
            .statReportMonthAccuracy(null, startDate, endDate);
        reportAccuracyMonthService.doSaveOrUpdate(monthVOList);
        return BaseResp.succResp();
    }


    /**
     * 模型融合算法调用 2020/6/12改动 新息算法调用移到实施的页面调用所有算法接口中 这块暂时修改成 调用模型融合算法
     */
    @ApiOperation("模型融合算法调用")
    @RequestMapping(value = "/doModelFusionAlgorithm", method = RequestMethod.POST)
    public BaseResp doModelFusionAlgorithm(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        BaseResp baseResp = new BaseResp();
        List<String> cityIds = forecsatRequest.getCityIds();
        List<String> caliberIds = forecsatRequest.getCaliberIds();
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        if (CollectionUtils.isEmpty(cityIds) || CollectionUtils.isEmpty(caliberIds)) {
            baseResp.setRetCode("T100");
            baseResp.setRetMsg("预测算法失败！ 请选择要预测的城市或口径");
        }
        if (cityIds.size() == 1 && cityIds.get(0).equals("all")) {
            List<CityDO> cityVOS = cityService.findAllCitys();
            cityIds = cityVOS.stream().map(CityDO::getId).collect(Collectors.toList());
        }
        if (caliberIds.size() == 1 && caliberIds.get(0).equals("all")) {
            List<CaliberDO> caliberVOS = caliberService.findAllCalibers();
            caliberIds = caliberVOS.stream().map(CaliberDO::getId).collect(Collectors.toList());
        }
        Integer forecastType = Integer.parseInt(environment.getProperty("implement.forecast.type"));
        List<AlgorithmEnum> enums = new ArrayList<>();
        enums.add(AlgorithmEnum.COMPREHENSIVE_MODEL);
        //1 所有城市
        for (String cityId : cityIds) {
            if (!cityId.equals(CityConstants.PROVINCE_ID)) {
                continue;
            }
            //2 所有口径
            for (String caliberId : caliberIds) {
                logger.info("开始预测：   城市id为=====" + cityId + ", 口径id为===========" + caliberId);
//                scheduledThreadPoolExecutor.schedule(new AutoForecastTask(forecastType, getUid(), cityId,
//                        caliberId, autoForecastService, startDate, endDate, enums, null, null, null), 0,
//                    TimeUnit.MILLISECONDS);
                autoForecastService.completionModelFusionforecast(cityId, caliberId, startDate, endDate,
                    AlgorithmConstants.FC_WAY_T2);
            }
        }
        logger.info(" 模型融合算法预测结束");
        baseResp = BaseResp.succResp(" 模型融合算法调用调用成功，正在后台执行");
        return baseResp;
    }

    /**
     *    功能描述: <br>  计算统计综合准确率   
     *
     * @return  
     * <AUTHOR> 
     * @since 1.0.0     
     */
    @ApiOperation("计算统计综合准确率")
    @RequestMapping(value = "/synthesize/accuracy", method = RequestMethod.POST)
    public BaseResp statSynthesizeReportAccuracy(@RequestBody ForecsatRequest request) throws Exception {
        this.statReportAccuracy(request);
        List<String> cityIds = request.getCityIds();
        if (cityIds != null && cityIds.size() > 0) {
            String city = cityIds.get(0);
            if (city.equals("all")) {
                List<CityDO> cityVOList = cityService.findAllCitys();
                List<String> cityId = cityVOList.stream().map(CityDO::getId).collect(Collectors.toList());
                cityIds.addAll(cityId);
            }
        }
        Date startDate = request.getStartDate();
        Date endDate = request.getEndDate();
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        List<String> stringList = new ArrayList<>();
        for (Date date : dateList) {
            String ym = DateUtil.getMonthByDate(date);
            if (!stringList.contains(ym)) {
                stringList.add(ym);
            }
        }
        for (String ym : stringList) {
            List<ReportAccuracySynthesizeMonthDO> synthesizeMonthVO = reportAccuracySynthesizeMonthService
                .statSynthesizeMonthAccuracy(cityIds, ym.substring(0, 4), ym.substring(5, 7));
            reportAccuracySynthesizeMonthService.doSaveOrUpdate(synthesizeMonthVO);
        }
        return BaseResp.succResp();
    }

    // 手动计算法  已废除
    @Deprecated
    @ApiOperation("集成模型算法")
    @PostMapping("/doIntegrateModelAlgorithm")
    public void integratedModelAlgorithm(String startDateStr, String endDateStr) throws Exception {
        Date startDate = DateUtils.string2Date(startDateStr, DateFormatType.SIMPLE_DATE_FORMAT_STR);
        Date endDate = DateUtils.string2Date(endDateStr, DateFormatType.SIMPLE_DATE_FORMAT_STR);
        List<CityDO> cityVOS = cityService.findAllCitys();
        List<CityDO> collect = cityVOS.stream().filter(t -> t.getType() == 1).collect(Collectors.toList());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(collect)) {
            CityDO cityVO = collect.get(0);
            while (startDate.before(DateUtils.addDays(endDate, 1))) {
                loadCityFcService.doIntegratedModelAlgorithmData(cityVO, startDate);
                startDate = DateUtils.addDays(startDate, 1);
            }
        }
    }

    /**
     * 实施的页面调用  补充96&288的超短期预测数据  type  5 分钟间隔 or 15分钟间隔
     */
    @ApiOperation("实施的页面调用超短期预测数据")
    @RequestMapping(value = "/short/algorithm", method = RequestMethod.POST)
    public BaseResp doShortForecast(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        int timeSpan;
        int startTimePointFlag = 0;
        if (ShortConstants.MINUTE.equals(forecsatRequest.getType())) {
            timeSpan = 5;
            startTimePointFlag = 288;
        } else {
            timeSpan = 15;
            startTimePointFlag = 96;
        }
        List<String> cityIds = forecsatRequest.getCityIds();
        List<String> caliberIds = Arrays.asList("1");
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        for (String cityId : cityIds) {
            for (String caliberId : caliberIds) {
                for (Date date : dateList) {
                    for (int startTimePoint = 0; startTimePoint < startTimePointFlag; startTimePoint++) {
                        //超短期预测
                        Thread.sleep(2000);
                        forecastService
                            .doShortForecast(cityId, caliberId, date, timeSpan, startTimePoint);
                    }
                }
            }
        }
        return baseResp;
    }

    /**
     * 实施的页面调用  统计超短期预测日准确率
     */
    @ApiOperation("实施的页面统计超短期预测日准确率")
    @RequestMapping(value = "/short/fcAccuracy", method = RequestMethod.POST)
    public BaseResp doStatShortFcAccuracy(@RequestBody ForecsatRequest request) throws Exception {
        List<String> cityIds = request.getCityIds();
        Date startDate = request.getStartDate();
        Date endDate = request.getEndDate();
        if (cityIds != null && cityIds.size() > 0) {
            String city = cityIds.get(0);
            if (city.equals("all")) {
                loadFeatureStatService.doShortFcLoadFeatureCityDay(null, startDate, endDate, null);
            } else {
                for (String cityId : cityIds) {
                    loadFeatureStatService.doShortFcLoadFeatureCityDay(cityId, startDate, endDate, null);
                }
            }
        }
        return BaseResp.succResp();

    }


    /**
     * 统计考核点准确率&综合准确率；
     */
//    @ApiOperation("统计考核点准确率&综合准确率；")
    @RequestMapping(value = "/assessComposite", method = RequestMethod.POST)
    public BaseResp doAssessComposite(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        List<String> cityIds = forecsatRequest.getCityIds();
        if (cityIds.size() == 1 && cityIds.get(0).equals("all")) {
            List<CityDO> cityVOS = cityService.findAllCitys();
            cityIds = cityVOS.stream().map(CityDO::getId).collect(Collectors.toList());
        }
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        for (String cityId : cityIds) {
            try {
                accuracyAssessService
                    .doCalculateAssessAccuracy(cityId, "1", null, startDate, endDate);
                accuracyCompositeService
                    .doCalculateCompositeAccuracy(cityId, "1", null, startDate, endDate);
            } catch (Exception e) {
                logger.error("统计考核点准确率&综合准确率失败", e);
            }
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setRetMsg("统计考核点准确率&综合准确率");
        return baseResp;
    }


    @Autowired
    TaskInfoService taskInfoService;


    /**
     * 功能描述:母线实施页面任务重新执行
     *
     * @return:
     * @since: 1.0.0
     * @Author:yangjin
     * @Date: 2020/3/10 13:16
     */
    @RequestMapping("/reSendTask")
    public BaseResp resendTask(String taskId, Date date) throws Exception {
        TaskInfoDO byId = taskInfoService.findById(taskId);
        String xxljobId = byId.getXxljobId();
        taskInfoService.reSendTask(taskId, date, xxljobId);
        return BaseResp.succResp();
    }


    @RequestMapping("/taskInfo")
    public BaseResp getTaskInfo(String type, String date) throws Exception {
        List<TaskInfoDO> byTypeAndDate = taskInfoService.getTaskInfo(type, date);
        List<TaskInfoVO> result = new ArrayList<>();
        for (TaskInfoDO taskInfoDO : byTypeAndDate) {
            TaskInfoVO taskInfoVO = new TaskInfoVO();
            BeanUtils.copyProperties(taskInfoDO, taskInfoVO);
            taskInfoVO.setName(taskInfoDO.getTaskName());
            taskInfoVO.setSuccess("0");
            result.add(taskInfoVO);
        }
        Map<Object, Object> map = new HashMap<>();
        map.put("datas", result);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(map);
        return baseResp;
    }

}
