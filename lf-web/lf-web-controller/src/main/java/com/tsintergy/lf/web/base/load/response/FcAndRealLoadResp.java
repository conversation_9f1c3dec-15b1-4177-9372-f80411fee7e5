package com.tsintergy.lf.web.base.load.response;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;

/**
 *
 * @date: 2/23/18 10:36 AM
 * @author: angel
 **/
@ApiModel
public class FcAndRealLoadResp {

    /**
     * forecasted load
     */
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "预测负荷" )
    List<BigDecimal> forecast;

    /**
     * real load
     */
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "实际负荷")
    List<BigDecimal> real;

    /**
     * 算法
     */
    @ApiModelProperty(value = "算法" ,example = "神经网络")
    private String algorithm;

    public List<BigDecimal> getForecast() {
        return forecast;
    }

    public void setForecast(List<BigDecimal> forecast) {
        this.forecast = forecast;
    }

    public List<BigDecimal> getReal() {
        return real;
    }

    public void setReal(List<BigDecimal> real) {
        this.real = real;
    }

    public String getAlgorithm() {
        return algorithm;
    }

    public void setAlgorithm(String algorithm) {
        this.algorithm = algorithm;
    }
}
