package com.tsintergy.lf.web.base.load.response;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/11/22
 */
@ApiModel
public class FeatureAnalyseResp implements Serializable {

    @ApiModelProperty(value = "日期",example = "2021-03-16")
    private Date date;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最大负荷",example = "123121")
    private BigDecimal maxLoad;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最小负荷",example = "123121")
    private BigDecimal minLoad;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "平均负荷",example = "123121")
    private BigDecimal avgLoad;
    @BigdecimalJsonFormat(percentConvert = 100)
    @ApiModelProperty(value = "负荷峰谷差率",example = "0.89")
    private BigDecimal loadGradient;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "电量",example = "123121")
    private BigDecimal energy;

    @ApiModelProperty(value = "雨量",example = "33")
    private BigDecimal rain;

    @ApiModelProperty(value = "最高温度",example = "38")
    private BigDecimal highestTemperature;

    /**
     * 周期均匀曲线 前三日(包含今日)的平均出力的平均值
     */
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "周期均匀曲线",example = "38")
    private BigDecimal avgPeriod;

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public BigDecimal getMaxLoad() {
        return maxLoad;
    }

    public void setMaxLoad(BigDecimal maxLoad) {
        this.maxLoad = maxLoad;
    }

    public BigDecimal getMinLoad() {
        return minLoad;
    }

    public void setMinLoad(BigDecimal minLoad) {
        this.minLoad = minLoad;
    }

    public BigDecimal getAvgLoad() {
        return avgLoad;
    }

    public void setAvgLoad(BigDecimal avgLoad) {
        this.avgLoad = avgLoad;
    }

    public BigDecimal getLoadGradient() {
        return loadGradient;
    }

    public void setLoadGradient(BigDecimal loadGradient) {
        this.loadGradient = loadGradient;
    }

    public BigDecimal getEnergy() {
        return energy;
    }

    public void setEnergy(BigDecimal energy) {
        this.energy = energy;
    }

    public BigDecimal getRain() {
        return rain;
    }

    public void setRain(BigDecimal rain) {
        this.rain = rain;
    }

    public BigDecimal getAvgPeriod() {
        return avgPeriod;
    }

    public void setAvgPeriod(BigDecimal avgPeriod) {
        this.avgPeriod = avgPeriod;
    }

    public BigDecimal getHighestTemperature() {
        return highestTemperature;
    }

    public void setHighestTemperature(BigDecimal highestTemperature) {
        this.highestTemperature = highestTemperature;
    }

}
