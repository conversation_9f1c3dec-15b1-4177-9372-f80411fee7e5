/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.acload.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsieframework.core.base.web.BaseRespBuilder;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.LoadFeatureAcHisService;
import com.tsintergy.lf.serviceapi.base.airconditioner.dto.AcFeatureAnalyseDTO;
import com.tsintergy.lf.serviceapi.base.airconditioner.dto.AcFeatureStatisticsDTO;
import com.tsintergy.lf.serviceapi.base.airconditioner.dto.AcLoadCurveDTO;
import com.tsintergy.lf.web.base.acload.request.AcLoadAnalyseRequest;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description:<br>
 *
 * @Author:<EMAIL>
 * @Date: 2022/5/23 14:32
 * @Version:1.0.0
 */
@RequestMapping("/ac/feature")
@RestController
@Api(tags = "空调负荷特性分析")
public class LoadFeatureAcHisController extends CommonBaseController {

    private Logger logger = LogManager.getLogger(LoadFeatureAcHisController.class);

    @Autowired
    LoadFeatureAcHisService loadFeatureAcHisService;

    /**
     * 日处理变化趋势图
     */
    @RequestMapping(value = "/power_K", method = RequestMethod.POST)
    @ApiOperation("获取空调负荷k线图")
    public BaseResp<List<AcFeatureAnalyseDTO>> getAcLoadKChart(@RequestBody AcLoadAnalyseRequest request)
        throws Exception {

        List<AcFeatureAnalyseDTO> AcFeatureAnalyseDTOList = loadFeatureAcHisService
            .getLoadFeatureAcHis(request.getCityId(), getCaliberId(), request.getStartDate(), request.getEndDate(),
                    request.getLoadType(), request.getWeatherType(), request.getWeatherId());
        return BaseRespBuilder.success().setData(AcFeatureAnalyseDTOList).build();
    }

    /**
     * 特性分析-日特性统计查询
     */
    @ApiOperation("空调负荷特性统计查询")
    @RequestMapping(value = "/statisticsAcFeature", method = RequestMethod.POST)
    public BaseResp<AcFeatureStatisticsDTO> statisticsAcFeature(@RequestBody AcLoadAnalyseRequest request)
        throws Exception {
        AcFeatureStatisticsDTO acFeatureStatisticsDTO = loadFeatureAcHisService
            .doStatisticsAcFeature(request.getCityId(), getCaliberId(), request.getDate(), request.getLoadType(),
                    request.getWeatherType(), request.getWeatherId());
        return BaseRespBuilder.success().setData(acFeatureStatisticsDTO, AcFeatureStatisticsDTO.class).build();
    }

    @ApiOperation("负荷曲线")
    @RequestMapping(value = "/loadCurve", method = RequestMethod.POST)
    public BaseResp<AcLoadCurveDTO> getLoadCurve(@RequestBody AcLoadAnalyseRequest request) {
        AcLoadCurveDTO acLoadCurveDTO = loadFeatureAcHisService
            .getLoadCurve(request.getCityId(), getCaliberId(), request.getStartDate(), request.getEndDate(), request.getWeatherId());
        return BaseRespBuilder.success().setData(acLoadCurveDTO, AcLoadCurveDTO.class).build();
    }

}