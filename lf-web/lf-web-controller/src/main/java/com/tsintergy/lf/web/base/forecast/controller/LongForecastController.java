/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.forecast.controller;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.SystemConstant;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.forecast.api.LongForecastService;
import com.tsintergy.lf.serviceapi.base.forecast.dto.LabelInfoDTO;
import com.tsintergy.lf.serviceapi.base.forecast.dto.LoadCityFcLongMonthDTO;
import com.tsintergy.lf.serviceapi.base.forecast.dto.LoadCityFcLongMonthUnit;
import com.tsintergy.lf.serviceapi.base.forecast.dto.LoadCityFcLongYearDTO;
import com.tsintergy.lf.serviceapi.base.forecast.dto.LoadCityFcLongYearUnit;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityFcLongMonthYearService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityMonthHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityFcLongMonthDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityFcLongYearDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityMonthHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.StatisticsAccuracyLongCityMonthDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureDayMonthLongFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherLongSettingService;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherFeatureLongFcDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherLongSettingDTO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherLongSettingDO;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import com.tsintergy.lf.web.base.forecast.request.WeatherFeatureLongFcRequest;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * Description: 中长期预测 <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/5/12 16:22
 * @Version: 1.0.0
 */

@RestController
@RequestMapping("/longForecast")
public class LongForecastController extends CommonBaseController {

    @Autowired
    private LongForecastService longForecastService;

    @Autowired
    private LoadCityFcLongMonthYearService loadCityFcLongMonthYearService;

    @Autowired
    private LoadFeatureCityMonthHisService loadFeatureCityMonthHisService;

    @Autowired
    private WeatherFeatureDayMonthLongFcService weatherFeatureDayMonthLongFcService;

    @Autowired
    private SettingSystemService settingSystemService;

    @Autowired
    private WeatherLongSettingService weatherLongSettingService;

    @ApiOperation("标签详情")
    @GetMapping("/label/getLabelInfo")
    public BaseResp<List<LabelInfoDTO>> getLabelInfo(String startYear, String endYear) {
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(longForecastService.findLabelInfo(startYear, endYear));
        return baseResp;
    }

    @ApiOperation("删除")
    @PostMapping("/label/delete")
    public BaseResp delete(@ApiParam @RequestBody List<String> ids) {
        BaseResp baseResp = BaseResp.succResp();
        longForecastService.deleteByIds(ids);
        return baseResp;
    }


    @ApiOperation("编辑添加")
    @PostMapping("/label/saveOrUpdate")
    public BaseResp saveOrUpdate(@ApiParam @RequestBody LabelInfoDTO labelInfoDTO) {
        BaseResp baseResp = BaseResp.succResp();
        longForecastService.saveOrUpdate(labelInfoDTO);
        return baseResp;
    }

    @ApiOperation("导入")
    @PostMapping("/label/import")
    public BaseResp labelImport(MultipartFile multipartFile) throws IOException {
        BaseResp baseResp = BaseResp.succResp();
        InputStream inputStream = multipartFile.getInputStream();
        longForecastService.doLabelImport(inputStream);
        return baseResp;
    }


    @ApiOperation("影响标签下拉框查询")
    @GetMapping("/label/getLabelType")
    public BaseResp<List<String>> getLabelType() throws IOException {
        BaseResp baseResp = BaseResp.succResp();
        List<String> labelType = longForecastService.getLabelType();
        baseResp.setData(labelType);
        return baseResp;
    }


    @ApiOperation("中长期预测查询页面-查询月度预测数据")
    @GetMapping("/month")
    public BaseResp getMonthData(String cityId, String month, String monthMode)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        LoadCityFcLongMonthDTO result = new LoadCityFcLongMonthDTO();
//        List<LoadCityFcLongMonthDO> vecMonth = loadCityFcLongMonthYearService
//            .findLoadFeatureMonth(cityId, AlgorithmEnum.LONG_VEC_MAX.getId(), month, month, super.getCaliberId());
//        if (CollectionUtils.isNotEmpty(vecMonth)) {
//            result.setVecData(process(vecMonth.get(0)));
//        }
        List<LoadCityFcLongMonthDO> sensitivityMonth = loadCityFcLongMonthYearService
            .findLoadFeatureMonth(cityId, AlgorithmEnum.LONE_COMPOSITE.getId(), month, month, super.getCaliberId());
        if (CollectionUtils.isNotEmpty(sensitivityMonth)) {
            result.setSensitivityData(process(sensitivityMonth.get(0)));
        }

        Date lastYearDate = DateUtils.addYears(DateUtil.getDate(month, DateUtil.DATE_FORMAT7), -1);
        String strDate = DateUtil.getStrDate(lastYearDate, DateUtil.DATE_FORMAT7);
        List<LoadFeatureCityMonthHisDO> monthHisDOS = loadFeatureCityMonthHisService
            .getLoadFeatureCityMonthHisDOS(cityId, strDate, strDate, super.getCaliberId());
        if (CollectionUtils.isNotEmpty(monthHisDOS)) {
            result.setLastYearHisData(process(monthHisDOS.get(0)));
        }
        Date date = DateUtils
            .addMonths(DateUtil.getDate(month + "-01", DateUtil.DATE_FORMAT2), -Integer.parseInt(monthMode));
        String startStr = new SimpleDateFormat(DateUtil.DATE_FORMAT7).format(date);
//        List<StatisticsAccuracyLongCityMonthDO> vecAccuracy = loadCityFcLongMonthYearService
//            .findLoadFeatureLongMonthAccuracy(cityId, AlgorithmEnum.LONG_VEC_MAX.getId(), startStr, month,
//                super.getCaliberId());
//        if (CollectionUtils.isNotEmpty(vecAccuracy)) {
//            result.setVecAccuracy(avgAndProcess(vecAccuracy));
//        }
        List<StatisticsAccuracyLongCityMonthDO> sensitivityAccuracy = loadCityFcLongMonthYearService
            .findLoadFeatureLongMonthAccuracy(cityId, AlgorithmEnum.LONE_COMPOSITE.getId(), startStr, month,
                super.getCaliberId());
        if (CollectionUtils.isNotEmpty(sensitivityAccuracy)) {
            result.setSensitivityAccuracy(avgAndProcess(sensitivityAccuracy));
        }
        baseResp.setData(result);
        return baseResp;
    }

    @ApiOperation("中长期预测查询页面-查询年度预测数据；")
    @GetMapping("/year")
    public BaseResp getYearData(String cityId, String year, String algorithmId)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        LoadCityFcLongYearDTO result = new LoadCityFcLongYearDTO();
        List<LoadCityFcLongYearDO> yearData = loadCityFcLongMonthYearService
            .findLoadFeatureYear(cityId,
//                "1".equals(algorithmId) ? AlgorithmEnum.LONE_COMPOSITE.getId() : AlgorithmEnum.LONG_VEC_MAX.getId(), year,
               AlgorithmEnum.LONE_COMPOSITE.getId(), year,
                super.getCaliberId());
        if (CollectionUtils.isNotEmpty(yearData)) {
            result.setForecastData(processYear(yearData));
        }
        Date lastYearDate = DateUtils.addYears(DateUtil.getDate(year, DateUtil.DATE_FORMAT12), -1);
        List<LoadFeatureCityMonthHisDO> lastYearData = loadFeatureCityMonthHisService
            .getLoadFeatureCityMonthHisVOByYear(cityId,
                DateUtil.getStrDate(lastYearDate, DateUtil.DATE_FORMAT12), super.getCaliberId());
        if (CollectionUtils.isNotEmpty(lastYearData)) {
            result.setLastYearforecastData(processLastYear(lastYearData));
        }
        baseResp.setData(result);
        return baseResp;
    }

    @ApiOperation("中长期预测查询页面-气象设置-查询接口")
    @GetMapping("/weatherSetting")
    public BaseResp getWeatherSettingData(String yearMonth, String year, String type)
        throws Exception {
        //前端暂未添加type参数；此处暂时按月度1处理；
        if (type == null) {
            type = "1";
        }
        BaseResp baseResp = BaseResp.succResp();
        WeatherFeatureLongFcDTO weatherFeatureDayMonthFcData;
        WeatherLongSettingDO weatherSetting;
        if (SystemConstant.LONG_FORECAST_MONTH.equals(type)) {
            weatherFeatureDayMonthFcData = weatherFeatureDayMonthLongFcService
                .findWeatherFeatureDayMonthFcData(yearMonth, yearMonth.split(Constants.SEPARATOR_BROKEN_LINE)[0]);
            weatherSetting = weatherLongSettingService
                .findWeatherSetting(yearMonth.split(Constants.SEPARATOR_BROKEN_LINE)[0],
                                    yearMonth.split(Constants.SEPARATOR_BROKEN_LINE)[1], type);

        } else {
            //年度不关注日维度气象；yearMonth为null
            weatherFeatureDayMonthFcData = weatherFeatureDayMonthLongFcService
                .findWeatherFeatureDayMonthFcData(null, year);
            weatherSetting = weatherLongSettingService.findWeatherSetting(year, null, type);
        }
        if (weatherSetting != null) {
            weatherFeatureDayMonthFcData.setMonthPlan(Integer.valueOf(weatherSetting.getPlan()));
            weatherFeatureDayMonthFcData.setForecastDetails(weatherSetting.getType());
            weatherFeatureDayMonthFcData.setAvgYear(
                Arrays.asList(weatherSetting.getPlanReplenish().split(Constants.SEPARATOR_PUNCTUATION)));
        }
        baseResp.setData(weatherFeatureDayMonthFcData);
        return baseResp;
    }

    @ApiOperation("中长期预测查询页面-气象设置-设置气象接口")
    @PostMapping("/weatherSetting")
    public BaseResp setYearData(@RequestBody WeatherFeatureLongFcRequest request)
        throws Exception {

        WeatherLongSettingDTO settingDTO = new WeatherLongSettingDTO();
        BeanUtils.copyProperties(request, settingDTO);
        weatherLongSettingService.doSaveOrUpdate(settingDTO);
        BaseResp baseResp = BaseResp.succResp();

        //预测内容是月度预测时；除了月气象，还需要入库日气象
        if (SystemConstant.LONG_FORECAST_MONTH.equals(String.valueOf(request.getForecastDetails()))) {
            weatherFeatureDayMonthLongFcService.doSaveOrUpdateLongWeatherSettingDay(request.getDaySetting());
        }
        //预测内容是年度；只入库月气象;其中的year参数，月度的从yearMonth获取，年度从year获取
        weatherFeatureDayMonthLongFcService
            .doSaveOrUpdateLongWeatherSettingMonth(request.getMonthSetting(),
                SystemConstant.LONG_FORECAST_MONTH.equals(String.valueOf(request.getForecastDetails())) ? request
                    .getYearMonth().split(Constants.SEPARATOR_BROKEN_LINE)[0] : request
                    .getYear());
        return baseResp;
    }

    private LoadCityFcLongYearUnit processLastYear(List<LoadFeatureCityMonthHisDO> srcList) {
        Map<String, LoadFeatureCityMonthHisDO> collect = srcList.stream()
            .collect(Collectors.toMap(LoadFeatureCityMonthHisDO::getMonth,
                Function.identity(), (o, n) -> n));
        List<String> monthList = DateUtil.getMonthList();
        List<BigDecimal> maxLoad = new ArrayList<>();
        List<BigDecimal> minLoad = new ArrayList<>();
        List<BigDecimal> monthElectricity = new ArrayList<>();
        for (String month : monthList) {
            maxLoad.add(collect.get(month) == null ? null : collect.get(month).getMaxLoad());
            minLoad.add(collect.get(month) == null ? null : collect.get(month).getMinLoad());
            monthElectricity.add(collect.get(month) == null ? null : collect.get(month).getEnergy());
        }
        LoadCityFcLongYearUnit forecastData = new LoadCityFcLongYearUnit();
        forecastData.setMinLoad(minLoad);
        forecastData.setMaxLoad(maxLoad);
        forecastData.setMonthElectricity(monthElectricity);
        return forecastData;
    }

    private LoadCityFcLongYearUnit processYear(List<LoadCityFcLongYearDO> srcList) {
        Map<String, LoadCityFcLongYearDO> collect = srcList.stream()
            .collect(Collectors.toMap(LoadCityFcLongYearDO::getMonth,
                Function.identity(), (o, n) -> n));
        List<String> monthList = DateUtil.getMonthList();
        List<BigDecimal> maxLoad = new ArrayList<>();
        List<BigDecimal> minLoad = new ArrayList<>();
        List<BigDecimal> monthElectricity = new ArrayList<>();
        for (String month : monthList) {
            maxLoad.add(collect.get(month) == null ? null : collect.get(month).getMaxLoad());
            minLoad.add(collect.get(month) == null ? null : collect.get(month).getMinLoad());
            monthElectricity.add(collect.get(month) == null ? null : collect.get(month).getEnergy());
        }
        LoadCityFcLongYearUnit forecastData = new LoadCityFcLongYearUnit();
        forecastData.setMinLoad(minLoad);
        forecastData.setMaxLoad(maxLoad);
        forecastData.setMonthElectricity(monthElectricity);
        return forecastData;
    }

    private LoadCityFcLongMonthUnit avgAndProcess(List<StatisticsAccuracyLongCityMonthDO> srcList) {
        LoadCityFcLongMonthUnit tar = new LoadCityFcLongMonthUnit();
        tar.setMaxLoad(BigDecimalFunctions.listAvg(srcList.stream().map(
            StatisticsAccuracyLongCityMonthDO::getMaxLoadAccuracy).collect(
            Collectors.toList())));
        tar.setMinLoad(BigDecimalFunctions.listAvg(srcList.stream().map(
            StatisticsAccuracyLongCityMonthDO::getMinLoadAccuracy).collect(
            Collectors.toList())));
        tar.setMonthElectricity(BigDecimalFunctions.listAvg(srcList.stream().map(
            StatisticsAccuracyLongCityMonthDO::getEnergyAccuracy).collect(
            Collectors.toList())));
        return tar;
    }

    private LoadCityFcLongMonthUnit process(LoadCityFcLongMonthDO src) {
        LoadCityFcLongMonthUnit tar = new LoadCityFcLongMonthUnit();
        BeanUtils.copyProperties(src, tar);
        tar.setMonthElectricity(src.getEnergy());
        return tar;
    }

    private LoadCityFcLongMonthUnit process(LoadFeatureCityMonthHisDO src) {
        LoadCityFcLongMonthUnit tar = new LoadCityFcLongMonthUnit();
        BeanUtils.copyProperties(src, tar);
        tar.setMonthElectricity(src.getEnergy());
        return tar;
    }


}