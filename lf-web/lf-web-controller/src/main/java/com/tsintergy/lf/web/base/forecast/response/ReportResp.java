/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司
 * Author:  wangchen
 * Date:  2020/6/15 9:35
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.forecast.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/6/15 
 * @since 1.0.0
 */
@ApiModel
public class ReportResp implements Serializable {

    @ApiModelProperty(value = "是否上报",example = "true")
    private Boolean report;

    @ApiModelProperty(value = "上报日期",example = "2021-03-17")
    private Date reportTime;

    public ReportResp() {
    }

    public ReportResp(Boolean report, Date reportTime) {
        this.report = report;
        this.reportTime = reportTime;
    }

    public Boolean getReport() {
        return report;
    }

    public void setReport(Boolean report) {
        this.report = report;
    }

    public Date getReportTime() {
        return reportTime;
    }

    public void setReportTime(Date reportTime) {
        this.reportTime = reportTime;
    }
}
