package com.tsintergy.lf.web.base.evalucation.request;


import com.tsintergy.lf.web.base.common.request.CommonRequest;
import io.swagger.annotations.ApiModelProperty;

/**
 * 准确率查询类
 * @date: 18-2-6 上午10:22
 * @author: taojingui
 **/
public class AccuracyRequest extends CommonRequest {

    /**
     * 开始时段
     */
    @ApiModelProperty(value = "开始时段")
    public String startPeriod;

    /**
     * 是否包含节假日
     */
    @ApiModelProperty(value = "是否包含节假日")
    private String isHoliday;

    /**
     * 	结束时段
     */
    @ApiModelProperty(value = "结束时段")
    public String endPeriod;

    /**
     * 	批次id
     */
    @ApiModelProperty(value = "批次id")
    public String batchId;

    public String getStartPeriod() {
        return startPeriod;
    }

    public void setStartPeriod(String startPeriod) {
        this.startPeriod = startPeriod;
    }

    public String getEndPeriod() {
        return endPeriod;
    }

    public void setEndPeriod(String endPeriod) {
        this.endPeriod = endPeriod;
    }

    public String getIsHoliday() {
        return isHoliday;
    }

    public void setIsHoliday(String isHoliday) {
        this.isHoliday = isHoliday;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }
}
