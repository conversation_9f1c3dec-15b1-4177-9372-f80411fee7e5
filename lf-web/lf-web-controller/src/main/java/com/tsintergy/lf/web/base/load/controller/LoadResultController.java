/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  wangfeng Date:  2020/11/27 4:56 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.load.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.base.load.api.LoadResultService;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadAccuracyDTO;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * Description:  <br>
 * 预测结果对比分析页面
 *
 * <AUTHOR>
 * @create 2020/11/27
 * @since 2.0.0
 */
@RestController
@RequestMapping(value = "/load/result")
@Api(tags = "负荷结果")
public class LoadResultController extends CommonBaseController {

    @Autowired
    private LoadResultService loadResultService;


    /**
     * 时刻准确率
     *
     * @param cityId
     * @param caliberId
     * @param date
     * @return
     * @throws Exception
     */
    @ApiOperation("时刻准确率")
    @GetMapping(value = "/point")
    public BaseResp<LoadAccuracyDTO> getPointAccuracy(String cityId, String caliberId, Date date) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        if (StringUtils.isEmpty(caliberId)) {
            caliberId = this.getCaliberId();
        }
        if (StringUtils.isEmpty(cityId)) {
            cityId = this.getLoginCityId();
        }
        LoadAccuracyDTO accuracyDTO = loadResultService.findAccuracy(cityId, caliberId, date);
        baseResp.setData(accuracyDTO);
        return baseResp;
    }


}