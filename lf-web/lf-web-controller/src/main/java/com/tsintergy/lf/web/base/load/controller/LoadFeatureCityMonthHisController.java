package com.tsintergy.lf.web.base.load.controller;


import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityMonthHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityMonthHisDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityMonthHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityMonthHisDO;
import com.tsintergy.lf.web.base.load.request.LoadAnalyseRequest;
import com.tsintergy.lf.web.base.load.response.LoadWeatherHisYearMonthResp;
import com.tsintergy.lf.web.base.load.response.YearMonthFeatureAnalyseResp;
import com.tsintergy.lf.web.base.load.response.YearMonthFeatureStatisticsResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 月负荷特性
 *
 * @author: wangh
 **/
@RequestMapping("/character/month")
@RestController
@Api(tags = "月负荷特性")
public class LoadFeatureCityMonthHisController extends BaseLoadFeatureController {

    @Autowired
    private WeatherFeatureCityMonthHisService weatherFeatureCityMonthHisService;

    @Autowired
    private LoadFeatureCityMonthHisService loadFeatureCityMonthHisService;

    @Autowired
    private WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    private LoadFeatureCityDayHisService loadFeatureCityDayHisService;

    @Autowired
    CityService cityService;

    /**
     * 负荷特性-月发电k线图
     */
    @ApiOperation("月发电k线图")
    @RequestMapping(value = "/power_K", method = RequestMethod.POST)
    public BaseResp<List<YearMonthFeatureAnalyseResp>> getMonthPowerFetureKChart(@RequestBody LoadAnalyseRequest monthRequest) throws Exception {
        //参数检查 如果时间为空，结束时间在系统时间当前时间，开始时间往前推12个月
        this.checkParam(monthRequest);
        String endYear = DateUtil.getYearByDate(this.getSystemDate());
        String endMonth = DateUtil.getMonthByDate(this.getSystemDate()).substring(5);
        String startYear = DateUtil.getYearByDate(DateUtils.addMonths(this.getSystemDate(), -12));
        String startMonth = endMonth;
        String startYM = startYear + "-" + startMonth;
        String endYM = endYear + "-" + endMonth;
        if (monthRequest.getStartDate() != null) {
            startYM = monthRequest.getStartDate();
        }
        if (monthRequest.getEndDate() != null) {
            endYM = monthRequest.getEndDate();
        }

        String weatherCityId = cityService.findWeatherCityId(monthRequest.getCityId());

        //月特性
        List<LoadFeatureCityMonthHisDO> powerFeatureCityMonthHisStatDOS = loadFeatureCityMonthHisService
                .getLoadFeatureCityMonthHisDOS(monthRequest.getCityId(), startYM, endYM, super.getCaliberId());
        List<WeatherFeatureCityMonthHisDO> weatherFeatureCityMonthHisStatDOS = weatherFeatureCityMonthHisService
                .findWeatherFeatureCityMonthHisStat(weatherCityId, startYM, endYM);

        //<year-month,rainfall> 将气象的日期和降雨量放到map中
        Map<String, BigDecimal> rainfallMap = new HashMap<>();
        //将气象的日期和最高温放到map中
        Map<String, BigDecimal> highestTemperatureMap = new HashMap<>();
        if (weatherFeatureCityMonthHisStatDOS != null && weatherFeatureCityMonthHisStatDOS.size() > 0) {
            //<year,List<WeatherFeatureCityMonthHisDO>>
            Map<String, List<WeatherFeatureCityMonthHisDO>> weatherFeatureYearMap = weatherFeatureCityMonthHisStatDOS
                    .stream().collect(Collectors.groupingBy(WeatherFeatureCityMonthHisDO::getYear));
            weatherFeatureYearMap.forEach((year, weatherFeatureCityMonthHisStatDOList) -> {
                //<month,rainfall>
                Map<String, BigDecimal> weatherFatureMonthMap = weatherFeatureCityMonthHisStatDOList.stream().collect(
                        Collectors.toMap(WeatherFeatureCityMonthHisDO::getMonth,
                                WeatherFeatureCityMonthHisDO::getRainfall));
                weatherFatureMonthMap.forEach((month, rainfall) -> {
                    rainfallMap.put((year + "-" + month), rainfall);
                });

                Map<String, BigDecimal> highestTemperatureDateMap = weatherFeatureCityMonthHisStatDOList.stream().collect(
                        Collectors.toMap(WeatherFeatureCityMonthHisDO::getMonth, WeatherFeatureCityMonthHisDO::getHighestTemperature));
                highestTemperatureDateMap.forEach((month, highestTemperature) -> {
                    highestTemperatureMap.put((year + "-" + month), highestTemperature);
                });
            });
        }
        Map<String, List<LoadFeatureCityMonthHisDO>> mapByDate = powerFeatureCityMonthHisStatDOS.stream()
                .collect(Collectors.groupingBy(dp -> dp.getYear() + "-" + Integer.valueOf(dp.getMonth())));

        //构建vo
        List<YearMonthFeatureAnalyseResp> resultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(powerFeatureCityMonthHisStatDOS)) {
            powerFeatureCityMonthHisStatDOS.forEach(monthDO -> {
                YearMonthFeatureAnalyseResp vo = new YearMonthFeatureAnalyseResp();
                vo.setAvgLoad(monthDO.getAveLoad());
                vo.setEnergy(monthDO.getEnergy());
                vo.setLoadGradient(monthDO.getLoadGradient());
                vo.setMaxLoad(monthDO.getMaxLoad());
                vo.setMinLoad(monthDO.getMinLoad());
                vo.setDate(
                        monthDO.getYear() + "-" + monthDO.getMonth());
                vo.setRain(rainfallMap.get(vo.getDate()));
                vo.setHighestTemperature(highestTemperatureMap.get(vo.getDate()));
                //获取前三个月的出力数据
                List<LoadFeatureCityMonthHisDO> firstData = mapByDate
                        .get(monthDO.getYear() + "-" + Integer.valueOf(monthDO.getMonth()));
                Integer secondMonth = Integer.valueOf(monthDO.getMonth()) - 1;
                Integer secondYear = Integer.valueOf(monthDO.getYear());
                //解决跨年日期
                if (secondMonth == 0) {
                    secondYear = secondYear - 1;
                    secondMonth = 12;
                }
                List<LoadFeatureCityMonthHisDO> secondData = mapByDate.get(secondYear + "-" + secondMonth);
                Integer thirdMonth = Integer.valueOf(monthDO.getMonth()) - 2;
                Integer thirdYear = Integer.valueOf(monthDO.getYear());
                if (thirdMonth == 0) {
                    thirdYear = thirdYear - 1;
                    thirdMonth = 12;
                }
                List<LoadFeatureCityMonthHisDO> thirdData = mapByDate.get(thirdYear + "-" + thirdMonth);
                if (firstData != null && secondData != null && thirdData != null) {
                    BigDecimal add1 = BigDecimalUtils
                            .add(BigDecimalUtils
                                            .add(firstData.get(0).getAveLoad(), secondData.get(0).getAveLoad()),
                                    thirdData.get(0).getAveLoad());
                    BigDecimal divide = BigDecimalUtils
                            .divide(add1, new BigDecimal(3), 4);
                    vo.setAvgPeriod(divide);
                }
                resultList.add(vo);
            });
        }
        if (resultList.size() < 1) {
            throw TsieExceptionUtils.newBusinessException("T706");
        }
        //按日期排序
        List<YearMonthFeatureAnalyseResp> collect = resultList.stream()
                .sorted(Comparator.comparing(YearMonthFeatureAnalyseResp::getDate)).collect(Collectors.toList());
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(collect);
        return baseResp;
    }


    /**
     * 特性分析-月特性统计查询
     */
    @ApiOperation("月特性统计查询")
    @RequestMapping(value = "/statisticsFeatureCity", method = RequestMethod.POST)
    public BaseResp<YearMonthFeatureStatisticsResp> statisticsFeatureCity(@RequestBody LoadAnalyseRequest loadAnalyseRequest) throws Exception {
        checkParam(loadAnalyseRequest);
        Date date = this.getSystemDate();
        if (loadAnalyseRequest.getDate() != null) {
            String dateStr = loadAnalyseRequest.getDate();
            date = DateUtils.string2Date(dateStr, DateFormatType.YEAR_MONTH_STR);
        }
        String year = DateUtil.getYearByDate(date);
        String month = DateUtil.getMonthByDate(date).substring(5);
        Date last = DateUtils.addYears(date,-1);
        String  lastYear= DateUtil.getYearByDate(last);
        String lastMonth= DateUtil.getMonthByDate(last).substring(5);
        LoadFeatureCityMonthHisDO loadMonth = loadFeatureCityMonthHisService
                .getLoadFeatureCityMonthHisDO(loadAnalyseRequest.getCityId(), year, month, super.getCaliberId());
        YearMonthFeatureStatisticsResp yearMonthFeatureStatisticsResp = new YearMonthFeatureStatisticsResp();
        if (loadMonth != null) {
            yearMonthFeatureStatisticsResp.setAvgloadGradient(loadMonth.getLoadGradient());
            yearMonthFeatureStatisticsResp.setAvgLoad(loadMonth.getAveLoad());
            yearMonthFeatureStatisticsResp.setMaxLoad(loadMonth.getMaxLoad());
            yearMonthFeatureStatisticsResp.setMinLoad(loadMonth.getMinLoad());
            yearMonthFeatureStatisticsResp.setTotalEnergy(loadMonth.getEnergy());
            yearMonthFeatureStatisticsResp.setDifferent(loadMonth.getDifferent());
            yearMonthFeatureStatisticsResp.setGradient(loadMonth.getGradient());
            yearMonthFeatureStatisticsResp.setDate(year + "-" + month);
        }
        LoadFeatureCityMonthHisDO lastData = loadFeatureCityMonthHisService
                .getLoadFeatureCityMonthHisDO(loadAnalyseRequest.getCityId(), lastYear, lastMonth, super.getCaliberId());
        if (lastData != null) {
            yearMonthFeatureStatisticsResp.setLastLoadGradient(lastData.getLoadGradient());
            yearMonthFeatureStatisticsResp.setLastAvgLoad(lastData.getAveLoad());
            yearMonthFeatureStatisticsResp.setLastMaxLoad(lastData.getMaxLoad());
            yearMonthFeatureStatisticsResp.setLastMinLoad(lastData.getMinLoad());
            yearMonthFeatureStatisticsResp.setLastDifferent(lastData.getDifferent());
            yearMonthFeatureStatisticsResp.setLastGradient(lastData.getGradient());
        }
//        List<WeatherFeatureCityMonthHisDO> weatherFeatureCityMonthHisStatDOS = weatherFeatureCityMonthHisService
//            .findWeatherFeatureCityMonthHisStat(loadAnalyseRequest.getCityId(), year + "-" + month, year + "-" + month);
//        if (CollectionUtils.isNotEmpty(weatherFeatureCityMonthHisStatDOS)) {
//            yearMonthFeatureStatisticsResp.setTotalRain(weatherFeatureCityMonthHisStatDOS.get(0).getRainfall());
//        }
//        if (CollectionUtils.isNotEmpty(weatherFeatureCityMonthHisStatDOS)) {
//            yearMonthFeatureStatisticsResp.setHighestTemperature(weatherFeatureCityMonthHisStatDOS.get(0).getHighestTemperature());
//        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(yearMonthFeatureStatisticsResp);
        return baseResp;
    }

    /**
     * 特性分析-月历史曲线
     */
    @ApiOperation("月历史曲线")
    @RequestMapping(value = "/hisPowerAndRain", method = RequestMethod.POST)
    public BaseResp<LoadWeatherHisYearMonthResp> getMonthHisPowerAndRain(@RequestBody LoadAnalyseRequest loadAnalyseRequest) throws Exception {
        checkParam(loadAnalyseRequest);
        Date date = this.getSystemDate();
        if (loadAnalyseRequest.getDate() != null) {
            String dateStr = loadAnalyseRequest.getDate();
            date = DateUtils.string2Date(dateStr, DateFormatType.YEAR_MONTH_STR);
        }
        Date startDate = DateUtil.getFirstDayOfMonth(date);
        Date endDate = DateUtil.getLastDayOfMonth(date);

        //获取月负荷特性
        List<LoadFeatureCityDayHisDO> loadList = loadFeatureCityDayHisService
                .findLoadFeatureCityDayHisDOS(loadAnalyseRequest.getCityId(), startDate, endDate, super.getCaliberId());
        if (CollectionUtils.isEmpty(loadList)) {
            throw TsieExceptionUtils.newBusinessException("T706");
        }
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        Map<java.sql.Date, LoadFeatureCityDayHisDO> collect = loadList.stream()
            .collect(Collectors.toMap(LoadFeatureCityDayHisDO::getDate, Function.identity(), (key1, key2) -> key2));
        List<LoadFeatureCityDayHisDO> resultList = new ArrayList<>();
        for (Date oneDay : dateList) {
            LoadFeatureCityDayHisDO hisDO = collect.get(new java.sql.Date(oneDay.getTime()));
            resultList.add(hisDO);
        }
        List<BigDecimal> minPower = resultList.stream()
                .map( load -> load ==null? null : load.getMinLoad()).collect(Collectors.toList());
        List<BigDecimal> avePower = resultList.stream()
                .map(load ->load==null? null: load.getAveLoad() ).collect(Collectors.toList());
        List<BigDecimal> loadGradient = resultList.stream()
                .map(load ->load==null? null:  load.getLoadGradient()).collect(Collectors.toList());
        List<BigDecimal> maxPower = resultList.stream()
                .map(load ->load==null? null:  load.getMaxLoad()).collect(Collectors.toList());
        LoadWeatherHisYearMonthResp powerWeatherHisYearMonthVO = new LoadWeatherHisYearMonthResp();
        powerWeatherHisYearMonthVO.setAveLoad(avePower);
        powerWeatherHisYearMonthVO.setLoadGradient(loadGradient);
        powerWeatherHisYearMonthVO.setMaxLoad(maxPower);
        powerWeatherHisYearMonthVO.setMinLoad(minPower);

        //查询气象特性数据
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisStatDOS = weatherFeatureCityDayHisService
                .listWeatherFeatureCityDayHisDO(loadAnalyseRequest.getCityId(), startDate, endDate);
        if (!CollectionUtils.isEmpty(weatherFeatureCityDayHisStatDOS)) {
            Map<java.sql.Date, WeatherFeatureCityDayHisDO> weatherCollect = weatherFeatureCityDayHisStatDOS.stream()
                .collect(Collectors.toMap(WeatherFeatureCityDayHisDO::getDate, Function.identity(), (key1, key2) -> key2));
            List<WeatherFeatureCityDayHisDO> weatherResultList = new ArrayList<>();
            for (Date oneDay : dateList) {
                WeatherFeatureCityDayHisDO weatherDO = weatherCollect.get(new java.sql.Date(oneDay.getTime()));
                weatherResultList.add(weatherDO);
            }
            //获取月降雨量
            List<BigDecimal> rain = weatherResultList.stream()
                .map(weather -> weather ==null? null : weather.getRainfall()).collect(Collectors.toList());

            //获取月温度
            List<BigDecimal> highestTemperature = weatherResultList.stream()
                .map(weather -> weather ==null? null: weather.getHighestTemperature()).collect(Collectors.toList());
            powerWeatherHisYearMonthVO.setRain(rain);
            powerWeatherHisYearMonthVO.setHighestTemperature(highestTemperature);
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(powerWeatherHisYearMonthVO);
        return baseResp;
    }
}
