/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.weather.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.algorithm.api.LongForecastAlgorithmService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayLongFcService;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherFeatureCityDayLongListVO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherFeatureCommonLongDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherFeatureLongDTO;
import com.tsintergy.lf.web.base.controller.BaseController;
import java.util.Date;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/5/19 10:19
 * @Version: 1.0.0
 */
@RestController
@RequestMapping(value = "/mediumAndLong")
public class WeatherLongController extends BaseController {

    @Autowired
    WeatherFeatureCityDayLongFcService weatherFeatureCityDayLongFcService;

    @Autowired
    LongForecastAlgorithmService longForecastAlgorithmService;

    @Autowired
    ScheduledThreadPoolExecutor scheduledThreadPoolExecutor;

    @GetMapping("/fcData")
    public BaseResp fcData(String cityId, Date startDate, Date endDate) throws Exception {
        WeatherFeatureLongDTO monthFcPageByParam = weatherFeatureCityDayLongFcService
            .findMonthFcPageByParam(cityId, startDate, endDate, this.getCaliberId());
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(monthFcPageByParam);
        return baseResp;
    }

    @PostMapping("/fcData")
    public BaseResp longFeature(@RequestBody WeatherFeatureCityDayLongListVO weatherFeatureCityDayLongListVO) {
        try {
            weatherFeatureCityDayLongFcService.saveOrUpdate(weatherFeatureCityDayLongListVO);
        } catch (Exception e) {
            BaseResp baseResp = BaseResp.failResp();
            baseResp.setRetMsg(e.getMessage());
            return baseResp;
        }
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }

    @PostMapping("/manualPrediction")
    public BaseResp manualPrediction(@RequestBody WeatherFeatureCommonLongDTO weatherFeatureCommonLongDTO,
        HttpServletRequest httpServletRequest) {
        try {
            httpServletRequest.getSession().setAttribute("longForecast", 0);
            HttpSession session = httpServletRequest.getSession();
            String caliberId = this.getCaliberId();
            scheduledThreadPoolExecutor.submit(((Runnable) () -> {
                try {
                    longForecastAlgorithmService
                        .statsLongForecast(caliberId, weatherFeatureCommonLongDTO.getCityId(),
                            weatherFeatureCommonLongDTO.getStartDate(), weatherFeatureCommonLongDTO.getEndDate());
                    session.setAttribute("longForecast", 1);
                } catch (Exception e) {
                    session.setAttribute("longForecast", 2);
                }
            }));
        } catch (Exception e) {
            BaseResp baseResp = BaseResp.failResp();
            baseResp.setRetMsg(e.getMessage());
            return baseResp;
        }
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }

    @PostMapping("/polling")
    public BaseResp polling(HttpServletRequest httpServletRequest) {
        HttpSession session = httpServletRequest.getSession();
        Integer longForecast = (Integer) session.getAttribute("longForecast");
        if (longForecast.equals(1)) {
            BaseResp baseResp = BaseResp.succResp();
            baseResp.setRetMsg("手动预测完成，正在查询最新结果。");
            return baseResp;
        } else if (longForecast.equals(2)) {
            BaseResp baseResp = BaseResp.failResp();
            baseResp.setRetCode("T888");
            baseResp.setRetMsg("手动预测失败！请检查算法运行条件。");
            return baseResp;
        } else {
            BaseResp baseResp = BaseResp.failResp();
            baseResp.setRetMsg("算法未调用完成");
            return baseResp;
        }
    }
}