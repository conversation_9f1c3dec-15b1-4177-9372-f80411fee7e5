package com.tsintergy.lf.web.base.forecast.controller;


import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsCityDayFcService;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.StatisticsCityDayDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.StatisticsDayDTO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadQueryDTO;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

@Api(tags = "预测查询")
@RequestMapping("/forecast/query")
@RestController
public class ForecastQueryController extends CommonBaseController {

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private StatisticsCityDayFcService statisticsCityDayFcService;


    /**
     * 预测查询---负荷预测
     *
     * @param cityId
     * @param startDate
     * @param endDate
     * @param algorithmId
     * @return
     * @throws Exception
     */
    @ApiOperation("负荷预测")
    @GetMapping(value = "/load")
    public BaseResp<LoadQueryDTO> findLoad(String cityId, Date startDate, Date endDate, String algorithmId, String batchId) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        String caliberId = this.getCaliberId();
        LoadQueryDTO queryDTO = loadCityHisService.findLoad(cityId, caliberId, startDate, endDate, algorithmId, batchId);
        if (queryDTO == null) {
            return new BaseResp("T706");
        }
        baseResp.setData(queryDTO);
        return baseResp;
    }


    /**
     * 预测查询--算法准确率
     *
     * @param cityId
     * @param startDate
     * @param endDate
     * @param algorithmId
     * @return
     * @throws Exception
     */
    @ApiOperation("预测算法准确率")
    @GetMapping(value = "/avg")
    public BaseResp<StatisticsCityDayDTO> findAccuracyAvg(String cityId, Date startDate, Date endDate, String algorithmId, String batchId) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        String caliberId = this.getCaliberId();
        StatisticsCityDayDTO statisticsCityDayDTO = statisticsCityDayFcService.getAccuracyAvg(cityId, caliberId, algorithmId, startDate, endDate, batchId);
        if (statisticsCityDayDTO == null) {
            return new BaseResp("T706");
        }
        baseResp.setData(statisticsCityDayDTO);
        return baseResp;
    }


    @ApiOperation("日准确率")
    @GetMapping(value = "/day")
    public BaseResp<List<StatisticsDayDTO>> getDayAccuracy(String cityId, String algorithmId, Date startDate, Date endDate, String batchId) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<StatisticsDayDTO> dayDTOS = statisticsCityDayFcService.getDayAccuracy(cityId, getCaliberId(), algorithmId, startDate, endDate, batchId);
        if (CollectionUtils.isEmpty(dayDTOS) || dayDTOS.size() < 1) {
            return new BaseResp("T706");
        }
        baseResp.setData(dayDTOS);
        return baseResp;
    }
}
