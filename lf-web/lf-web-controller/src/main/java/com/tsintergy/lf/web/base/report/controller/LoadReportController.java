/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  wangfeng Date:  2020/1/2 11:01 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.report.controller;

import com.tsieframework.cloud.security.serviceapi.system.api.SecurityService;
import com.tsieframework.cloud.security.serviceapi.system.pojo.TsieUserVO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DataUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.LoadUserDO;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsCityDayFcService;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayFcService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.load.api.ReportLoadHisMonthEnergyService;
import com.tsintergy.lf.serviceapi.base.load.dto.UserLoadReportDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.ReportLoadHisMonthEnergyDO;
import com.tsintergy.lf.serviceapi.base.report.api.ReportInfoLogService;
import com.tsintergy.lf.serviceapi.base.report.api.ReportLoadDayService;
import com.tsintergy.lf.serviceapi.base.report.api.ReportLoadHolidayService;
import com.tsintergy.lf.serviceapi.base.report.api.ReportLoadMonthService;
import com.tsintergy.lf.serviceapi.base.report.api.ReportLoadPeakService;
import com.tsintergy.lf.serviceapi.base.report.api.ReportLoadQuarterService;
import com.tsintergy.lf.serviceapi.base.report.api.ReportLoadWeekService;
import com.tsintergy.lf.serviceapi.base.report.api.ReportLoadYearService;
import com.tsintergy.lf.serviceapi.base.report.dto.ReportLoadPeakDTO;
import com.tsintergy.lf.serviceapi.base.report.dto.ReportUserAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportInfoLogDO;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportLoadDayDO;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportLoadHolidayDO;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportLoadMonthDO;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportLoadPeakDO;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportLoadQuarterDO;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportLoadWeekDO;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportLoadYearDO;
import com.tsintergy.lf.core.enums.ReportTypeEnum;
import com.tsintergy.lf.serviceapi.base.security.api.UserService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import com.tsintergy.lf.web.base.report.request.*;
import com.tsintergy.lf.web.base.report.response.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description:  <br> 负荷预测填报
 *
 * <AUTHOR>
 * @create 2020/1/2
 * @since 1.0.0
 */
@RequestMapping("/report")
@RestController
@Api(tags = "负荷预测填报")
public class LoadReportController extends CommonBaseController {

    @Autowired
    private ReportLoadDayService reportLoadDayService;

    @Autowired
    private ReportLoadWeekService reportLoadWeekService;

    @Autowired
    private ReportLoadMonthService reportLoadMonthService;

    @Autowired
    private ReportLoadQuarterService reportLoadQuarterService;

    @Autowired
    private ReportLoadYearService reportLoadYearService;

    @Autowired
    private ReportLoadHolidayService reportLoadHolidayService;

    @Autowired
    private ReportInfoLogService reportInfoLogService;

    @Autowired
    private HolidayService holidayService;

    @Autowired
    private ReportLoadHisMonthEnergyService reportLoadHisMonthEnergyService;

    @Autowired
    private CityService cityService;

    @Autowired
    private LoadCityFcService loadCityFcService;

    @Autowired
    private ReportLoadPeakService reportLoadPeakService;

    @Autowired
    private UserService userService;

    @Autowired
    private StatisticsCityDayFcService statisticsCityDayFcService;

    @Autowired
    private SecurityService securityService;

    @Autowired
    private LoadFeatureCityDayFcService loadFeatureCityDayFcService;


    @Autowired
    private LoadFeatureCityDayHisService loadFeatureCityDayHisService;

    @Autowired
    private SettingSystemService settingSystemService;
    /**
     * 早晚峰预测查询
     */
    @ApiOperation("早晚峰预测查询")
    @RequestMapping(value = "/find/peak", method = RequestMethod.GET)
    public BaseResp<ReportLoadPeakDTO> findPeakInfo(@ApiParam("城市ID") String cityId, @ApiParam("日期") Date date)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        ReportLoadPeakDTO reportLoadPeakDTO = new ReportLoadPeakDTO();
        List<ReportLoadPeakDO> reportLoadPeakVOS = reportLoadPeakService.findReportPeak(cityId, date, date);
        if (reportLoadPeakVOS == null) {
            //如果早晚峰上报表为空 从该日地调上报的96点负荷预测数据中获得
            List<LoadCityFcDO> loadCityFcVOS = loadCityFcService.getReportLoadCityFcDO(cityId, "1", date, date, 1);
            if (!CollectionUtils.isEmpty(loadCityFcVOS)) {
                List<BigDecimal> fcBigDecimals = BasePeriodUtils
                    .toList(loadCityFcVOS.get(0), Constants.WEATHER_CURVE_POINT_NUM,
                        Constants.WEATHER_CURVE_START_WITH_ZERO);
                reportLoadPeakDTO.setAmLoadTotal(fcBigDecimals == null ? null
                    : fcBigDecimals.get(45).setScale(0, BigDecimal.ROUND_HALF_UP));  //11：15
                reportLoadPeakDTO.setPmLoadTotal(fcBigDecimals == null ? null
                    : fcBigDecimals.get(67).setScale(0, BigDecimal.ROUND_HALF_UP));  //16：45
            }
        } else {
            // ->map(peakType{1.早高峰 2.晚高峰} , ReportLoadPeakDO)
            Map<Integer, ReportLoadPeakDO> peakTypeMap = reportLoadPeakVOS.stream()
                .collect(Collectors.toMap(e -> e.getPeakType(), e -> e, (oldv, curv) -> curv));
            ReportLoadPeakDO aMReportLoadPeakDO = peakTypeMap.get(1);
            ReportLoadPeakDO pMReportLoadPeakDO = peakTypeMap.get(2);
            reportLoadPeakDTO.setAmLoadTotal(
                aMReportLoadPeakDO == null || aMReportLoadPeakDO.getLoadTotal() == null ? null
                    : aMReportLoadPeakDO.getLoadTotal().setScale(0, BigDecimal.ROUND_HALF_UP));
            reportLoadPeakDTO.setAmLoadPlace(
                aMReportLoadPeakDO == null || aMReportLoadPeakDO.getLoadPlace() == null ? null
                    : aMReportLoadPeakDO.getLoadPlace().setScale(0, BigDecimal.ROUND_HALF_UP));
            reportLoadPeakDTO.setAmLoadSupply(
                aMReportLoadPeakDO == null || aMReportLoadPeakDO.getLoadSupply() == null ? null
                    : aMReportLoadPeakDO.getLoadSupply().setScale(0, BigDecimal.ROUND_HALF_UP));
            reportLoadPeakDTO.setPmLoadTotal(
                pMReportLoadPeakDO == null || pMReportLoadPeakDO.getLoadTotal() == null ? null
                    : pMReportLoadPeakDO.getLoadTotal().setScale(0, BigDecimal.ROUND_HALF_UP));
            reportLoadPeakDTO.setPmLoadPlace(
                pMReportLoadPeakDO == null || pMReportLoadPeakDO.getLoadPlace() == null ? null
                    : pMReportLoadPeakDO.getLoadPlace().setScale(0, BigDecimal.ROUND_HALF_UP));
            reportLoadPeakDTO.setPmLoadSupply(
                pMReportLoadPeakDO == null || pMReportLoadPeakDO.getLoadSupply() == null ? null
                    : pMReportLoadPeakDO.getLoadSupply().setScale(0, BigDecimal.ROUND_HALF_UP));
        }
        reportLoadPeakDTO.setDate(new java.sql.Date(date.getTime()));
        reportLoadPeakDTO.setCityId(cityId);
        baseResp.setData(reportLoadPeakDTO);
        return baseResp;
    }


    /**
     * 日负荷预测填报
     */
    @ApiOperation("日负荷预测填报")
    @RequestMapping(value = "/peak", method = RequestMethod.POST)
    public BaseResp reportDayInfo(@RequestBody ReportLoadPeakDTO reportRequest) throws Exception {
        if (reportRequest == null) {
            throw TsieExceptionUtils.newBusinessException("T705");
        }
        List<ReportLoadPeakDO> reportLoadPeakVOS = new ArrayList<>();
        //am
        ReportLoadPeakDO reportLoadPeakVOAM = new ReportLoadPeakDO();
        reportLoadPeakVOAM.setCityId(reportRequest.getCityId());
        reportLoadPeakVOAM.setDate(reportRequest.getDate());
        reportLoadPeakVOAM.setPeakType(1);
        reportLoadPeakVOAM.setLoadTotal(reportRequest.getAmLoadTotal());
        reportLoadPeakVOAM.setLoadPlace(reportRequest.getAmLoadPlace());
        reportLoadPeakVOAM.setLoadSupply(reportRequest.getAmLoadSupply());
        reportLoadPeakVOS.add(reportLoadPeakVOAM);
        //pm
        ReportLoadPeakDO reportLoadPeakVOPM = new ReportLoadPeakDO();
        reportLoadPeakVOPM.setCityId(reportRequest.getCityId());
        reportLoadPeakVOPM.setDate(reportRequest.getDate());
        reportLoadPeakVOPM.setPeakType(2);
        reportLoadPeakVOPM.setLoadTotal(reportRequest.getPmLoadTotal());
        reportLoadPeakVOPM.setLoadPlace(reportRequest.getPmLoadPlace());
        reportLoadPeakVOPM.setLoadSupply(reportRequest.getPmLoadSupply());
        reportLoadPeakVOS.add(reportLoadPeakVOPM);
        reportLoadPeakService.doSaveorUpdate(reportLoadPeakVOS);
        return BaseResp.succResp();
    }


    /**
     * 日负荷预测查询
     */
    @ApiOperation("日负荷预测查询")
    @GetMapping(value = "/find/day")
    public BaseResp<ReportLoadDayDO> findDayInfo(@ApiParam("城市ID") String cityId, @ApiParam("日期") Date date)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<ReportLoadDayDO> reportLoadDayVOS = reportLoadDayService.findReportDay(cityId, date, date);
        if (reportLoadDayVOS == null) {
            return new BaseResp("T706");
        }
        baseResp.setData(reportLoadDayVOS.get(0));
        return baseResp;
    }

    /**
     * 日负荷预测填报
     */
    @ApiOperation("日负荷预测填报")
    @RequestMapping(value = "/day", method = RequestMethod.POST)
    public BaseResp reportDayInfo(@RequestBody ReportRequest reportRequest) throws Exception {
        String dateStr = reportRequest.getDate();
        Date date = DateUtils.string2Date(dateStr, DateFormatType.SIMPLE_DATE_FORMAT_STR);
        List<ReportLoadDayDO> reportLoadDayVOS = reportLoadDayService.findReportDay(reportRequest.getCityId(), date,
            DateUtils.string2Date(reportRequest.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR));
        if (reportLoadDayVOS == null) {
            ReportLoadDayDO reportLoadDayVO = new ReportLoadDayDO();
            BeanUtils.copyProperties(reportRequest, reportLoadDayVO);
            reportLoadDayVO.setDate(new java.sql.Date(date.getTime()));
            reportLoadDayVO.setReportTime(new Date());
            reportLoadDayVO.setReport(true);
            reportLoadDayService.doSave(reportLoadDayVO);
        } else {
            ReportLoadDayDO reportLoadDayVO = reportLoadDayVOS.get(0);
            reportLoadDayVO.setReportTime(new Date());
            reportLoadDayVO.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            BeanUtils.copyProperties(reportRequest, reportLoadDayVO, "id");
            reportLoadDayService.doUpdate(reportLoadDayVO);
        }
        //保存到日志表中
        reportInfoLogService
            .doReportInfo(ReportTypeEnum.DAY.getType(), reportRequest.getCityId(), null, null, null, dateStr, null);
        return BaseResp.succResp();
    }

    /**
     * 查询周负荷预测填报
     */
    @ApiOperation("查询周负荷预测填报")
    @GetMapping(value = "/find/week")
    public BaseResp<ReportLoadWeekDO> findWeekInfo(@ApiParam("城市ID") String cityId, @ApiParam("年份") String year,
        @ApiParam("周") String week) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<ReportLoadWeekDO> weekLoadVOS = reportLoadWeekService.findWeekReport(cityId, year, week);
        if (CollectionUtils.isEmpty(weekLoadVOS) || weekLoadVOS.size() < 1) {
            return new BaseResp("T706");
        }
        baseResp.setData(weekLoadVOS.get(0));
        return baseResp;
    }

    /**
     * 周负荷预测填报
     */
    @ApiOperation("周负荷预测填报")
    @RequestMapping(value = "/week", method = RequestMethod.POST)
    public BaseResp reportWeekInfo(@RequestBody ReportRequest reportRequest) throws Exception {
        List<ReportLoadWeekDO> weekLoadVOS = reportLoadWeekService
            .findWeekReport(reportRequest.getCityId(), reportRequest.getYear(), reportRequest.getWeek());
        if (CollectionUtils.isNotEmpty(weekLoadVOS) && weekLoadVOS.size() > 0) {
            ReportLoadWeekDO reportLoadWeekVO = weekLoadVOS.get(0);
            reportLoadWeekVO.setReport(true);
            BeanUtils.copyProperties(reportRequest, reportLoadWeekVO, "id");
            reportLoadWeekVO.setReportTime(new Date());
            reportLoadWeekVO.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            reportLoadWeekService.doUpdate(reportLoadWeekVO);
        } else {
            ReportLoadWeekDO weekLoadVO = new ReportLoadWeekDO();
            weekLoadVO.setReport(true);
            BeanUtils.copyProperties(reportRequest, weekLoadVO);
            weekLoadVO.setReportTime(new Date());
            weekLoadVO.setDate(new java.sql.Date(
                DateUtils.string2Date(reportRequest.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR).getTime()));
            reportLoadWeekService.doSaveReportWeek(weekLoadVO);
        }
        reportInfoLogService
            .doReportInfo(ReportTypeEnum.WEEK.getType(), reportRequest.getCityId(), reportRequest.getYear(), null,
                reportRequest.getWeek(), null, null);
        return BaseResp.succResp();
    }

    /**
     * 查询月负荷预测填报
     */
    @ApiOperation("查询月负荷预测填报")
    @GetMapping(value = "/find/month")
    public BaseResp<ReportLoadMonthDO> findMonthInfo(@ApiParam("城市ID") String cityId, @ApiParam("年份") String year,
        @ApiParam("月份") String month) throws Exception {
        BaseResp<ReportLoadMonthDO> baseResp = BaseResp.succResp();
        List<ReportLoadMonthDO> reportLoadMonthVOS = reportLoadMonthService.findReportMonth(cityId, year, month);
        if (CollectionUtils.isNotEmpty(reportLoadMonthVOS) && reportLoadMonthVOS.size() > 0) {
            baseResp.setData(reportLoadMonthVOS.get(0));
            return baseResp;
        }
        return new BaseResp("T706");

    }

    /**
     * 月负荷预测填报
     */
    @ApiOperation("月负荷预测填报")
    @RequestMapping(value = "/month", method = RequestMethod.POST)
    public BaseResp reportMonthInfo(@RequestBody ReportRequest reportRequest) throws Exception {
        List<ReportLoadMonthDO> reportMonth = reportLoadMonthService
            .findReportMonth(reportRequest.getCityId(), reportRequest.getYear(), reportRequest.getMonth());
        if (CollectionUtils.isNotEmpty(reportMonth) && reportMonth.size() > 0) {
            ReportLoadMonthDO reportLoadMonthVO = reportMonth.get(0);
            BeanUtils.copyProperties(reportRequest, reportLoadMonthVO, "id");
            reportLoadMonthVO.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            reportLoadMonthVO.setReportTime(new Date());
            reportLoadMonthVO.setReport(true);
            reportLoadMonthService.doUpdate(reportLoadMonthVO);
        } else {
            ReportLoadMonthDO monthLoadVO = new ReportLoadMonthDO();
            BeanUtils.copyProperties(reportRequest, monthLoadVO);
            monthLoadVO.setReportTime(new Date());
            monthLoadVO.setReport(true);
            reportLoadMonthService.doSaveReportMonth(monthLoadVO);
        }
        reportInfoLogService
            .doReportInfo(ReportTypeEnum.MONTH.getType(), reportRequest.getCityId(), reportRequest.getYear(),
                reportRequest.getMonth(), null, null, null);
        return BaseResp.succResp();
    }

    /**
     * 查询季负荷填报
     */
    @ApiOperation("查询季负荷填报")
    @GetMapping(value = "/find/quarter")
    public BaseResp<List<ReportLoadQuarterDO>> findQuarterInfo(@ApiParam("城市ID") String cityId,
        @ApiParam("年份") String year, @ApiParam("quarter") String quarter)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        quarter = "0" + quarter;
        List<ReportLoadQuarterDO> reportLoadQuarterVOS = reportLoadQuarterService
            .findReportQuarter(cityId, year, quarter, null);
        if (CollectionUtils.isNotEmpty(reportLoadQuarterVOS) && reportLoadQuarterVOS.size() > 0) {
            baseResp.setData(reportLoadQuarterVOS);
            return baseResp;
        } else {
            return new BaseResp("T706");
        }
    }

    /**
     * 季负荷预测填报
     */
    @ApiOperation("季负荷预测填报")
    @RequestMapping(value = "/quarter", method = RequestMethod.POST)
    public BaseResp reportQuarterInfo(@RequestBody LoadRequest reportRequests) throws Exception {
        List<ReportRequest> requests = reportRequests.getReportRequests();
        for (ReportRequest reportRequest : requests) {
            List<ReportLoadQuarterDO> reportLoadQuarterVOS = reportLoadQuarterService
                .findReportQuarter(reportRequest.getCityId(), reportRequest.getYear(), reportRequest.getQuarter(),
                    reportRequest.getMonth());
            if (CollectionUtils.isNotEmpty(reportLoadQuarterVOS) && reportLoadQuarterVOS.size() > 0) {
                ReportLoadQuarterDO quarterLoadVO = reportLoadQuarterVOS.get(0);
                BeanUtils.copyProperties(reportRequest, quarterLoadVO, "id");
                quarterLoadVO.setReport(true);
                quarterLoadVO.setYear(reportRequest.getYear());
                quarterLoadVO.setReportTime(new Date());
                quarterLoadVO.setUpdateTime(new Timestamp(System.currentTimeMillis()));
                reportLoadQuarterService.doUpdate(quarterLoadVO);
            } else {
                ReportLoadQuarterDO quarterLoadVO = new ReportLoadQuarterDO();
                BeanUtils.copyProperties(reportRequest, quarterLoadVO);
                quarterLoadVO.setReport(true);
                quarterLoadVO.setYear(reportRequest.getYear());
                quarterLoadVO.setReportTime(new Date());
                reportLoadQuarterService.doSave(quarterLoadVO);
            }
        }
        ReportRequest reportRequest = requests.get(0);
        reportInfoLogService
            .doReportInfo(ReportTypeEnum.QUARTER.getType(), reportRequest.getCityId(), reportRequest.getYear(), null,
                null, null, reportRequest.getQuarter());
        return BaseResp.succResp();
    }


    /**
     * 查询年负荷预测填报
     */
    @ApiOperation("查询年负荷预测填报")
    @GetMapping(value = "/find/year")
    public BaseResp<List<ReportLoadYearDO>> findYearInfo(@ApiParam("城市ID") String cityId, @ApiParam("年份") String year)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<ReportLoadYearDO> reportLoadYearVOList = reportLoadYearService.findReportYear(cityId, year, null);
        if (CollectionUtils.isNotEmpty(reportLoadYearVOList) && reportLoadYearVOList.size() > 0) {
            baseResp.setData(reportLoadYearVOList);
            return baseResp;
        } else {
            return new BaseResp("T706");
        }
    }


    /**
     * 年负荷预测填报
     */
    @ApiOperation("年负荷预测填报")
    @RequestMapping(value = "/year", method = RequestMethod.POST)
    public BaseResp reportYearInfo(@RequestBody LoadRequest reportRequests) throws Exception {
        List<ReportRequest> requests = reportRequests.getReportRequests();
        if (CollectionUtils.isNotEmpty(requests)) {
            for (ReportRequest reportRequest : requests) {
                List<ReportLoadYearDO> yearLoadVOS = reportLoadYearService
                    .findReportYear(reportRequest.getCityId(), reportRequest.getYear(), reportRequest.getMonth());
                if (CollectionUtils.isNotEmpty(yearLoadVOS) && yearLoadVOS.size() > 0) {
                    ReportLoadYearDO yearLoadVO = yearLoadVOS.get(0);
                    yearLoadVO.setUpdateTime(new Timestamp(System.currentTimeMillis()));
                    BeanUtils.copyProperties(reportRequest, yearLoadVO, "id");
                    yearLoadVO.setReport(true);
                    yearLoadVO.setReportTime(new Date());
                    reportLoadYearService.doUpdate(yearLoadVO);
                } else {
                    ReportLoadYearDO reportLoadYearVO = new ReportLoadYearDO();
                    BeanUtils.copyProperties(reportRequest, reportLoadYearVO);
                    reportLoadYearVO.setReportTime(new Date());
                    reportLoadYearVO.setReport(true);
                    reportLoadYearService.doSave(reportLoadYearVO);
                }
            }
            ReportRequest reportRequest = requests.get(0);
            reportInfoLogService
                .doReportInfo(ReportTypeEnum.YEAR.getType(), reportRequest.getCityId(), reportRequest.getYear(), null,
                    null, null, null);
        }
        return BaseResp.succResp();
    }


    /**
     * 查询节假日预测填报
     */
    @ApiOperation("查询节假日预测填报")
    @GetMapping(value = "/find/holiday")
    public BaseResp<List<ReportLoadHolidayDO>> findHolidayInfo(@ApiParam("城市ID") String cityId,
        @ApiParam("年份") String year, @ApiParam("code") Integer code)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<HolidayDO> holidayVOS = holidayService.findByYearAndCode(year, code);
        HolidayDO holidayVO = holidayVOS.get(0);
        List<ReportLoadHolidayDO> reportLoadHolidayDOS = reportLoadHolidayService
            .findReportHoliday(cityId, holidayVO.getId(), null);
        if (CollectionUtils.isNotEmpty(reportLoadHolidayDOS) && reportLoadHolidayDOS.size() > 0) {
            baseResp.setData(reportLoadHolidayDOS);
            return baseResp;
        } else {
            List<ReportLoadHolidayDO> holidayVOList = new ArrayList<>();
            Date star = holidayVO.getStartDate();
            Date end = holidayVO.getEndDate();
            List<Date> holidayDate = DateUtil.getListBetweenDay(star, end);
            for (Date date : holidayDate) {
                ReportLoadHolidayDO reportLoadHolidayDO = new ReportLoadHolidayDO();
                reportLoadHolidayDO.setDate(new java.sql.Date(date.getTime()));
                holidayVOList.add(reportLoadHolidayDO);
            }
            baseResp.setData(holidayVOList);
        }
        return baseResp;
    }


    /**
     * 节假日预测填报
     */
    @ApiOperation("节假日预测填报")
    @PostMapping(value = "/holiday")
    public BaseResp reportHolidayInfo(@RequestBody LoadRequest reportRequests) throws Exception {
        List<ReportRequest> requests = reportRequests.getReportRequests();
        if (CollectionUtils.isNotEmpty(requests)) {
            String year = requests.get(0).getYear();
            Integer code = requests.get(0).getHolidayCode();
            List<HolidayDO> holidayVOS = holidayService.findByYearAndCode(year, code);
            HolidayDO holidayVO = holidayVOS.get(0);
            for (ReportRequest reportRequest : requests) {
                List<ReportLoadHolidayDO> reportLoadHolidayDOS = reportLoadHolidayService
                    .findReportHoliday(reportRequest.getCityId(), holidayVO.getId(),
                        DateUtils.string2Date(reportRequest.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR));
                if (CollectionUtils.isEmpty(reportLoadHolidayDOS) || reportLoadHolidayDOS.size() < 1) {
                    ReportLoadHolidayDO reportLoadHolidayDO = new ReportLoadHolidayDO();
                    BeanUtils.copyProperties(reportRequest, reportLoadHolidayDO);
                    reportLoadHolidayDO.setHolidayId(holidayVO.getId());
                    reportLoadHolidayDO.setReportTime(new Date());
                    reportLoadHolidayDO.setReport(true);
                    reportLoadHolidayDO.setDate(new java.sql.Date(
                        DateUtils.string2Date(reportRequest.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR)
                            .getTime()));
                    reportLoadHolidayService.doSave(reportLoadHolidayDO);
                } else {
                    ReportLoadHolidayDO reportLoadHolidayDO = reportLoadHolidayDOS.get(0);
                    reportLoadHolidayDO.setUpdateTime(new Timestamp(System.currentTimeMillis()));
                    reportLoadHolidayDO.setReport(true);
                    BeanUtils.copyProperties(reportRequest, reportLoadHolidayDO, "id");
                    reportLoadHolidayDO.setReportTime(new Date());
                    reportLoadHolidayService.doUpdate(reportLoadHolidayDO);
                }
            }
            ReportRequest reportRequest = requests.get(0);
            String typeName = reportRequest.getYear() + "-" + holidayVO.getHoliday();
            reportInfoLogService
                .doReportInfo(ReportTypeEnum.HOLIDAY.getType(), reportRequest.getCityId(), typeName, null, null, null,
                    null);
        }
        return BaseResp.succResp();
    }


    /**
     * 获取上报记录
     */
    @ApiOperation("获取上报记录")
    @GetMapping(value = "/record")
    public BaseResp<List<ReportInfoLogDO>> getReportLog(@ApiParam("城市ID")String cityId, @ApiParam("类型")Integer type) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<ReportInfoLogDO> reportInfoLogVOS = reportInfoLogService.findReportInfoLog(cityId, type, null);
        if (CollectionUtils.isEmpty(reportInfoLogVOS) || reportInfoLogVOS.size() < 1) {
            return new BaseResp("T706");
        }
        baseResp.setData(reportInfoLogVOS);
        return baseResp;
    }

    /**
     * 月实际电量填报查询接口
     *
     * @param date 格式 yyyy-mm
     */
    @ApiOperation("月实际电量填报查询接口")
    @RequestMapping(value = "/monthHis", method = RequestMethod.GET)
    public BaseResp<List<ReportLoadHisMonthEnergyResp>> getReportLog(@ApiParam("日期")String date) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<ReportLoadHisMonthEnergyResp> dateByDate = this.findDateByDate(date + "-01");
        if (CollectionUtils.isEmpty(dateByDate) || dateByDate.size() < 1) {
            return new BaseResp("T706");
        }
        baseResp.setData(dateByDate);
        return baseResp;
    }

    /**
     * 月实际电量填报 更新or 新增
     */
    @ApiOperation("月实际电量填报")
    @RequestMapping(value = "/monthHis", method = RequestMethod.POST)
    public BaseResp getReportLog(@RequestBody EnergyRequest request) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        for (ReportLoadHisMonthEnergyRequest entity : request.getData()) {
            ReportLoadHisMonthEnergyDO vo = new ReportLoadHisMonthEnergyDO();
            vo.setCityId(entity.getCityId());
            vo.setDate(new java.sql.Date(
                DateUtils.string2Date(entity.getDate() + "-01", DateFormatType.SIMPLE_DATE_FORMAT_STR).getTime()));
            if (entity.getHisEnergy() != null) {
                vo.setHisEnergy(entity.getHisEnergy().multiply(new BigDecimal(100000)));
            }
            this.reportLoadHisMonthEnergyService.doSaveOrUpdate(vo);
            List<ReportLoadMonthDO> fcEnergyMonth = this.reportLoadMonthService
                .findReportMonth(entity.getCityId(), entity.getDate().substring(0, 4),
                    entity.getDate().substring(5, 7));
            if (!CollectionUtils.isEmpty(fcEnergyMonth)) {
                ReportLoadMonthDO reportLoadMonthVO = fcEnergyMonth.get(0);
                if (entity.getReqEnergy() != null) {
                    reportLoadMonthVO.setReqEnergy(entity.getReqEnergy().multiply(new BigDecimal(100000)));
                }
                reportLoadMonthService.doUpdate(reportLoadMonthVO);
            } else {
                if (entity.getReqEnergy() != null) {
                    ReportLoadMonthDO monthVO = new ReportLoadMonthDO();
                    monthVO.setReqEnergy(entity.getReqEnergy().multiply(new BigDecimal(100000)));
                    monthVO.setCityId(entity.getCityId());
                    monthVO.setYear(entity.getDate().substring(0, 4));
                    monthVO.setMonth(entity.getDate().substring(5, 7));
                    monthVO.setCreateTime(new Timestamp(System.currentTimeMillis()));
                    reportLoadMonthService.doSaveReportMonth(monthVO);
                }
            }


        }
        return baseResp;
    }


    /**
     * 查询 月实际电量报表数据
     *
     * @param date 时间格式为 yyyy-mm-dd
     */
    private List<ReportLoadHisMonthEnergyResp> findDateByDate(String date) throws Exception {
        List<ReportLoadHisMonthEnergyResp> resultList = new ArrayList<>();
        List<CityDO> allCitys = this.cityService.findAllCitys();
        List<ReportLoadHisMonthEnergyDO> hisEnergyMonth = this.reportLoadHisMonthEnergyService
            .findHisEnergyMonth(null, date);
        Map<String, ReportLoadHisMonthEnergyDO> hisCollect = hisEnergyMonth.stream().collect(
            Collectors.toMap(ReportLoadHisMonthEnergyDO::getCityId, Function.identity(), (o, n) -> n));
        List<ReportLoadMonthDO> fcEnergyMonth = this.reportLoadMonthService
            .findReportMonth(null, date.substring(0, 4), date.substring(5, 7));
        Map<String, ReportLoadMonthDO> fcCollect = fcEnergyMonth.stream().collect(
            Collectors.toMap(ReportLoadMonthDO::getCityId, Function.identity(), (o, n) -> n));
        for (CityDO city : allCitys) {
            ReportLoadHisMonthEnergyResp result = new ReportLoadHisMonthEnergyResp();
            String key = city.getId();
            result.setCity(city.getCity());
            result.setCityId(key);
            result.setDate(date.substring(0, 7));
            ReportLoadHisMonthEnergyDO hisVo = hisCollect.get(key);
            if (hisVo != null && hisVo.getHisEnergy() != null) {
                result.setHisEnergy(hisVo.getHisEnergy().divide(new BigDecimal(100000)));
            }
            ReportLoadMonthDO fcVo = fcCollect.get(key);
            if (fcVo != null && fcVo.getReqEnergy() != null) {
                result.setReqEnergy(fcVo.getReqEnergy().divide(new BigDecimal(100000)));
            }
            resultList.add(result);
        }
        return resultList;
    }



    /**
     * 多日用户上报准确率统计
     */
    @ApiOperation("多日用户上报准确率统计")
    @RequestMapping(value = "/find/userAccuracy", method = RequestMethod.POST)
    public BaseResp<ReportAccuracyResp> getUserAccuracy(@RequestBody ReportAccuracyRequest reportAccuracyRequest) throws Exception {
        //参数校验
        if (StringUtils.isEmpty(reportAccuracyRequest.getCaliberId())) {
            reportAccuracyRequest.setCaliberId(this.getCaliberId());
        }
        if (StringUtils.isEmpty(reportAccuracyRequest.getCityId())) {
            reportAccuracyRequest.setCityId(this.getLoginCityId());
        }
        Map<String, String> userMap = new HashMap<>();
        List<String> userList = new ArrayList<>();
        if (reportAccuracyRequest.getUserIds() == null || StringUtils
            .equals("all", reportAccuracyRequest.getUserIds().get(0))) {
            List<LoadUserDO> loadUserList = userService.findLoadUserList(reportAccuracyRequest.getCityId());
            if (CollectionUtils.isEmpty(loadUserList)) {
                return new BaseResp("T706");
            }
            for (LoadUserDO LoadUserDO : loadUserList) {
                userList.add(LoadUserDO.getTsieUId());
            }
            reportAccuracyRequest.setUserIds(userList);
        }
        for (String userId : reportAccuracyRequest.getUserIds()) {
            DBQueryParam param = DBQueryParamBuilder.create().build();
            param.getQueryConditions().put("_se_id", userId);
            TsieUserVO vo =  securityService.queryTsieUserVo(param);
            if (vo == null) {
                continue;
            }
            userMap.put(userId, vo.getNickname());
        }


        ReportAccuracyResp reportAccuracyResp = new ReportAccuracyResp();
        List<ReportUserAccuracyDTO> reportUserAccuracyDTOS = new ArrayList<>();
        List<BigDecimal> totalAccuracy = new ArrayList<>();
        //查询每个用户上报准确率信息
        for (String userId : reportAccuracyRequest.getUserIds()) {
            ReportUserAccuracyDTO reportUserAccuracyDTO = new ReportUserAccuracyDTO();
            List<LoadCityFcDO> LoadCityFcList = loadCityFcService
                .findLoadCityFcReportByUserId(reportAccuracyRequest.getCityId(), reportAccuracyRequest.getCaliberId(),
                    userId, reportAccuracyRequest.getStartDate(), reportAccuracyRequest.getEndDate());
            if (CollectionUtils.isEmpty(LoadCityFcList)) {
                continue;
            }
            List<BigDecimal> accuracyList = new ArrayList<>();
            //查询上报准确率
            for (LoadCityFcDO loadCityFcDO : LoadCityFcList) {
                List<StatisticsCityDayFcDO> reportAccuracy = statisticsCityDayFcService
                    .getReportAccuracy(loadCityFcDO.getCityId(), loadCityFcDO.getCaliberId(), loadCityFcDO.getDate(),
                        loadCityFcDO.getDate());
                if (CollectionUtils.isEmpty(reportAccuracy)) {
                    continue;
                }
                accuracyList.add(reportAccuracy.get(0).getAccuracy());
            }
            totalAccuracy.addAll(accuracyList);
            reportUserAccuracyDTO.setUserId(userId);
            reportUserAccuracyDTO.setName(userMap.get(userId));
            reportUserAccuracyDTO.setDays(LoadCityFcList.size());
            reportUserAccuracyDTO.setAvgAccuracy(BigDecimalUtils.avgList(accuracyList, 4, false));
            reportUserAccuracyDTO.setMaxAccuracy(BigDecimalUtils.getMax(accuracyList));
            reportUserAccuracyDTO.setMinAccuracy(BigDecimalUtils.getMin(accuracyList));
            reportUserAccuracyDTOS.add(reportUserAccuracyDTO);
        }
        //计算总准确率信息
        reportAccuracyResp.setReportDays(totalAccuracy.size());
        reportAccuracyResp.setUserReportAccuracy(reportUserAccuracyDTOS);
        reportAccuracyResp.setAvgAccuracy(BigDecimalUtils.avgList(totalAccuracy, 4, false));
        reportAccuracyResp.setMaxAccuracy(BigDecimalUtils.getMax(totalAccuracy));
        reportAccuracyResp.setMinAccuracy(BigDecimalUtils.getMin(totalAccuracy));

        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(reportAccuracyResp);
        return baseResp;
    }


    /**
     * 单用户上报详细信息
     */
    @ApiOperation("单用户上报详细信息")
    @RequestMapping(value = "/user/loadInfo", method = RequestMethod.GET)
    public BaseResp<List<UserLoadReportDTO>> getUserLoadInfo(@ApiParam("城市id") String cityId,
        @ApiParam("口径id") String caliberId, @ApiParam("用户id")String userId,
        @ApiParam("开始时间")Date startDate, @ApiParam("结束时间")Date endDate)
        throws Exception {
        if (StringUtils.isEmpty(caliberId)) {
            caliberId = this.getCaliberId();
        }
        if (StringUtils.isEmpty(cityId)) {
            cityId = this.getLoginCityId();
        }
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.getQueryConditions().put("_se_id", userId);
        TsieUserVO vo = this.securityService.queryTsieUserVo(param);
        if (vo == null) {
            return BaseResp.failResp("查询用户失败");
        }

        //查询用户上报的预测数据
        List<UserLoadReportDTO> userLoadReportDTOS = new ArrayList<>();
        List<LoadCityFcDO> LoadCityFcList = loadCityFcService
            .findLoadCityFcReportByUserId(cityId, caliberId, userId, startDate, endDate);
        if (CollectionUtils.isEmpty(LoadCityFcList)) {
            return new BaseResp("T706");
        }
        //根据预测数据查询对应日期下详细信息
        for (LoadCityFcDO loadCityFcVO : LoadCityFcList) {
            UserLoadReportDTO userLoadReportDTO = new UserLoadReportDTO();
            List<StatisticsCityDayFcDO> reportAccuracy = statisticsCityDayFcService
                .getReportAccuracy(loadCityFcVO.getCityId(), loadCityFcVO.getCaliberId(), loadCityFcVO.getDate(),
                    loadCityFcVO.getDate());
            LoadFeatureCityDayFcDO loadFeatureCityDayFcVO = loadFeatureCityDayFcService
                .findLoadFeatureCityDayFcReport(cityId, caliberId, loadCityFcVO.getDate());
            List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisVOS = loadFeatureCityDayHisService
                .findLoadFeatureCityDayHisVOS(cityId, loadCityFcVO.getDate(), loadCityFcVO.getDate(), caliberId);
            if (CollectionUtils.isEmpty(reportAccuracy) || CollectionUtils.isEmpty(loadFeatureCityDayHisVOS)
                || loadFeatureCityDayFcVO == null) {
                continue;
            }
            userLoadReportDTO.setUserId(userId);
            userLoadReportDTO.setName(vo.getNickname());
            userLoadReportDTO.setAccuracy(reportAccuracy.get(0).getAccuracy());
            userLoadReportDTO.setReportTime(com.tsintergy.lf.core.util.DateUtil
                .getDateToStrFORMAT(loadCityFcVO.getReportTime(),
                    com.tsintergy.lf.core.util.DateUtil.DATE_FORMAT1));
            userLoadReportDTO.setDate(loadCityFcVO.getDate());
            userLoadReportDTO.setMaxLoadFc(loadFeatureCityDayFcVO.getMaxLoad().setScale(0, BigDecimal.ROUND_HALF_UP));
            userLoadReportDTO.setMinLoadFc(loadFeatureCityDayFcVO.getMinLoad().setScale(0, BigDecimal.ROUND_HALF_UP));
            userLoadReportDTO
                .setMaxLoadHis(loadFeatureCityDayHisVOS.get(0).getMaxLoad().setScale(0, BigDecimal.ROUND_HALF_UP));
            userLoadReportDTO
                .setMinLoadHis(loadFeatureCityDayHisVOS.get(0).getMinLoad().setScale(0, BigDecimal.ROUND_HALF_UP));
            userLoadReportDTOS.add(userLoadReportDTO);
        }

        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(userLoadReportDTOS);
        return baseResp;
    }

    /**
     * 多地市月上报率查询
     */
    @ApiOperation("多地市月上报率查询")
    @RequestMapping(value = "/findMultCity/month", method = RequestMethod.POST)
    public BaseResp<List<ReportStatMonthResp>> findMultCityReportByMonth(@RequestBody ReportStatMonthRequest req) throws Exception {
        //参数校验
        BaseResp baseResp = BaseResp.succResp();
        if (StringUtils.isEmpty(req.getCaliberId())) {
            req.setCaliberId(this.getCaliberId());
        }
        //获取该月第一天和最后一天，如果该月还未结束，结束时间等于查询该天
        Date date = DateUtil.getDate(req.getDate(), DateFormatType.YEAR_MONTH_STR.getValue());
        Date startDate = DateUtil.getFirstDayOfMonth(date);
        Date endDate = DateUtil.getLastDayOfMonth(date);
        if (StringUtils.equals(DateUtil.getCureDateStr(DateFormatType.YEAR_MONTH_STR.getValue()),
            DateUtil.getStrDate(date, DateFormatType.YEAR_MONTH_STR.getValue()))) {
            endDate = DateUtil.getCureDate();
        }
        List<CityDO> cityList = new ArrayList<>();
        if (req.getCityIds() == null || req.getCityIds().size() == 0 || "all".equals(req.getCityIds().get(0))) {
            cityList = cityService.findAllCitys();
        } else {
            for (String cityId : req.getCityIds()) {
                CityDO cityById = cityService.findCityById(cityId);
                if (cityById != null) {
                    cityList.add(cityById);
                }
            }
        }

        //查询上报截止时间
        String time = settingSystemService.findEndReportTime();
        if (CollectionUtils.isEmpty(cityList)) {
            baseResp.setRetCode("T706");
            baseResp.setRetMsg("查询数据为空");
            return baseResp;
        }
        //获取上报天数
        List<ReportStatMonthResp> reportStatMonthDTOS = new ArrayList<>();
        for (CityDO cityVO : cityList) {
            ReportStatMonthResp reportStatMonthDTO = new ReportStatMonthResp();
            reportStatMonthDTO.setCity(cityVO.getCity());
            reportStatMonthDTO.setCityId(cityVO.getId());
            List<LoadCityFcDO> loadCityFcVOS = loadCityFcService
                .listReportLoadCityFc(cityVO.getId(), req.getCaliberId(), startDate, endDate,
                    AlgorithmEnum.FORECAST_MODIFY.getId());
            for (LoadCityFcDO loadCityFcVO : loadCityFcVOS) {
                String endReportTime =
                    DateUtil.getStrDate(loadCityFcVO.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR.getValue()) + " "
                        + time;
                if (loadCityFcVO.getReportTime() == null
                    || endReportTime.compareTo(loadCityFcVO.getReportTime().toString()) >= 0) {
                    reportStatMonthDTO.setReportNormal(reportStatMonthDTO.getReportNormal() + 1);
                } else {
                    reportStatMonthDTO.setReprotDelayed(reportStatMonthDTO.getReprotDelayed() + 1);
                }
            }
            reportStatMonthDTO.setReportTotal(loadCityFcVOS.size());
            reportStatMonthDTOS.add(reportStatMonthDTO);
        }

        baseResp.setData(reportStatMonthDTOS);
        return baseResp;
    }


    /**
     * 单地市月上报率查询
     */
    @ApiOperation("单地市月上报率查询")
    @RequestMapping(value = "/findReportStats/month", method = RequestMethod.GET)
    public BaseResp<ReportStatMonthRateResp> findReportStatsByMonth(@ApiParam("城市id") String cityId,
        @ApiParam("口径id")String caliberId, @ApiParam("日期") String date) throws Exception {
        //参数校验
        BaseResp baseResp = BaseResp.succResp();
        if (StringUtils.isEmpty(caliberId)) {
            caliberId = this.getCaliberId();
        }
        //获取该月第一天和最后一天，如果该月还未结束，结束时间等于查询该天
        Date startDate = DateUtil.getFirstDayOfMonth(DateUtil.getDate(date, DateFormatType.YEAR_MONTH_STR.getValue()));
        Date endDate = DateUtil.getLastDayOfMonth(DateUtil.getDate(date, DateFormatType.YEAR_MONTH_STR.getValue()));
        if (StringUtils.equals(DateUtil.getCureDateStr(DateFormatType.YEAR_MONTH_STR.getValue()), DateUtil
            .getStrDate(DateUtil.getDate(date, DateFormatType.YEAR_MONTH_STR.getValue()),
                DateFormatType.YEAR_MONTH_STR.getValue()))) {
            endDate = DateUtil.getCureDate();
        }
        //查询上报截止时间,统计月上报率，记录未上报和延时上报详细信息，
        ReportStatMonthRateResp reportStatMonthRateResp = new ReportStatMonthRateResp();
        List<DelayReportResp> delayReports = new ArrayList<>();
        String time = settingSystemService.findEndReportTime();
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        double normal = 0.0000;
        double delayed = 0.0000;
        int days = DateUtil.countDaysOfMonth(date.substring(0, 4), date.substring(5, 7));
        for (Date date1 : dateList) {
            DelayReportResp delayReportResp = new DelayReportResp();
            List<LoadCityFcDO> loadCityFcVOS = loadCityFcService
                .listReportLoadCityFc(cityId, caliberId, date1, date1, AlgorithmEnum.FORECAST_MODIFY.getId());
            LoadCityFcDO loadCityFcVO = null;
            if (CollectionUtils.isEmpty(loadCityFcVOS)) {
                delayReportResp.setDate(date1);
                delayReports.add(delayReportResp);
                continue;
            } else {
                loadCityFcVO = loadCityFcVOS.get(0);
            }
            //比较是否在截止上报直接之前，是则正常上报，否则延时上报
            String endReportTime =
                DateUtil.getStrDate(loadCityFcVO.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR.getValue()) + " "
                    + time;
            if (loadCityFcVO.getReportTime() == null
                || endReportTime.compareTo(loadCityFcVO.getReportTime().toString()) >= 0) {
                normal++;
            } else {
                delayReportResp.setDate(loadCityFcVO.getDate());
                delayReportResp.setReoprtDate(
                    DateUtil.getStrDate(loadCityFcVO.getReportTime(), DateFormatType.DATE_FORMAT_STR.getValue()));
                DBQueryParam param = DBQueryParamBuilder.create().build();
                //DBQueryParam param = new DBQueryParam();
                param.getQueryConditions().put("_se_id", loadCityFcVO.getUserId());
                TsieUserVO vo = this.securityService.queryTsieUserVo(param);
                if (vo != null && loadCityFcVO.getUserId() != null) {
                    delayReportResp.setNickName(vo.getNickname());
                }
                delayReports.add(delayReportResp);
                delayed++;
            }
        }

        reportStatMonthRateResp.setCity(cityService.findCityById(cityId).getCity());
        reportStatMonthRateResp
            .setNormal(new BigDecimal(normal / days).multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_DOWN));
        reportStatMonthRateResp.setDelayed(
            new BigDecimal(delayed / days).multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_DOWN));
        reportStatMonthRateResp.setTotal(
            new BigDecimal((delayed + normal) / days).multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_DOWN));
        reportStatMonthRateResp.setDelayReports(delayReports);
        baseResp.setData(reportStatMonthRateResp);
        return baseResp;
    }

    @ApiOperation("上传文件至国调服务器")
    @RequestMapping(value = "/reportDayForecastResultFileToGuoDiao", method = RequestMethod.GET)
    public BaseResp<String> reportDayForecastResultFileToGuoDiao(@ApiParam("日期") String date) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        loadCityFcService.reportDayForecastResultFileToGuoDiao(DateUtil.getDate(date, DateFormatType.SIMPLE_DATE_FORMAT_STR.getValue()));
        baseResp.setData("ok");
        return baseResp;
    }

    @ApiOperation("上传文件至华东服务器")
    @RequestMapping(value = "/reportDayForecastResultFileToDky", method = RequestMethod.GET)
    public BaseResp<String> reportDayForecastResultFileToDky(@ApiParam("日期") String date) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        loadCityFcService.reportDayForecastResultFileToDky(DateUtil.getDate(date, DateFormatType.SIMPLE_DATE_FORMAT_STR.getValue()));
        baseResp.setData("ok");
        return baseResp;
    }


}