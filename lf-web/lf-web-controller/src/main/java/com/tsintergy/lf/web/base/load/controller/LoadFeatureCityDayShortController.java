package com.tsintergy.lf.web.base.load.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.base.load.api.LoadBatchAccuracyService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayFcShortService;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadBatchAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadBatchAccuracyRequest;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureShortFcDayDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadShortFcAccuracyDTO;
import com.tsintergy.lf.web.base.load.request.LoadShortFcFeatureRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.org.eclipse.jdt.core.dom.NullLiteral;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * 超短期日负荷特性
 **/
@RequestMapping("/shortFc/feature")
@RestController
@Slf4j
@Api(tags = "超短期日负荷特性")
public class LoadFeatureCityDayShortController  extends BaseLoadFeatureController{

    @Autowired
    private LoadFeatureCityDayFcShortService loadFeatureCityDayFcShortService;

    @Autowired
    private LoadBatchAccuracyService loadBatchAccuracyService;

    /**
     * 日准确率
     */
    @ApiOperation("日准确率")
    @RequestMapping(value = "/dayAccuracy", method = RequestMethod.POST)
    public BaseResp<List<LoadFeatureShortFcDayDTO>> statisticsFeatureCity(@RequestBody LoadShortFcFeatureRequest request) throws Exception {
        BaseResp baseResp = null;
        try {
            String caliberId = getCaliberId();
            List<LoadFeatureShortFcDayDTO> loadFeatureShortFcDayDTOS = loadFeatureCityDayFcShortService
                    .findFeatureStatisDTOS(request.getStartDate() , request.getEndDate() , caliberId , request.getCityId() ,request.getType());
            baseResp = BaseResp.succResp();
            baseResp.setData(loadFeatureShortFcDayDTOS);
        }catch (Exception e){
            e.printStackTrace();
            baseResp =   BaseResp.failResp("查询超短期预测日准确率失败");
        }
        return  baseResp;
    }


    /**
     * 时刻准确率
     */
    @ApiOperation("时刻准确率")
    @RequestMapping(value = "/dayTimeAccuracy", method = RequestMethod.POST)
    public BaseResp<List<LoadShortFcAccuracyDTO>> dayTimeAccuracy(@RequestBody LoadShortFcFeatureRequest request) throws Exception {
        BaseResp baseResp = null;
        try {
            String caliberId = getCaliberId();
            Date date = request.getDate();
            List<LoadShortFcAccuracyDTO> loadShortFcAccuracyDTOS = loadFeatureCityDayFcShortService.findDayTimeAccuracy(date , request.getCityId() , caliberId , request.getType());
            baseResp = BaseResp.succResp();
            baseResp.setData(loadShortFcAccuracyDTOS);
        }catch (Exception e){
            e.printStackTrace();
            baseResp = BaseResp.failResp("查询超短期预测时刻准确率失败");
        }
        return  baseResp;
    }

    /**
     * 批次准确率
     */
    @ApiOperation("批次准确率")
    @RequestMapping(value = "/batchFcAccuracy", method = RequestMethod.POST)
    public BaseResp<List<LoadBatchAccuracyDTO>> batchFcFeatureCity(@RequestBody LoadBatchAccuracyRequest request) throws Exception {
        BaseResp baseResp = null;
        try {
            String caliberId = getCaliberId();
            request.setCaliberId(caliberId);
            List<LoadBatchAccuracyDTO> loadBatchAccuracyDTOS = loadBatchAccuracyService
                    .findBatchFcAccuracy(request);
            baseResp = BaseResp.succResp();
            baseResp.setData(loadBatchAccuracyDTOS);
        }catch (Exception e){
            e.printStackTrace();
            baseResp = BaseResp.failResp("查询批次准确率失败");
        }
        return  baseResp;
    }


    /**
     * 批次时刻准确率
     */
    @ApiOperation("批次时刻准确率")
    @RequestMapping(value = "/batchFcTimeAccuracy", method = RequestMethod.GET)
    public BaseResp<List<LoadShortFcAccuracyDTO>> batchFcTimeAccuracy(@ApiParam("批次") String batchId) throws Exception {
        BaseResp baseResp = null;
        try {
            List<LoadShortFcAccuracyDTO> loadShortFcAccuracyDTOS = loadBatchAccuracyService.findBatchFcTimeAccuracy(batchId);
            baseResp = BaseResp.succResp();
            baseResp.setData(loadShortFcAccuracyDTOS);
        }catch (Exception e){
            e.printStackTrace();
            baseResp = BaseResp.failResp("查询超短期批次时刻准确率失败");
        }
        return  baseResp;
    }



}
