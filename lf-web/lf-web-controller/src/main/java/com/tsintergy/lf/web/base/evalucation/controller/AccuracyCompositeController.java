package com.tsintergy.lf.web.base.evalucation.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils;
import com.tsintergy.lf.serviceapi.base.evalucation.api.AccuracyCompositeService;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.AssessAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.BatchAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.CityAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.forecast.dto.AlgorithmAccuracyDTO;
import com.tsintergy.lf.web.base.controller.BaseController;
import com.tsintergy.lf.web.base.evalucation.response.BatchAccuracyResp;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 综合准确率查询页面
 *
 * <AUTHOR>
 * @create 2024/4/14
 * @since 1.0.0
 */
@RequestMapping("/composite")
@RestController
public class AccuracyCompositeController extends BaseController {

    private static List<String> defaultAlgorithmIds = Arrays.asList(AlgorithmEnum.FORECAST_MODIFY.getId(),
            AlgorithmEnum.Day_96_FORECAST_FC.getId(), AlgorithmEnum.Day_96_FORECAST_EC.getId());

    @Autowired
    private AccuracyCompositeService accuracyCompositeService;

    @RequestMapping(value = "/accuracy", method = RequestMethod.GET)
    public BaseResp getListByCityIdAndDate(String cityId, String caliberId, String accuracyName, Date startDate,
                                           Date endDate, String isHoliday, String batchId) {
        BaseResp baseResp = BaseResp.succResp();
        if (batchId == null) {
            batchId = "1";
        }
        List<CityAccuracyDTO> cityAccuracy = accuracyCompositeService
                .getCityAccuracy(cityId, caliberId, accuracyName, startDate, endDate, isHoliday, batchId);
        baseResp.setData(cityAccuracy);
        return baseResp;
    }

    @RequestMapping(value = "/doCalculate", method = RequestMethod.GET)
    public BaseResp doCalculate(String cityId, String caliberId, String algorithmId, Date startDate,
                                Date endDate) {
        BaseResp baseResp = BaseResp.succResp();
        accuracyCompositeService
                .doCalculateCompositeAccuracy(cityId, caliberId, algorithmId, startDate, endDate);
        return baseResp;
    }

    @RequestMapping(value = "/doCalculate/recall", method = RequestMethod.GET)
    public BaseResp doCalculateRecall(String cityId, String caliberId, String algorithmId, Date startDate,
                                      Date endDate) {
        BaseResp baseResp = BaseResp.succResp();
        accuracyCompositeService
                .doCalculateRecallCompositeAccuracy(cityId, caliberId, algorithmId, startDate, endDate);
        return baseResp;
    }

    @RequestMapping(value = "/avg-accuracy", method = RequestMethod.GET)
    public BaseResp getCityAvgAccuracy(String cityId, String caliberId, String accuracyName, Date startDate,
                                       Date endDate, String isHoliday, String batchId) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        if (batchId == null) {
            batchId = "1";
        }
        if (isHoliday == null) {
            isHoliday = "0";
        }
        List<CityAccuracyDTO> cityAccuracy = accuracyCompositeService
                .getCityAccuracy(cityId, caliberId, accuracyName, startDate, endDate, isHoliday, batchId);
        List<AlgorithmAccuracyDTO> avgaAccuracyList = accuracyCompositeService.getCityAvgAccuracy(cityAccuracy, cityId);
        avgaAccuracyList.sort(Comparator.comparing(AlgorithmAccuracyDTO::getAlgorithmId));
        baseResp.setData(avgaAccuracyList);
        return baseResp;
    }

    /**
     * @param cityId      城市id
     * @param algorithmId 算法id
     * @param startDate   起始时间
     * @param endDate     结束时间
     * @param batchId     批次id
     * @param statMethod  统计方法 1:综合准确率  2:平均准确率
     * @throws Exception
     */
    @ApiOperation("获取日前七天所有批次的预测准确率")
    @RequestMapping(value = "/batch/accuracy", method = RequestMethod.GET)
    public BaseResp getBatchAccuracy(String cityId, String algorithmId, Date startDate, Date endDate,
                                     String batchId, String statMethod) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<BatchAccuracyDTO> batchAccuracyDTOS = accuracyCompositeService.getBatchAccuracy(cityId,
                this.getCaliberId(), Collections.singletonList(algorithmId), startDate, endDate, batchId, statMethod);
        batchAccuracyDTOS = fillMissingDaysByDate(batchAccuracyDTOS);

        // 根据日期分组
        Map<Date, List<BatchAccuracyDTO>> groupedByDate = batchAccuracyDTOS.stream().collect(Collectors.groupingBy(BatchAccuracyDTO::getDate));

        // 转换为BatchAccuracyResp对象列表
        List<BatchAccuracyResp> areaAccuracyResps = groupedByDate.entrySet().stream().map(entry -> {
            BatchAccuracyResp resp = new BatchAccuracyResp();
            resp.setDate(entry.getKey());
            List<BatchAccuracyDTO> accuracyDTOS = entry.getValue();
            resp.setBatchAccuracyDTOList(accuracyDTOS);
            return resp;
        }).sorted(Comparator.comparing(BatchAccuracyResp::getDate).reversed()).collect(Collectors.toList());
        baseResp.setData(areaAccuracyResps);
        return baseResp;
    }

    private List<BatchAccuracyDTO> fillMissingDaysByDate(List<BatchAccuracyDTO> result) {
        BatchAccuracyDTO batchAccuracyDTO = result.get(0);
        // 按日期分组
        Map<Date, List<BatchAccuracyDTO>> groupedByDate = result.stream()
                .collect(Collectors.groupingBy(BatchAccuracyDTO::getDate));

        for (Map.Entry<Date, List<BatchAccuracyDTO>> entry : groupedByDate.entrySet()) {
            Date date = entry.getKey();
            List<BatchAccuracyDTO> dateGroup = entry.getValue();

            // 将当前日期的数据按 days 存储到 Map 中
            Map<Integer, BatchAccuracyDTO> daysMap = dateGroup.stream()
                    .collect(Collectors.toMap(BatchAccuracyDTO::getDays, Function.identity()));

            // 检查 D-1 到 D-7 是否缺失，缺失则填充
            for (int i = 1; i <= 10; i++) {
                if (!daysMap.containsKey(i)) {
                    BatchAccuracyDTO dto = new BatchAccuracyDTO();
                    dto.setBatchName("D-" + i);
                    dto.setDays(i);
                    dto.setDate(date);
                    dto.setCreateTime(null);
                    dto.setBatchId(batchAccuracyDTO.getBatchId());
                    dto.setAlgorithmId(batchAccuracyDTO.getBatchId());
                    dto.setAlgorithmName(batchAccuracyDTO.getAlgorithmName());
                    result.add(dto);
                }
            }
        }
        result.sort(Comparator
                .comparing(BatchAccuracyDTO::getDate, Comparator.reverseOrder())
                .thenComparing(BatchAccuracyDTO::getDays)
        );
        return result;
    }

    /**
     * @param cityId     城市id
     * @param startDate  起始时间
     * @param endDate    结束时间
     * @param batchId    批次id
     * @param statMethod 统计方法 1:综合准确率  2:平均准确率
     * @throws Exception
     */
    @ApiOperation("获取日前七天所有算法所有批次的平均准确率")
    @RequestMapping(value = "/batch/avg-accuracy", method = RequestMethod.GET)
    public BaseResp getAvgBatchAccuracy(String cityId, Date startDate, Date endDate, String batchId, String statMethod) throws Exception {
        BaseResp baseResp = BaseResp.succResp();

        List<BatchAccuracyDTO> batchAccuracyDTOS = accuracyCompositeService.getBatchAccuracy(cityId,
                this.getCaliberId(), defaultAlgorithmIds, startDate, endDate, batchId, statMethod);

        // 按 algorithmId 和 batchName 分组
        Map<String, Map<String, List<BatchAccuracyDTO>>> groupedByAlgorithmAndBatch = batchAccuracyDTOS.stream()
                .collect(Collectors.groupingBy(BatchAccuracyDTO::getAlgorithmId,
                        Collectors.groupingBy(BatchAccuracyDTO::getBatchName)));

        List<BatchAccuracyResp> result = new ArrayList<>();
        // 计算每个算法每个 batchName 的平均准确率
        for (Map.Entry<String, Map<String, List<BatchAccuracyDTO>>> entry : groupedByAlgorithmAndBatch.entrySet()) {
            BatchAccuracyResp accuracyResp = new BatchAccuracyResp();
            List<BatchAccuracyDTO> accuracyDTOList = new ArrayList<>();
            Map<String, List<BatchAccuracyDTO>> batchGroups = entry.getValue();
            for (Map.Entry<String, List<BatchAccuracyDTO>> batchEntry : batchGroups.entrySet()) {
                List<BatchAccuracyDTO> batchList = batchEntry.getValue();
                BatchAccuracyDTO batchAccuracyDTO = batchList.get(0);
                BatchAccuracyDTO accuracyDTO = new BatchAccuracyDTO();
                // 计算平均准确率
                List<BigDecimal> accuracys = batchList.stream().map(BatchAccuracyDTO::getAccuracy).collect(Collectors.toList());
                BigDecimal averageAccuracy = BigDecimalUtils.listAvg(accuracys);
                accuracyDTO.setAccuracy(averageAccuracy);
                accuracyDTO.setAlgorithmId(batchAccuracyDTO.getAlgorithmId());
                accuracyDTO.setBatchName(batchAccuracyDTO.getBatchName());
                accuracyDTOList.add(accuracyDTO);
                accuracyResp.setAlgorithmName(batchAccuracyDTO.getAlgorithmName());
            }
            accuracyDTOList.sort(Comparator.comparing(BatchAccuracyDTO::getBatchName));
            accuracyResp.setBatchAccuracyDTOList(accuracyDTOList);
            result.add(accuracyResp);
        }
        result.sort(Comparator.comparing(resp -> resp.getBatchAccuracyDTOList().get(0).getAlgorithmId()));
        baseResp.setData(result);
        return baseResp;
    }

    @ApiOperation("获取考核点准确率")
    @RequestMapping(value = "/batch/assess-accuracy", method = RequestMethod.GET)
    public BaseResp getAssessAccuracy(String cityId, Date startDate, Date endDate, Integer days, String batchId) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<AssessAccuracyDTO> assessAccuracy = accuracyCompositeService.getAssessAccuracy(cityId, this.getCaliberId(), defaultAlgorithmIds, batchId, startDate, endDate, days);
        baseResp.setData(assessAccuracy);
        return baseResp;
    }
}
