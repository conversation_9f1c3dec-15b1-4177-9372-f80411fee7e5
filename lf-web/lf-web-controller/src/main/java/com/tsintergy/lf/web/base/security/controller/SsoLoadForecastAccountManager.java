/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  wang<PERSON>
 * Date:  2019/9/25 3:12
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.security.controller;

import com.alibaba.fastjson.JSON;
import com.tsieframework.cloud.security.serviceapi.system.api.RedisService;
import com.tsieframework.cloud.security.serviceapi.system.api.SecurityService;
import com.tsieframework.cloud.security.serviceapi.system.bean.TokenBean;
import com.tsieframework.cloud.security.serviceapi.system.bean.TsieMenuTreeBean;
import com.tsieframework.cloud.security.serviceapi.system.pojo.TsieMenuVO;
import com.tsieframework.cloud.security.serviceapi.system.pojo.TsieRoleVO;
import com.tsieframework.cloud.security.serviceapi.system.pojo.TsieUserVO;
import com.tsieframework.cloud.security.web.common.security.TokenManager;
import com.tsieframework.cloud.security.web.common.security.UserTokenHelper;
import com.tsieframework.cloud.security.web.common.security.account.DefaultAccountManager;
import com.tsieframework.cloud.security.web.common.security.account.LoginRequest;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.CacheConstants;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.SystemConstant;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.LoadUserDO;
import com.tsintergy.lf.serviceapi.base.security.api.UserService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingSystemDO;
import com.tsintergy.lf.web.base.common.util.IpUtil;
import com.tsintergy.lf.web.base.security.properties.SecurityProperties;
import com.tsintergy.lf.web.base.security.response.LoginResp;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * Description:  <br>
 * 登录扩展类
 *
 * <AUTHOR>
 * @create 2019/9/25
 * @since 1.0.0
 */
public class SsoLoadForecastAccountManager extends DefaultAccountManager {

    public static final long DAY_MS = 24 * 60 * 60 * 1000;

    @Autowired
    private SecurityProperties securityProperties;

    @Autowired
    private UserService userService;

    @Autowired
    private CityService cityService;

    @Autowired
    private RedisService redisService;

    @Autowired
    SettingSystemService settingSystemService;

    @Value("${tsie.cas.server.restfulRedirectUrl}")
    public  String restfulRedirectUrl;


    protected SecurityService securityService;
    protected TokenManager tokenManager;



    public SsoLoadForecastAccountManager(SecurityService securityService, TokenManager tokenManager) {
        this.securityService = securityService;
        this.tokenManager = tokenManager;
    }


    public BaseResp login(HttpServletRequest request, HttpServletResponse response, String userName) throws Exception {
        BaseResp resp = BaseResp.succResp();
        String cookieToken = getCookieToken();
        if (StringUtils.isNotBlank(cookieToken)) {
            return resp;
        }
        DBQueryParam param = DBQueryParamBuilder.create().where(QueryOp.StringEqualTo, "username", userName).queryDataOnly().build();
        TsieUserVO user =  securityService.queryTsieUserVo(param);
        this.validEnable(null, user);
        this.validExpired(null, user);
        String token = tokenManager.setCookieToken(user);
        List<TsieMenuVO> menuList = securityService.queryTsieMenuAllByUserId(user.getId(),null);
        TokenBean tokenBean = tokenManager.createTokenBean(token, user, menuList);
        tokenManager.addTokenBeanToCache(user.getUsername(), tokenBean);
        return resp;
    }




    /**
     * 项目自定义操作，扩展TSIE组件内容
     *
     * @param resp
     * @param request
     * @param user
     * @throws Exception
     */
    @Override
    protected void afterLoginSuccess(BaseResp resp, LoginRequest request, TsieUserVO user) throws Exception {
        HttpServletRequest httpServletRequest =
                ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        HttpSession session = httpServletRequest.getSession();
        LoginResp loginResp = new LoginResp();
        loginResp.setPasswdChangePrompt(user.getPasswdChangePrompt());
        LoadUserDO LoadUserDetailDO = this.userService.findUserById(user.getId());
        logger.debug("userService返回的 = " + JSON.toJSON(user));
        if (null != LoadUserDetailDO) {
            loginResp.setCityId(LoadUserDetailDO.getCityId());
            session.setAttribute(CacheConstants.USER_ID, user.getId());
            session.setAttribute(CacheConstants.CITY_ID, LoadUserDetailDO.getCityId());
            if (StringUtils.isNotBlank(LoadUserDetailDO.getCityId())) {
                CityDO cityVO = cityService.findCityById(LoadUserDetailDO.getCityId());
                if (cityVO != null) {
                    loginResp.setCity(cityVO.getCity());
                    loginResp.setCityType(cityVO.getType());
                }
            }
//            redisService.redisAdd(securityProperties.getTokenCacheKeyPrefix()+"_dict.lastUpdatePasswordTime." + user.getUsername(),LoadUserDetailDO.getLastUpdatePasswordTime());
//            redisService.redisAdd(securityProperties.getTokenCacheKeyPrefix()+"_dict.createTime." + user.getUsername(),LoadUserDetailDO.getCreateTime());
//
//            //判断是否需要重置密码
//            reseatPassword(LoadUserDetailDO,loginResp);
        } else {
            throw TsieExceptionUtils.newBusinessException("T814");
        }
        loginResp.setRoleSystemTime(false);
        //查询用户的权限
        List<TsieRoleVO> userRoles = securityService.queryUserRoleAll(user.getId());
        if (CollectionUtils.isNotEmpty(userRoles) && userRoles.size() > 0) {
            for (TsieRoleVO tsieRoleVO : userRoles) {
                if ("ROLE_ADMIN".equals(tsieRoleVO.getRole())) {
                    loginResp.setRoleSystemTime(true);
                }
            }
        }
        loginResp.setNickname(user.getNickname());

        //设置系统时间
        setSystemTime(loginResp);

        CityDO cityById = cityService.findCityById(LoadUserDetailDO.getCityId());

        //设置系统时间
        setDefalutMenuPath(loginResp, user, cityById);

        List<SettingSystemDO> settingSystemDOS=  settingSystemService.findByKey(SystemConstant.colorStr);
        List<SettingSystemDO> settingSystemDO= settingSystemService.findByKey(SystemConstant.isOpenReseau);
        List<SettingSystemDO> peakSectionTime = settingSystemService.findByKey(SystemConstant.PEAK_SECTION_TIME);


        if (CollectionUtils.isNotEmpty(settingSystemDOS)){
            loginResp.setColorStr(settingSystemDOS.get(0).getValue());
        }else {
            loginResp.setColorStr("#5B5B5B");
        }
        if (CollectionUtils.isNotEmpty(settingSystemDO)){
            loginResp.setOpenReseau(settingSystemDO.get(0).getValue());
        }else {
            loginResp.setOpenReseau(SystemConstant.OpenReseau);
        }

        if(CollectionUtils.isNotEmpty(peakSectionTime)){
            loginResp.setPeakSectionTime(peakSectionTime.get(0).getValue());
        }else {
            loginResp.setPeakSectionTime("08:00~22:00");
        }
        loginResp.setLoginUrl(restfulRedirectUrl);
        loginResp.setLoginMessage(StringUtils.isNotBlank(loginResp.getLoginMessage()) ? loginResp.getLoginMessage() : "登陆成功");
        loginResp.setStartWithZero(Constants.LOAD_CURVE_START_WITH_ZERO);
        resp.setData(loginResp);
        logger.debug("时间 = " + new Date() + "用户 username = " + user.getUsername() + " ip = " + IpUtil.getRealIP(this.getServletRequest()) + "登陆成功");
    }

    private void setSystemTime(LoginResp loginResp) {
        Object sysTime = this.getServletRequest().getSession().getAttribute(CacheConstants.SYSTEM_DATE);
        if (sysTime != null) {
            loginResp.setSystemTime((String) sysTime);
        } else {
            List<SettingSystemDO> settingSystemDOS = settingSystemService.findByKey(SystemConstant.SYSTEM_TIME);
            if (CollectionUtils.isNotEmpty(settingSystemDOS)) {
                String systmeTime = settingSystemDOS.get(0).getValue();
                if (StringUtils.isNotBlank(systmeTime)) {
                    if (systmeTime.length() == DateFormatType.SIMPLE_DATE_FORMAT_STR.getValue().length()) {
                        systmeTime = systmeTime + " " + DateUtils.date2String(new Date(), DateFormatType.DATE_FORMAT_STR).split(" ")[1];
                        loginResp.setSystemTime(systmeTime);
                    } else if (systmeTime.length() == DateFormatType.DATE_FORMAT_STR.getValue().length()) {
                        loginResp.setSystemTime(systmeTime);
                    }
                } else {
                    loginResp.setSystemTime(DateUtils.date2String(new Date(), DateFormatType.DATE_FORMAT_STR));
                }
            } else {
                loginResp.setSystemTime(DateUtils.date2String(new Date(), DateFormatType.DATE_FORMAT_STR));
            }

            this.getServletRequest().getSession().setAttribute(CacheConstants.SYSTEM_DATE, loginResp.getSystemTime());
        }
    }

    private String setDefalutMenuPath(LoginResp loginResp, TsieUserVO user, CityDO cityById) throws Exception {

        TsieMenuTreeBean menuTreeBean = securityService.queryTsieUserMenuTree(user.getId(),null);

        String defalutMenuPath;
        SettingSystemDO byFieldId = null;
        if(cityById.getType().equals(CityConstants.CITY_TYPE)){
            byFieldId = settingSystemService.findByFieldId(SystemConstant.CITY_DEFAULT_INDEX);
        }else {
            byFieldId = settingSystemService.findByFieldId(SystemConstant.PROVINCE_DEFAULT_INDEX);
        }

        if(byFieldId == null){
            defalutMenuPath=getDefalutMenuPathList(menuTreeBean,getDefalutMenuPath(menuTreeBean));
        }else {
            defalutMenuPath=getDefalutMenuPathList(menuTreeBean,byFieldId.getValue());
        }

        if (StringUtils.isBlank(defalutMenuPath)) {
            throw TsieExceptionUtils.newBusinessException("T813", "用户没有赋予权限或权限分配不合理，请联系管理员！");
        }

        loginResp.setDefaultMenuPath(defalutMenuPath);
        List<TsieMenuVO> datas = securityService.queryAllMenu(DBQueryParamBuilder.create().build()).getDatas();
        String[] defaultMenuPaths= loginResp.getDefaultMenuPath().split("/");
        String defaultMenuPath = "/"+defaultMenuPaths[defaultMenuPaths.length - 1];
        List<TsieMenuVO> collect = datas.stream().filter(t -> {
            if (defaultMenuPath.equals(t.getMenupath())) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());
        loginResp.setDefaultMenuPathName(collect.get(0).getName());
        return defalutMenuPath;
    }

    private String getDefalutMenuPathList(TsieMenuTreeBean tsieMenuTreeBean, String key) {
        if (tsieMenuTreeBean.getChildren() == null || tsieMenuTreeBean.getChildren().size() == 0) {
            return null;
        }
        if (tsieMenuTreeBean != null) {
            for (TsieMenuTreeBean menuTreeBean : tsieMenuTreeBean.getChildren()) {

                if (menuTreeBean.getMenu().getMenupath().equals(key)) {
                    return menuTreeBean.getMenu().getMenupath();
                }
                String defalutMenuPathList = getDefalutMenuPathList(menuTreeBean, key);
                if (defalutMenuPathList != null) {
                    return menuTreeBean.getMenu().getMenupath() + defalutMenuPathList;
                }

            }
        }
        return null;
    }
    private void reseatPassword(LoadUserDO loadUserVO,LoginResp loginResp) {
        Date lastUpdatePassword = loadUserVO.getLastUpdatePasswordTime();
        if (lastUpdatePassword == null) {
            lastUpdatePassword = loadUserVO.getCreateTime();
        }
        Integer resetInterval = securityProperties.getPassword().getResetInterval();
        Integer warningInterval = securityProperties.getPassword().getWarningInterval();

        //如果使用同一个密码超过resetInterval-warningInterval，提示重置密码
        long useTime = System.currentTimeMillis() - lastUpdatePassword.getTime();
        long remainingTime = DAY_MS * resetInterval - useTime;
        if (remainingTime < DAY_MS * warningInterval) {
            long remainingDay = (remainingTime % DAY_MS == 0 ? remainingTime / DAY_MS : remainingTime / DAY_MS + 1);
            loginResp.setLoginMessage("距密码过期还剩" + (remainingDay < 0 ? 0 : remainingDay) + "天，请重置密码");

            //如果使用同一个密码超过resetInterval，重置密码
            if (remainingDay <= 0) {
                loginResp.setPasswdChangePrompt(TsieUserVO.PASSWD_CHANGE_PROMPT_YES);
            }
        }
    }
    private String getDefalutMenuPath(TsieMenuTreeBean tsieMenuTreeBean) {
        String defaultMenuPath = tsieMenuTreeBean.getMenu().getMenupath();
        if (tsieMenuTreeBean.getChildren() != null && tsieMenuTreeBean.getChildren().size() > 0) {
            tsieMenuTreeBean = tsieMenuTreeBean.getChildren().get(0);
            if (tsieMenuTreeBean.getMenu().getType() == 1) {
                defaultMenuPath = getDefalutMenuPath(tsieMenuTreeBean);
                if (defaultMenuPath != null) {
                    return defaultMenuPath;
                }
            }
            return defaultMenuPath;
        }
        return defaultMenuPath;
    }


    public BaseResp<LoginResp> getSystemInfo() throws Exception {
        BaseResp resp = BaseResp.succResp();
        TokenBean bean = getTokenBean();
        TsieUserVO user = bean.getTsieUserVO();
        user.setPasswd((String) null);
        user.setLoginMode(this.getUserLoginMode(user));
        this.setUserPasswdChangePrompt(user, true);
        Map<String, Object> data = new HashMap();
        data.put("user", user);
        resp.setData(data);
        this.afterLoginSuccess(resp, null, user);
        return resp;
    }

    private TokenBean getTokenBean() {
        TokenBean bean;
        String token = tokenManager.getCookieToken();
        if (null == token) {
            if (logger.isDebugEnabled()) {
                logger.debug("1. cookie 未找到 loginTokenKey = " + UserTokenHelper.getIPAddress());
            }

            throw new BusinessException("T000", "cookie 未找到 loginTokenKey = " + UserTokenHelper.getIPAddress());
        } else {
            Claims claims = Jwts.parser().setSigningKey("base64EncodedSecretKey").parseClaimsJws(token).getBody();
            String username = claims.get("username", String.class);
            String tokenBeanCacheKey = this.tokenManager.getTokenBeanCacheKey(username);
            bean = (TokenBean) redisService.redisGet(tokenBeanCacheKey, TokenBean.class);
            if (null == bean) {
                if (logger.isDebugEnabled()) {
                    logger.debug("2. 缓存找不到 redisKey = dict.token." + username);
                }

                throw new BusinessException("T000", "2. 缓存找不到 redisKey = dict.token." + username);
            }
        }
        return bean;
    }


    @Override
    protected void validUserPassword(LoginRequest request, TsieUserVO user) {
        if (!this.passwordValidator.valid(request, user, this)) {
            this.throwBusinessException("T8810");
        }
    }

    @Override
    protected void validEnable(LoginRequest request, TsieUserVO user) {
        if (!TsieUserVO.isEnable(user)) {
            this.throwBusinessException("T8810");
        }
    }

    @Override
    protected TsieUserVO getUserAndValidRequest(LoginRequest request) throws Exception {
        if (StringUtils.isEmpty(request.getUsername())) {
            this.throwBusinessException("T809");
        }

        if (StringUtils.isEmpty(request.getPassword())) {
            this.throwBusinessException("T810");
        }

        TsieUserVO user = this.getUserByUsername(request.getUsername());
        if (user == null) {
            this.throwBusinessException("T8810");
        }

        return user;
    }

    protected String getCookieToken() {
        Cookie[] cookies = getServletRequest().getCookies();
        if (cookies == null) {
            return null;
        }

        String token = null;
        for (Cookie c : cookies) {
            if (c.getName().equals(tokenManager.getLoginTokenKey())) {
                token = c.getValue();
            }
        }
        TokenBean bean;
        if (null == token) {
            return null;
        } else {
            Claims claims = Jwts.parser().setSigningKey("base64EncodedSecretKey").parseClaimsJws(token).getBody();
            String username = claims.get("username", String.class);
            String tokenBeanCacheKey = this.tokenManager.getTokenBeanCacheKey(username);
            bean = (TokenBean) redisService.redisGet(tokenBeanCacheKey, TokenBean.class);
            if (null == bean) {
                return null;
            }
        }
        return token;
    }
}