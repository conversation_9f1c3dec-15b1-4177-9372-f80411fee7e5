package com.tsintergy.lf.web.base.forecast.controller;


import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.AlgorithmService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.dto.AlgorithmDTO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.ForecastCharacterService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureStatService;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadAndWeatherFeatureRespDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureFcDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemData;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureStatService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayFcDO;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import com.tsintergy.lf.web.base.forecast.request.SimilarDayHisFeatureRequest;
import com.tsintergy.lf.web.base.forecast.response.LoadAndWeatherFeatureAllFcResp;
import com.tsintergy.lf.web.base.forecast.response.LoadAndWeatherFeatureFcResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 预测特性
 *
 * <AUTHOR>
 * @date 2020-03-27
 */
@Api(tags = "预测特性")
@RestController
public class ForecastCharacterController extends CommonBaseController {

    @Autowired
    private WeatherFeatureStatService weatherFeatureStatService;

    @Autowired
    private LoadCityFcService loadCityFcService;

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private LoadFeatureStatService loadFeatureStatService;

    @Autowired
    private WeatherCityFcService weatherCityFcService;

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Autowired
    private CityService cityService;

    @Autowired
    private SettingSystemService settingSystemService;

    @Autowired
    private ForecastCharacterService forecastCharacterService;

    @Autowired
    private AlgorithmService algorithmService;

    /**
     * 获取预测/实际特性
     */
    @ApiOperation("获取预测/实际特性")
    @RequestMapping(value = "/get/character", method = RequestMethod.GET)
    public BaseResp<LoadAndWeatherFeatureFcResp> getForecastCharacter(@ApiParam(value = "城市id") String cityId, @ApiParam(value = "算法id") String algorithmId, @ApiParam(value = "日期") java.util.Date date, Integer loadType) throws Exception {
        String weatherCityId = cityService.findWeatherCityId(cityId);
        List<WeatherCityFcDO> weatherCityFcDOList;
        LoadCityFcDO loadCityFc = new LoadCityFcDO();
        String caliberId = super.getCaliberId();

        if (loadType != null && loadType == 1) {
            List<WeatherCityHisDO> weatherCityHisDOs = weatherCityHisService.findWeatherCityHisDOs(weatherCityId, null, date, date);
            weatherCityFcDOList = weatherCityHisDOs.stream().map(t -> {
                WeatherCityFcDO weatherCityFcDO = new WeatherCityFcDO();
                BeanUtils.copyProperties(t, weatherCityFcDO);
                return weatherCityFcDO;
            }).collect(Collectors.toList());
            LoadCityHisDO loadCityHisDO = loadCityHisService.getOne(date, caliberId, cityId);
            if (loadCityHisDO != null) {
                BeanUtils.copyProperties(loadCityHisDO, loadCityFc);
            }
        } else {
            weatherCityFcDOList = weatherCityFcService.findWeatherCityFcDOs(weatherCityId, null, date, date);
            loadCityFc = loadCityFcService.getLoadCityFc(cityId, caliberId, date, date, algorithmId);
        }

        WeatherFeatureCityDayFcDO weatherFcVo = new WeatherFeatureCityDayFcDO();
        if (weatherCityFcDOList != null && !weatherCityFcDOList.isEmpty()) {
            weatherFcVo = weatherFeatureStatService.doStatWeatherByCityAndType(weatherCityFcDOList);
        }

        if ((weatherCityFcDOList == null || weatherCityFcDOList.isEmpty()) && loadCityFc == null) {
            BaseResp baseResp = BaseResp.failResp();
            baseResp.setRetCode("T706");
            baseResp.setRetMsg("数据为空");
            return baseResp;
        }
        LoadFeatureFcDTO loadFeatureFcDTO = loadFeatureStatService.findStatisticsDayFeature(loadCityFc);
        LoadAndWeatherFeatureFcResp resp = new LoadAndWeatherFeatureFcResp(weatherFcVo, loadFeatureFcDTO);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(resp);
        return baseResp;
    }



    /**
     * 获取预测特性
     */
    @ApiOperation("获取预测特性")
    @RequestMapping(value = "/get/characterList", method = RequestMethod.GET)
    public BaseResp<List<LoadAndWeatherFeatureAllFcResp>> getForecastCharacterList(
        @ApiParam(value = "城市id") String cityId, @ApiParam(value = "日期") Date date) throws Exception {
        List<LoadAndWeatherFeatureAllFcResp> resp = new ArrayList<>();
        String weatherCityId = cityService.findWeatherCityId(cityId);
        List<WeatherCityFcDO> weatherFeature = this.weatherCityFcService
            .findWeatherCityFcDOs(weatherCityId, null, date, date);
        WeatherFeatureCityDayFcDO weatherFcVo = new WeatherFeatureCityDayFcDO();
        if (weatherFeature.size() != 0) {
            weatherFcVo = weatherFeatureStatService
                .doStatWeatherByCityAndType(weatherFeature);
        }
        String caliberId = super.getCaliberId();
        // 算法id集合
        List<String> algorithmIdList = getAlgorithmReportV2(date, caliberId, cityId).stream().map(AlgorithmDTO::getId)
            .collect(Collectors.toList());

        List<LoadCityFcDO> loadCityFcList = this.loadCityFcService.listLoadCityFc(cityId, caliberId, date, date, null);
        if (weatherFeature.size() == 0 && CollectionUtils.isEmpty(loadCityFcList)) {
            BaseResp baseResp = BaseResp.failResp();
            baseResp.setRetCode("T706");
            baseResp.setRetMsg("数据为空");
            return baseResp;
        }

        if (CollectionUtils.isNotEmpty(algorithmIdList)) {
            if (CollectionUtils.isEmpty(loadCityFcList)) {
                for (String algorithmId : algorithmIdList) {
                    LoadAndWeatherFeatureAllFcResp result = new LoadAndWeatherFeatureAllFcResp(weatherFcVo, null);
                    result.setAlgorithmId(algorithmId);
                    resp.add(result);
                }
            } else {
                Set<String> algorithmIdSet = new HashSet<>();
                for (LoadCityFcDO loadCityFc : loadCityFcList) {
                    String algorithmId = loadCityFc.getAlgorithmId();
                    if(!algorithmIdList.contains(algorithmId)){
                        continue;
                    }
                    LoadFeatureFcDTO loadFeatureFcDTO = this.loadFeatureStatService.findStatisticsDayFeature(
                        loadCityFc);
                    LoadAndWeatherFeatureAllFcResp result = new LoadAndWeatherFeatureAllFcResp(weatherFcVo,
                        loadFeatureFcDTO);
                    result.setAlgorithmId(algorithmId);
                    algorithmIdSet.add(algorithmId);
                    resp.add(result);
                }
                for (String algorithmId : algorithmIdList) {
                    if (!algorithmIdSet.contains(algorithmId)) {
                        LoadAndWeatherFeatureAllFcResp result = new LoadAndWeatherFeatureAllFcResp(weatherFcVo, null);
                        result.setAlgorithmId(algorithmId);
                        resp.add(result);
                    }
                }
            }
        }

        resp = resp.stream().sorted(Comparator.comparing(t->t.getAlgorithmId())).collect(Collectors.toList());
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(resp);
        return baseResp;
    }


    @ApiOperation("获取相似日历史特性")
    @RequestMapping(value = "/getSimilarDayHisFeature", method = RequestMethod.POST)
    public BaseResp<List<LoadAndWeatherFeatureRespDTO>> getSimilarDayHisFeature(
        @RequestBody SimilarDayHisFeatureRequest request)
        throws Exception {
        if (CollectionUtils.isEmpty(request.getDateList())) {
            throw new BusinessException("", "预测日期的相似日日期参数不能为空");
        }
        String caliberId = super.getCaliberId();
        List<LoadAndWeatherFeatureRespDTO> resp = forecastCharacterService.findSimilarDayHisFeature(request.getCityId(),
            caliberId, request.getDateList());
        if (CollectionUtils.isEmpty(resp)) {
            BaseResp baseResp = BaseResp.failResp();
            baseResp.setRetCode("T706");
            baseResp.setRetMsg("数据为空");
            return baseResp;
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(resp);
        return baseResp;
    }



    /**
     * 查询预测方法
     *
     * @param date 日期
     * @param caliberId 口径
     * @param cityId 城市id
     * @return 返回预测方法集合
     * @throws Exception 异常
     */
    private List<AlgorithmDTO> getAlgorithmReportV2(Date date, String caliberId, String cityId) throws Exception {
        if (date == null) {
            date = this.getSystemDate();
        }

        if (StringUtils.isEmpty(caliberId)) {
            caliberId = this.getCaliberId();
        }

        if (StringUtils.isEmpty(cityId)) {
            cityId = this.getLoginCityId();
        }

        List<AlgorithmDTO> dtos = new ArrayList<>();
        CityDO cityDO = cityService.findCityById(cityId);
        String reportAlgorithmId = null;
        LoadCityFcDO fcVO = loadCityFcService.getReport(cityId, caliberId, date);

        if (fcVO != null) {
            reportAlgorithmId = AlgorithmConstants.MD_ALGORITHM_ID;
        } else {
            SystemData systemSetting = settingSystemService.getSystemSetting();
            if (cityId.equals(CityConstants.PROVINCE_ID)) {
                reportAlgorithmId = systemSetting.getProvinceNormalAlgorithm();
            } else {
                reportAlgorithmId = systemSetting.getCityNormalAlgorithm();
            }
        }

        List<AlgorithmDO> allAlgorithmsNotCache = algorithmService.getAllAlgorithmsNotCache();
        if (!org.springframework.util.CollectionUtils.isEmpty(allAlgorithmsNotCache)) {
            List<AlgorithmDO> pageAlgorithms = null;

            pageAlgorithms = allAlgorithmsNotCache.stream().filter(
                t -> AlgorithmConstants.COMMON_ALGORITHM_TYPE.equals(t.getType())
                    || AlgorithmConstants.NORMAL_ALGORITHM_TYPE
                    .equals(t.getType()))
                .collect(Collectors.toList());

            List<AlgorithmDO> viewAlgorithms = null;
            if (!org.springframework.util.CollectionUtils.isEmpty(pageAlgorithms)) {
                if (CityConstants.PROVINCE_TYPE.equals(cityDO.getType())) {
                    viewAlgorithms = pageAlgorithms.stream().filter(t -> t.getProvinceView() == true)
                        .collect(Collectors.toList());
                } else if (CityConstants.CITY_TYPE.equals(cityDO.getType())) {
                    viewAlgorithms = pageAlgorithms.stream().filter(t -> t.getCityView() == true)
                        .collect(Collectors.toList());
                }
            }

            // flag 标识位，用来标识是否有算法设置位true
            Boolean flag = false;
            if (!org.springframework.util.CollectionUtils.isEmpty(viewAlgorithms)) {
                for (AlgorithmDO algorithmDO : viewAlgorithms) {
                    AlgorithmDTO dto = new AlgorithmDTO();
                    dto.setAlgorithm(algorithmDO.getAlgorithmCn());
                    dto.setId(algorithmDO.getId());
                    if (algorithmDO.getId().equals(reportAlgorithmId)) {
                        dto.setDefault(true);
                        flag = true;
                    }
                    dtos.add(dto);
                }
            }

            if (!flag) {
                for (AlgorithmDTO dto : dtos) {
                    if (dto.getId().equals(reportAlgorithmId)) {
                        dto.setDefault(true);
                        flag = true;
                        break;
                    }
                }
            }

            if (!flag) {
                AlgorithmDTO dto = dtos.get(0);
                dto.setDefault(true);
                dtos.set(0, dto);
            }
        }
        return dtos;
    }

}

