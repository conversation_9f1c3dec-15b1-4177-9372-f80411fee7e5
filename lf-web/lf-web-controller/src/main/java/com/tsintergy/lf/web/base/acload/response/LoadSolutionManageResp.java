/**
 * Copyright (C), 2015‐2022, 北京清能互联科技有限公司 Author:  <EMAIL> Date:  2022/5/24 0:51 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.acload.response;

import com.tsintergy.lf.serviceapi.base.common.enumeration.DateType2;
import com.tsintergy.lf.serviceapi.base.common.enumeration.TemperatureIndex;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.Column;
import lombok.Data;

/**
 * Description: 基础负荷方案管理 <br>
 *
 * <AUTHOR>
 * @create 2022/5/24
 * @since 1.0.0
 */
@ApiModel
@Data
public class LoadSolutionManageResp {

    @ApiModelProperty(value = "方案id", example = "123456")
    String id;

    @ApiModelProperty(value = "年份", example = "2022")
    String solutionYear;

    @ApiModelProperty(value = "季节", example = "2022")
    private String season;

    @ApiModelProperty(value = "日期类型", example = "1")
    DateType2 dateType;

    @ApiModelProperty(value = "春季目标范围开始日期", example = "2022-03-01")
    Date springTargetRangeStartDate;

    @ApiModelProperty(value = "春季目标范围结束日期", example = "2022-04-30")
    Date springTargetRangeEndDate;

    @ApiModelProperty(value = "秋季目标范围开始日期", example = "2022-09-01")
    Date fallTargetRangeStartDate;

    @ApiModelProperty(value = "秋季目标范围开始日期", example = "2022-10-31")
    Date fallTargetRangeEndDate;

    @ApiModelProperty(value = "温度指标", example = "MAX_TEMPERATURE")
    TemperatureIndex temperatureIndex;

    @ApiModelProperty(value = "温度范围开始(℃)", example = "20")
    @Column(name = "temperature_range_start")
    private BigDecimal TemperatureRangeStart;

    @ApiModelProperty(value = "温度范围结束(℃)", example = "25")
    @Column(name = "temperature_range_end")
    private BigDecimal TemperatureRangeEnd;

    @ApiModelProperty(value = "是否限制降水", example = "true")
    Boolean limitRain;

    @ApiModelProperty(value = "最大降水量(mm)", example = "0.5")
    BigDecimal maxRain;

    @ApiModelProperty(value = "是否应用基础负荷曲线", example = "true")
    Boolean enableCurve;
}
