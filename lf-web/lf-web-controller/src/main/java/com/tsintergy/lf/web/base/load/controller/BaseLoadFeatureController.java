package com.tsintergy.lf.web.base.load.controller;

import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import com.tsintergy.lf.web.base.load.request.BaseLoadAnalyseRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 荷特性年月日通用
 *
 * @author: wangh
 **/
public class BaseLoadFeatureController extends CommonBaseController {

    public void checkParam(BaseLoadAnalyseRequest loadAnalyseRequest) {
        if (StringUtils.isBlank(loadAnalyseRequest.getCityId())) {
            loadAnalyseRequest.setCityId(super.getLoginCityId());
        }
        if (StringUtils.isNotBlank(loadAnalyseRequest.getCountyId())) {
            loadAnalyseRequest.setCityId(loadAnalyseRequest.getCountyId());
        }

    }

    public Map<String, BigDecimal> getMaxMinAvgPower(
        List<LoadFeatureCityDayHisDO> loadList) {
        if (CollectionUtils.isNotEmpty(loadList)) {
            List<BigDecimal> maxPowerList = loadList.stream()
                .map(LoadFeatureCityDayHisDO::getMaxLoad).collect(Collectors.toList());
            BigDecimal maxPower = BasePeriodUtils.getMaxMinAvg(maxPowerList, null).get("max");
            List<BigDecimal> minPowerList = loadList.stream()
                .map(LoadFeatureCityDayHisDO::getMinLoad).collect(Collectors.toList());
            BigDecimal minPower = BasePeriodUtils.getMaxMinAvg(minPowerList, null).get("min");
            List<BigDecimal> avgPowerList = loadList.stream()
                .map(LoadFeatureCityDayHisDO::getAveLoad).collect(Collectors.toList());
            BigDecimal avgPower = BasePeriodUtils.getMaxMinAvg(avgPowerList, null).get("avg");
           List<BigDecimal> different=loadList.stream()
                   .map(LoadFeatureCityDayHisDO::getDifferent).collect(Collectors.toList());
            BigDecimal avgDifferent = BasePeriodUtils.getMaxMinAvg(different, null).get("avg");
            List<BigDecimal> gradient=loadList.stream()
                    .map(LoadFeatureCityDayHisDO::getGradient).collect(Collectors.toList());
            BigDecimal avgGradient = BasePeriodUtils.getMaxMinAvg(gradient, null).get("avg");
            Map<String, BigDecimal> maxMinAvg = new HashMap<>();
            maxMinAvg.put("max", maxPower);
            maxMinAvg.put("min", minPower);
            maxMinAvg.put("avg", avgPower);
            maxMinAvg.put("different",avgDifferent);
            maxMinAvg.put("gradient",avgGradient);
            return maxMinAvg;
        }
        return null;
    }

}
