package com.tsintergy.lf.web.base.implement.controller;

import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.aif.tool.core.enums.WeatherEnum;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.core.util.PeriodDataUtil;
import com.tsintergy.lf.serviceapi.algorithm.api.DayMaxLoadForecastService;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.base.api.AlgorithmInvoker;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.common.enumeration.CityEnum;
import com.tsintergy.lf.serviceapi.base.forecast.api.AlgorithmService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcBatchService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcBatchDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.web.base.common.util.ExcelUtil;
import io.swagger.annotations.Api;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/** @Description
 * <AUTHOR>
 * 外部数据导入
 * @Date 2025/7/3 09:55
 **/
@Api(tags = "外部数据导入")
@RestController
@RequestMapping("/externalData")
public class ExternalDataImportController {

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    WeatherCityHisService weatherCityHisService;

    @Autowired
    private WeatherCityFcService weatherCityFcService;

    @Autowired
    private WeatherCityFcBatchService weatherCityFcBatchService;

    @Autowired
    private AlgorithmService algorithmService;


    @RequestMapping(value = "/importLoad", method = RequestMethod.POST)
    public BaseResp importLoad(@RequestParam(value = "file", required = false) MultipartFile file) throws Exception {
        BaseResp resp = BaseResp.succResp();
        List<List<List<String>>> read = ExcelUtil.read(file.getInputStream(), false);

        Map<Date, Map<String, BigDecimal>> resultMap = new HashMap<>();

        read.stream()
                .flatMap(List::stream)
                .skip(1)
                .forEach(list -> {
                    String load = list.get(2);
                    if (StringUtils.isEmpty(load)) return;
                    BigDecimal loadValue = new BigDecimal(load);
                    String timeStr = list.get(1);
                    String[] strings = timeStr.split(" ");
                    String date = strings[0];
                    Date newDate = DateUtil.getDate(date, "yyyy-MM-dd");
                    String time = strings[1];
                    String field = "t" + time.split(":")[0] + time.split(":")[1];
                    resultMap.computeIfAbsent(newDate, k -> new HashMap<>())
                            .put(field, loadValue);
                });

        for (Map.Entry<Date, Map<String, BigDecimal>> entry : resultMap.entrySet()) {
            Date date = entry.getKey();
            Map<String, BigDecimal> valueMap = entry.getValue();
            completion2400(date, resultMap, valueMap,null,null);
            LoadCityHisDO loadCityHisDO = new LoadCityHisDO();
            loadCityHisDO.setCityId("410000");
            loadCityHisDO.setDate(new java.sql.Date(date.getTime()));
            loadCityHisDO.setCaliberId("1");
            BasePeriodUtils.setAllFiled(loadCityHisDO, valueMap);
            loadCityHisService.doInsertOrUpdateNotNull(loadCityHisDO);
        }
        return resp;
    }

    @Autowired
    private CityService cityService;

    @RequestMapping(value = "/importHisWeather", method = RequestMethod.POST)
    public BaseResp importHisWeather(@RequestParam(value = "file") MultipartFile file) throws Exception {
        BaseResp resp = BaseResp.succResp();
        List<List<List<String>>> read = ExcelUtil.read(file.getInputStream(), false);

        Map<String, String> cityMap = cityService.findAllCitys().stream().collect(Collectors.toMap(CityDO::getCity, CityDO::getId));
        Map<String, Map<Date, Map<String, BigDecimal>>> tmpMap = new HashMap<>();
        Map<String, Map<Date, Map<String, BigDecimal>>> humMap = new HashMap<>();
        Map<String, Map<Date, Map<String, BigDecimal>>> windMap = new HashMap<>();
        Map<String, Map<Date, Map<String, BigDecimal>>> rainMap = new HashMap<>();
        read.stream()
                .flatMap(List::stream)
                .skip(1) // 跳过标题行
                .forEach(list -> {
                    String cityName = list.get(0);
                    String cityId = cityMap.get(cityName);
                    if (cityId == null) {
                        cityId = cityMap.get(cityName + "市");
                    }
                    if (cityId == null) return;

                    // 解析时间
                    // 解析时间
                    String[] timeParts = list.get(10).split(" ");
                    Date date = DateUtil.getDate(timeParts[0], "yyyy-MM-dd");
                    String field = "t" + timeParts[1] + "00";
                    // 处理温度数据
                    makeMapValue(list, tmpMap, cityId, date, field, 3);

                    // 处理湿度数据
                    makeMapValue(list, humMap, cityId, date, field, 4);

                    // 处理雨量数据
                    makeMapValue(list, rainMap, cityId, date, field, 5);


                    // 处理风速数据
                    makeMapValue(list, windMap, cityId, date, field, 6);

                });


        saveHisWeather(tmpMap, WeatherEnum.TEMPERATURE.getType());
        saveHisWeather(humMap, WeatherEnum.HUMIDITY.getType());
        saveHisWeather(windMap, WeatherEnum.WINDSPEED.getType());
        saveHisWeather(rainMap, WeatherEnum.RAINFALL.getType());

        return resp;
    }

    private void saveHisWeather(Map<String, Map<Date, Map<String, BigDecimal>>> tmpMap, int type) throws Exception {
        for (Map.Entry<String, Map<Date, Map<String, BigDecimal>>> cityEntry : tmpMap.entrySet()) {
            String cityId = cityEntry.getKey();
            Map<Date, Map<String, BigDecimal>> dateMap = cityEntry.getValue();
            for (Map.Entry<Date, Map<String, BigDecimal>> dateEntry : cityEntry.getValue().entrySet()) {
                Date date = dateEntry.getKey();
                Map<String, BigDecimal> valueMap = dateEntry.getValue();
                //补全2400值
                completion2400(date, dateMap, valueMap,cityId,type);
                WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                weatherCityHisDO.setCityId(cityId);
                weatherCityHisDO.setDate(new java.sql.Date(date.getTime()));
                weatherCityHisDO.setType(type);
                BasePeriodUtils.setAllFiled(weatherCityHisDO, valueMap);
                PeriodDataUtil.do24To96VO(weatherCityHisDO);
                weatherCityHisService.doInsertOrUpdate(weatherCityHisDO);
                System.out.println(1);
            }
        }
    }

    @RequestMapping(value = "/importFcWeather", method = RequestMethod.POST)
    public BaseResp importFcWeather(@RequestParam(value = "file") MultipartFile file) throws Exception {
        BaseResp resp = BaseResp.succResp();
        List<List<List<String>>> read = ExcelUtil.read(file.getInputStream(), false);

        Map<String, String> cityMap = cityService.findAllCitys().stream().collect(Collectors.toMap(CityDO::getCity, CityDO::getId));
        Map<String, Map<Date, Map<String, BigDecimal>>> tmpMap = new HashMap<>();
        Map<String, Map<Date, Map<String, BigDecimal>>> humMap = new HashMap<>();
        Map<String, Map<Date, Map<String, BigDecimal>>> tgwdMap = new HashMap<>();
        Map<String, Map<Date, Map<String, BigDecimal>>> rainMap = new HashMap<>();

        AtomicReference<Date> preLoadDate = new AtomicReference<>();
        read.stream()
                .flatMap(List::stream)
                .skip(1) // 跳过标题行
                .forEach(list -> {
                    String cityName = list.get(0);
                    String cityId = cityMap.get(cityName);
                    if (cityId == null) return;

                    // 解析时间
                    String[] timeParts = list.get(7).split(" ");
                    Date date = DateUtil.getDate(timeParts[0], "yyyy-MM-dd");
                    String[] hourMinute = timeParts[1].split(":");
                    String field = "t" + hourMinute[0] + hourMinute[1];

                    //预测负荷日期
                    if (Objects.isNull(preLoadDate.get())) {
                        Date preDate = DateUtil.getDate(list.get(6).split(" ")[0], "yyyy-MM-dd");
                        preLoadDate.set(DateUtil.getMoveDay(preDate, 2));
                    }
                    // 处理温度数据
                    makeMapValue(list, tmpMap, cityId, date, field, 1);

                    // 处理湿度数据
                    makeMapValue(list, humMap, cityId, date, field, 2);

                    // 处理雨量数据
                    makeMapValue(list, rainMap, cityId, date, field, 3);


                    // 处理体感温度数据
                    makeMapValue(list, tgwdMap, cityId, date, field, 4);
                });


        saveFcWeather(tmpMap, WeatherEnum.TEMPERATURE.getType(), preLoadDate);
        saveFcWeather(humMap, WeatherEnum.HUMIDITY.getType(), preLoadDate);
        saveFcWeather(tgwdMap, WeatherEnum.EFFECTIVE_TEMPERATURE.getType(), preLoadDate);
        saveFcWeather(rainMap, WeatherEnum.RAINFALL.getType(), preLoadDate);

        return resp;
    }

    private void saveFcWeather(Map<String, Map<Date, Map<String, BigDecimal>>> tmpMap, int type, AtomicReference<Date> preLoadDate) throws Exception {
        for (Map.Entry<String, Map<Date, Map<String, BigDecimal>>> cityEntry : tmpMap.entrySet()) {
            Map<Date, Map<String, BigDecimal>> dateMap = cityEntry.getValue();
            for (Map.Entry<Date, Map<String, BigDecimal>> dateEntry : cityEntry.getValue().entrySet()) {
                Date date = dateEntry.getKey();
                Map<String, BigDecimal> valueMap = dateEntry.getValue();
                //补全缺失的时间点值
                completionMissingValues(date, dateMap, valueMap);
            }
        }

        for (Map.Entry<String, Map<Date, Map<String, BigDecimal>>> cityEntry : tmpMap.entrySet()) {
            String cityId = cityEntry.getKey();
            for (Map.Entry<Date, Map<String, BigDecimal>> dateEntry : cityEntry.getValue().entrySet()) {
                Date date = dateEntry.getKey();
                Map<String, BigDecimal> valueMap = dateEntry.getValue();
                //补全缺失的时间点值
                WeatherCityFcDO weatherCityHisDO = new WeatherCityFcDO();
                weatherCityHisDO.setCityId(cityId);
                weatherCityHisDO.setDate(new java.sql.Date(date.getTime()));
                weatherCityHisDO.setType(type);
                BasePeriodUtils.setAllFiled(weatherCityHisDO, valueMap);
                PeriodDataUtil.do24To96VO(weatherCityHisDO);
                weatherCityFcService.doInsertOrUpdate(weatherCityHisDO);
                if (Objects.nonNull(preLoadDate.get())) {
                    WeatherCityFcBatchDO weatherCityFcBatchDO = new WeatherCityFcBatchDO();
                    BeanUtils.copyProperties(weatherCityHisDO, weatherCityFcBatchDO);
                    weatherCityFcBatchDO.setPredLoadDate(preLoadDate.get());
                    weatherCityFcBatchDO.setSource("fc");
                    weatherCityFcBatchDO.setId(null);
                    weatherCityFcBatchService.doSaveOuUpdatePreLoadWeather(weatherCityFcBatchDO);
                }
            }
        }

    }

    private static void makeMapValue(List<String> list, Map<String, Map<Date, Map<String, BigDecimal>>> weatherMap,
                                     String cityId, Date date, String field, int type) {
        try {
            BigDecimal weatherValue = new BigDecimal(list.get(type));
            weatherMap.computeIfAbsent(cityId, k -> new HashMap<>())
                    .computeIfAbsent(date, k -> new HashMap<>())
                    .put(field, weatherValue);
        } catch (Exception e) {
            weatherMap.computeIfAbsent(cityId, k -> new HashMap<>())
                    .computeIfAbsent(date, k -> new HashMap<>())
                    .put(field, null);
        }

    }

    /**
     * 补全缺失的时间点值，包括 t0000-t2400 的所有缺失值
     * 补值逻辑：
     * 1. 数据中只有 t0200-t2300 的值
     * 2. 根据当天的t2300和第二天的t0200进行差值平均补值
     * 3. 补全 t0000-t0200 和 t2300-t2400 的所有缺失值
     * 4. 保证今日t0000与昨天的t2400值相同
     */
    private static void completionMissingValues(Date date, Map<Date, Map<String, BigDecimal>> dateMap, Map<String, BigDecimal> valueMap) {
        // 1. 根据跨天数据补全 t2300 到第二天 t0200 之间的所有值
        completeCrossDayRange(date, dateMap, valueMap);

        // 2. 处理跨天一致性：确保今日t0000与昨天的t2400值相同
        handleCrossDayConsistency(date, dateMap, valueMap);
    }

    /**
     * 根据跨天数据补全从当天t2300到第二天t0200之间的所有值
     * 使用当天t2300和第二天t0200进行差值平均补值
     */
    private static void completeCrossDayRange(Date date, Map<Date, Map<String, BigDecimal>> dateMap, Map<String, BigDecimal> valueMap) {
        BigDecimal currentT2300 = valueMap.get("t2300");
        if (currentT2300 == null) {
            return;
        }

        // 获取第二天的数据
        Date nextDay = DateUtil.getMoveDay(date, 1);
        Map<String, BigDecimal> nextDayMap = dateMap.get(nextDay);
        BigDecimal nextT0200 = null;
        if (Objects.nonNull(nextDayMap)) {
            nextT0200 = nextDayMap.get("t0200");
        }

        if (nextT0200 != null) {
            // 如果有第二天的t0200值，使用差值平均补值
            completeCrossDayRangeWithNextDay(valueMap, nextDayMap, currentT2300, nextT0200);
        } else {
            // 如果没有第二天的t0200值，使用当天数据进行简单补值
            completeCrossDayRangeWithoutNextDay(valueMap, currentT2300);
        }
    }

    /**
     * 使用当天t2300和第二天t0200进行差值平均补值
     */
    private static void completeCrossDayRangeWithNextDay(Map<String, BigDecimal> currentDayMap,
                                                         Map<String, BigDecimal> nextDayMap,
                                                         BigDecimal currentT2300,
                                                         BigDecimal nextT0200) {
        // 生成从t2300到第二天t0200的所有时间点
        List<String> crossDayTimePoints = getCrossDayTimePoints();

        // 计算总的差值和间隔数
        BigDecimal totalDiff = nextT0200.subtract(currentT2300);
        int totalIntervals = crossDayTimePoints.size() - 1; // 从t2300到t0200的总间隔数
        BigDecimal increment = totalDiff.divide(new BigDecimal(totalIntervals), 4, RoundingMode.HALF_UP);

        // 补全当天的值（t2315, t2330, t2345, t2400）
        for (int i = 1; i <= 4; i++) {
            String timePoint = crossDayTimePoints.get(i);
            if (currentDayMap.get(timePoint) == null) {
                BigDecimal interpolatedValue = currentT2300.add(increment.multiply(new BigDecimal(i)));
                currentDayMap.put(timePoint, interpolatedValue);
            }
        }

        // 补全第二天的值（t0000, t0015, t0030, t0045, t0100, t0115, t0130, t0145）
        for (int i = 5; i < crossDayTimePoints.size() - 1; i++) {
            String timePoint = crossDayTimePoints.get(i);
            if (nextDayMap.get(timePoint) == null) {
                BigDecimal interpolatedValue = currentT2300.add(increment.multiply(new BigDecimal(i)));
                nextDayMap.put(timePoint, interpolatedValue);
            }
        }
    }

    /**
     * 当没有第二天t0200值时的补值方法
     */
    private static void completeCrossDayRangeWithoutNextDay(Map<String, BigDecimal> valueMap, BigDecimal currentT2300) {
        // 简单地基于t2300值进行微小变化补值
        String[] timePoints = {"t2315", "t2330", "t2345", "t2400"};
        BigDecimal baseIncrement = currentT2300.multiply(new BigDecimal("0.001")); // 0.1%的微小变化

        for (int i = 0; i < timePoints.length; i++) {
            if (valueMap.get(timePoints[i]) == null) {
                BigDecimal value = currentT2300.add(baseIncrement.multiply(new BigDecimal(i + 1)));
                valueMap.put(timePoints[i], value);
            }
        }
    }

    /**
     * 获取从当天t2300到第二天t0200的所有时间点
     */
    private static List<String> getCrossDayTimePoints() {
        return Arrays.asList(
                "t2300",  // 0 - 当天23:00
                "t2315",  // 1 - 当天23:15
                "t2330",  // 2 - 当天23:30
                "t2345",  // 3 - 当天23:45
                "t2400",  // 4 - 当天24:00
                "t0000",  // 5 - 第二天00:00
                "t0015",  // 6 - 第二天00:15
                "t0030",  // 7 - 第二天00:30
                "t0045",  // 8 - 第二天00:45
                "t0100",  // 9 - 第二天01:00
                "t0115",  // 10 - 第二天01:15
                "t0130",  // 11 - 第二天01:30
                "t0145",  // 12 - 第二天01:45
                "t0200"   // 13 - 第二天02:00
        );
    }


    /**
     * 处理跨天一致性：确保今日t0000与昨天的t2400值相同
     */
    private static void handleCrossDayConsistency(Date date, Map<Date, Map<String, BigDecimal>> dateMap, Map<String, BigDecimal> valueMap) {
        // 优先从前一天获取t2400作为今天的t0000
        Date previousDay = DateUtil.getMoveDay(date, -1);
        Map<String, BigDecimal> previousMap = dateMap.get(previousDay);
        if (Objects.nonNull(previousMap)) {
            BigDecimal previousT2400 = previousMap.get("t2400");
            if (previousT2400 != null) {
                // 如果前一天已经有t2400值，使用它作为今天的t0000
                valueMap.put("t0000", previousT2400);
            } else {
                // 如果前一天没有t2400值，将今天的t0000设置给前一天的t2400
                BigDecimal currentT0000 = valueMap.get("t0000");
                if (currentT0000 != null) {
                    previousMap.put("t2400", currentT0000);
                }
            }
        }

        // 将今天的t2400设置给明天的t0000
        BigDecimal currentT2400 = valueMap.get("t2400");
        if (currentT2400 != null) {
            Date nextDay = DateUtil.getMoveDay(date, 1);
            Map<String, BigDecimal> nextMap = dateMap.get(nextDay);
            if (Objects.nonNull(nextMap)) {
                nextMap.put("t0000", currentT2400);
            }
        }
    }


    /**
     * 获取两个时间点之间的所有时间点（包括起始和结束点）
     */
    private static List<String> getTimePointsBetween(String startTime, String endTime) {
        List<String> timePoints = new ArrayList<>();

        // 解析起始时间
        int startHour = Integer.parseInt(startTime.substring(1, 3));
        int startMinute = Integer.parseInt(startTime.substring(3, 5));

        // 解析结束时间
        int endHour = Integer.parseInt(endTime.substring(1, 3));
        int endMinute = Integer.parseInt(endTime.substring(3, 5));

        // 生成时间点列表（每15分钟一个点）
        int currentHour = startHour;
        int currentMinute = startMinute;

        while (currentHour < endHour || (currentHour == endHour && currentMinute <= endMinute)) {
            String timePoint = String.format("t%02d%02d", currentHour, currentMinute);
            timePoints.add(timePoint);

            // 增加15分钟
            currentMinute += 15;
            if (currentMinute >= 60) {
                currentMinute = 0;
                currentHour++;
            }

            // 处理跨天情况（例如从23:45到第二天的00:00）
            if (currentHour >= 24) {
                if (endHour == 24 && endMinute == 0) {
                    // 特殊处理 t2400
                    timePoints.add("t2400");
                }
                break;
            }
        }

        return timePoints;
    }

     void completion2400(Date date, Map<Date, Map<String, BigDecimal>> dateMap, Map<String, BigDecimal> valueMap,String cityId,Integer type) throws Exception {
        Date moveDay = DateUtil.getMoveDay(date, 1);
        Map<String, BigDecimal> nextMap = dateMap.get(moveDay);
        if (Objects.nonNull(nextMap)) {
            valueMap.put("t2400", nextMap.get("t0000"));
        }else{
            if (type ==null){
                return;
            }
            WeatherCityHisDO nextDo = weatherCityHisService.findWeatherCityHisDO(cityId, type, moveDay);
            if (Objects.nonNull(nextDo)) {
                valueMap.put("t2400", nextDo.getT0000());
            }
        }
    }


    @Autowired
    private DayMaxLoadForecastService dayMaxLoadForecastService;

    @GetMapping("/dayMaxLoadForecast")
    public BaseResp dayMaxLoadForecast(Date startDate, Date endDate) {
        if (Objects.isNull(startDate)) {
            startDate = DateUtil.getMoveDay(DateUtil.getFormatDate(new Date()), 1);
            endDate = DateUtil.getMoveDay(startDate, 7);
        }
        Date finalStartDate = startDate;
        Date finalEndDate = endDate;
        AlgorithmInvoker.getInstance().invoke(() -> {
            try {
                dayMaxLoadForecastService.doMaxLoadForecast(finalStartDate, finalEndDate, "1", "410000", false, null, null, null);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        return BaseResp.succResp();
    }


    @GetMapping("/trainDayMaxLoadForecast")
    public BaseResp trainDayMaxLoadForecast(Date startDate, Date endDate) {
        try {
            if (Objects.isNull(startDate)) {
                startDate = DateUtil.getMoveDay(DateUtil.getFormatDate(new Date()), 1);
                endDate = DateUtil.getMoveDay(startDate, 7);
            }
            Date finalStartDate = startDate;
            Date finalEndDate = endDate;
            AlgorithmInvoker.getInstance().invoke(() -> {
                try {
                    dayMaxLoadForecastService.doMaxLoadForecast(finalStartDate, finalEndDate, "1", "410000", true, null, null, null);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return BaseResp.succResp();
    }

    @GetMapping("/supplementDay96LoadForecast")
    public BaseResp supplementDay96LoadForecast(Date startDate, Date endDate,Integer recall,String algorithmId,Integer point) throws Exception {
        if (Objects.isNull(startDate)) {
            startDate = DateUtil.getMoveDay(DateUtil.getFormatDate(new Date()), 1);
            endDate = DateUtil.getMoveDay(startDate, 9);
        }
        boolean isRecall = recall.equals(1);
        List<AlgorithmDO> allAlgorithmsNotCache = algorithmService.getAllAlgorithmsNotCache();
        List<Date> listBetweenDay = DateUtil.getListBetweenDay(startDate, endDate);

        AlgorithmInvoker instance = AlgorithmInvoker.getInstance();
        String uid = DateUtil.getUid();
        for (Date date : listBetweenDay) {
            for (AlgorithmDO algorithmDO : allAlgorithmsNotCache) {
                if (algorithmId.equals(algorithmDO.getId())) {
                    instance.invoke(() -> {
                        try {
                            dayMaxLoadForecastService.do96LoadForecast(date, date, "410000", "1", algorithmDO, true, point, uid, isRecall);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    });
                }
            }
        }
        return BaseResp.succResp();
    }

    @GetMapping("/trainDay96LoadForecast")
    public BaseResp trainDay96LoadForecast(Date startDate, Date endDate) throws Exception {
        if (Objects.isNull(startDate)){
            startDate = DateUtil.getMoveDay(DateUtil.getFormatDate(new Date()),1);
            endDate = DateUtil.getMoveDay(startDate,9);
        }
        Date finalStartDate = startDate;
        Date finalEndDate = endDate;
        List<AlgorithmDO> allAlgorithmsNotCache = algorithmService.getAllAlgorithmsNotCache();
        AlgorithmInvoker instance = AlgorithmInvoker.getInstance();
        String uid = DateUtil.getUid();
        for (AlgorithmDO algorithmDO : allAlgorithmsNotCache) {
            if (AlgorithmEnum.Day_96_FORECAST_FC.getId().equals(algorithmDO.getId()) ||
                    AlgorithmEnum.Day_96_FORECAST_EC.getId().equals(algorithmDO.getId())){
                instance.invoke(()->{
                    try {
                        dayMaxLoadForecastService.do96LoadForecast(finalStartDate, finalEndDate,"410000","1",algorithmDO,true,null,uid,false);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                });
            }
        }
        return BaseResp.succResp();
    }

    @GetMapping("/importHisLoad")
    public BaseResp importHisLoad(@RequestParam(value = "file") MultipartFile file) {
        try {
            List<List<List<String>>> read = ExcelUtil.read(file.getInputStream(), false);
            Map<Date, LoadCityHisDO> resultMap = new HashMap<>();
            read.stream()
                    .flatMap(List::stream)
                    .skip(1) // 跳过标题行
                    .forEach(list -> {
                        String cityId = CityConstants.PROVINCE_ID;
                        List<BigDecimal> value = list.subList(1, list.size()).stream().map(t -> Objects.isNull(t) || t.isEmpty() ? null : new BigDecimal(t)).collect(Collectors.toList());
                        LoadCityHisDO loadCityHisDO = new LoadCityHisDO();
                        loadCityHisDO.setCityId(cityId);
                        loadCityHisDO.setDate(new java.sql.Date(DateUtil.getDate(list.get(0), "yyyy-MM-dd").getTime()));
                        loadCityHisDO.setCaliberId("1");
                        try {
                            BasePeriodUtils.setAllFiled(loadCityHisDO, ColumnUtil.listToMap(value, true));
                            resultMap.put(DateUtil.getDate(list.get(0), "yyyy-MM-dd"), loadCityHisDO);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }

                    });

            for (Map.Entry<Date, LoadCityHisDO> entry : resultMap.entrySet()) {
                Date date = entry.getKey();
                LoadCityHisDO value = entry.getValue();
                LoadCityHisDO loadCityHisDO = resultMap.get(DateUtil.getMoveDay(date, 1));
                if (Objects.nonNull(loadCityHisDO)) {
                    value.setT2400(loadCityHisDO.getT0000());
                }
                loadCityHisService.doCreate(value);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return BaseResp.succResp();
    }

    @GetMapping("/importYearHisWeather")
    public BaseResp importYearHisWeather(@RequestParam(value = "file") MultipartFile file) {
        try {
            List<List<List<String>>> read = ExcelUtil.read(file.getInputStream(), false);

            Map<String, Map<Date, Map<Integer, WeatherCityHisDO>>> resultMap = new HashMap<>();

            read.stream()
                    .flatMap(List::stream)
                    .skip(1).parallel() // 跳过标题行
                    .forEach(list -> {
                        String cityName = list.get(1);
                        String cityId = CityEnum.getIdByCity(cityName);
                        if (Objects.isNull(cityId)) {
                            return;
                        }
                        String type = list.get(2);
                        String dateStr = list.get(0);
                        Date date = DateUtil.getDate(dateStr, "yyyy-MM-dd");
                        Integer typeByName = com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum.getTypeByName(type);
                        if (Objects.isNull(typeByName)) {
                            return;
                        }
                        List<BigDecimal> value = list.subList(3, list.size()).stream().map(t -> Objects.isNull(t) || t.isEmpty() ? null : new BigDecimal(t)).collect(Collectors.toList());
                        WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                        weatherCityHisDO.setCityId(cityId);
                        weatherCityHisDO.setType(typeByName);
                        try {
                            BasePeriodUtils.setAllFiled(weatherCityHisDO, ColumnUtil.listToMap(value, true));
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                        weatherCityHisDO.setDate(new java.sql.Date(date.getTime()));
                        resultMap.computeIfAbsent(cityId, k -> new HashMap<>())
                                .computeIfAbsent(date, k -> new HashMap<>())
                                .put(typeByName, weatherCityHisDO);

                    });

            for (Map.Entry<String, Map<Date, Map<Integer, WeatherCityHisDO>>> stringMapEntry : resultMap.entrySet()) {
                String cityId = stringMapEntry.getKey();
                for (Map.Entry<Date, Map<Integer, WeatherCityHisDO>> dateMapEntry : stringMapEntry.getValue().entrySet()) {
                    Date date = dateMapEntry.getKey();
                    for (Map.Entry<Integer, WeatherCityHisDO> entry : dateMapEntry.getValue().entrySet()) {
                        Integer type = entry.getKey();
                        WeatherCityHisDO value = entry.getValue();
                        Map<Date, Map<Integer, WeatherCityHisDO>> dateMapMap = stringMapEntry.getValue();
                        if (Objects.nonNull(dateMapMap)) {
                            Map<Integer, WeatherCityHisDO> integerWeatherCityHisDOMap = dateMapMap.get(DateUtil.getMoveDay(date, 1));
                            if (Objects.nonNull(integerWeatherCityHisDOMap)) {
                                WeatherCityHisDO nextDo = integerWeatherCityHisDOMap.get(type);
                                if (Objects.nonNull(nextDo)) {
                                    value.setT2400(nextDo.getT0000());
                                }
                            }else{
                                WeatherCityHisDO nextDo = weatherCityHisService.findWeatherCityHisDO(cityId, type, DateUtil.getMoveDay(date, 1));                                if (Objects.nonNull(nextDo)) {
                                    value.setT2400(nextDo.getT0000());
                                }

                            }
                        }
                        PeriodDataUtil.do24To96VO(value);
                        weatherCityHisService.doCreate(value);
                    }

                }
            }

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return BaseResp.succResp();
    }


    private final static List<String> cityIds = Arrays.asList("410100", "410300", "411300", "411400", "411700", "411600");

    @GetMapping("/doStatWeatherFcCurve")
    public BaseResp doStatWeatherFcCurve(Date startDate, Date endDate) throws Exception {
        for (String cityId : cityIds) {
            weatherCityFcService.doAutoStatFcDaysTemperature(cityId, startDate, endDate);
        }
        return BaseResp.succResp();
    }
}