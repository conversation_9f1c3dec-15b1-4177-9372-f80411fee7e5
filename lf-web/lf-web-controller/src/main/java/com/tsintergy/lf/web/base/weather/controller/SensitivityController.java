/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangfeng Date:  2019/8/14 3:22 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.weather.controller;


import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.forecast.api.ForecastService;
import com.tsintergy.lf.serviceapi.base.forecast.dto.FeatureDTO;
import com.tsintergy.lf.serviceapi.base.forecast.dto.ResultDTO;
import com.tsintergy.lf.serviceapi.base.forecast.dto.ResultMonthDTO;
import com.tsintergy.lf.serviceapi.base.forecast.dto.SensitivityAlgorithmDTO;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import com.tsintergy.lf.web.base.weather.request.SensitivityAcConfigRequest;
import com.tsintergy.lf.web.base.weather.request.SensitivityAcRequest;
import com.tsintergy.lf.web.base.weather.request.SensitivityRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.*;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/8/14
 * @since 1.0.0
 */
@RestController
@RequestMapping(value = "/sensitivity")
@Api(value = "灵敏度控制器", tags = "灵敏度控制器")
public class SensitivityController extends CommonBaseController {

    @Autowired
    private ForecastService forecastService;

    @RequestMapping(value = "/analyze", method = RequestMethod.POST)
    @ApiOperation("灵敏度分析计算查询接口")
    public BaseResp<ResultDTO> doSensitivity(@RequestBody SensitivityRequest request) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        SensitivityAlgorithmDTO algorithmDTO = new SensitivityAlgorithmDTO();
        BeanUtils.copyProperties(request, algorithmDTO);
        algorithmDTO.setCaliberId(this.getCaliberId());
        algorithmDTO.setUserId(this.getLoginUserId());
        //调整排除时间字段数据
        if (CollectionUtils.isNotEmpty(request.getDateNotIncluded())) {
            algorithmDTO.setDateNotIncluded(processWebDateList(request.getDateNotIncluded()));
        }
        ResultDTO resultDTO = forecastService.doSensitivityAlgorithm(algorithmDTO);
        baseResp.setData(resultDTO);
        return baseResp;
    }

    @RequestMapping(value = "/ac/save", method = RequestMethod.POST)
    @ApiOperation("配置保存")
    public BaseResp<Void> doAcSaveConfig(@RequestBody SensitivityAcConfigRequest request) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        forecastService.doAcSensitivitySaveConfig(request.getCityId(), request.getYear(), request.getSeasonType(),
                request.getType(), request.getLoadType(), request.getValue());
        baseResp.setRetMsg("保存成功");
        return baseResp;
    }

    @RequestMapping(value = "/ac/date", method = RequestMethod.POST)
    @ApiOperation("获取排除日期")
    public BaseResp<List<String>> queryAcSaveConfig(@RequestBody SensitivityAcConfigRequest request) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<String> dates = forecastService.queryAcSensitivitySaveConfig(request.getCityId(), request.getYear(),
                request.getSeasonType(), request.getType(), request.getLoadType(), request.getValue());
        baseResp.setData(dates);
        return baseResp;
    }

    @RequestMapping(value = "/ac/date/delete", method = RequestMethod.POST)
    @ApiOperation("保存或者删除排除日期")
    public BaseResp<String> acSaveDateConfig(@RequestBody SensitivityAcConfigRequest request) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        forecastService.acSaveDateConfig(request.getCityId(), request.getYear(),
                request.getSeasonType(), request.getType(),
                request.getLoadType(), request.getDateNotIncluded(), request.getAcType());
        if (request.getAcType() == 1) {
            baseResp.setData("保存成功");
        } else if (request.getAcType() == 2) {
            baseResp.setData("删除成功");
        }
        return baseResp;
    }

    @RequestMapping(value = "/ac/analyze", method = RequestMethod.POST)
    @ApiOperation("灵敏度分析计算查询接口")
    public BaseResp<ResultDTO> doAcSensitivity(@RequestBody SensitivityAcRequest request) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        SensitivityAlgorithmDTO algorithmDTO = new SensitivityAlgorithmDTO();
        BeanUtils.copyProperties(request, algorithmDTO);
        algorithmDTO.setCaliberId(this.getCaliberId());
        algorithmDTO.setUserId(this.getLoginUserId());
        ResultDTO resultDTO = forecastService.doAcSensitivityAlgorithm(algorithmDTO);
        baseResp.setData(resultDTO);
        return baseResp;
    }

    @RequestMapping(value = "/ac/feature", method = RequestMethod.POST)
    @ApiOperation("灵敏度分析特性散点图")
    public BaseResp<FeatureDTO> doAcSensitivityFeature(@RequestBody SensitivityAcRequest request) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        SensitivityAlgorithmDTO algorithmDTO = new SensitivityAlgorithmDTO();
        BeanUtils.copyProperties(request, algorithmDTO);
        algorithmDTO.setCaliberId(this.getCaliberId());
        algorithmDTO.setUserId(this.getLoginUserId());
        FeatureDTO resultDTO = forecastService.doAcSensitivityFeatureAlgorithm(algorithmDTO);
        baseResp.setData(resultDTO);
        return baseResp;
    }

    @RequestMapping(value = "/ac/month/analyze", method = RequestMethod.POST)
    @ApiOperation("灵敏度分析计算查询接口")
    public BaseResp<ResultMonthDTO> doAcMonthSensitivity(@RequestBody SensitivityAcRequest request) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        SensitivityAlgorithmDTO algorithmDTO = new SensitivityAlgorithmDTO();
        BeanUtils.copyProperties(request, algorithmDTO);
        algorithmDTO.setCaliberId(this.getCaliberId());
        algorithmDTO.setUserId(this.getLoginUserId());
        ResultMonthDTO resultDTO = forecastService.doAcMonthSensitivityAlgorithm(algorithmDTO);
        baseResp.setData(resultDTO);
        return baseResp;
    }

    /**
     * 清洗前段传输的时间列表
     *
     * @param dateList 元素示例： 2021-01-01~2021-01-02
     */
    private List<String> processWebDateList(List<String> dateList) {
        List<String> result = new ArrayList<>();
        for (String date : dateList) {
            String[] split = date.split(Constants.DATE_SEPARATOR_PUNCTUATION);
            List<String> listBetweenDay = DateUtil.getListBetweenDay(split[0], split[1]);
            result.addAll(listBetweenDay);
        }
        Set<String> resultList = new TreeSet<>(result);
        dateList.clear();
        dateList.addAll(resultList);
        List<String> collect = dateList.stream().map(src -> src.replace("-", "")).collect(Collectors.toList());
        return collect;
    }

}