package com.tsintergy.lf.web.base.load.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class LoadShortFcFeatureRequest {
    @ApiModelProperty(value = "开始时间")
    private Date startDate;
    @ApiModelProperty(value = "结束时间")
    private Date endDate;
    @ApiModelProperty(value = "日期")
    private Date date;

    /**
    *
    * 时间间隔 1 五分钟 2 十五分钟
    */
    private Integer type;

    private String cityId;
}
