package com.tsintergy.lf.web.base.load.controller;

import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityMonthHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityYearHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityMonthHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityYearHisDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityMonthHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityQuarterHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityMonthHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityQuarterHisDO;
import com.tsintergy.lf.web.base.load.request.LoadAnalyseRequest;
import com.tsintergy.lf.web.base.load.response.PowerWeatherHisYearMonthResp;
import com.tsintergy.lf.web.base.load.response.YearMonthFeatureAnalyseResp;
import com.tsintergy.lf.web.base.load.response.YearMonthFeatureStatisticsResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 月负荷特性
 *
 * @author: wangh
 **/
@RequestMapping("/character/year")
@RestController
@Api(tags = "月负荷特性")
public class LoadFeatureCityYearHisController extends BaseLoadFeatureController {

    @Autowired
    private WeatherFeatureCityMonthHisService weatherFeatureCityMonthHisService;

    @Autowired
    private LoadFeatureCityMonthHisService loadFeatureCityMonthHisService;

    @Autowired
    private LoadFeatureCityYearHisService loadFeatureCityYearHisService;

    @Autowired
    private WeatherFeatureCityQuarterHisService weatherFeatureCityQuarterHisService;

    @Autowired
    CityService cityService;

    /**
     * 负荷特性-年发电k线图
     */
    @ApiOperation("年发电k线图")
    @RequestMapping(value = "/power_K", method = RequestMethod.POST)
    public BaseResp<List<YearMonthFeatureAnalyseResp>> getYearPowerFeatureKChart(@RequestBody LoadAnalyseRequest request) throws Exception {
        checkParam(request);
        String startYear = DateUtil.getYearByDate(this.getSystemDate());
        String endYear = DateUtil.getYearByDate(DateUtils.addYears(this.getSystemDate(), -12));
        if (request.getStartDate() != null) {
            startYear = request.getStartDate();
        }
        if (request.getEndDate() != null) {
            endYear = request.getEndDate();
        }

        List<LoadFeatureCityYearHisDO> yearDOList = loadFeatureCityYearHisService
            .getLoadFeatureCityYearHisVOS(request.getCityId(), startYear, endYear, super.getCaliberId());

        if (CollectionUtils.isEmpty(yearDOList)) {
            throw TsieExceptionUtils.newBusinessException("T706");
        }

        String weatherCityId = cityService.findWeatherCityId(request.getCityId());
        //通过季负荷特性，统计年降水量
        Map<String, BigDecimal> yearRainMap = new HashMap<>();
        //通过季负荷特性，统计最高温度
        Map<String, BigDecimal> yearHghestTemperatureMap = new HashMap<>();
        List<WeatherFeatureCityQuarterHisDO> weatherDOlist = weatherFeatureCityQuarterHisService
            .getListWeatherFeatureCityQuarterHisStatDOS(weatherCityId, startYear, endYear);
        if (CollectionUtils.isNotEmpty(weatherDOlist)) {
            Map<String, List<WeatherFeatureCityQuarterHisDO>> yearMap = weatherDOlist.stream()
                .collect(Collectors.groupingBy(WeatherFeatureCityQuarterHisDO::getYear));
            yearMap.forEach((year, list) -> {
                BigDecimal totalRain = list.stream().map(WeatherFeatureCityQuarterHisDO::getRainfall)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                yearRainMap.put(year, totalRain);
                List<BigDecimal> highestTemperatures = list.stream().map(WeatherFeatureCityQuarterHisDO::getHighestTemperature).filter(Objects::nonNull).collect(Collectors.toList());
                yearHghestTemperatureMap.put(year, CollectionUtils.isEmpty(highestTemperatures) ? BigDecimal.ZERO : highestTemperatures.stream().max(BigDecimal::compareTo).get());
            });
        }
        Map<String, List<LoadFeatureCityYearHisDO>> mapByDate = yearDOList.stream()
            .collect(Collectors.groupingBy(LoadFeatureCityYearHisDO::getYear));

        //构建vo
        List<YearMonthFeatureAnalyseResp> vos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(yearDOList)) {
            yearDOList.forEach(yearDO -> {
                YearMonthFeatureAnalyseResp vo = new YearMonthFeatureAnalyseResp();
                vo.setMinLoad(yearDO.getMinLoad());
                vo.setMaxLoad(yearDO.getMaxLoad());
                vo.setLoadGradient(yearDO.getLoadGradient());
                vo.setEnergy(yearDO.getEnergy());
                vo.setAvgLoad(yearDO.getAveLoad());
                vo.setDate(yearDO.getYear());
                vo.setRain(yearRainMap.get(yearDO.getYear()));
                vo.setHighestTemperature(yearHghestTemperatureMap.get(yearDO.getYear()));
                //获取前三年的出力数据
                List<LoadFeatureCityYearHisDO> firstData = mapByDate.get(vo.getDate());
                List<LoadFeatureCityYearHisDO> secondData = mapByDate
                    .get(String.valueOf(Integer.valueOf(vo.getDate()) - 1));
                List<LoadFeatureCityYearHisDO> thirdData = mapByDate
                    .get(String.valueOf(Integer.valueOf(vo.getDate()) - 2));
                if (firstData != null && secondData != null && thirdData != null) {
                    BigDecimal add = BigDecimalUtils
                        .add(firstData.get(0).getAveLoad(), secondData.get(0).getAveLoad());
                    BigDecimal add1 = BigDecimalUtils
                        .add(add, thirdData.get(0).getAveLoad());
                    BigDecimal divide = BigDecimalUtils
                        .divide(add1, new BigDecimal(3), 4);
                    vo.setAvgPeriod(divide);
                }
                vos.add(vo);
            });

        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(vos);
        return baseResp;
    }


    /**
     * 特性分析-年特性统计查询
     */
    @ApiOperation("年特性统计查询")
    @RequestMapping(value = "/statisticsFeatureCity", method = RequestMethod.POST)
    public BaseResp<YearMonthFeatureStatisticsResp> statisticsFeatureCity(@RequestBody LoadAnalyseRequest loadAnalyseRequest) throws Exception {
        String year = DateUtil.getYearByDate(this.getSystemDate());
        if (loadAnalyseRequest.getDate() != null) {
            year = loadAnalyseRequest.getDate();
        }
        LoadFeatureCityYearHisDO loadYear = loadFeatureCityYearHisService
            .getLoadFeatureCityYearHisVO(loadAnalyseRequest.getCityId(), year, super.getCaliberId());
        YearMonthFeatureStatisticsResp yearMonthFeatureStatisticsResp = new YearMonthFeatureStatisticsResp();
        if (loadYear != null) {
            yearMonthFeatureStatisticsResp.setTotalEnergy(loadYear.getEnergy());
            yearMonthFeatureStatisticsResp.setMinLoad(loadYear.getMinLoad());
            yearMonthFeatureStatisticsResp.setMaxLoad(loadYear.getMaxLoad());
            yearMonthFeatureStatisticsResp.setAvgLoad(loadYear.getAveLoad());
            yearMonthFeatureStatisticsResp.setAvgloadGradient(loadYear.getLoadGradient());
            yearMonthFeatureStatisticsResp.setDate(loadYear.getYear());
            yearMonthFeatureStatisticsResp.setGradient(loadYear.getGradient());
            yearMonthFeatureStatisticsResp.setDifferent(loadYear.getDifferent());
        }
        String lastYear=String.valueOf(new BigDecimal(year).subtract(new BigDecimal(1)));
        LoadFeatureCityYearHisDO lastData = loadFeatureCityYearHisService
                .getLoadFeatureCityYearHisVO(loadAnalyseRequest.getCityId(), lastYear, super.getCaliberId());
        if (lastData != null) {
            yearMonthFeatureStatisticsResp.setLastMinLoad(lastData.getMinLoad());
            yearMonthFeatureStatisticsResp.setLastMaxLoad(lastData.getMaxLoad());
            yearMonthFeatureStatisticsResp.setLastAvgLoad(lastData.getAveLoad());
            yearMonthFeatureStatisticsResp.setLastLoadGradient(lastData.getLoadGradient());
            yearMonthFeatureStatisticsResp.setLastDifferent(lastData.getDifferent());
            yearMonthFeatureStatisticsResp.setLastGradient(lastData.getGradient());
        }
//        List<WeatherFeatureCityQuarterHisDO> weatherFeatureCityQuarterHisStatDOS = weatherFeatureCityQuarterHisService
//            .listWeatherFeatureCityQuarterHisStatDOS(loadAnalyseRequest.getCityId(), year, year);
//        BigDecimal rain = weatherFeatureCityQuarterHisStatDOS.stream()
//            .map(WeatherFeatureCityQuarterHisDO::getRainfall).reduce(BigDecimal.ZERO, BigDecimal::add);
//        yearMonthFeatureStatisticsResp.setTotalRain(rain);
//        List<BigDecimal> highestTemperatures = weatherFeatureCityQuarterHisStatDOS.stream().map(WeatherFeatureCityQuarterHisDO::getHighestTemperature).filter(Objects::nonNull).collect(Collectors.toList());
//        yearMonthFeatureStatisticsResp.setHighestTemperature(CollectionUtils.isEmpty(highestTemperatures) ? BigDecimal.ZERO : highestTemperatures.stream().max(BigDecimal::compareTo).get());
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(yearMonthFeatureStatisticsResp);
        return baseResp;
    }

    /**
     * 特性分析-年历史曲线
     */
    @ApiOperation("年历史曲线")
    @RequestMapping(value = "/hisPowerAndRain", method = RequestMethod.POST)
    public BaseResp<PowerWeatherHisYearMonthResp> getMonthHisPowerAndRain(@RequestBody LoadAnalyseRequest loadAnalyseRequest) throws Exception {
        String year = DateUtil.getYearByDate(this.getSystemDate());
        if (loadAnalyseRequest.getDate() != null) {
            year = loadAnalyseRequest.getDate();
        }
        List<LoadFeatureCityMonthHisDO> powerFeatureCityMonthHisStatDOS = loadFeatureCityMonthHisService
            .getLoadFeatureCityMonthHisVOByYear(loadAnalyseRequest.getCityId(), year, super.getCaliberId());
        if (CollectionUtils.isEmpty(powerFeatureCityMonthHisStatDOS)) {
            throw TsieExceptionUtils.newBusinessException("T706");
        }
        List<String> monthList = DateUtil.getMonthList();
        Map<String, LoadFeatureCityMonthHisDO> hisCollect = powerFeatureCityMonthHisStatDOS.stream()
            .collect(Collectors.toMap(LoadFeatureCityMonthHisDO::getMonth, Function.identity(), (key1, key2) -> key2));
        List<LoadFeatureCityMonthHisDO> monthResultList = new ArrayList<>();
        for (String oneMonth : monthList) {
            LoadFeatureCityMonthHisDO weatherDO = hisCollect.get(oneMonth);
            monthResultList.add(weatherDO);
        }

        PowerWeatherHisYearMonthResp powerWeatherHisYearMonthVO = new PowerWeatherHisYearMonthResp();
        if (CollectionUtils.isNotEmpty(monthResultList)) {
            List<BigDecimal> maxPower = monthResultList.stream()
                .map(monthData -> monthData ==null? null : monthData.getMaxLoad()).collect(Collectors.toList());
            List<BigDecimal> minPower = monthResultList.stream()
                .map(monthData -> monthData ==null? null :  monthData.getMinLoad()).collect(Collectors.toList());
            List<BigDecimal> avePower = monthResultList.stream()
                .map(monthData -> monthData ==null? null :  monthData.getAveLoad()).collect(Collectors.toList());
            List<BigDecimal> loadGradient = monthResultList.stream()
                .map(monthData -> monthData ==null? null :  monthData.getLoadGradient()).collect(Collectors.toList());
            powerWeatherHisYearMonthVO.setMinLoad(minPower);
            powerWeatherHisYearMonthVO.setMaxLoad(maxPower);
            powerWeatherHisYearMonthVO.setAveLoad(avePower);
            powerWeatherHisYearMonthVO.setLoadGradient(loadGradient);
        }
        List<WeatherFeatureCityMonthHisDO> weatherFeatureCityMonthHisStatDOS = weatherFeatureCityMonthHisService
            .getWeatherFeatureCityYearHisDO(loadAnalyseRequest.getCityId(), year);
        if (CollectionUtils.isNotEmpty(weatherFeatureCityMonthHisStatDOS)) {
            Map<String, WeatherFeatureCityMonthHisDO> weatherMonthCollect = weatherFeatureCityMonthHisStatDOS.stream()
                .collect(Collectors.toMap(WeatherFeatureCityMonthHisDO::getMonth, Function.identity(), (key1, key2) -> key2));
            List<WeatherFeatureCityMonthHisDO> weatherMonthResultList = new ArrayList<>();
            for (String oneMonth : monthList) {
                WeatherFeatureCityMonthHisDO weatherDO = weatherMonthCollect.get(oneMonth);
                weatherMonthResultList.add(weatherDO);
            }
            List<BigDecimal> rain = weatherMonthResultList.stream()
                .map(weather -> weather ==null? null : weather.getRainfall()).collect(Collectors.toList());
            powerWeatherHisYearMonthVO.setRain(rain);
            List<BigDecimal> highestTemperature = weatherMonthResultList.stream()
                    .map(weather -> weather ==null? null : weather.getHighestTemperature()).collect(Collectors.toList());
            powerWeatherHisYearMonthVO.setHighestTemperature(highestTemperature);
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(powerWeatherHisYearMonthVO);
        return baseResp;
    }
}
