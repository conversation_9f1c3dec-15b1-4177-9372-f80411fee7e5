package com.tsintergy.lf.web.base.security.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tsieframework.cloud.security.serviceapi.system.bean.TsieMenuTreeBean;
import java.io.Serializable;

/**
 * @date: 6/22/18 2:47 PM
 * @author: angel
 **/
public class LoginResp implements Serializable{

    private String cityId;

    private String city;

    private Integer cityType;

    private String nickname;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private String systemTime;

    private Integer passwdChangePrompt;

    private TsieMenuTreeBean tsieMenuTreeBean;

    private String defaultMenuPath;

    private String defaultMenuPathName;

    private String loginMessage;
    /**
     * 是否可以切换日期
     */
    private Boolean roleSystemTime;

    /**
     * 时刻点从00000开始，为true  时刻点从0015开始，为false
     */
    private Boolean startWithZero;

    /**
     * 颜色
     */
    private  String colorStr;

    /**
     * 是否开启网格
     */
    private  String openReseau;

    private String peakSectionTime;

    private String loginUrl;

    public String getPeakSectionTime() {
        return peakSectionTime;
    }

    public void setPeakSectionTime(String peakSectionTime) {
        this.peakSectionTime = peakSectionTime;
    }

    public String getColorStr() {
        return colorStr;
    }

    public void setColorStr(String colorStr) {
        this.colorStr = colorStr;
    }

    public String getOpenReseau() {
        return openReseau;
    }

    public void setOpenReseau(String openReseau) {
        this.openReseau = openReseau;
    }

    public Boolean getStartWithZero() {
        return startWithZero;
    }

    public void setStartWithZero(Boolean startWithZero) {
        this.startWithZero = startWithZero;
    }

    public Boolean getRoleSystemTime() {
        return roleSystemTime;
    }

    public void setRoleSystemTime(Boolean roleSystemTime) {
        this.roleSystemTime = roleSystemTime;
    }

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getSystemTime() {
        return systemTime;
    }

    public void setSystemTime(String systemTime) {
        this.systemTime = systemTime;
    }

    public Integer getCityType() {
        return cityType;
    }

    public void setCityType(Integer cityType) {
        this.cityType = cityType;
    }

    public Integer getPasswdChangePrompt() {
        return passwdChangePrompt;
    }

    public void setPasswdChangePrompt(Integer passwdChangePrompt) {
        this.passwdChangePrompt = passwdChangePrompt;
    }

    public TsieMenuTreeBean getTsieMenuTreeBean() {
        return tsieMenuTreeBean;
    }

    public void setTsieMenuTreeBean(TsieMenuTreeBean tsieMenuTreeBean) {
        this.tsieMenuTreeBean = tsieMenuTreeBean;
    }

    public String getDefaultMenuPath() {
        return defaultMenuPath;
    }

    public void setDefaultMenuPath(String defaultMenuPath) {
        this.defaultMenuPath = defaultMenuPath;
    }

    public String getLoginMessage() {
        return loginMessage;
    }

    public void setLoginMessage(String loginMessage) {
        this.loginMessage = loginMessage;
    }

    public String getDefaultMenuPathName() {
        return defaultMenuPathName;
    }

    public void setDefaultMenuPathName(String defaultMenuPathName) {
        this.defaultMenuPathName = defaultMenuPathName;
    }

    public String getLoginUrl() {
        return loginUrl;
    }

    public void setLoginUrl(String loginUrl) {
        this.loginUrl = loginUrl;
    }
}
