/**
 * Copyright (C), 2015‐2022, 北京清能互联科技有限公司 Author:  <EMAIL> Date:  2022/5/24 0:44 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.acload.request;

import com.tsintergy.lf.serviceapi.base.common.enumeration.DateType2;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.persistence.Convert;
import lombok.Data;

/**
 * Description: 负荷方案管理 <br>
 *
 * <AUTHOR>
 * @create 2022/5/24
 * @since 1.0.0
 */
@Data
@ApiModel
public class LoadSolutionManageRequest {

    @ApiModelProperty(value = "城市id", example = "1")
    String cityId;

    @ApiModelProperty(value = "口径id", example = "1")
    String caliberId;

    @ApiModelProperty(value = "日期类型", example = "1")
    @Convert(converter = DateType2.DateType2Convertor.class)
    DateType2 dateType;

    @ApiModelProperty(value = "开始年份", example = "2021")
    String startYear;

    @ApiModelProperty(value = "结束年份", example = "2022")
    String endYear;

    String season;
}  
