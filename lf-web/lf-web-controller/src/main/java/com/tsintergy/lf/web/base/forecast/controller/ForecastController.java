package com.tsintergy.lf.web.base.forecast.controller;

import com.alibaba.excel.EasyExcel;
import com.tsieframework.cloud.security.web.businesslog.annotation.OperateLog;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.SimilarDateBean;
import com.tsintergy.lf.serviceapi.base.area.api.BaseAreaService;
import com.tsintergy.lf.serviceapi.base.area.pojo.BaseAreaDO;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.base.dto.SkipHolidayDTO;
import com.tsintergy.lf.serviceapi.base.base.pojo.BaseLoadFeatureCityDayDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceapi.base.common.enumeration.CommonSetting;
import com.tsintergy.lf.serviceapi.base.forecast.api.*;
import com.tsintergy.lf.serviceapi.base.forecast.dto.*;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.*;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureStatService;
import com.tsintergy.lf.serviceapi.base.load.dto.CityValueDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureFcDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemData;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayFcService;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherNameDTO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayFcDO;
import com.tsintergy.lf.web.base.common.util.ExcelMoreDayForecastListener;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import com.tsintergy.lf.web.base.forecast.request.*;
import com.tsintergy.lf.web.base.forecast.response.*;
import com.tsintergy.lf.web.base.util.CheckoutFileTypeSecurity;
import com.tsintergy.lf.web.base.weather.response.WeatherResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.tsieframework.core.base.exception.TsieExceptionUtils.newBusinessException;

/**
 * 预测
 *
 * <AUTHOR>
 * @date 2018-02-10
 */
@Api(tags = "预测")
@RequestMapping("/forecast")
@RestController
public class ForecastController extends CommonBaseController {

    /**
     * 为空的错误码
     */
    private final String NULL_ERR = "01C20180002";
    //结果上报
    private final Integer PUSH = 1;
    //结果暂存
    private final Integer SAVE = 2;
    @Autowired
    private ForecastService forecastService;
    @Autowired
    private WeatherFeatureCityDayFcService weatherFeatureCityDayFcService;
    @Autowired
    private SettingSystemService settingSystemService;
    @Autowired
    private SimilarDayService similarDayService;
    @Autowired
    private WeatherCityFcService weatherCityFcService;
    @Autowired
    private LoadCityHisService loadCityHisService;
    @Autowired
    private LoadCityFcService loadCityFcService;
    @Autowired
    private LoadCityFcTempService loadCityFcTempService;
    @Autowired
    private WeatherCityHisService weatherCityHisService;
    @Autowired
    private ForecastInfoService forecastInfoService;
    @Autowired
    private AlgorithmService algorithmService;
    @Autowired
    private ForecastResultStatService forecastResultStatService;
    @Autowired
    private HolidayService holidayService;
    @Autowired
    private CityService cityService;
    @Autowired
    private LoadFeatureStatService loadFeatureStatService;
    @Autowired
    private LoadCityFcBatchService loadCityFcBatchService;

    @Autowired
    private BaseAreaService baseAreaService;

    /**
     * 预测概况
     */
    @ApiOperation("预测概况")
    @GetMapping("/overview")
    public BaseResp<ForecastOverviewDTO> getMethodsPrecision(@ApiParam(value = "日期") Date date, @ApiParam(value = "口径id") String caliberId) throws Exception {

        if (date == null) {
            // 显示昨天的数据
            date = DateUtils.addDays(this.getSystemDate(), -1);
        }
        if (caliberId == null) {
            caliberId = this.getCaliberId();
        }
        String cityId = this.getLoginCityId();
        BaseResp resp = BaseResp.succResp();
        ForecastOverviewDTO forecastOverviewDTO = forecastService.getForecastOverview(cityId, date, caliberId);
        if (forecastOverviewDTO != null) {
            resp.setData(forecastOverviewDTO);
            return resp;
        } else {
            return new BaseResp("T706");
        }


    }

    /**
     * 获取气象预测的特征和综合指标 气象预报信息
     */
    @ApiOperation("获取气象预测的特征和综合指标")
    @GetMapping("/weather/overview")
    public BaseResp<WeatherFeatureCityDayFcDO> getFcWeather(@ApiParam(value = "日期") Date targetDate) throws Exception {
        String cityId = this.getLoginCityId();
        //cityId = generateCityId(cityId);
        cityId = cityService.findWeatherCityId(cityId);
        BaseResp<WeatherFeatureCityDayFcDO> response = BaseResp.succResp("查询成功");
        if (null == targetDate) {
            throw newBusinessException("T701");
        }
        response.setData(weatherFeatureCityDayFcService.findWeatherFeatureCityFcVOByDate(cityId, targetDate));
        return response;
    }


    /**
     * 获取辅助修正（营销口径）负荷曲线和特性
     */
    @ApiOperation("获取营销口径负荷曲线和特性")
    @GetMapping("/market/load")
    public BaseResp<ForecastLoadFeatureDTO> getMarketLoadData(Date targetDate, String cityId, String algorithmId)
        throws Exception {
        BaseResp<ForecastLoadFeatureDTO> response = BaseResp.succResp("查询成功");
        if (null == targetDate) {
            throw newBusinessException("T701");
        }
        ForecastLoadFeatureDTO fcLoadFeatureData = forecastService.getFcLoadFeatureData(targetDate, cityId, algorithmId);
        response.setData(fcLoadFeatureData);
        return response;
    }


    /**
     * get autoForecast data and its ref data
     */
    @ApiOperation("获取预测负荷、基准日、相似日和高峰时间")
    @RequestMapping(value = "/auto", method = RequestMethod.GET)
    public BaseResp<ForecastResp> getAutoForecast(ForecastRequest forecastRequest) throws Exception {
        Date today = this.getSystemDate();
        BaseResp<ForecastResp> resp = BaseResp.succResp("查询成功");
        ForecastResp forecastResp = new ForecastResp();
        //目标日---》系统日期后一天
        //基准日---》目标日期前两天
        //预测曲线-》 预测负荷
        //相似日曲线》预测气象数据当作目标气象，查询相似日
        if (forecastRequest.getTargetDay() == null) {
            forecastRequest.setTargetDay(DateUtil.getMoveDay(today, 1));
        }
        if (forecastRequest.getCityId() == null) {
            forecastRequest.setCityId(this.getLoginCityId());
        }
        if (forecastRequest.getCaliberId() == null) {
            forecastRequest.setCaliberId(this.getCaliberId());
        }
        String cityId = forecastRequest.getCityId();

        //查询预测曲线
        ForecastDTO forecastDTO = loadCityFcService
                .findForecastDTO(forecastRequest.getTargetDay(), cityId, forecastRequest.getAlgorithmId(),
                        forecastRequest.getCaliberId());
        forecastResp.setForecast(forecastDTO);

        if (forecastResp.getForecast() != null && forecastResp.getForecast().getList() != null) {
            forecastResp.setModifyLoad(forecastResp.getForecast().getList());
        } else {
            //如果查询的数据为空，则返回一条修正曲线为0的曲线。
            List reportLoad = new ArrayList() {
                {
                    for (int i = 0; i < 96; i++) {
                        add(new BigDecimal(0));
                    }
                }
            };
            forecastResp.setModifyLoad(reportLoad);
        }

        //查询最终上报的曲线
        LoadCityFcDO report = loadCityFcService
                .getReport(forecastRequest.getCityId(), forecastRequest.getCaliberId(),
                        forecastRequest.getTargetDay());
        if (report != null) {//有最终上报的曲线 展示最终上报的曲线
            forecastResp.setReportLoad(BasePeriodUtils
                    .toList(report, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            forecastResp.setModifyLoad(forecastResp.getReportLoad());
        }

        //人工修正的时候不返回置信最上限下限
        if (forecastDTO != null && !forecastRequest.getAlgorithmId()
                .equals(AlgorithmConstants.MD_ALGORITHM_ID)) {
            //获取置信最大最小限
            Map<String, List<BigDecimal>> map = forecastService
                    .getMaxMinConfidence(cityId, forecastRequest.getCaliberId(),
                            forecastResp.getForecast().getAlgorithmId(), forecastRequest.getTargetDay());
            if (!CollectionUtils.isEmpty(map) || map != null) {
                //置信上限
                forecastResp.setMax(map.get("max"));
                //置信下限
                forecastResp.setMin(map.get("min"));
            }
        }

        //基准日
        List<LoadCityHisDO> loadCityHisVOS = loadCityHisService
                .getLoadCityHisDOS(cityId, forecastRequest.getCaliberId(),
                        DateUtils.addDays(forecastRequest.getTargetDay(), -2),
                        DateUtils.addDays(forecastRequest.getTargetDay(), -2));
        if (!CollectionUtils.isEmpty(loadCityHisVOS)) {
            forecastResp.setBase(BasePeriodUtils.toList(loadCityHisVOS.get(0), Constants.LOAD_CURVE_POINT_NUM,
                    Constants.LOAD_CURVE_START_WITH_ZERO));
        }

        //获取近七日平均偏差率
        List<BigDecimal> list = forecastService
                .getAvgDeviation(cityId, forecastRequest.getCaliberId(), forecastRequest.getAlgorithmId(),
                        forecastRequest.getTargetDay());
        forecastResp.setDeviation(list);

        //气象取值----如果是省 则用省会城市的气象
        String weatherCityId = cityService.findWeatherCityId(cityId);
        forecastResp.setWeatherNameDTOS(weatherCityFcService.findAllByDateAndCityId(weatherCityId,
                forecastRequest.getTargetDay()));

        //当日实际曲线
        List<BigDecimal> hisLoadList = loadCityHisService.findLoadCityHisDO(forecastRequest.getTargetDay(), cityId,
                forecastRequest.getCaliberId());
        forecastResp.setHisLoad(hisLoadList);

        //地市预测累加
        if (Constants.PROVINCE_ID.equals(cityId)) {
            List<BigDecimal> cityFcAdd = loadCityFcService.findCityFcAdd(forecastRequest.getCaliberId(), forecastRequest.getAlgorithmId(),
                    forecastRequest.getTargetDay());
            forecastResp.setCityFcAdd(cityFcAdd);
        }

        resp.setData(forecastResp);
        return resp;
    }


    /**
     * get all type weather 逐时气象
     */
    @ApiOperation("获取预测负荷、基准日、相似日和高峰时间")
    @RequestMapping(value = "/weather", method = RequestMethod.GET)
    public BaseResp<WeatherResp> getWeatherFc(@ApiParam(value = "目标日") Date targetDate) throws Exception {
        if (null == targetDate) {
            throw newBusinessException("T701");
        }
        String cityId = this.getLoginCityId();
        cityId = this.generateCityId(cityId);
        BaseResp<WeatherResp> resp = BaseResp.succResp("查询成功");
        WeatherResp weatherResp = new WeatherResp();
        weatherResp.setWeatherList(weatherCityFcService.findAllByDateAndCityId(cityId, targetDate));
        resp.setData(weatherResp);
        return resp;
    }

    private String generateCityId(String cityId) throws Exception {
        CityDO cityVO = cityService.findCityById(cityId);
        if (cityVO.getType() == 1) {
            // todo wangchen 城市表设计不太合理，这里是要选择省会，先写死
            cityId = "2";
        }
        return cityId;
    }

    /**
     * 曲线平滑
     */
    @ApiOperation("曲线平滑")
    @RequestMapping(value = "/auto/{id}/data", method = RequestMethod.POST)
    public BaseResp<SmoothLineResp> smoothLine(@PathVariable("id") String id, @Validated @RequestBody SmoothLineRequest smoothLineRequest,
                                               BindingResult result) throws Exception {
        validateFormValue(result);
        BaseResp<SmoothLineResp> resp = BaseResp.succResp("平滑成功");
        SmoothLineResp smoothLineResp = new SmoothLineResp();
        smoothLineResp.setForecast(forecastService.smoothLine(smoothLineRequest.getForecast()));
        resp.setData(smoothLineResp);
        return resp;
    }

    /**
     * 根据考核点进行曲线平滑
     */
    @ApiOperation("根据考核点进行曲线平滑")
    @RequestMapping(value = "/auto/{id}/data/assess", method = RequestMethod.POST)
    public BaseResp<SmoothLineResp> smoothLineByAssess(@PathVariable("id") String id, @Validated @RequestBody SmoothLineRequest smoothLineRequest,
                                                       BindingResult result) throws Exception {
        validateFormValue(result);
        BaseResp<SmoothLineResp> resp = BaseResp.succResp("平滑成功");
        SmoothLineResp smoothLineResp = new SmoothLineResp();
        smoothLineResp.setForecast(forecastService.smoothLineByAssess(smoothLineRequest.getForecast()));
        resp.setData(smoothLineResp);
        return resp;
    }

    /**
     * 曲线修正
     */
    @ApiOperation("曲线修正")
    @RequestMapping(value = "/auto/recorrect", method = RequestMethod.POST)
    @OperateLog(operate = "曲线修正")
    public BaseResp<SmoothLineResp> recorrectLoad(@Validated @RequestBody RecorrectRequest recorrectRequest, BindingResult result)
            throws Exception {
        validateFormValue(result);
        BaseResp<SmoothLineResp> resp = BaseResp.succResp("修正完成");
        SmoothLineResp smoothLineResp = new SmoothLineResp();
        smoothLineResp.setForecast(forecastService
                .recorrectLoad(recorrectRequest.getForecast(), recorrectRequest.getMaxLoad(),
                        recorrectRequest.getMinLoad()));
        resp.setData(smoothLineResp);
        return resp;
    }

    @ApiOperation("根据考核点曲线修正")
    @RequestMapping(value = "/auto/recorrect/assess", method = RequestMethod.POST)
    @OperateLog(operate = "曲线修正")
    public BaseResp<SmoothLineResp> recorrectLoadByAssess(@Validated @RequestBody RecorrectRequest recorrectRequest, BindingResult result)
            throws Exception {
        BaseResp<SmoothLineResp> resp = BaseResp.succResp("修正完成");
        SmoothLineResp smoothLineResp = new SmoothLineResp();
        smoothLineResp.setForecast(forecastService.recorrectLoadByAssess(recorrectRequest.getForecast(), recorrectRequest.getEarlyPeak(),
                recorrectRequest.getNoonPeak(), recorrectRequest.getEveningPeak(), recorrectRequest.getWaistLoad(), recorrectRequest.getTrough()));
        resp.setData(smoothLineResp);
        return resp;
    }

    /**
     * 结果上报or暂存
     */
    @ApiOperation("结果上报or暂存")
    @RequestMapping(value = "/auto", method = RequestMethod.POST)
    @OperateLog(operate = "结果上报")
    public BaseResp saveResult(@RequestBody @Validated ResultPushOrSaveRequest resultRequest, BindingResult result)
            throws Exception {
        validateFormValue(result);
        BaseResp resp = BaseResp.succResp("保存成功");
        if (StringUtils.isBlank(resultRequest.getCaliberId())) {
            resultRequest.setCaliberId(this.getCaliberId());
        }
        if (StringUtils.isBlank(resultRequest.getCityId())) {
            resultRequest.setCityId(this.getLoginCityId());
        }
        // 结果上报
        if (PUSH.equals(resultRequest.getType())) {
            BaseResp checkResp = checkReportDeadlineTime(resultRequest.getCityId(), CommonSetting.ORDINARY_DAY,
                    resultRequest.getForecastList().getTargetDay(), resultRequest.getForecastList().getTargetDay(), null);
            if (checkResp.getRetCode().equals("T0")) {
                return checkResp;
            }
            LoadCityFcDO loadCityFcVO = new LoadCityFcDO();
            // 人工决策算法
            loadCityFcVO.setAlgorithmId(AlgorithmConstants.MD_ALGORITHM_ID);
            loadCityFcVO.setCityId(resultRequest.getCityId());
            loadCityFcVO.setDate(new java.sql.Date(resultRequest.getForecastList().getTargetDay().getTime()));
            loadCityFcVO.setUserId(this.getLoginUserId());
            Map<String, BigDecimal> dataMap = ColumnUtil
                    .listToMap(resultRequest.getForecastList().getValue(), Constants.LOAD_CURVE_START_WITH_ZERO);
            BasePeriodUtils.setAllFiled(loadCityFcVO, dataMap);
            loadCityFcVO.setCaliberId(resultRequest.getCaliberId());
            loadCityFcVO.setReport(true);
            loadCityFcVO.setReportTime(new Timestamp(System.currentTimeMillis()));
            loadCityFcVO.setSucceed(true);
            loadCityFcVO.setRecommend(false);
            loadCityFcVO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            try {
                loadCityFcService.doReport(loadCityFcVO);
                loadCityFcBatchService.doSave(loadCityFcVO);
            } catch (BusinessException e) {
                if ("03D20180601".equals(e.getErrorCode())) {
                    resp.setRetCode("T0");
                    resp.setRetMsg("操作失败 超过上报时间，不允许上报！");
                    return resp;
                }
            }
            // 统计预测结果
            forecastResultStatService.statForecastResult(loadCityFcVO.getCityId(), null, loadCityFcVO.getCaliberId(),
                    loadCityFcVO.getDate(), loadCityFcVO.getDate());
            //统计预测负荷特性
            loadFeatureStatService.doStatLoadFeatureCityDayFc(Arrays.asList(resultRequest.getCityId()), loadCityFcVO.getDate(), loadCityFcVO.getDate(),
                    loadCityFcVO.getCaliberId());
        }
        // 结果暂存
        else if (SAVE.equals(resultRequest.getType())) {
            LoadCityFcTempDO loadCityFcTempVO = new LoadCityFcTempDO();
            loadCityFcTempVO.setCityId(resultRequest.getCityId());
            loadCityFcTempVO.setCaliberId(resultRequest.getCaliberId());
            loadCityFcTempVO.setDate(new java.sql.Date(resultRequest.getForecastList().getTargetDay().getTime()));
            if (StringUtils.isBlank(resultRequest.getForecastList().getAlgorithmId())) {
                loadCityFcTempVO.setAlgorithmId(AlgorithmConstants.MD_ALGORITHM_ID);
            } else {
                loadCityFcTempVO.setAlgorithmId(resultRequest.getForecastList().getAlgorithmId());
            }
            loadCityFcTempVO.setUserId(this.getLoginUserId());
            Map<String, BigDecimal> dataMap = ColumnUtil
                    .listToMap(resultRequest.getForecastList().getValue(), Constants.LOAD_CURVE_START_WITH_ZERO);
            BasePeriodUtils.setAllFiled(loadCityFcTempVO, dataMap);
            loadCityFcTempService.doSaveOrUpdateLoadCityFcTempDO(loadCityFcTempVO);

        }
        return resp;
    }

    @ApiOperation("添加参照曲线")
    @RequestMapping(value = "/addReference", method = RequestMethod.GET)
    @OperateLog(operate = "添加参照曲线")
    public BaseResp<ReferenceResponse> getReferenceLoad(ReferenceRequest referenceRequest) throws Exception {
        if (StringUtils.isBlank(referenceRequest.getCaliberId())) {
            referenceRequest.setCaliberId(this.getCaliberId());
        }
        if (StringUtils.isBlank(referenceRequest.getCityId())) {
            referenceRequest.setCityId(this.getLoginCityId());
        }

        String caliberId = referenceRequest.getCaliberId();
        String cityId = referenceRequest.getCityId();
        Date date = referenceRequest.getReferenceDate();
        List<BigDecimal> load;
        List<WeatherNameDTO> weatherCityHisVOS;
        if (referenceRequest.getLoadType() == 1) {
            load = loadCityHisService.findLoadCityHisDO(date, cityId, caliberId);
            weatherCityHisVOS = weatherCityHisService
                    .findAllByDateAndCityId(cityService.findWeatherCityId(cityId), referenceRequest.getReferenceDate());
        } else if (referenceRequest.getLoadType() == 0) {
            load = loadCityFcService.findReportLoadCityFcDO(date, cityId, caliberId, null, true);
            weatherCityHisVOS = weatherCityFcService
                    .findAllByDateAndCityId(cityService.findWeatherCityId(cityId), referenceRequest.getReferenceDate());
        } else {
            load = loadCityFcService.findReportLoadCityFcDO(date, cityId, caliberId, "0", null);
            weatherCityHisVOS = weatherCityFcService
                    .findAllByDateAndCityId(cityService.findWeatherCityId(cityId), referenceRequest.getReferenceDate());
        }
        if (CollectionUtils.isEmpty(weatherCityHisVOS) && CollectionUtils.isEmpty(load)) {
            return new BaseResp("T706");
        }
        BaseResp baseResp = BaseResp.succResp();
        ReferenceResponse response = new ReferenceResponse();
        response.setDate(referenceRequest.getReferenceDate());
        CityDO city = cityService.findCityById(referenceRequest.getCityId());
        String dateStr = DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_STR);
        response.setReferenceName(city.getCity() + "-" + dateStr);
        response.setLoad(load);
        response.setWeatherLoad(weatherCityHisVOS);
        baseResp.setData(response);
        return baseResp;
    }


    /**
     * 查询暂存结果
     */
    @ApiOperation("查询暂存结果")
    @RequestMapping(value = "/auto/cache", method = RequestMethod.GET)
    public BaseResp<CacheResp> queryLoadCache(@ApiParam(value = "城市id") String cityId, @ApiParam(value = "口径id") String caliberId) throws Exception {
        if (StringUtils.isBlank(caliberId)) {
            caliberId = this.getCaliberId();
        }
        if (StringUtils.isBlank(cityId)) {
            cityId = this.getLoginCityId();
        }
        Date today = this.getSystemDate();
        BaseResp<CacheResp> resp = BaseResp.succResp("查询成功");
        CacheResp cacheResp = new CacheResp();
        LoadCityFcTempDO loadCityFcTempVO;
        try {
            loadCityFcTempVO = loadCityFcTempService.findLoadCityFcTempDO(cityId, caliberId, today);
        } catch (BusinessException e) {
            if (NULL_ERR.equals(e.getErrorCode())) {
                return resp;
            } else {
                throw e;
            }
        }
        if (loadCityFcTempVO == null || loadCityFcTempVO.getDate().before(today)) { // 如果暂存的结果是历史日期的，则不需要返回
            return new BaseResp("T706");
        }
        //暂存曲线
        ForecastDTO forecastDTO = new ForecastDTO();
        forecastDTO.setId(loadCityFcTempVO.getId());
        forecastDTO.setAlgorithmId(loadCityFcTempVO.getAlgorithmId());
        forecastDTO.setList(BasePeriodUtils
                .toList(loadCityFcTempVO, Constants.LOAD_CURVE_POINT_NUM,
                        Constants.LOAD_CURVE_START_WITH_ZERO));
        forecastDTO.setCaliberId(loadCityFcTempVO.getCaliberId());
        cacheResp.setForecast(forecastDTO);
        cacheResp.setTargetDay(loadCityFcTempVO.getDate());
        resp.setData(cacheResp);
        return resp;

    }

    /**
     * 功能描述: <br>
     * <p>
     * 获取预测信息详情  forecast_info_basic 预测信息表
     *
     * @return:
     * @since: 1.0.0
     * @Author:wangchen
     * @Date: 2018/7/31 16:25
     */
    @ApiOperation("获取预测信息详情")
    @RequestMapping(value = "/auto/detail", method = RequestMethod.GET)
    public BaseResp<ForecastInfoResp> queryForecastInfo(@ApiParam(value = "id") String id, @ApiParam(value = "开始时间") Timestamp startTime, @ApiParam(value = "口径id") String caliberId) throws Exception {
        if (StringUtils.isBlank(caliberId)) {
            caliberId = this.getCaliberId();
        }
        ForecastInfoDO forecastInfoVO = null;
        if (StringUtils.isNotBlank(id) || null != startTime) {
            Thread.sleep(8000);
            int i = 6;
            while (i-- > 0) {

                forecastInfoVO = forecastInfoService.findForecastInfoDOByPk(id);

                if (forecastInfoVO.getUpdatetime().after(startTime) || null != forecastInfoVO.getReportType()) {
                    break;
                }
                Thread.sleep(8000);
            }
        } else {
            forecastInfoVO = forecastInfoService.findLatestForecastInfoByCityId(this.getLoginCityId(), caliberId);
        }
        forecastInfoVO = forecastInfoService.findLatestForecastInfoByCityId(this.getLoginCityId(), caliberId);
        BaseResp<ForecastInfoResp> baseResp = BaseResp.succResp();
        ForecastInfoResp forecastInfoResp = new ForecastInfoResp();
        BeanUtils.copyProperties(forecastInfoVO, forecastInfoResp);
        forecastInfoResp.setAlgorithm(algorithmService.getAlgorithmCn(forecastInfoVO.getAlgorithmId()));
        baseResp.setData(forecastInfoResp);
        return baseResp;
    }


    @ApiOperation("获取预测设置详情")
    @RequestMapping(value = "/setting", method = RequestMethod.GET)
    public BaseResp<ForecastSettingResp> getForecastSetting() throws Exception {
        BaseResp<ForecastSettingResp> baseResp = BaseResp.succResp();
        ForecastSettingResp forecastSettingResp = new ForecastSettingResp();
        SystemData systemSetting = this.settingSystemService.getSystemSetting();
        if (this.getLoginCityId().equals(CityConstants.PROVINCE_ID)) {
            forecastSettingResp.setCustomizeAlgorithmId(systemSetting.getProvinceNormalAlgorithm());
            forecastSettingResp.setRecommendAlgorithmId(systemSetting.getProvinceNormalAlgorithm());
            forecastSettingResp
                    .setRecommendAlgorithm(algorithmService.getAlgorithmCn(systemSetting.getProvinceNormalAlgorithm()));
        } else {
            forecastSettingResp.setCustomizeAlgorithmId(systemSetting.getCityNormalAlgorithm());
            forecastSettingResp.setRecommendAlgorithmId(systemSetting.getCityNormalAlgorithm());
            forecastSettingResp
                    .setRecommendAlgorithm(algorithmService.getAlgorithmCn(systemSetting.getCityNormalAlgorithm()));
        }
        forecastSettingResp.setIsRecommend(true);
        forecastSettingResp.setStartTime("08:00");
        List<AlgorithmDO> algorithmVOS = algorithmService.getAllAlgorithms();
        List<AlgorithmDTO> algorithmDTOS = new ArrayList<AlgorithmDTO>(10);
        for (AlgorithmDO algorithmVO : algorithmVOS) {
            AlgorithmDTO algorithmDTO = new AlgorithmDTO();
            algorithmDTO.setId(algorithmVO.getId());
            algorithmDTO.setAlgorithm(algorithmVO.getAlgorithmCn());
            algorithmDTOS.add(algorithmDTO);
        }
        forecastSettingResp.setAlgorithms(algorithmDTOS);
        baseResp.setData(forecastSettingResp);
        return baseResp;
    }

    /**
     * 导入预测负荷
     */
    @ApiOperation("预测负荷导入")
    @RequestMapping(value = "/auto/import", method = RequestMethod.POST)
    @OperateLog(operate = "预测负荷导入")
    public BaseResp importForecastData(
            @RequestParam(value = "forecastFile", required = false) MultipartFile forecastFile, @ApiParam(value = "城市id") String cityId)
            throws Exception {
        boolean check = CheckoutFileTypeSecurity.checkoutFileTypeSecurity(forecastFile);
        if (!check) {
            return BaseResp.failResp("文件安全性存在问题");
        }
        EasyExcel.read(forecastFile.getInputStream(), ExcelMoreDayForecastData.class,
                        new ExcelMoreDayForecastListener(loadCityFcService, cityId, getLoginUserId(), getCaliberId()))
                .sheet()
                .headRowNumber(1).doRead();
        return BaseResp.succResp("导入成功");
    }

    /**
     * 导入多天预测负荷
     */
    @ApiOperation("导入多天预测负荷")
    @RequestMapping(value = "/days/import", method = RequestMethod.POST)
    @OperateLog(operate = "多天预测负荷导入")
    public BaseResp importMoreDayForecastData(
            @RequestParam(value = "forecastFile", required = false) MultipartFile forecastFile, @ApiParam(value = "城市id") String cityId)
            throws Exception {
        boolean check = CheckoutFileTypeSecurity.checkoutFileTypeSecurity(forecastFile);
        if (!check) {
            return BaseResp.failResp("文件安全性存在问题");
        }
        EasyExcel.read(forecastFile.getInputStream(), ExcelMoreDayForecastData.class,
                        new ExcelMoreDayForecastListener(loadCityFcService, cityId, getLoginUserId(), getCaliberId()))
                .sheet()
                .headRowNumber(1).doRead();
        return BaseResp.succResp("导入成功");
    }

    /**
     * 废弃 暂时保留by wangh
     *
     * @param forecastNormalRequest
     * @param result
     * @return
     * @throws Exception
     */
    @ApiOperation("高级预测正常日")
    @RequestMapping(value = "/normal", method = RequestMethod.POST)
    @OperateLog(operate = "高级预测正常日")
    @Deprecated
    public BaseResp<List<ForecastNormalDTO>> forecastNormal(@Validated @RequestBody ForecastNormalRequest forecastNormalRequest,
                                                            BindingResult result)
            throws Exception {
        validateFormValue(result);
        BaseResp resp = BaseResp.succResp();
        if (StringUtils.isBlank(forecastNormalRequest.getCityId())) {
            forecastNormalRequest.setCityId(this.getLoginCityId());
        }
        if (StringUtils.isBlank(forecastNormalRequest.getCaliberId())) {
            forecastNormalRequest.setCaliberId(this.getCaliberId());
        }
        if (forecastNormalRequest.getTargetDay() == null) {
            forecastNormalRequest.setTargetDay(this.getSystemDate());
        }
        if (null != forecastNormalRequest.getFirst() && forecastNormalRequest.getFirst()) {
            List<ForecastNormalDTO> forecastNormalDTOS =
                    forecastService.getForecastNormal(forecastNormalRequest.getCityId(), this.getCaliberId(),
                            forecastNormalRequest.getTargetDay(), forecastNormalRequest.getTargetDay());
            resp.setData(forecastNormalDTOS);

        } else {
            SkipHolidayDTO skipHolidayDTO = holidayService.findSkipHolidays(forecastNormalRequest.getTargetDay(),
                    DateUtil.getMoveDay(forecastNormalRequest.getTargetDay(),
                            forecastNormalRequest.getForecastDay() - 1));
            if (null != skipHolidayDTO.getHolidays() && skipHolidayDTO.getHolidays().size() > 0) {
                StringBuilder msg = new StringBuilder("预测日中包含节假日(");
                for (Date holiday : skipHolidayDTO.getHolidays()) {
                    msg.append(DateUtils.date2String(holiday, DateFormatType.SIMPLE_DATE_FORMAT_STR) + "、");
                }
                msg.replace(msg.length() - 2, msg.length() - 1, ")");
                msg.append(",已为您自动跳过并完成预测！");
                resp.setRetMsg(msg.toString());
            } else {
                resp.setRetMsg("预测成功！");
            }
            List<ForecastNormalDTO> forecastNormalDTOS = null;
            forecastNormalDTOS =
                    forecastService.doAdvancedForecastNormal(forecastNormalRequest.getCityId(),
                            forecastNormalRequest.getCaliberId(), skipHolidayDTO.getNormalDays(),
                            super.getNormalAlgorithmEnums());
            resp.setData(forecastNormalDTOS);
        }
        return resp;
    }


    @ApiOperation("高级预测节假日")
    @RequestMapping(value = "/holiday", method = RequestMethod.POST)
    @OperateLog(operate = "高级预测节假日")
    @Deprecated
    public BaseResp<List<ForecastNormalDTO>> forecastHoliday(@Validated ForecastHolidayRequest forecastHolidayRequest, BindingResult result)
            throws Exception {
        validateFormValue(result);
        BaseResp resp = BaseResp.succResp("预测成功！");
        if (StringUtils.isBlank(forecastHolidayRequest.getCityId())) {
            forecastHolidayRequest.setCityId(this.getLoginCityId());
        }
        if (StringUtils.isBlank(forecastHolidayRequest.getCaliberId())) {
            forecastHolidayRequest.setCaliberId(this.getCaliberId());
        }
        HolidayDO holidayVO = holidayService.findHolidayVOByPk(forecastHolidayRequest.getHolidayId());
        Date startDate = holidayVO.getStartDate();
        Date endDate = holidayVO.getEndDate();
        List<ForecastNormalDTO> forecastNormalDTOS = forecastService.doForecastHoliday(getUid(),
                forecastHolidayRequest.getCityId(), forecastHolidayRequest.getCaliberId(), startDate, endDate);
        if (forecastNormalDTOS == null || forecastNormalDTOS.size() < 1) {
            throw newBusinessException("T706");
        }
        resp.setData(forecastNormalDTOS);
        return resp;
    }


    @ApiOperation("获取相似日")
    @RequestMapping(value = "/get/similarDays", method = RequestMethod.GET)
    public BaseResp<SimilardayResp> getSimilarDays(@ApiParam() String cityId, String caliberId, Date date) throws Exception {
        if (StringUtils.isBlank(cityId)) {
            cityId = this.getLoginCityId();
        }
        if (StringUtils.isBlank(caliberId)) {
            caliberId = this.getCaliberId();
        }
        if (date == null) {//系统日期后一天
            date = DateUtils.addDays(this.getSystemDate(), 1);
        }
        Date start = DateUtils.addDays(date, -70);
        Date end = DateUtils.addDays(date, -1);
        List<SimilarDateBean> similarDateBeans = null;
        try {
//            similarDateBeans = similarDayService.listSimilarDayWithFcWeather(cityId, caliberId,
//                    date, start, end, 5, super.getLoginUserId());
        } catch (Exception e) {
            return new BaseResp("T706");
        }
        if (CollectionUtils.isEmpty(similarDateBeans)) {
            return new BaseResp("T706");
        }
        List<SimilarDayDO> similarDayVOS = new ArrayList<>();
        for (SimilarDateBean similarDateBean : similarDateBeans) {
            SimilarDayDO similarDayVO = new SimilarDayDO();
            similarDayVO.setSimilarDay(new java.sql.Date(similarDateBean.getDate().getTime()));
            similarDayVO.setDegree(similarDateBean.getDegree());
            similarDayVOS.add(similarDayVO);
        }
        SimilardayResp similardayResp = new SimilardayResp();
        similardayResp.setTargetDate(date);
        similardayResp.setSimilarDayVOList(similarDayVOS);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(similardayResp);
        return baseResp;
    }


    @ApiOperation("获取上报状态")
    @RequestMapping(value = "/get/status", method = RequestMethod.GET)
    public BaseResp<SimilardayResp> getReportStatu(String cityId, String caliberId, Date date) throws Exception {
        SimilardayResp similardayResp = new SimilardayResp();
        LoadCityFcDO loadCityFcVO = loadCityFcService.getReport(cityId, caliberId, date);
        if (loadCityFcVO != null) {
            Date reportTime = loadCityFcVO.getReportTime();
            if (reportTime == null) {
                if (loadCityFcVO.getUpdatetime() != null) {
                    reportTime = loadCityFcVO.getUpdatetime();
                } else {
                    reportTime = loadCityFcVO.getCreatetime();
                }
            }
            if (reportTime != null) {
                boolean report = settingSystemService.findIsLater(reportTime, loadCityFcVO);
                if (report) {
                    similardayResp.setReportStatus(3);
                } else {
                    similardayResp.setReportStatus(1);
                }
                similardayResp.setTodayReportTime(DateUtils.date2String(reportTime, DateFormatType.DATE_FORMAT_STR));
            }
        } else {
            similardayResp.setReportStatus(2);
        }


        Integer cityType = cityService.findCityById(cityId).getType();

        //子网的上报时间要查询系统设置
        SystemData systemSetting = settingSystemService.getSystemSetting();
        if (CityConstants.PROVINCE_TYPE.equals(cityType)) {
            String endReportTime = systemSetting.getProvinceEndReportTime();
            similardayResp.setReportTime(endReportTime);
        } else {
            //上报截止时间
            similardayResp.setReportTime(systemSetting.getEndReportTime());
        }


        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(similardayResp);
        return baseResp;
    }

    @ApiOperation("导出")
    @RequestMapping("/export")
    //@OperateLog(operate = "导出预测曲线")
    public BaseResp importForecast(ForecastRequest forecastRequest, HttpServletResponse response,
                                   HttpServletRequest request) throws Exception {

        //查询预测曲线
        ForecastDTO forecastDTO = loadCityFcService
                .findForecastDTO(forecastRequest.getTargetDay(), forecastRequest.getCityId(),
                        forecastRequest.getAlgorithmId(),
                        forecastRequest.getCaliberId());

        if (forecastDTO == null || forecastDTO.getList() == null) {
            throw newBusinessException("T706");
        }

        // 创建工作簿对象
        XSSFWorkbook xssfWorkbook = new XSSFWorkbook();
        XSSFSheet sheet = xssfWorkbook.createSheet();

        XSSFRow firstRow = sheet.createRow(0);
        XSSFRow secondRow = sheet.createRow(1);
        XSSFRow thirdRow = sheet.createRow(2);

        XSSFCell secondRowFirstCell = secondRow.createCell(0);
        secondRowFirstCell.setCellValue(DateUtils.date2String(forecastRequest.getTargetDay(),
                DateFormatType.SIMPLE_DATE_FORMAT_STR));

        XSSFCell firstRowFirstCell = firstRow.createCell(0);
        firstRowFirstCell.setCellValue("预测负荷上报信息");

        XSSFCell thirdRowFirstCell = thirdRow.createCell(0);
        XSSFCell thirdRowSecondCell = thirdRow.createCell(1);
        thirdRowFirstCell.setCellValue("时间");
        thirdRowSecondCell.setCellValue("预测负荷");

        CellRangeAddress cellRangeAddress = new CellRangeAddress(0, 0, 0, 1);
        CellRangeAddress cellRangeAddress2 = new CellRangeAddress(1, 1, 0, 1);
        sheet.addMergedRegion(cellRangeAddress);
        sheet.addMergedRegion(cellRangeAddress2);

        for (int i = 3; i < forecastDTO.getList().size() + 3; i++) {
            int a = i - 3;
            String s = (a / 4 < 10 ? ("0" + a / 4) : (a / 4 + "")) + (":") + (a % 4 == 0 ? "00" : a % 4 * 15);
            XSSFRow row = sheet.createRow(i);
            XSSFCell firstCell = row.createCell(0);
            XSSFCell secondCell = row.createCell(1);
            firstCell.setCellValue(s);
            secondCell.setCellValue(forecastDTO.getList().get(a).doubleValue());
        }
        if (xssfWorkbook != null) {
            try {
                response.setContentType("text/html;charset=UTF-8");
                request.setCharacterEncoding("UTF-8");
                String dateStr = DateUtils.date2String(forecastRequest.getTargetDay(),
                        DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
                AlgorithmDO algorithmVOById = algorithmService.getAlgorithmDOById(forecastRequest.getAlgorithmId());
                String fileName = dateStr + "负荷预测数据-" + algorithmVOById.getAlgorithmCn() + ".xlsx";

                String header = request.getHeader("User-Agent").toUpperCase();
                if (request.getHeader("User-Agent").toLowerCase().indexOf("firefox") > 0) {
                    fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1"); // firefox浏览器
                } else if (header.contains("MSIE") || header.contains("TRIDENT") || header.contains("EDGE")) {
                    fileName = URLEncoder.encode(fileName, "UTF-8");// IE浏览器
                } else if (request.getHeader("User-Agent").toUpperCase().indexOf("CHROME") > 0) {
                    fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");// 谷歌
                }
                OutputStream out = response.getOutputStream();
                response.setHeader("Content-disposition",
                        "attachment; filename=" + fileName);
                xssfWorkbook.write(out);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setRetMsg("导出成功");
        return baseResp;
    }

    @ApiOperation("导出日期范围内预测曲线和上报曲线")
    @RequestMapping("/exportForecastAndReport")
    @OperateLog(operate = "导出日期范围内预测曲线和上报曲线")
    public BaseResp exportForecastAndReport(ForecastRequest forecastRequest, HttpServletResponse response,
                                            HttpServletRequest request) throws Exception {

        String cityId = forecastRequest.getCityId();
        String caliberId = forecastRequest.getCaliberId();
        Date startDate = forecastRequest.getStartDate();
        Date endDate = forecastRequest.getEndDate();
        String algorithmId = forecastRequest.getAlgorithmId();

        CityDO cityDO = cityService.findCityById(cityId);
        String algorithmCn = algorithmService.getAlgorithmCn(algorithmId);

        //查询预测曲线和上报曲线
        List<LoadCityFcDO> loadCityFcDOS = loadCityFcService.listLoadCityFc(cityId, caliberId, startDate, endDate, algorithmId);
        List<LoadCityFcDO> reportLoadCityFc = loadCityFcService.listReportLoadCityFc(cityId, caliberId, startDate, endDate, AlgorithmConstants.MD_ALGORITHM_ID);
        List<LoadCityFcDO> result = new ArrayList<>();
        result.addAll(loadCityFcDOS);
        result.addAll(reportLoadCityFc);
        if (CollectionUtils.isEmpty(result)) {
            throw newBusinessException("T706");
        }
        result.sort(Comparator.comparing(LoadCityFcDO::getDate));

        // 创建工作簿对象
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("预测数据");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        // 创建类型、日期和时间列标题
        XSSFRow headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("单位");
        headerRow.createCell(1).setCellValue("日期");
        headerRow.createCell(2).setCellValue("预测方法");
        headerRow.createCell(3).setCellValue("午间最大负荷");
        headerRow.createCell(4).setCellValue("晚间最大负荷");
        headerRow.createCell(5).setCellValue("日最大负荷");

        // 初始化小时和分钟为00:15
        int hour = 0;
        int minute = 15;
        for (int i = 0; i < 96; i++) {
            String time = String.format("%02d:%02d", hour, minute);
            headerRow.createCell(i + 6).setCellValue(time);
            minute += 15;
            if (minute >= 60) {
                hour++;
                minute -= 60;
            }
        }

        // 填充数据行
        int currentRow = 1;
        for (LoadCityFcDO fcDO : result) {
            XSSFRow dataRow = sheet.createRow(currentRow++);
            dataRow.createCell(0).setCellValue(cityDO.getCity());
            dataRow.createCell(1).setCellValue(dateFormat.format(fcDO.getDate()));
            dataRow.createCell(2).setCellValue(fcDO.getAlgorithmId().equals(algorithmId) ? algorithmCn : "人工修正上报");
            LoadFeatureFcDTO forecastStats = loadFeatureStatService.findStatisticsDayFeature(fcDO);
            dataRow.createCell(3).setCellValue(forecastStats.getNoonMaxLoad().doubleValue());
            dataRow.createCell(4).setCellValue(forecastStats.getEveningMaxLoad().doubleValue());
            dataRow.createCell(5).setCellValue(forecastStats.getMaxLoad().doubleValue());
            for (int i = 0; i < 96; i++) {
                double forecastValue = fcDO.getloadList().get(i).doubleValue();
                dataRow.createCell(i + 6).setCellValue(forecastValue);
            }
        }
        // 设置响应头和内容类型
        String fileName = dateFormat.format(startDate) + "日-" + dateFormat.format(endDate) + "日预测及上报负荷数据.xlsx";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));

        // 写入Excel文件
        workbook.write(response.getOutputStream());
        workbook.close();
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setRetMsg("导出成功");
        return baseResp;
    }

    @ApiOperation("十日bp预测")
    @GetMapping("/fc/check")
    public BaseResp<List<ForecastBpCheckDTO>> getBpCheckData(Date date, String cityId, String caliberId) throws Exception {
        BaseResp resp = BaseResp.succResp();
        if (caliberId == null) {
            caliberId = this.getCaliberId();
        }
        List<ForecastBpCheckDTO> forecastBpCheckData = forecastService.getForecastBpCheckData(cityId, caliberId, date);
        if (forecastBpCheckData != null) {
            resp.setData(forecastBpCheckData);
            return resp;
        } else {
            return new BaseResp("T706");
        }
    }

    @ApiOperation("获取清能/清软预测特性")
    @RequestMapping(value = "/fc/feature", method = RequestMethod.GET)
    public BaseResp<ForecastDTO> getForecast(@ApiParam(value = "城市id") String cityId, @ApiParam(value = "口径id") String caliberId,
                                             @ApiParam(value = "预测日期") Date date, String algorithmId) throws Exception {
        BaseResp resp = BaseResp.succResp();
        if (caliberId == null) {
            caliberId = this.getCaliberId();
        }
        List<ForecastBpCheckDTO> forecastBpCheckData = forecastService.getForecastBpCheckFeatureData(cityId, caliberId,
                date, algorithmId);
        if (forecastBpCheckData != null) {
            resp.setData(forecastBpCheckData);
            return resp;
        } else {
            return new BaseResp("T706");
        }
    }

    @ApiOperation("实际")
    @GetMapping("/his/load")
    public BaseResp<List<BigDecimal>> getBpCheckHisData(Date date, String cityId, String caliberId) throws Exception {
        BaseResp resp = BaseResp.succResp();
        if (caliberId == null) {
            caliberId = this.getCaliberId();
        }
        List<BigDecimal> forecastBpCheckData = forecastService.getBpCheckHisData(cityId, caliberId, date);
        if (forecastBpCheckData != null) {
            resp.setData(forecastBpCheckData);
            return resp;
        } else {
            return new BaseResp("T706");
        }
    }

    @ApiOperation("保存")
    @PostMapping("/save/load")
    public BaseResp<Void> saveBpCheckHisData(@RequestBody ForecastDataRequest forecastDataRequest) throws Exception {
        BaseResp resp = BaseResp.succResp();
        if (forecastDataRequest.getCaliberId() == null) {
            forecastDataRequest.setCaliberId(this.getCaliberId());
        }
        for (Date date : forecastDataRequest.getDates()) {
            forecastService.saveBpCheckHisData(forecastDataRequest.getCityId(),
                    forecastDataRequest.getCaliberId(), date, forecastDataRequest.getData(),
                    forecastDataRequest.getTargetDate(), this.getLoginUserId());
        }
        resp.setRetMsg("保存成功");
        return resp;
    }

    @ApiOperation("保存")
    @GetMapping("/save/his/load")
    public BaseResp<Void> saveBpCheckHisData(Date date, String cityId, String caliberId) throws Exception {
        BaseResp resp = BaseResp.succResp();
        if (caliberId == null) {
            caliberId = this.getCaliberId();
        }
        forecastService.saveDayBpCheckHisData(date, cityId, caliberId, this.getLoginUserId());
        resp.setRetMsg("保存成功");
        return resp;
    }

    @ApiOperation("上报国调")
    @GetMapping("/push/load")
    public BaseResp<Void> pushBpCheckHisData(Date date) throws Exception {
        BaseResp resp = BaseResp.succResp();
        loadCityFcService.reportDayForecastResultFileToGuoDiao(date);
        resp.setRetMsg("上报成功");
        return resp;
    }


    @ApiOperation("五区预测负荷")
    @GetMapping("/getAreaFcLoad")
    public BaseResp<List<CityValueDTO>> getAreaFcLoad(String caliberId,Date date,String algorithmId) throws Exception {
        BaseResp resp = BaseResp.succResp();
        if (StringUtils.isEmpty(caliberId)){
            caliberId = this.getCaliberId();
        }
        List<CityValueDTO> result = new ArrayList<>();
        List<BaseAreaDO> allAreas = baseAreaService.getAllAreas();
        List<LoadCityFcDO> loadCityFcDOS = loadCityFcService.findFcByAlgorithmId(null, caliberId, algorithmId, date, date);
        Map<String, LoadCityFcDO> loadMap = loadCityFcDOS.stream().collect(Collectors.toMap(LoadCityFcDO::getCityId, Function.identity(), (o, n) -> n));
        for (BaseAreaDO allArea : allAreas) {
            CityValueDTO cityValueDTO = new CityValueDTO();
            cityValueDTO.setCityId(allArea.getId());
            cityValueDTO.setCity(allArea.getName());
            result.add(cityValueDTO);
            LoadCityFcDO loadCityFcDO = loadMap.get(allArea.getId());
            if (Objects.nonNull(loadCityFcDO)){
                cityValueDTO.setValue(loadCityFcDO.getloadList());
            }
        }
        resp.setData(result);
        return resp;
    }
}

