package com.tsintergy.lf.web.base.common.util;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureExtendDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherDTO;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

/**
 * excel工具类
 *
 * <AUTHOR>
 * @date 2016年12月23日 下午2:06:42
 * @Tudo poi分析excel
 */
public class ExcelUtil {

    private ExcelUtil(){

    }

    /**
     * 总sheet数
     */
    private static int totalSheet = 0;

    /**
     * 总行数
     */
    private static int totalRows = 0;

    /**
     * 总列数
     */
    private static int totalCells = 0;

    /**
     * 错误信息
     */
    private static String errorInfo;


    public static boolean isExcel2007(String filePath) {
        return filePath.matches("^.+\\.(?i)(xlsx)$");
    }

    public static boolean isExcel2003(String filePath) {
        return filePath.matches("^.+\\.(?i)(xls)$");
    }

    /**
     * @描述：验证excel文件
     * @返回值：boolean
     */

    public static boolean validateExcel(String filePath) {
        /** 检查文件是否存在 */
        File file = new File(filePath);
        if (file == null || !file.exists()) {
            errorInfo = "文件不存在";
            return false;
        }
        /** 检查文件名是否为空或者是否是Excel格式的文件 */
        if (!(ExcelUtil.isExcel2003(filePath) || ExcelUtil.isExcel2007(filePath))) {
            errorInfo = "文件名不是excel格式";
            return false;
        }
        return true;
    }

    /**
     * 根据文件解析Excel,[notice]文件不存在，返回Null,调用者自行处理。
     * @param filePath 文件路径
     * @return
     */
    public static List<List<List<String>>> read(String filePath) {
        List<List<List<String>>> dataList = new ArrayList<List<List<String>>>();
        /** 调用本类提供的根据流读取的方法 */
        File file = new File(filePath);

        try(InputStream is = new FileInputStream(file);) {
            /** 验证文件是否合法 */
            if (!validateExcel(filePath)) {
                return null;
            }
            /** 判断文件的类型，是2003还是2007 */
            boolean isExcel2003 = true;
            if (ExcelUtil.isExcel2007(filePath)) {
                isExcel2003 = false;
            }

            dataList = read(is, isExcel2003);
            is.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return dataList;

    }

    /**
     * @描述：根据流读取Excel文件
     * @返回值：List
     */

    public static List<List<List<String>>> read(InputStream inputStream, boolean isExcel2003) {
        List<List<List<String>>> dataList = null;
        try {
            /** 根据版本选择创建Workbook的方式 */
            Workbook wb = createWorkBook(inputStream,isExcel2003);
            dataList = read(wb);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return dataList;
    }

    /**
     * @描述：读取数据
     * @返回值：List<List<String>> 结构为  List < sheet < cell
     */

    private static List<List<List<String>>> read(Workbook wb) {
        List<List<List<String>>> dataList = Lists.newArrayList();
        /** 得到sheetd的总数 */
        totalSheet = wb.getNumberOfSheets();
        for (int s = 0; s < totalSheet; s++) {
            List<List<String>> sheetList = new ArrayList<List<String>>();
            /** 得到第s个sheet */
            Sheet sheet = wb.getSheetAt(s);
            if (sheet == null) {
                continue;
            }
            /** 得到Excel的行数 */
            totalRows = sheet.getPhysicalNumberOfRows();
            /** 得到Excel的列数 */
            if (totalRows >= 1 && sheet.getRow(0) != null) {
                totalCells = sheet.getRow(0).getPhysicalNumberOfCells();
            }
            /** 循环Excel的行 */
            for (int r = 0; r < totalRows; r++) {
                Row row = sheet.getRow(r);
                if (row == null) {
                    continue;
                }
                List<String> rowList = new ArrayList<String>();
                /** 循环Excel的列 */
                for (int c = 0; c < getTotalCells(); c++) {
                    Cell cell = row.getCell(c);
                    String cellValue = "";
                    if (null != cell) {
                        cellValue = getCellContent(cell)==null ? "" : getCellContent(cell).toString();
                    }
                    rowList.add(cellValue);
                }
                sheetList.add(rowList);
            }
            dataList.add(sheetList);
        }
        return dataList;
    }

    /**
     *
     * @param sheetName sheet 名称
     * @param heads
     * @param data
     * @return
     * @throws Exception
     */
    public static InputStream getExcel(String sheetName,String [] heads,List<WeatherDTO> data) throws Exception{
        ByteArrayOutputStream bos = null;
        ByteArrayInputStream bis = null;
        //create excel
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet(sheetName);
        //first row,tittle
        XSSFRow tittleRow = sheet.createRow(0);
        XSSFCell cell = null;
        for (int i=0;i<heads.length;i++) {
            cell = tittleRow.createCell(i);
            cell.setCellValue(heads[i]);
        }
        //data(这部分暂时无法做到通用）
        int dataRows=data.size();
        for (int i=0;i<dataRows;i++) {
            XSSFRow dataRow = sheet.createRow(i+1);
            dataRow.createCell(0).setCellValue(data.get(i).getCity());
            dataRow.createCell(1).setCellValue(DateUtils.date2String(data.get(i).getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR));
            dataRow.createCell(2).setCellValue(data.get(i).getWeek());
            for (int j=0;j<data.get(i).getData().size();j++) {
                cell = dataRow.createCell(j + 3);
                cell.setCellValue(String.valueOf(data.get(i).getData().get(j)));
            }
        }
        bos = new ByteArrayOutputStream();
        workbook.write(bos);
        byte[] ba = bos.toByteArray();
        bis = new ByteArrayInputStream(ba);
        bos.flush();
        bos.close();
        bis.close();
        return bis;
    }

    public static InputStream getExcel2(String sheetName,String [] heads,List<LoadFeatureExtendDTO> data) throws Exception{
        ByteArrayOutputStream bos = null;
        ByteArrayInputStream bis = null;
        try{
            //create excel
            XSSFWorkbook workbook = new XSSFWorkbook();
            XSSFSheet sheet = workbook.createSheet(sheetName);
            //first row,tittle
            XSSFRow tittleRow = sheet.createRow(0);
            XSSFCell cell = null;
            for (int i=0;i<heads.length;i++) {
                cell = tittleRow.createCell(i);
                cell.setCellValue(heads[i]);
            }
            //data(这部分暂时无法做到通用）
            int dataRows=data.size();
            for (int i=0;i<dataRows;i++) {
                XSSFRow dataRow = sheet.createRow(i+1);
                dataRow.createCell(0).setCellValue(data.get(i).getCity());
                dataRow.createCell(1).setCellValue(data.get(i).getDate());
                dataRow.createCell(2).setCellValue(data.get(i).getWeek());
                dataRow.createCell(3).setCellValue(String.valueOf(data.get(i).getMaxLoad()));
                dataRow.createCell(4).setCellValue(String.valueOf(data.get(i).getMinLoad()));
                dataRow.createCell(5).setCellValue(String.valueOf(data.get(i).getAveLoad()));
                dataRow.createCell(6).setCellValue(String.valueOf(data.get(i).getPeak()));
                dataRow.createCell(7).setCellValue(String.valueOf(data.get(i).getTrough()));
                dataRow.createCell(8).setCellValue(String.valueOf(data.get(i).getDifferent()));
                dataRow.createCell(9).setCellValue(String.valueOf(data.get(i).getGradient()));

            }
            bos = new ByteArrayOutputStream();
            workbook.write(bos);
            byte[] ba = bos.toByteArray();
            bis = new ByteArrayInputStream(ba);
            bos.flush();
            bos.close();
            bis.close();
            return bis;
        }catch(Exception e){
            bos.flush();
            bos.close();
            bis.close();
            throw e;
        }
    }


    /**
     * 解析Excel文件字节流，生成List<Map<String,Object>>对象，其中每一个Map代表一行，key=Integer,列索引从0开始，value=值
     * 已知HSSFWorkbook 支持解析.xlsx
     *
     * @param file 解析文件
     * @param sheetIndex  Excel sheet索引，从1开始
     * @return
     */
    public static List<Map<Integer, Object>> resolve(File file, int sheetIndex,boolean isExcel2003) throws IOException {
        InputStream inputStream = new FileInputStream(file);
        return resolve(inputStream, sheetIndex,isExcel2003);
    }

    /**
     * 解析Excel文件字节流，生成List<Map<String,Object>>对象，其中每一个Map代表一行，key=Integer,列索引从0开始，value=值
     * 已知HSSFWorkbook 支持解析.xlsx
     *
     * @param inputStream
     * @param sheetIndex  Excel sheet索引，从1开始
     * @return
     */
    public static List<Map<Integer, Object>> resolve(InputStream inputStream, int sheetIndex,boolean isExcel2003) throws IOException {
        Workbook workbook = createWorkBook(inputStream,isExcel2003);
        Sheet sheet = workbook.getSheetAt(sheetIndex - 1);
        List<Map<Integer, Object>> rst = Lists.newArrayList();
        // 行迭代
        for (int r = sheet.getFirstRowNum(); r <= sheet.getLastRowNum(); r++) {
            Row row = sheet.getRow(r);
            if (row == null) {
                continue;
            }
            Map<Integer, Object> rowMap = Maps.newHashMap();
            // 列迭代
            for (int c = 0; c <= row.getLastCellNum(); c++) {
                Cell cell = row.getCell(c);
                if (cell == null) {
                    continue;
                }
                rowMap.put(c, getCellContent(cell));
            }
            rst.add(rowMap);
        }
        return rst;
    }

    /**
     * 解析Cell文本内容
     * @param cell cell 单元格
     * @return 文本内容
     */
    private static Object getCellContent(Cell cell) {
        Object buffer = null;
        switch (cell.getCellType()) {
            // 数字
            case Cell.CELL_TYPE_NUMERIC:
                if (HSSFDateUtil.isCellDateFormatted(cell)) {//时间类型
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    buffer = sdf.format(cell.getDateCellValue());
                } else {
                    buffer = cell.getNumericCellValue();//纯数字
                }
                break;
            // 布尔
            case Cell.CELL_TYPE_BOOLEAN:
                buffer = cell.getBooleanCellValue();
                break;
            // 公式
            case Cell.CELL_TYPE_FORMULA:
                buffer = cell.getCellFormula();
                break;
            // 字符串
            case Cell.CELL_TYPE_STRING:
                buffer = cell.getStringCellValue();
                break;
            // 空值
            case Cell.CELL_TYPE_BLANK:
                break;
            // 故障
            case Cell.CELL_TYPE_ERROR:
                break;
            default:
                break;
        }
        return buffer;
    }


    /**
     * 支持Excel2003、2007版本
     * @param inputStream Exce文件流
     * @param isExcel2003 是否2003版本Excel
     * @return Workbook 对象
     * @throws IOException
     */
    private static Workbook createWorkBook(InputStream inputStream,boolean isExcel2003) throws IOException{
        Workbook workbook;
        if (isExcel2003) {
            workbook = new HSSFWorkbook(inputStream);
        } else {
            workbook = new XSSFWorkbook(inputStream);
        }
        return workbook;
    }

    //------getter -------------------------
    /**
     * @描述：得到总行数
     * @返回值：int
     */

    public static int getTotalRows() {
        return totalRows;
    }

    /**
     * @描述：得到总列数
     * @返回值：int
     */

    public static int getTotalCells() {
        return totalCells;
    }

    /**
     * @描述：得到错误信息
     * @返回值：String
     */

    public static String getErrorInfo() {
        return errorInfo;
    }

}
