/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.acload.request;

import com.tsintergy.lf.web.base.weather.request.SensitivityRequest;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.persistence.criteria.CriteriaBuilder.In;
import lombok.Data;

/**
 * Description:<br>
 *
 * @Author:<EMAIL>
 * @Date: 2022/5/24 11:54
 * @Version:1.0.0
 */
@Data
public class AcSensitivityRequest extends SensitivityRequest {

    /**
     * 是否正常日
     */
    @ApiModelProperty(value = "正常日")
    private boolean isNormalDay;

    /**
     * 时候休息日
     */
    @ApiModelProperty(value = "休息日")
    private boolean isRestDay;

    /**
     * 最小日降水量
     */
    @ApiModelProperty(value = "最小日降水量")
    private BigDecimal minRain;

    /**
     * 最大日降水量
     */
    @ApiModelProperty(value = "最大日降水量")
    private BigDecimal maxRain;
}