package com.tsintergy.lf.web.base.forecast.response;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import com.tsintergy.lf.serviceapi.base.forecast.dto.ForecastDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherNameDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @date: 2/27/18 5:23 PM
 * @author: angel
 **/
@ApiModel
@Data
public class ForecastResp {

    @ApiModelProperty(value = "基准日")
    private List<BigDecimal> base;


    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "当日实际曲线")
    private List<BigDecimal> hisLoad;
    /**
     * 最终上报的曲线
     */
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最终上报的曲线")
    private List<BigDecimal> reportLoad;

    /**
     * 修正的曲线
     */
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "修正的曲线")
    private List<BigDecimal> modifyLoad;

    /**
     * 预测数据
     */
    @ApiModelProperty(value = "预测数据")
    ForecastDTO forecast;

    /**
     * 置信上限
     */
    @ApiModelProperty(value = "置信上限")
    List<BigDecimal> max;

    /**
     * 置信下限
     */
    @ApiModelProperty(value = "置信下限")
    List<BigDecimal> min;

    /**
     * 相似日
     */
    @ApiModelProperty(value = "相似日")
    List<BigDecimal> reference;


    /**
     * 偏差
     */
    @ApiModelProperty(value = "偏差")
    List<BigDecimal> deviation;

    /**
     * 气象数据
     */
    @ApiModelProperty(value = "气象数据")
    private List<WeatherNameDTO> weatherNameDTOS;


    @ApiModelProperty(value = "地市预测累加")
    List<BigDecimal> cityFcAdd;

}
