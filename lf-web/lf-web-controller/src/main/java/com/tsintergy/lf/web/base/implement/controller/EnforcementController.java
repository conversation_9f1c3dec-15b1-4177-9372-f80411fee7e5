/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  ThinkPad Date:  2019/3/12 13:10 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.implement.controller;



import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.ParamConstants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.api.CustomizationForecastService;
import com.tsintergy.lf.serviceapi.algorithm.api.ShortAlgorithmForecastService;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.algorithm.dto.LongCompositeParam;
import com.tsintergy.lf.serviceapi.algorithm.dto.LongCompositeResult;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.FoundationLoadHisMonthService;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.LoadAcHisBasicService;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.LoadFeatureAcHisService;
import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.FoundationLoadHisMonthDO;
import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.LoadAcHisBasicDO;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CaliberDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.common.enumeration.LongMonthCategoryEnum;
import com.tsintergy.lf.serviceapi.base.evalucation.api.AccuracyAssessService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.AccuracyCompositeService;
import com.tsintergy.lf.serviceapi.base.forecast.api.*;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.industry.api.IndustryCityLoadDayHisClctNewService;
import com.tsintergy.lf.serviceapi.base.industry.api.IndustryCityLoadDayHisClctService;
import com.tsintergy.lf.serviceapi.base.industry.pojo.IndustryCityLoadDayHisClctDO;
import com.tsintergy.lf.serviceapi.base.industry.pojo.IndustryCityLoadDayHisClctNewDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityFcLongMonthYearService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import com.tsintergy.lf.web.base.forecast.thread.AutoForecastRecallTask;
import com.tsintergy.lf.web.base.forecast.thread.AutoForecastTask;
import com.tsintergy.lf.web.base.load.request.AirConditionLoadFeatureRequest;
import com.tsintergy.lf.web.base.load.request.ForecsatRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 实施页面 部分接口
 *
 * <AUTHOR>
 * @create 20120/8/20
 * @since 1.0.0
 */
@RequestMapping(value = "/test")
@RestController
@Api(tags = "实施页面")
@Slf4j
public class EnforcementController extends CommonBaseController {

    @Autowired
    Environment environment;
    @Resource
    private CustomizationForecastService<LongCompositeParam, LongCompositeResult> longSensitivityForecastService;
    @Autowired
    private ForecastService forecastService;
    @Autowired
    private AutoForecastService autoForecastService;
    @Autowired
    private CaliberService caliberService;
    @Autowired
    private CityService cityService;
    @Autowired
    private ScheduledThreadPoolExecutor scheduledThreadPoolExecutor;

    @Autowired
    private AlgorithmService algorithmService;

    @Autowired
    private LoadCityFcLongMonthYearService loadCityFcLongMonthYearService;
    @Autowired
    private LoadFeatureAcHisService loadFeatureAcHisService;
    @Autowired
    private FoundationLoadHisMonthService foundationLoadHisMonthService;
    @Autowired
    private LoadAcHisBasicService loadAcHisBasicService;
    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private ShortAlgorithmForecastService shortAlgorithmForecastService;

    @Autowired
    private ForecastResultStatService forecastResultStatService;

    @Autowired
    private AccuracyAssessService accuracyAssessService;

    @Autowired
    private AccuracyCompositeService accuracyCompositeService;

    @Autowired
    private IndustryCityLoadDayHisClctService industryCityLoadDayHisClctService;

    @Autowired
    private IndustryCityLoadDayHisClctNewService industryCityLoadDayHisClctNewService;

    @Autowired
    private LoadCityFcService loadCityFcService;

    @Autowired
    private LoadCityFcBatchService loadCityFcBatchService;

    /**
     * 实施调用(数据修复算法）
     */
    @ApiOperation("数据修复算法")
    @RequestMapping(value = "/allDataRepair", method = RequestMethod.GET)
    public void doAllDataRepair() throws Exception {
        Date startDate = DateUtils.string2Date("2019-03-29", DateFormatType.SIMPLE_DATE_FORMAT_STR);
        Date endDate = DateUtils.string2Date("2019-04-01", DateFormatType.SIMPLE_DATE_FORMAT_STR);
        List<CityDO> cityVOS = cityService.findAllCitys();
        List<CaliberDO> caliberVOS = caliberService.findAllCalibers();
        for (CityDO cityVO : cityVOS) {
            for (CaliberDO caliberVO : caliberVOS) {
                try {
                    forecastService.doDataRepairAlgorithm(cityVO.getId(), caliberVO.getId(), startDate, endDate);
                } catch (Exception ignored) {
                }

            }
        }
    }

    /**
     * 实施的页面所调用的接口
     */
    @ApiOperation("实施的页面所调用的接口")
    @RequestMapping(value = "/forecast/algorithm", method = RequestMethod.POST)
    public BaseResp doForecast(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<String> cityIds = forecsatRequest.getCityIds();
        List<String> caliberIds = forecsatRequest.getCaliberIds();
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        if (CollectionUtils.isEmpty(cityIds) || CollectionUtils.isEmpty(caliberIds)
                || forecsatRequest.getWeatherType() == null || forecsatRequest.getType() == null) {
            baseResp.setRetCode("T100");
            baseResp.setRetMsg("预测算法失败！ 请检查参数");
        }
        if (cityIds.size() == 1 && cityIds.get(0).equals(ParamConstants.ALL)) {
            List<CityDO> cityVOS = cityService.findAllCitys();
            cityIds = cityVOS.stream().map(CityDO::getId).collect(Collectors.toList());
        }
        Integer forecastType = Integer.parseInt(environment.getProperty("implement.forecast.type"));
        List<AlgorithmEnum> enums = new ArrayList<>();
        if (CollectionUtils.isEmpty(forecsatRequest.getAlgorithmIds()) || forecsatRequest.getAlgorithmIds().contains(
                ParamConstants.ALL)) {

            List<AlgorithmDO> allAlgorithmsNotCache = algorithmService.getAllAlgorithmsNotCache();
            List<AlgorithmDO> pageAlgorithms = allAlgorithmsNotCache.stream().filter(
                            t -> AlgorithmConstants.NORMAL_ALGORITHM_TYPE.equals(t.getType()) && !AlgorithmEnum.COMPREHENSIVE_MODEL
                                    .getType().equals(t.getCode()))
                    .collect(Collectors.toList());
            pageAlgorithms.forEach(e -> {
                AlgorithmEnum byCode = AlgorithmEnum.findByCode(e.getCode());
                enums.add(byCode);
            });
        } else {
            forecsatRequest.getAlgorithmIds().forEach(e -> {
                enums.add(AlgorithmEnum.findById(e));
            });
        }
        String uid = getUid();
        List<Date> dates = DateUtil.getListBetweenDay(startDate, endDate);
        //lgb及xg算法不支持多线程调用
        if (enums.contains(AlgorithmEnum.REPLENISH_LGB) || enums.contains(AlgorithmEnum.FORECAST_XGBOOST)) {
            ThreadPoolExecutor executor = new ThreadPoolExecutor(1, 1,
                    0L, TimeUnit.MILLISECONDS,
                    new LinkedBlockingQueue<>());
            for (Date date : dates) {
                for (String cityId : cityIds) {
                    for (String caliberId : caliberIds) {
                        executor.execute(new AutoForecastTask(forecastType, uid, cityId,
                                caliberId,
                                autoForecastService, date, date, enums, forecsatRequest.getWeatherType()
                                , forecsatRequest.getType(), forecsatRequest.getPointNum()));
                    }
                }
            }
            return baseResp;
        }
        for (Date date : dates) {
            for (String cityId : cityIds) {
                for (String caliberId : caliberIds) {
                    scheduledThreadPoolExecutor.schedule(new AutoForecastTask(forecastType, uid, cityId, caliberId,
                            autoForecastService,
                            date, date, enums, forecsatRequest.getWeatherType()
                            , forecsatRequest.getType(), forecsatRequest.getPointNum()), 0, TimeUnit.MILLISECONDS);
                }
            }
        }
        return baseResp;
    }

    @ApiOperation("实施的页面所调用的接口")
    @RequestMapping(value = "/forecastRecall/algorithm", method = RequestMethod.POST)
    public BaseResp doForecastRecall(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<String> cityIds = forecsatRequest.getCityIds();
        List<String> caliberIds = forecsatRequest.getCaliberIds();
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        Integer pointNum = forecsatRequest.getPointNum();

        // 统计要预测的城市
        if (CollectionUtils.isEmpty(cityIds) || CollectionUtils.isEmpty(caliberIds)) {
            baseResp.setRetCode("T100");
            baseResp.setRetMsg("预测算法失败！ 请检查参数");
        }
        if (cityIds.size() == 1 && cityIds.get(0).equals(ParamConstants.ALL)) {
            List<CityDO> cityVOS = cityService.findAllCitys();
            cityIds = cityVOS.stream().map(CityDO::getId).collect(Collectors.toList());
        }

        // 获取预测的类型
        Integer forecastType = Integer.parseInt(environment.getProperty("implement.forecast.type"));

        // 统计要跑的算法
        List<AlgorithmEnum> enums = new ArrayList<>();
        if (CollectionUtils.isEmpty(forecsatRequest.getAlgorithmIds()) || forecsatRequest.getAlgorithmIds().contains(
                ParamConstants.ALL)) {
            List<AlgorithmDO> allAlgorithmsNotCache = algorithmService.getAllAlgorithmsNotCache();
            List<AlgorithmDO> pageAlgorithms = allAlgorithmsNotCache.stream().filter(
                            t -> AlgorithmConstants.NORMAL_ALGORITHM_TYPE.equals(t.getType()) && !AlgorithmEnum.COMPREHENSIVE_MODEL.getType().equals(t.getCode()))
                    .collect(Collectors.toList());
            pageAlgorithms.forEach(e -> {
                AlgorithmEnum byCode = AlgorithmEnum.findByCode(e.getCode());
                enums.add(byCode);
            });
        } else {
            forecsatRequest.getAlgorithmIds().forEach(e -> {
                enums.add(AlgorithmEnum.findById(e));
            });
        }

        String uid = getUid();
        List<Date> dates = DateUtil.getListBetweenDay(startDate, endDate);

        this.runNewAlgorithmForecastRecall(enums, dates, pointNum, uid);

        //lgb及xg算法不支持多线程调用
        if (enums.contains(AlgorithmEnum.REPLENISH_LGB) || enums.contains(AlgorithmEnum.FORECAST_XGBOOST)) {
            for (Date date : dates) {
                for (String cityId : cityIds) {
                    for (String caliberId : caliberIds) {
                        autoForecastService.autoForecastRecall(forecastType, uid, cityId, caliberId, date, date, enums,
                                0, 2, pointNum);
                    }
                }
            }
        } else {
            if (enums.size() != 0) {
                for (Date date : dates) {
                    for (String cityId : cityIds) {
                        for (String caliberId : caliberIds) {
                            scheduledThreadPoolExecutor.schedule(new AutoForecastRecallTask(forecastType, uid, cityId, caliberId,
                                            autoForecastService, date, date, enums, 0, 2, pointNum),
                                    0, TimeUnit.MILLISECONDS);
                        }
                    }
                }
            }
        }
        return baseResp;
    }

    @SneakyThrows
    private void runNewAlgorithmForecastRecall(List<AlgorithmEnum> enums, List<Date> dates, Integer pointNum, String uid) {
        // Gru多因素算法
        if (enums.contains(AlgorithmEnum.GRU_FACTORS)) {
            for (Date date : dates) {
                autoForecastService.doGruForecastRecall(date, date, true, pointNum, uid);
            }
            // 移除算法枚举
            enums.remove(AlgorithmEnum.GRU_FACTORS);
        }

        // Trans算法回溯
        if (enums.contains(AlgorithmEnum.TRANS)) {
            for (Date date : dates) {
                autoForecastService.transForecastRecall(date, date, pointNum, uid);
            }
            // 移除算法枚举
            enums.remove(AlgorithmEnum.TRANS);
        }
    }

    /**
     * 实施的页面 每日预测算法列表
     */
    @ApiOperation("每日预测算法列表")
    @RequestMapping(value = "/forecast/algorithmList", method = RequestMethod.GET)
    public BaseResp<List<AlgorithmDO>> algorithmList() throws Exception {


        List<AlgorithmDO> allAlgorithmsNotCache = algorithmService.getAllAlgorithmsNotCache();
        List<AlgorithmDO> pageAlgorithms = allAlgorithmsNotCache.stream().filter(
                        t -> AlgorithmConstants.NORMAL_ALGORITHM_TYPE.equals(t.getType()))
                .collect(Collectors.toList());

        List<AlgorithmDO> algorithmVOS = new ArrayList<>();
        pageAlgorithms.forEach(e -> {
            if (!e.getCode().equals(AlgorithmEnum.COMPREHENSIVE_MODEL.getType())) {
                AlgorithmDO algorithmVO = new AlgorithmDO();
                algorithmVO.setId(e.getId());
                algorithmVO.setAlgorithmCn(e.getAlgorithmCn());
                algorithmVOS.add(algorithmVO);
            }
        });
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(algorithmVOS);
        return baseResp;
    }

    /**
     * 实施页面 中长期月度&年度算法触发；当前暂用于测试中长期算法
     */
    @ApiOperation("中长期算法vec月&年")
    @RequestMapping(value = "/forecast/long/vec", method = RequestMethod.GET)
    public BaseResp<List<AlgorithmDO>> algorithmLongForecastVec(Date start, Date end, String caliberId)
            throws Exception {
        if (caliberId == null) {
            caliberId = "5";
        }
        try {
            List<AlgorithmEnum> enums = new ArrayList<>();
            enums.add(AlgorithmEnum.LONG_VEC_MAX);
            enums.add(AlgorithmEnum.LONG_VEC_MIN);
            enums.add(AlgorithmEnum.LONG_VEC_ENERGY);
            for (AlgorithmEnum one : enums) {
                autoForecastService
                        .autoLongMonthForecast(null, "1", caliberId, start, end,
                                Arrays.asList(one),
                                LongMonthCategoryEnum.MONTH);
                autoForecastService
                        .autoLongMonthForecast(null, "1", caliberId, start, end,
                                Arrays.asList(one),
                                LongMonthCategoryEnum.YEAR);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }

    /**
     * 实施页面 中长期月度&年度算法触发；当前暂用于测试中长期算法
     */
    @ApiOperation("中长期算法灵敏度月&年")
    @RequestMapping(value = "/forecast/long/sensitivity", method = RequestMethod.GET)
    public BaseResp<List<AlgorithmDO>> algorithmLongForecastSensitivity(Date start, Date end, String caliberId)
            throws Exception {
        if (caliberId == null) {
            caliberId = "5";
        }
        LongCompositeParam param1 = new LongCompositeParam("1", caliberId, AlgorithmEnum.LONE_COMPOSITE, start,
                end,
                new ArrayList<>(), LongMonthCategoryEnum.MONTH);

        longSensitivityForecastService.forecast(param1);
        LongCompositeParam param2 = new LongCompositeParam("1", caliberId, AlgorithmEnum.LONE_COMPOSITE, start,
                end,
                new ArrayList<>(), LongMonthCategoryEnum.YEAR);

        longSensitivityForecastService.forecast(param2);

        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }


    /**
     * 实施的页面 触发中长期月度预测准确率；暂用作测试
     */
    @ApiOperation("每日预测算法列表")
    @RequestMapping(value = "/forecast/statLongAccuracy", method = RequestMethod.GET)
    public BaseResp<List<AlgorithmDO>> statLongAccuracy(Date start, Date end) throws Exception {
        loadCityFcLongMonthYearService
                .doStatisticsMonthLong(null, DateUtils.date2String(start, DateFormatType.YEAR_MONTH_STR),
                        DateUtils.date2String(end, DateFormatType.YEAR_MONTH_STR), null);
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }

    /**
     * 实施的页面 空调负荷特性分析
     */
    @ApiOperation("空调负荷特性分析")
    @RequestMapping(value = "/stat/airConditionLoad", method = RequestMethod.POST)
    public BaseResp statAirConditionLoadFeature(@RequestBody AirConditionLoadFeatureRequest request) throws Exception {
        List<String> cityIds = request.getCityIds();
        List<String> caliberIds = request.getCaliberIds();
        Date startDate = request.getStartDate();
        Date endDate = request.getEndDate();
        if (CollectionUtils.isEmpty(cityIds)) {
            cityIds = cityService.findAllCitys().stream().map(CityDO::getId).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(caliberIds)) {
            caliberIds = caliberService.findAllCalibers().stream().map(CaliberDO::getId).collect(Collectors.toList());
        }

        //计算基础负荷
        this.calcFoundLoadHisMonth(startDate, endDate);

        //计算空调负荷
        this.calcAcLoadHis(cityIds, caliberIds, startDate, endDate);

        //分析空调负荷特性
        loadFeatureAcHisService.doHisLoadFeatureCityDay(startDate, endDate);
        return BaseResp.succResp("空调负荷特性分析成功");
    }

    @SneakyThrows
    private void calcFoundLoadHisMonth(Date startDate, Date endDate) {

        while (startDate.before(DateUtils.addMonths(endDate, 1))) {
            Calendar instance = Calendar.getInstance();
            instance.setTime(startDate);
            int year = instance.get(Calendar.YEAR);
            int month = instance.get(Calendar.MONTH);
            int resultMonth = month + 1;
            for (int type = 1; type <= 2; type++) {
                //补数据不用考虑下一个月，但调用的是计算是下一个月的方法，故这里月加1
                foundationLoadHisMonthService.wrapperNowYearBasicLoad("1", "1", resultMonth, year, type);
            }
            startDate = DateUtils.addMonths(startDate, 1);
        }
    }

    @SneakyThrows
    private void calcAcLoadHis(List<String> cityIds, List<String> caliberIds, Date startDate, Date endDate) {
        while (startDate.before(endDate)) {
            Calendar instance = Calendar.getInstance();
            instance.setTime(startDate);
            int year = instance.get(Calendar.YEAR);
            int month = instance.get(Calendar.MONTH) + 1;
            for (String cityId : cityIds) {
                for (String caliberId : caliberIds) {
                    if (cityId.equals("1") && caliberId.equals("1")) {
                        wrapperAcLoad(startDate, year, month, cityId, caliberId);
                        break;
                    }
                }
            }
            startDate = DateUtils.addDays(startDate, 1);
        }
    }

    private void wrapperAcLoad(Date date, int year, int month, String cityId, String caliberId) throws Exception {
        List<LoadCityHisDO> loadCityHisDOS = loadCityHisService.findLoadCityHisDOS(cityId, date, date, caliberId);
        if (CollectionUtils.isEmpty(loadCityHisDOS)) {
            log.info("caliberId:" + caliberId + ", cityId:" + cityId + "," + DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR) + "实际负荷为空无法生产空调负荷实际数据");
        } else {
            LoadCityHisDO loadCityHisDO = loadCityHisDOS.get(0);
            Calendar instance = Calendar.getInstance();
            instance.setTime(date);
            String yearStr = String.valueOf(year);
            String monthStr = String.valueOf(month < 10 ? "0" + month : month);

            Boolean workingDay = DateUtil.isWorkingDay(date.getTime());

            //判断前一天是工作日还是休息日
            int type = 1;
            if (!workingDay) {
                type = 2;
            }

            List<FoundationLoadHisMonthDO> foundationLoadHisMonth = foundationLoadHisMonthService.getFoundationLoadHisMonth(cityId, caliberId, yearStr, monthStr, type);
            if (CollectionUtils.isEmpty(foundationLoadHisMonth)) {
                log.info("caliberId:" + caliberId + ", cityId:" + cityId + "," + DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR) + "月基础负荷曲线没有生成");
            } else {
                FoundationLoadHisMonthDO foundationLoadHisMonthDO = foundationLoadHisMonth.get(0);
                List<BigDecimal> foundationLoaBigDecimals = BasePeriodUtils.toList(foundationLoadHisMonthDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
                List<BigDecimal> loaBigDecimals = BasePeriodUtils.toList(loadCityHisDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
                List<BigDecimal> result = new ArrayList<>();
                for (int i = 0; i < loaBigDecimals.size(); i++) {
                    BigDecimal load = loaBigDecimals.get(i);
                    BigDecimal found = foundationLoaBigDecimals.get(i);
                    if (load != null && found != null) {
                        BigDecimal difference = load.subtract(found);
                        result.add(difference.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : difference);
                    } else {
                        result.add(null);
                    }
                }
                Map<String, BigDecimal> stringBigDecimalMap = ColumnUtil.listToMap(result, Constants.LOAD_CURVE_START_WITH_ZERO);
                List<LoadAcHisBasicDO> loadAcHisBasicDOS = loadAcHisBasicService.getloadAcHisBasicDOList(cityId, caliberId, date, date);
                if (CollectionUtils.isEmpty(loadAcHisBasicDOS)) {
                    LoadAcHisBasicDO loadAcHisBasicDO = new LoadAcHisBasicDO();
                    loadAcHisBasicDO.setId(UUID.randomUUID().toString().replace("-", "").toLowerCase());
                    loadAcHisBasicDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
                    loadAcHisBasicDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                    loadAcHisBasicDO.setDate(new java.sql.Date(date.getTime()));
                    loadAcHisBasicDO.setCaliberId(caliberId);
                    loadAcHisBasicDO.setCityId(cityId);
                    BasePeriodUtils.setAllFiled(loadAcHisBasicDO, stringBigDecimalMap);
                    loadAcHisBasicService.doSave(loadAcHisBasicDO);
                } else {
                    LoadAcHisBasicDO loadAcHisBasicDO = loadAcHisBasicDOS.get(0);
                    loadAcHisBasicDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                    BasePeriodUtils.setAllFiled(loadAcHisBasicDO, stringBigDecimalMap);
                    loadAcHisBasicService.doUpdate(loadAcHisBasicDO);
                }
            }
        }
    }

    @GetMapping("/copy/forecastResult")
    public BaseResp copyForecastResult(String sourceAlgoId, String targetAlgoId, Date startDate, Date endDate) throws Exception {
//        forecastService.copyForecastResult(sourceAlgoId, targetAlgoId, startDate, endDate);
        log.error("------开始统计算法准确率， 开始时间：{}, 结束时间：{}", startDate, endDate);
        forecastResultStatService.statForecastResult(Constants.PROVINCE_ID, targetAlgoId, Constants.CALIBER_CITY_ID,
                startDate, endDate);
        log.error("------算法准确率统计完成。开始统计考核点准确率， 开始时间：{}, 结束时间：{}", startDate, endDate);
        accuracyAssessService.doCalculateAssessAccuracy(Constants.PROVINCE_ID, Constants.CALIBER_CITY_ID, targetAlgoId, startDate, endDate);
        log.error("------考核点准确率统计完成。开始统计综合准确率， 开始时间：{}, 结束时间：{}", startDate, endDate);
        accuracyCompositeService.doCalculateCompositeAccuracy(Constants.PROVINCE_ID, Constants.CALIBER_CITY_ID, targetAlgoId, startDate, endDate);
        log.error("------综合准确率统计完成。");
        return BaseResp.succResp();
    }

    @GetMapping("/doSaveBatchFc")
    public BaseResp doSaveBatchFc(String algorithmId, Date startDate, Date endDate) throws Exception {
        List<LoadCityFcDO> fcByAlgorithmId = loadCityFcService.findFcByAlgorithmId(Constants.PROVINCE_ID, Constants.CALIBER_CITY_ID, algorithmId, startDate, endDate);
        for (LoadCityFcDO loadCityFcDO : fcByAlgorithmId) {
            loadCityFcBatchService.doSave(loadCityFcDO);
        }
        return BaseResp.succResp();
    }

    @GetMapping("/copy/industry")
    public BaseResp copyIndustry(Date startDate, Date endDate) throws Exception {
        List<Date[]> dates = DateUtil.splitDateRange(startDate, endDate, 30);
        for (Date[] dateArr : dates) {
            List<IndustryCityLoadDayHisClctNewDO> newIndustryList = new ArrayList<>();
            Date start = dateArr[0];
            Date end = dateArr[1];
            end = DateUtils.addDays(end, 1);
            List<IndustryCityLoadDayHisClctDO> oldIndustryList = industryCityLoadDayHisClctService.findByDate(null, null, start, end);
            Map<String, IndustryCityLoadDayHisClctDO> clctDOMap = oldIndustryList.stream().collect(
                    Collectors.toMap(clctDO -> clctDO.getCity_id() + clctDO.getDate().getTime() + clctDO.getTradeCode(),
                            Function.identity(), (o, n) -> n));
            for (IndustryCityLoadDayHisClctDO industryCityLoadDayHisClctDO : oldIndustryList) {
                IndustryCityLoadDayHisClctNewDO newIndustry = new IndustryCityLoadDayHisClctNewDO();
                BeanUtils.copyProperties(industryCityLoadDayHisClctDO, newIndustry);
                newIndustry.setId(null);
                List<BigDecimal> bigDecimals = industryCityLoadDayHisClctDO.getloadList();
                if (allElementsAreNull(bigDecimals)) {
                    log.error("行业数据为空 - cityId: {}, date: {}, tradeCode: {}, tradeCodeDsc: {}",
                            industryCityLoadDayHisClctDO.getCity_id(),
                            industryCityLoadDayHisClctDO.getDate(),
                            industryCityLoadDayHisClctDO.getTradeCode(),
                            industryCityLoadDayHisClctDO.getTradeCodeDsc());
                    continue;
                }
                //从T0000开始
                BasePeriodUtils.setAllFiled(newIndustry, ColumnUtil.listToMap(bigDecimals, true));

                //补今天T2400的数据
                IndustryCityLoadDayHisClctDO oldIndustry = clctDOMap.get(industryCityLoadDayHisClctDO.getCity_id() +
                        DateUtils.addDays(industryCityLoadDayHisClctDO.getDate(), +1).getTime() + industryCityLoadDayHisClctDO.getTradeCode());
                if (oldIndustry != null) {
                    newIndustry.setT2400(oldIndustry.getloadList().get(0));
                }
                newIndustryList.add(newIndustry);
            }

            for (IndustryCityLoadDayHisClctNewDO hisClctNewDO : newIndustryList) {
                try {
                    industryCityLoadDayHisClctNewService.saveOrUpdate(hisClctNewDO);
                } catch (Exception e) {
                    log.error("保存行业数据异常 - cityId: {}, date: {}, tradeCode: {}, tradeCodeDsc: {}, 异常信息: {}",
                            hisClctNewDO.getCity_id(),
                            hisClctNewDO.getDate(),
                            hisClctNewDO.getTradeCode(),
                            hisClctNewDO.getTradeCodeDsc(),
                            e.getMessage(),
                            e);
                }
            }
        }
        return BaseResp.succResp();
    }

    public static <T> boolean allElementsAreNull(List<T> list) {
        if (list == null) {
            return true;
        }
        return list.stream().noneMatch(Objects::nonNull);
    }
}