package com.tsintergy.lf.web.base.evalucation.controller;


import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.base.evalucation.api.AccuracyAlgorithmService;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyAlgorithmCurveDataDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyAlgorithmDataDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyDetailValueDataDTO;
import com.tsintergy.lf.web.base.controller.BaseController;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 多算法预测准确率查询页面
 *
 * <AUTHOR>
 * @create 2025-02-13
 * @since 1.0.0
 */
@RequestMapping("/algorithm")
@RestController
public class AccuracyAlgorithmController extends BaseController {

    @Autowired
    AccuracyAlgorithmService accuracyAlgorithmService;

    @RequestMapping(value = "/accuracy", method = RequestMethod.GET)
    public BaseResp<List<AccuracyAlgorithmDataDTO>> getListByCityIdAndAlgorithmDate(String cityId, Date startDate, Date endDate, String algorithmId)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<AccuracyAlgorithmDataDTO> algorithmListData = accuracyAlgorithmService.getAlgorithmListData(cityId,
            startDate, endDate, algorithmId, this.getCaliberId());
        baseResp.setData(algorithmListData);
        return baseResp;
    }

    @RequestMapping(value = "/accuracy/detail", method = RequestMethod.GET)
    public BaseResp<List<AccuracyDetailValueDataDTO>> getListByCityIdAndAlgorithmDetailDate(String cityId, Date startDate, Date endDate,
        String algorithmId, String days) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<AccuracyDetailValueDataDTO> algorithmListDetailData = accuracyAlgorithmService.getAlgorithmListDetailData(
            cityId, startDate, endDate, algorithmId, this.getCaliberId(), days);
        baseResp.setData(algorithmListDetailData);
        return baseResp;
    }

    @RequestMapping(value = "/accuracy/detail/curve", method = RequestMethod.GET)
    public BaseResp<List<AccuracyAlgorithmCurveDataDTO>> getListByCityIdAndAlgorithmDetailCurveDate(String cityId, Date startDate, Date endDate,
        String algorithmId, String days) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<AccuracyAlgorithmCurveDataDTO> algorithmListDetailData = accuracyAlgorithmService.getAlgorithmListDetailCurveData(
            cityId, startDate, endDate, algorithmId, this.getCaliberId(), days);
        baseResp.setData(algorithmListDetailData);
        return baseResp;
    }
}
