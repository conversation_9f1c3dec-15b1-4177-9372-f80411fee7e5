package com.tsintergy.lf.web.base.forecast.request;

import com.alibaba.fastjson.JSONArray;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

import java.math.BigDecimal;
import java.util.List;

/**
 * User:tao<PERSON><PERSON>i
 * Date:18-3-15
 * Time:下午5:21
 */
public class SmoothLineRequest {

    @ApiModelProperty(value = "预测数据")
    @NotEmpty(message = "预测数据不能为空")
    private List<BigDecimal> forecast;

    public List<BigDecimal> getForecast() {
        return forecast;
    }

    public void setForecast(String str) {
        this.forecast = JSONArray.parseArray(str,BigDecimal.class);
    }
}
