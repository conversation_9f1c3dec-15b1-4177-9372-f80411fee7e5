package com.tsintergy.lf.web.base.load.request;


import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * @date: 2/23/18 9:50 AM
 * @author: angel
 **/
public class MonitorRequest {

    /**
     * algorithmId
     */
   @ApiModelProperty(value = "算法id")
    private String algorithmId;

    /**
     * cityId
     */
    @ApiModelProperty(value = "城市id")
    private String cityId;

    /**
     * 口径ID
     */
    @ApiModelProperty(value = "口径ID")
    private String caliberId;

    /**
     * date
     */

    @ApiModelProperty(value = "日期")
    private Date date;

    /**
     * 批次ID
     */
    @ApiModelProperty(value = "批次ID")
    private String batchId;


    public String getAlgorithmId() {
        return algorithmId;
    }

    public void setAlgorithmId(String algorithmId) {
        this.algorithmId = algorithmId;
    }

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getCaliberId() {
        return caliberId;
    }

    public void setCaliberId(String caliberId) {
        this.caliberId = caliberId;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }
}
