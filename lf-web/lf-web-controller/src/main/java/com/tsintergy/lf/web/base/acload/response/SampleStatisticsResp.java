/**
 * Copyright (C), 2015‐2022, 北京清能互联科技有限公司 Author:  <EMAIL> Date:  2022/5/24 1:17 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.acload.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * Description: 样本统计情况 <br>
 *
 * <AUTHOR>
 * @create 2022/5/24
 * @since 1.0.0
 */
@ApiModel
@Data
public class SampleStatisticsResp {

    @ApiModelProperty(value = "方案id", example = "123456")
    String solutionId;

    @ApiModelProperty(value = "样本数据统计值")
    List<StatisticsResp> dataValue;

    @ApiModelProperty(value = "样本数据")
    List<SampleResp> sampleList;
}  
