package com.tsintergy.lf.web.base.datamanage.controller;


import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.datamanage.dto.LoadBatchQueryDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsCityDayFcBatchService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsCityDayFcService;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.StatisticsDayBatchDTO;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcBatchService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingBatchInitService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingBatchInitDO;
import com.tsintergy.lf.web.base.controller.BaseController;
import com.tsintergy.lf.web.base.datamanage.request.BatchRequest;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @date: 4/11/18 12:02 PM
 * @author: angel
 **/
@RestController
@RequestMapping("/data")
public class DataManageBatchController extends BaseController {

    @Autowired
    LoadCityFcService loadCityFcService;

    @Autowired
    CityService cityService;

    @Autowired
    CaliberService caliberService;

    @Autowired
    LoadCityHisService loadCityHisService;

    @Autowired
    StatisticsCityDayFcService statisticsCityDayFcService;

    @Autowired
    LoadCityFcBatchService loadCityFcBatchService;

    @Autowired
    private StatisticsCityDayFcBatchService statisticsCityDayFcBatchService;

    @Autowired
    private SettingBatchInitService settingBatchInitService;

    @PostMapping("/batchFcWeather")
    public BaseResp batchFcWeatherData(@RequestBody BatchRequest batchRequest) throws Exception {
        LoadBatchQueryDTO weatherInfoFcLoadForecast = loadCityFcBatchService
            .findWeather(batchRequest.getBatchIds(), batchRequest.getAlgorithmIds(), batchRequest.getCityId(),
                batchRequest.getCaliberId()
                , batchRequest.getStartDate(), batchRequest.getEndDate(), batchRequest.getType());
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(weatherInfoFcLoadForecast);
        return baseResp;
    }


    @PostMapping("/fcLoadDataList")
    public BaseResp batchFcLoadData(@RequestBody BatchRequest batchRequest) throws Exception {
        LoadBatchQueryDTO loadCityFcBatchDOS = loadCityFcBatchService
            .findLoad(batchRequest.getBatchIds(), batchRequest.getAlgorithmIds(), batchRequest.getCityId(),
                batchRequest.getCaliberId()
                , batchRequest.getStartDate(), batchRequest.getEndDate());
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(loadCityFcBatchDOS);
        return baseResp;
    }

    @PostMapping("/fcAccuracyBatch")
    public BaseResp<List<StatisticsDayBatchDTO>> fcAccuracyBatch(@RequestBody BatchRequest batchRequest)
        throws Exception {
        List<StatisticsDayBatchDTO> loadCityFcBatchDOS = statisticsCityDayFcBatchService
            .getDayAccuracy(batchRequest.getCityId(), batchRequest.getCaliberId(), batchRequest.getAlgorithmIds(),
                batchRequest.getBatchIds(), batchRequest.getStartDate(), batchRequest.getEndDate());
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(loadCityFcBatchDOS);
        return baseResp;
    }

    @GetMapping("/getBatch")
    public BaseResp<List<SettingBatchInitDO>> getBatch(){
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(settingBatchInitService.getBatchList());
        return baseResp;
    }

}
