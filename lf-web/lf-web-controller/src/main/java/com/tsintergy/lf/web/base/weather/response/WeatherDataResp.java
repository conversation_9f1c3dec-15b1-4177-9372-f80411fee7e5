package com.tsintergy.lf.web.base.weather.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

/**
 * 气象历史数据和预测数据
 *
 * @date: 18-2-7
 * @author: ta<PERSON><PERSON><PERSON><PERSON>
 **/
@ApiModel
public class WeatherDataResp {

    /**
     * 历史数据
     */
    @ApiModelProperty(value = "历史数据")
    private List<BigDecimal> real;

    /**
     * 昨日历史数据,只有在查寻一天的气象时才展示，查询多天气象不展示
     */
    @ApiModelProperty(value = "昨日历史数据")
    private List<BigDecimal> yesterday;

    /**
     * 预测数据
     */
    @ApiModelProperty(value = "预测数据")
    private List<BigDecimal> forecast;

    @ApiModelProperty(value = "预测数据")
    private List<BigDecimal> fc;

    public List<BigDecimal> getReal() {
        return real;
    }

    public void setReal(List<BigDecimal> real) {
        this.real = real;
    }

    public List<BigDecimal> getForecast() {
        return forecast;
    }

    public void setForecast(List<BigDecimal> forecast) {
        this.forecast = forecast;
    }

    public List<BigDecimal> getFc() {
        return fc;
    }

    public void setFc(List<BigDecimal> fc) {
        this.fc = fc;
    }

    public List<BigDecimal> getYesterday() {
        return yesterday;
    }

    public void setYesterday(List<BigDecimal> yesterday) {
        this.yesterday = yesterday;
    }
}
