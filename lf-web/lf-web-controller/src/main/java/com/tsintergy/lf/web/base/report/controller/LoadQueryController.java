/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  wangfeng Date:  2020/1/11 3:48 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.report.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceapi.base.common.enumeration.CityEnum;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.report.api.*;
import com.tsintergy.lf.serviceapi.base.report.dto.LoadQueryDTO;
import com.tsintergy.lf.serviceapi.base.report.dto.ReportLoadPeakDTO;
import com.tsintergy.lf.serviceapi.base.report.pojo.*;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import com.tsintergy.lf.web.base.report.response.ReportPeakDataDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Proxy;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/1/11
 * @since 1.0.0
 */
@RequestMapping(value = "/query")
@RestController
@Api(tags = "加载数据控制器")
public class LoadQueryController extends CommonBaseController {

    @Autowired
    private ReportLoadDayService reportLoadDayService;

    @Autowired
    private ReportLoadWeekService reportLoadWeekService;

    @Autowired
    private ReportLoadMonthService reportLoadMonthService;

    @Autowired
    private ReportLoadQuarterService reportLoadQuarterService;

    @Autowired
    private ReportLoadYearService reportLoadYearService;

    @Autowired
    private ReportLoadHolidayService reportLoadHolidayService;

    @Autowired
    private LoadCityFcService loadCityFcService;

    @Autowired
    private CityService cityService;


    @Autowired
    private HolidayService holidayService;

    @Autowired
    private ReportLoadPeakService reportLoadPeakService;

    /**
     * 查询日负荷预测填报
     */
    @ApiOperation("查询日负荷预测填报")
    @GetMapping(value = "/day/feature")
    public BaseResp<List<LoadQueryDTO>> findDayFeature(@ApiParam("日期") Date date) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        //String cityId = getDefaultCityId();
        List<CityDO> allCitys = cityService.findAllCitys();
        List<ReportLoadDayDO> loadDayVOS = reportLoadDayService.findReportDay(null, date, date);

        List<LoadQueryDTO> loadQueryDTOS = new ArrayList<>();

        for (CityDO city : allCitys) {
            //过滤掉地方点，直接用id过滤
            if ("23".equals(city.getId())) {
                break;
            }
            Boolean flag = true;
            if (!CollectionUtils.isEmpty(loadDayVOS)) {
                for (ReportLoadDayDO loadDayVO : loadDayVOS) {
                    if (city.getId().equals(loadDayVO.getCityId())) {
                        LoadQueryDTO queryDTO = new LoadQueryDTO();
                        BeanUtils.copyProperties(loadDayVO, queryDTO);
                        queryDTO.setCity(cityService.findCityById(loadDayVO.getCityId()).getCity());
                        loadQueryDTOS.add(queryDTO);
                        flag = false;
                    }
                }
            }
            if (flag) {
                LoadQueryDTO queryDTO = new LoadQueryDTO();
                queryDTO.setDate(date);
                queryDTO.setCity(city.getCity());
                loadQueryDTOS.add(queryDTO);
            }
        }
        baseResp.setData(loadQueryDTOS);
        return baseResp;
    }


    /**
     * 查询早晚峰预测填报
     */
    @ApiOperation("查询早晚峰预测填报")
    @GetMapping(value = "/peak")
    public BaseResp<List<ReportLoadPeakDTO>> findPeakReport(@ApiParam("日期") Date date) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<ReportLoadPeakDO> reportLoadPeakDOS = reportLoadPeakService.findReportPeak(null, date, date);
        Map<String, Map<Integer, ReportLoadPeakDO>> map = new HashMap<>();
        List<ReportLoadPeakDTO> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(reportLoadPeakDOS)) {
            // -> map(cityId , map(peakType , ReportLoadPeakDO))
            map = reportLoadPeakDOS.stream().collect(Collectors.groupingBy
                (ReportLoadPeakDO::getCityId,
                    Collectors.toMap(ReportLoadPeakDO::getPeakType, e -> e, (oldv, curv) -> curv)));

        }
        for (CityEnum cityEnum : CityEnum.values()) {
            ReportLoadPeakDTO dto = new ReportLoadPeakDTO();
            dto.setCityName(cityEnum.getName());
            dto.setAmLoadTotal(map.get(cityEnum.getId()) == null ? null
                : map.get(cityEnum.getId()).get(1) == null ? null
                    : converInter(map.get(cityEnum.getId()).get(1).getLoadTotal()));
            dto.setAmLoadPlace(map.get(cityEnum.getId()) == null ? null
                : map.get(cityEnum.getId()).get(1) == null ? null
                    : converInter(map.get(cityEnum.getId()).get(1).getLoadPlace()));
            dto.setAmLoadSupply(map.get(cityEnum.getId()) == null ? null
                : map.get(cityEnum.getId()).get(1) == null ? null
                    : converInter(map.get(cityEnum.getId()).get(1).getLoadSupply()));
            dto.setPmLoadTotal(map.get(cityEnum.getId()) == null ? null
                : map.get(cityEnum.getId()).get(2) == null ? null
                    : converInter(map.get(cityEnum.getId()).get(2).getLoadTotal()));
            dto.setPmLoadPlace(map.get(cityEnum.getId()) == null ? null
                : map.get(cityEnum.getId()).get(2) == null ? null
                    : converInter(map.get(cityEnum.getId()).get(2).getLoadPlace()));
            dto.setPmLoadSupply(map.get(cityEnum.getId()) == null ? null
                : map.get(cityEnum.getId()).get(2) == null ? null
                    : converInter(map.get(cityEnum.getId()).get(2).getLoadSupply()));
            result.add(dto);
        }
        baseResp.setData(result);
        return baseResp;
    }

    /**
     * Decimal 保留整数
     */
    private BigDecimal converInter(BigDecimal val) {
        if (val == null) {
            return null;
        }
        return val.setScale(0, BigDecimal.ROUND_HALF_UP);
    }


    /**
     * 查询早晚峰预测填报 EXCEL下载
     */
    @ApiOperation("查询早晚峰预测填报EXCEL下载")
    @PostMapping(value = "/peak/download")
    public BaseResp downloadPeakReport(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String dataStr = request.getParameter("date");
        if (StringUtils.isBlank(dataStr)) {
            return BaseResp.failResp();
        }
        Date date = DateUtils.string2Date(dataStr, DateFormatType.SIMPLE_DATE_FORMAT_STR);
        String dateHead = DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_CHINESE);
        //修改 ReportPeakDataDTO head0 日期属性
        //获取ReportPeakDataDTO的city字段
        Field field = ReportPeakDataDTO.class.getDeclaredField("city");
        //获取city字段上的ExcelProperty注解实例
        ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
        //获取 excelProperty 这个代理实例所持有的 InvocationHandler
        InvocationHandler h = Proxy.getInvocationHandler(excelProperty);
        // 获取 AnnotationInvocationHandler 的 memberValues 字段
        Field hField = h.getClass().getDeclaredField("memberValues");
        hField.setAccessible(true);
        // 获取 memberValues
        Map memberValues = (Map) hField.get(h);
        // 修改 value 属性值
        memberValues.put("value", new String[]{dataStr, "地区"});
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode(dateHead + "各地区负荷预测", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xls");
        EasyExcel.write(response.getOutputStream(), ReportPeakDataDTO.class).sheet("各地区负荷预测")
            .doWrite(getPeakReportData(date));
        return BaseResp.succResp();
    }


    /**
     * 查询早晚峰预测填报 EXCEL  动态头，实时生成头写入
     */
    private List<List<String>> head(String dateHead) {
        List<List<String>> list = new ArrayList<List<String>>();
        List<String> head0 = new ArrayList<String>();
        head0.add(dateHead);
        head0.add("地区");
        List<String> head1 = new ArrayList<String>();
        head1.add("11:15的预测值（MW）");
        head1.add("地区总负荷");
        List<String> head2 = new ArrayList<String>();
        head2.add("11:15的预测值（MW）");
        head2.add("地方电源");
        List<String> head3 = new ArrayList<String>();
        head3.add("11:15的预测值（MW）");
        head3.add("网供负荷需求");
        List<String> head4 = new ArrayList<String>();
        head4.add("16:45的预测值（MW）");
        head4.add("地区总负荷");
        List<String> head5 = new ArrayList<String>();
        head5.add("16:45的预测值（MW）");
        head5.add("地方电源");
        List<String> head6 = new ArrayList<String>();
        head6.add("16:45的预测值（MW）");
        head6.add("网供负荷需求");
        list.add(head0);
        list.add(head1);
        list.add(head2);
        list.add(head3);
        list.add(head4);
        list.add(head5);
        list.add(head6);
        return list;
    }

    private List getPeakReportData(Date date) throws Exception {
        List<ReportLoadPeakDO> reportLoadPeakDOS = reportLoadPeakService.findReportPeak(null, date, date);
        Map<String, Map<Integer, ReportLoadPeakDO>> map = new HashMap<>();
        List<ReportLoadPeakDTO> reportLoadPeakDTOS = new ArrayList<>();
        if (!CollectionUtils.isEmpty(reportLoadPeakDOS)) {
            // -> map(cityId , map(peakType , ReportLoadPeakDO))
            map = reportLoadPeakDOS.stream().collect(Collectors.groupingBy
                (ReportLoadPeakDO::getCityId,
                    Collectors.toMap(ReportLoadPeakDO::getPeakType, e -> e, (oldv, curv) -> curv)));

        }
        //按照枚举顺序取数据
        for (CityEnum cityEnum : CityEnum.values()) {
            ReportLoadPeakDTO dto = new ReportLoadPeakDTO();
            dto.setCityName(cityEnum.getName());
            dto.setAmLoadTotal(map.get(cityEnum.getId()) == null ? null
                : map.get(cityEnum.getId()).get(1) == null ? null
                    : converInter(map.get(cityEnum.getId()).get(1).getLoadTotal()));
            dto.setAmLoadPlace(map.get(cityEnum.getId()) == null ? null
                : map.get(cityEnum.getId()).get(1) == null ? null
                    : converInter(map.get(cityEnum.getId()).get(1).getLoadPlace()));
            dto.setAmLoadSupply(map.get(cityEnum.getId()) == null ? null
                : map.get(cityEnum.getId()).get(1) == null ? null
                    : converInter(map.get(cityEnum.getId()).get(1).getLoadSupply()));
            dto.setPmLoadTotal(map.get(cityEnum.getId()) == null ? null
                : map.get(cityEnum.getId()).get(2) == null ? null
                    : converInter(map.get(cityEnum.getId()).get(2).getLoadTotal()));
            dto.setPmLoadPlace(map.get(cityEnum.getId()) == null ? null
                : map.get(cityEnum.getId()).get(2) == null ? null
                    : converInter(map.get(cityEnum.getId()).get(2).getLoadPlace()));
            dto.setPmLoadSupply(map.get(cityEnum.getId()) == null ? null
                : map.get(cityEnum.getId()).get(2) == null ? null
                    : converInter(map.get(cityEnum.getId()).get(2).getLoadSupply()));
            reportLoadPeakDTOS.add(dto);
        }
        List<ReportPeakDataDTO> result = new ArrayList<>();
        Integer allAmLoadTotal = 0;
        Integer allAmLoadPlace = 0;
        Integer allAmLoadSupply = 0;
        Integer allPmLoadTotal = 0;
        Integer allPmLoadPlace = 0;
        Integer allPmLoadSupply = 0;
        for (ReportLoadPeakDTO peakDTO : reportLoadPeakDTOS) {
            ReportPeakDataDTO data = new ReportPeakDataDTO();
            data.setCity(peakDTO.getCityName());
            data.setAmLoadTotal(peakDTO.getAmLoadTotal() == null ? null : peakDTO.getAmLoadTotal().intValue());
            allAmLoadTotal += data.getAmLoadTotal() == null ? 0 : data.getAmLoadTotal();
            data.setAmLoadPlace(peakDTO.getAmLoadPlace() == null ? null : peakDTO.getAmLoadPlace().intValue());
            allAmLoadPlace += data.getAmLoadPlace() == null ? 0 : data.getAmLoadPlace();
            data.setAmLoadSupply(peakDTO.getAmLoadSupply() == null ? null : peakDTO.getAmLoadSupply().intValue());
            allAmLoadSupply += data.getAmLoadSupply() == null ? 0 : data.getAmLoadSupply();
            data.setPmLoadTotal(peakDTO.getPmLoadTotal() == null ? null : peakDTO.getPmLoadTotal().intValue());
            allPmLoadTotal += data.getPmLoadTotal() == null ? 0 : data.getPmLoadTotal();
            data.setPmLoadPlace(peakDTO.getPmLoadPlace() == null ? null : peakDTO.getPmLoadPlace().intValue());
            allPmLoadPlace += data.getPmLoadPlace() == null ? 0 : data.getPmLoadPlace();
            data.setPmLoadSupply(peakDTO.getPmLoadSupply() == null ? null : peakDTO.getPmLoadSupply().intValue());
            allPmLoadSupply += data.getPmLoadSupply() == null ? 0 : data.getPmLoadSupply();
            result.add(data);
        }
        ReportPeakDataDTO sum = new ReportPeakDataDTO();
        sum.setCity("合计");
        sum.setAmLoadTotal(allAmLoadTotal);
        sum.setAmLoadPlace(allAmLoadPlace);
        sum.setAmLoadSupply(allAmLoadSupply);
        sum.setPmLoadTotal(allPmLoadTotal);
        sum.setPmLoadPlace(allPmLoadPlace);
        sum.setPmLoadSupply(allPmLoadSupply);
        result.add(sum);
        return result;

    }

    /**
     * 查询96点预测填报
     */
    @GetMapping(value = "/day")
    @ApiOperation("查询96点预测填报")
    public BaseResp<List<LoadQueryDTO>> findDayReport(@ApiParam("开始日期") Date date,
        @ApiParam("城市ID") String cityId) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<CityDO> allCitys = null;
        if (StringUtils.isEmpty(cityId)) {
            cityId = this.getLoginCityId();
        }

        CityDO cityById = cityService.findCityById(cityId);

        if(cityById.getType().equals(CityConstants.CITY_TYPE)) {
            allCitys = new ArrayList<>();
            allCitys.add(cityById);
        }else if(cityById.getType().equals(CityConstants.PROVINCE_TYPE)) {
            allCitys = cityService.findAllCitys();
            // 如果是省调用户，city置为空，查询全部数据
            cityId = null;
        }

        List<LoadCityFcDO> loadCityFcVOS = loadCityFcService
            .listReportLoadCityFc(cityId, getCaliberId(), date, date, null);
        Map<java.sql.Date, List<LoadCityFcDO>> collect = loadCityFcVOS.stream()
            .collect(Collectors.groupingBy(LoadCityFcDO::getDate));
        List<LoadQueryDTO> loadQueryDTOS = new ArrayList<>();
        Date endDate = DateUtils.addDays(date, 1);
        while (date.before(endDate)) {
            List<LoadCityFcDO> loadCityFcDOS = collect.get(date);
            if (CollectionUtils.isEmpty(loadCityFcDOS)) {
                for (CityDO city : allCitys) {
                    LoadQueryDTO loadQueryDTO = new LoadQueryDTO();
                    loadQueryDTO.setCity(city.getCity());
                    loadQueryDTO.setDate(date);
                    loadQueryDTOS.add(loadQueryDTO);
                }
            } else {
                Map<String, List<LoadCityFcDO>> listMap = loadCityFcDOS.stream()
                    .collect(Collectors.groupingBy(LoadCityFcDO::getCityId));
                for (CityDO cityDO : allCitys) {
                    if (!CollectionUtils.isEmpty(listMap.get(cityDO.getId()))) {
                        List<LoadCityFcDO> fcVOS = listMap.get(cityDO.getId());
                        LoadCityFcDO fcVO = fcVOS.get(0);
                        LoadQueryDTO loadQueryDTO = new LoadQueryDTO();
                        loadQueryDTO.setCity(cityService.findCityById(fcVO.getCityId()).getCity());
                        loadQueryDTO.setPointLoads(BasePeriodUtils
                            .toList(fcVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
                        BeanUtils.copyProperties(fcVO, loadQueryDTO);
                        loadQueryDTOS.add(loadQueryDTO);
                    } else {
                        LoadQueryDTO loadQueryDTO = new LoadQueryDTO();
                        loadQueryDTO.setCity(cityDO.getCity());
                        loadQueryDTO.setDate(date);
                        loadQueryDTOS.add(loadQueryDTO);
                    }
                }
            }
            date = DateUtils.addDays(date, 1);
        }
        baseResp.setData(loadQueryDTOS);
        return baseResp;
    }


    /**
     * 查询周负荷预测填报
     */
    @ApiOperation("查询周负荷预测填报")
    @GetMapping(value = "/week")
    public BaseResp<List<LoadQueryDTO>> findWeekReport(@ApiParam("年份") String year, @ApiParam("周") String week)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        //String cityId = getDefaultCityId();
        List<CityDO> allCitys = cityService.findAllCitys();
        List<ReportLoadWeekDO> reportLoadWeekVOS = reportLoadWeekService.findWeekReport(null, year, week);

        List<LoadQueryDTO> loadQueryDTOS = new ArrayList<>();

        for (CityDO city : allCitys) {
            //过滤掉地方点，直接用id过滤
            if ("23".equals(city.getId())) {
                break;
            }
            Boolean flag = true;
            if (!CollectionUtils.isEmpty(reportLoadWeekVOS)) {
                for (ReportLoadWeekDO weekVO : reportLoadWeekVOS) {
                    if (city.getId().equals(weekVO.getCityId())) {
                        LoadQueryDTO loadQueryDTO = new LoadQueryDTO();
                        BeanUtils.copyProperties(weekVO, loadQueryDTO);
                        loadQueryDTO.setCity(cityService.findCityById(weekVO.getCityId()).getCity());
                        loadQueryDTOS.add(loadQueryDTO);
                        flag = false;
                    }
                }
            }
            if (flag) {
                LoadQueryDTO queryDTO = new LoadQueryDTO();
                queryDTO.setWeek(week);
                queryDTO.setCity(city.getCity());
                loadQueryDTOS.add(queryDTO);
            }
        }
        baseResp.setData(loadQueryDTOS);
        return baseResp;
    }


    /**
     * 查询月负荷预测填报
     */
    @ApiOperation("查询月负荷预测填报")
    @GetMapping(value = "/month")
    public BaseResp<List<LoadQueryDTO>> findMonthReport(@ApiParam("年份") String year, @ApiParam("月份") String month)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        //String cityId = getDefaultCityId();
        List<CityDO> allCitys = cityService.findAllCitys();
        List<ReportLoadMonthDO> reportLoadMonthVOS = reportLoadMonthService.findReportMonth(null, year, month);

        List<LoadQueryDTO> loadQueryDTOS = new ArrayList<>();
        for (CityDO city : allCitys) {
            //过滤掉地方点，直接用id过滤
            if ("23".equals(city.getId())) {
                break;
            }
            Boolean flag = true;
            if (!CollectionUtils.isEmpty(reportLoadMonthVOS)) {
                for (ReportLoadMonthDO monthVO : reportLoadMonthVOS) {
                    if (city.getId().equals(monthVO.getCityId())) {
                        LoadQueryDTO loadQueryDTO = new LoadQueryDTO();
                        BeanUtils.copyProperties(monthVO, loadQueryDTO);
                        loadQueryDTO.setCity(cityService.findCityById(monthVO.getCityId()).getCity());
                        loadQueryDTOS.add(loadQueryDTO);
                        flag = false;
                    }
                }
            }
            if (flag) {
                LoadQueryDTO queryDTO = new LoadQueryDTO();
                queryDTO.setMonth(month);
                queryDTO.setCity(city.getCity());
                loadQueryDTOS.add(queryDTO);
            }
        }
        baseResp.setData(loadQueryDTOS);
        return baseResp;
    }


    /**
     * 查询季负荷预测填报
     */
    @ApiOperation("查询季负荷预测填报")
    @GetMapping(value = "/quarter")
    public BaseResp<List<LoadQueryDTO>> findQuarterReport(@ApiParam("年份") String year,
        @ApiParam("quarter") String quarter,
        @ApiParam("月份") String month) throws Exception {

        BaseResp baseResp = BaseResp.succResp();
        List<CityDO> allCitys = cityService.findAllCitys();
        List<String> months = new ArrayList<>();
        List<LoadQueryDTO> loadQueryDTOS = new ArrayList<>();
        if (month == null) {
            switch (quarter) {
                case "01":
                    for (int i = 1; i <= 3; i++) {
                        months.add("0" + i);
                    }
                    break;
                case "02":
                    for (int i = 4; i <= 6; i++) {
                        months.add("0" + i);

                    }
                    break;

                case "03":
                    for (int i = 7; i <= 9; i++) {
                        months.add("0" + i);
                    }
                    break;

                case "04":
                    for (int i = 10; i <= 12; i++) {
                        months.add("" + i);
                    }
                    break;
                default:
                    break;
            }
        } else {
            months.add(month);
        }

        for (CityDO city : allCitys) {
            //过滤掉地方点，直接用id过滤
            if ("23".equals(city.getId())) {
                break;
            }
            for (String mon : months) {
                List<ReportLoadQuarterDO> reportLoadQuarterVOS = reportLoadQuarterService
                    .findReportQuarter(city.getId(), year, quarter, mon);
                if (CollectionUtils.isEmpty(reportLoadQuarterVOS)) {
                    LoadQueryDTO queryDTO = new LoadQueryDTO();
                    queryDTO.setYear(year);
                    queryDTO.setMonth(mon);
                    queryDTO.setCity(city.getCity());
                    loadQueryDTOS.add(queryDTO);
                } else {
                    LoadQueryDTO loadQueryDTO = new LoadQueryDTO();
                    loadQueryDTO.setCity(city.getCity());
                    BeanUtils.copyProperties(reportLoadQuarterVOS.get(0), loadQueryDTO);
                    loadQueryDTOS.add(loadQueryDTO);
                }
            }
        }


        baseResp.setData(loadQueryDTOS);
        return baseResp;
    }


    /**
     * 查询年负荷预测填报
     */
    @ApiOperation("查询年负荷预测填报")
    @GetMapping(value = "/year")
    public BaseResp<List<LoadQueryDTO>> findYearReport2(@ApiParam("年份") String year, @ApiParam("月份") String month)
        throws Exception {
//        BaseResp baseResp = BaseResp.succResp();
//        //String cityId = getDefaultCityId();
//        List<CityDO> allCitys = cityService.findAllCitys();
//        List<ReportLoadYearDO> yearVOS = reportLoadYearService.findReportYear(null, year, month);
//        List<LoadQueryDTO> loadQueryDTOS = new ArrayList<>();
//        for(CityDO city:allCitys) {
//            //过滤掉地方点，直接用id过滤
//            if ("23".equals(city.getId())) {
//                break;
//            }
//            Boolean flag = true;
//            if (!CollectionUtils.isEmpty(yearVOS)) {
//                for (ReportLoadYearDO yearVO : yearVOS) {
//                    if (city.getId().equals(yearVO.getCityId())) {
//                        LoadQueryDTO loadQueryDTO = new LoadQueryDTO();
//                        BeanUtils.copyProperties(yearVO, loadQueryDTO);
//                        loadQueryDTO.setCity(cityService.findCityById(yearVO.getCityId()).getCity());
//                        loadQueryDTOS.add(loadQueryDTO);
//                        flag = false;
//                    }
//                }
//            }
//            if(flag){
//                if(month == null){
//                    for(int i =1;i<=12;i++) {
//                        LoadQueryDTO queryDTO = new LoadQueryDTO();
//                        queryDTO.setYear(year);
//                        queryDTO.setMonth(i>10?(""+i):"0"+i);
//                        queryDTO.setCity(city.getCity());
//                        loadQueryDTOS.add(queryDTO);
//                    }
//                }else {
//                    LoadQueryDTO queryDTO = new LoadQueryDTO();
//                    queryDTO.setYear(year);
//                    queryDTO.setMonth(month);
//                    queryDTO.setCity(city.getCity());
//                    loadQueryDTOS.add(queryDTO);
//                }
//            }
//        }
//        baseResp.setData(loadQueryDTOS);
//        return baseResp;

        List<String> months = new ArrayList<>();
        if (month == null) {
            for (int i = 1; i <= 12; i++) {
                months.add(i > 9 ? ("" + i) : "0" + i);
            }
        } else {
            months.add(month);
        }
        List<CityDO> allCitys = cityService.findAllCitys();
        List<LoadQueryDTO> loadQueryDTOS = new ArrayList<>();
        for (CityDO city : allCitys) {
            //过滤掉地方点，直接用id过滤
            if ("23".equals(city.getId())) {
                break;
            }
            for (String mon : months) {
                List<ReportLoadYearDO> yearVOS = reportLoadYearService.findReportYear(city.getId(), year, mon);
                if (CollectionUtils.isEmpty(yearVOS)) {
                    LoadQueryDTO queryDTO = new LoadQueryDTO();
                    queryDTO.setYear(year);
                    queryDTO.setMonth(mon);
                    queryDTO.setCity(city.getCity());
                    loadQueryDTOS.add(queryDTO);
                } else {
                    LoadQueryDTO loadQueryDTO = new LoadQueryDTO();
                    loadQueryDTO.setCity(city.getCity());
                    BeanUtils.copyProperties(yearVOS.get(0), loadQueryDTO);
                    loadQueryDTOS.add(loadQueryDTO);
                }
            }
        }

        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(loadQueryDTOS);
        return baseResp;
    }


    /**
     * 查询节假日负荷预测填报
     */
    @ApiOperation("查询节假日负荷预测填报")
    @GetMapping(value = "/holiday")
    public BaseResp<List<LoadQueryDTO>> findHolidayReport(@ApiParam("假日ID") String holidayId,
        @ApiParam("非常规天") boolean veryDay) throws Exception {
//        BaseResp baseResp = BaseResp.succResp();
//
//        //如果是节假日当天
//        List<Date> veryHolidays = new ArrayList<>();
//        if (veryDay) {
//            if (StringUtils.isNotBlank(holidayId)) {
//                HolidayDO holidayVO = holidayService.findHolidayDOByPk(holidayId);
//                veryHolidays.add(holidayVO.getDate());
//            }
//        }
//        List<CityDO> allCitys = cityService.findAllCitys();
//        //String cityId = getDefaultCityId();
//        List<ReportLoadHolidayDO> holidayVOList = reportLoadHolidayService.findHolidayReport(null, holidayId, veryHolidays);
//
//        List<LoadQueryDTO> loadQueryDTOS = new ArrayList<>();
//
//        for(CityDO city:allCitys) {
//            //过滤掉地方点，直接用id过滤
//            if ("23".equals(city.getId())) {
//                break;
//            }
//            Boolean flag = true;
//            if (!CollectionUtils.isEmpty(holidayVOList)) {
//                for (ReportLoadHolidayDO holidayVO : holidayVOList) {
//                    if (city.getId().equals(holidayVO.getCityId())) {
//                        LoadQueryDTO loadQueryDTO = new LoadQueryDTO();
//                        loadQueryDTO.setCity(cityService.findCityById(holidayVO.getCityId()).getCity());
//                        BeanUtils.copyProperties(holidayVO, loadQueryDTO);
//                        loadQueryDTOS.add(loadQueryDTO);
//                        flag = false;
//                    }
//                }
//            }
//
//            if(flag){
//                for(Date date:veryHolidays){
//                LoadQueryDTO queryDTO = new LoadQueryDTO();
//                queryDTO.setDate(date);
//                queryDTO.setCity(city.getCity());
//                loadQueryDTOS.add(queryDTO);
//                }
//            }
//        }
//        baseResp.setData(loadQueryDTOS);
//        return baseResp;

        BaseResp baseResp = BaseResp.succResp();
        //如果是节假日当天
        List<Date> veryHolidays = new ArrayList<>();
        if (veryDay) {
            if (StringUtils.isNotBlank(holidayId)) {
                HolidayDO holidayVO = holidayService.findHolidayVOByPk(holidayId);
                veryHolidays.add(holidayVO.getDate());
            }
        } else {
            HolidayDO holidayVOByPk = holidayService.findHolidayVOByPk(holidayId);
            java.sql.Date startDate = holidayVOByPk.getStartDate();
            java.sql.Date endDate = holidayVOByPk.getEndDate();
            while (startDate.before(endDate)) {
                veryHolidays.add(startDate);
                Date date = new Date(startDate.getTime());
                startDate = new java.sql.Date(DateUtils.addDays(date, 1).getTime());
            }
            //因为用before比较日期默认是不包括endDate的
            veryHolidays.add(endDate);
        }
        List<LoadQueryDTO> loadQueryDTOS = new ArrayList<>();
        List<CityDO> allCitys = cityService.findAllCitys();
        for (CityDO cityVO : allCitys) {
            if ("23".equals(cityVO.getId())) {
                break;
            }
            for (Date date : veryHolidays) {
                List<Date> list = new ArrayList<>();
                list.add(date);
                List<ReportLoadHolidayDO> holidayVOList = reportLoadHolidayService
                    .findHolidayReport(cityVO.getId(), holidayId, list);
                if (CollectionUtils.isEmpty(holidayVOList)) {
                    LoadQueryDTO queryDTO = new LoadQueryDTO();
                    queryDTO.setDate(date);
                    queryDTO.setCity(cityVO.getCity());
                    loadQueryDTOS.add(queryDTO);
                } else {
                    LoadQueryDTO loadQueryDTO = new LoadQueryDTO();
                    loadQueryDTO.setCity(cityVO.getCity());
                    BeanUtils.copyProperties(holidayVOList.get(0), loadQueryDTO);
                    loadQueryDTOS.add(loadQueryDTO);
                }
            }
        }
        baseResp.setData(loadQueryDTOS);
        return baseResp;
    }


    private String getDefaultCityId() throws Exception {
        String cityId = getLoginCityId();
        CityDO cityVO = cityService.findCityById(cityId);
        if (cityVO.getType() == 1) {
            return null;
        }
        return cityId;
    }
}