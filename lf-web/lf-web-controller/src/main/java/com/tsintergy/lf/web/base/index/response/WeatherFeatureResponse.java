/**
 * Copyright(C),2015‐2021,北京清能互联科技有限公司
 * Author:yangjin
 * Date:2021/2/310:49
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.index.response;

import com.alibaba.fastjson.annotation.JSONField;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 *Description: <br>
 *
 *<AUTHOR>
 *@create 2021/2/3
 *@since 1.0.0
 */
@Data
public class WeatherFeatureResponse implements Serializable{

    private Date date;
    private BigDecimal maxTem;
    private BigDecimal minTem;
    private BigDecimal rainfull;
    private String windDirection;
    private BigDecimal windSpedd;
    private BigDecimal hum;
    private String cityName;

    private BigDecimal maxLoad;
    private BigDecimal minLoad;

    @JSONField(format = "yyyy-MM-dd HH:mm")
    private Date updatetime;


}