package com.tsintergy.lf.web.base.evalucation.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.base.evalucation.api.AccuracyAssessService;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyAssessDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyAssessDataDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyDistributionDTO;
import com.tsintergy.lf.serviceapi.base.recall.dto.StatisticsAccuracyDTO;
import com.tsintergy.lf.web.base.controller.BaseController;
import io.swagger.annotations.ApiOperation;
import java.util.Date;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 考核点准确率查询页面
 *
 * <AUTHOR>
 * @create 2024/4/14
 * @since 1.0.0
 */
@RequestMapping("/assess")
@RestController
public class AccuracyAssessController extends BaseController {

    @Autowired
    private AccuracyAssessService accuracyAssessService;

    @RequestMapping(value = "/accuracyEstimation", method = RequestMethod.GET)
    public BaseResp findAccuracyDTO(String cityId, String caliberId, String algorithmId, Date startDate,
        Date endDate,String batchIds, Integer day) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<AccuracyAssessDTO> dto = accuracyAssessService
            .findAccuracyDTO(cityId, caliberId, algorithmId, startDate, endDate,batchIds, day);
        baseResp.setData(dto);
        return baseResp;
    }


    /**
     * 名称列表
     */
    @ApiOperation("名称列表")
    @RequestMapping(value = "/findNameList", method = RequestMethod.GET)
    public BaseResp findNameList(String cityId, String caliberId, String algorithmId, Date startDate,
        Date endDate) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<String> nameList = accuracyAssessService
            .findNameList(cityId, caliberId, algorithmId, startDate, endDate);
        baseResp.setData(nameList);
        return baseResp;
    }

    /**
     * 准确率评估-准确率分布
     */
    @ApiOperation("准确率分布")
    @RequestMapping(value = "/accuracyDistribution", method = RequestMethod.GET)
    public BaseResp getListByCityIdAndDate(String cityId, String caliberId, String algorithmId, Date startDate,
        Date endDate, String filterName,String batchIds, Integer day) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<AccuracyDistributionDTO> accuracyDistribution = accuracyAssessService
            .findAccuracyDistribution(cityId, caliberId, algorithmId, filterName, startDate, endDate,batchIds, day);
        baseResp.setData(accuracyDistribution);
        return baseResp;
    }

    @RequestMapping(value = "/doCalculate", method = RequestMethod.GET)
    public BaseResp findAccuracyDTO(String cityId, String caliberId, String algorithmId, Date startDate,
        Date endDate) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        accuracyAssessService
            .doCalculateAssessAccuracy(cityId, caliberId, algorithmId, startDate, endDate);
        return baseResp;
    }

    @ApiOperation("回溯准确率结果")
    @RequestMapping(value = "/accuracyEstimationRecall", method = RequestMethod.GET)
    public BaseResp findAccuracyRecallList(String cityId, String caliberId, String algorithmId, Date startDate,
                                           Date endDate,String batchIds, Integer day){
        BaseResp baseResp = BaseResp.succResp();
        List<AccuracyAssessDTO> recallList = accuracyAssessService
                .findAccuracyAccessRecallList(cityId, caliberId, algorithmId, startDate, endDate, batchIds, day);
        baseResp.setData(recallList);
        return baseResp;
    }

    @ApiOperation("回溯准确率统计")
    @RequestMapping(value = "/statistics/accuracy", method = RequestMethod.GET)
    public BaseResp<StatisticsAccuracyDTO> statisticsAccuracy(String cityId, Date startDate, Date endDate, String algorithmId
            , String batchIds, String filterName, Integer day) {
        BaseResp baseResp = null;
        try {
            String caliberId = getCaliberId();
            StatisticsAccuracyDTO accuracyLoadFcRecallDTOS = accuracyAssessService
                    .getStatisticsAccuracyAccessRecall(cityId, caliberId, startDate, endDate, algorithmId, batchIds,
                            filterName, day);
            baseResp = BaseResp.succResp();
            baseResp.setData(accuracyLoadFcRecallDTOS);
        } catch (Exception e) {
            e.printStackTrace();
            baseResp = BaseResp.failResp("准确率统计失败");
        }
        return baseResp;
    }

    @RequestMapping(value = "/doCalculate/recall", method = RequestMethod.GET)
    public BaseResp findCalculateRecallAssessAccuracy(String cityId, String caliberId, String algorithmId, Date startDate,
                                    Date endDate) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        accuracyAssessService
                .doCalculateRecallAssessAccuracy(cityId, caliberId, algorithmId, startDate, endDate);
        return baseResp;
    }

}
