/**
 * Copyright (C), 2015‐2022, 北京清能互联科技有限公司 Author:  <EMAIL> Date:  2022/5/24 1:16 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.acload.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * Description: 基础负荷曲线 <br>
 *
 * <AUTHOR>
 * @create 2022/5/24
 * @since 1.0.0
 */
@ApiModel
@Data
public class BaseLoadCurveResp {

    @ApiModelProperty(value = "方案id", example = "123456")
    String solutionId;

    @ApiModelProperty(value = "96点曲线值")
    List<BigDecimal> dataValue;
}  
