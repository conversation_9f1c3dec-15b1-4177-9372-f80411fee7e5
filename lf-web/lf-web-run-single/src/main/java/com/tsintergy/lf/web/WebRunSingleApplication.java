package com.tsintergy.lf.web;

import com.tsieframework.cloud.security.serviceapi.system.api.SecurityService;
import com.tsieframework.cloud.security.serviceimpl.system.impl.SecurityServiceImpl;
import com.tsintergy.lf.web.annotation.LfBootApplication;
import com.ulisesbocchio.jasyptspringboot.environment.StandardEncryptableEnvironment;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.context.annotation.Bean;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @date 2020-02-18 11:00:43
 */
@LfBootApplication
public class WebRunSingleApplication {

    public static void main(String[] args) {
        new SpringApplicationBuilder()
            .environment(new StandardEncryptableEnvironment())
            .sources(WebRunSingleApplication.class)
            .run(args);
    }

//    @Bean
//    public MenuIdGenerator myMenuIdGeneraltor(){
//        return new MyMenuIdGeneraltor();
//    }


    @Bean
    public SecurityService securityService(){
        return new SecurityServiceImpl();
    }

}
