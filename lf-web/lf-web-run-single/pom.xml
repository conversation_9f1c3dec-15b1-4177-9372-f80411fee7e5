<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>lf-web</artifactId>
        <groupId>com.tsintergy.lf</groupId>
        <version>${lf.version}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>lf-web-run-single</artifactId>

    <dependencies>
        <!-- 第三方 -->
        <!-- swagger，访问地址doc.html -->
        <!-- tsie3.1;Knife4j替代swagger-bootstrap-ui-->
        <!--        <dependency>-->
        <!--            <groupId>com.github.xiaoymin</groupId>-->
        <!--            <artifactId>swagger-bootstrap-ui</artifactId>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>com.tsieframework.boot</groupId>
            <artifactId>tsie-boot-autoconfigure</artifactId>
            <version>3.1.0</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.tsintergy.algorithm</groupId>
            <artifactId>tsie-alginv-starter-web-run-single</artifactId>
        </dependency>


        <dependency>
            <groupId>com.tsieframework.cloud.security</groupId>
            <artifactId>tsie-cloud-security-starter-web-run-single</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>cas-client-autoconfig-support</artifactId>
                    <groupId>net.unicon.cas</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cas-client-support-saml</artifactId>
                    <groupId>org.jasig.cas.client</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cas-client-core</artifactId>
                    <groupId>org.jasig.cas.client</groupId>
                </exclusion>
            </exclusions>
            <!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <artifactId>tsie-cloud-security-i18n</artifactId>-->
<!--                    <groupId>com.tsieframework.cloud.security</groupId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
        </dependency>

        <!-- job -->
        <!--<dependency>-->
            <!--<groupId>com.tsintergy.job</groupId>-->
            <!--<artifactId>tsie-cloud-job-starter-web-run-single</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>com.tsintergy.job</groupId>-->
            <!--<artifactId>tsie-cloud-job-web-static</artifactId>-->
        <!--</dependency>-->
        <!-- 第三方 -->

        <dependency>
            <groupId>com.tsintergy.lf</groupId>
            <artifactId>lf-starter-web-run-single</artifactId>
        </dependency>

        <dependency>
            <groupId>com.tsintergy.lf</groupId>
            <artifactId>lf-config</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.tsintergy.lf</groupId>
            <artifactId>lf-web-static</artifactId>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>package-springboot</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <version>2.1.6.RELEASE</version>
                        <!--
                            配置依赖外放，当spring-boot-maven-plugin生成可执行包时不把lf-config模块打包在一起。
                            外放后的配置文件放到执行包同级目录的config目录下，最终结构为：
                            application.jar
                            config/
                                1.yaml
                                2.yaml
                         -->
                        <configuration>
                            <excludes>
                                <exclude>
                                    <groupId>com.tsintergy.lf</groupId>
                                    <artifactId>lf-config</artifactId>
                                </exclude>
                            </excludes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>package-docker</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.google.cloud.tools</groupId>
                        <artifactId>jib-maven-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>