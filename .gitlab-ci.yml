
# GitLab CI/CD 配置文件 - AI 代码审查集成 (仅MR环境)

stages:
  - ai-review
  - sonarqube-check

# AI 代码审查 - 仅在MR环境中执行
ai-code-review:
  stage: ai-review
  image: node:16
  tags:
    - dev  # 使用group runner #537
  only:
    - merge_requests
  script:
    - echo "🤖 开始 MR AI 代码审查..."
    - echo "  - 源分支：$CI_MERGE_REQUEST_SOURCE_BRANCH_NAME"
    - echo "  - 目标分支：$CI_MERGE_REQUEST_TARGET_BRANCH_NAME"
    - echo "  - 项目：$CI_PROJECT_NAME"
    # 设置目标分支
    - TARGET_BRANCH="origin/$CI_MERGE_REQUEST_TARGET_BRANCH_NAME"
    - echo "  - 比较目标：$TARGET_BRANCH"
    # 确保脚本有执行权限
    - chmod +x ai-code-review-gitlab.js
    # 设置Git配置（避免权限问题）
    - echo "🔄 设置Git配置..."
    # - git -v
    # # - git config --global --add safe.directory $CI_PROJECT_DIR
    - git config user.name "lyy"
    - git config user.email "<EMAIL>"
    - git fetch origin $CI_MERGE_REQUEST_TARGET_BRANCH_NAME
    # 执行AI代码审查，使用MR的目标分支进行对比
    - echo "🔍 开始分析MR代码变更（$CI_MERGE_REQUEST_SOURCE_BRANCH_NAME → $CI_MERGE_REQUEST_TARGET_BRANCH_NAME）..."
    - node ai-code-review-gitlab.js $TARGET_BRANCH
    - echo "✅ MR AI 代码审查完成"
  variables:
    # GitLab 环境变量
    GIT_DEPTH: 0  # 获取完整的Git历史
  allow_failure: true  # AI审查失败不阻塞MR
  timeout: 10m  # 设置超时时间
  retry:
    max: 2
    when:
      - api_failure
      - runner_system_failure



# SonarQube 检查
sonarqube-check:
  stage: sonarqube-check 
  image: maven:3.6.3-openjdk-8
  tags:
    - dev
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"
    GIT_DEPTH: 0
  only:
    - merge_requests
  script:
    - echo "🔍 开始执行 SonarQube 检查..."
    - mvn $MAVEN_CLI_OPTS verify org.sonarsource.scanner.maven:sonar-maven-plugin:3.11.0.3922:sonar \
      -Dsonar.projectKey=eo_prod_lf_lf_backend_AZVKt__kcNnLQmq7twh0 \
      -Dsonar.host.url=http://9000.grd2bd63.01pxwbcl.*************.nip.io \
      -Dsonar.login=**************************************** \
      -Dsonar.qualitygate.wait=true 
  allow_failure: false
# 配置缓存
cache:
  key: tsie-scc-common
  paths:
    - .m2/repository/
    - .sonar/cache

