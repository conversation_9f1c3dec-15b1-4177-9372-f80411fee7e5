
package com.tsintergy.lf.serviceapi.base.load.api;


import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsintergy.lf.serviceapi.base.load.dto.*;
import com.tsintergy.lf.serviceapi.base.load.pojo.BaseLoadIntervalDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version $Id: LoadCityHisService.java, v 0.1 2018-01-31 10:50:32 tao Exp $$
 */


public interface LoadCityHisService {

    List<LoadCityHisDO> findLoadCityHisDOS(String cityId, Date startDate, Date endDate, String caliberId)
        throws Exception;

    /**
     * search entity list
     *
     * @param param
     * @return
     * @throws BusinessException
     */
    public DataPackage queryLoadCityHisDO(DBQueryParam param) throws Exception;

    /**
     * create entity
     *
     * @param vo
     * @return
     * @throws Exception
     */
    public LoadCityHisDO doCreate(LoadCityHisDO vo) throws Exception;


    /**
     * delete entity by object
     *
     * @param vo
     * @throws Exception
     */
    public void doRemoveLoadCityHisDO(LoadCityHisDO vo) throws Exception;

    /**
     * delete entity by PK
     *
     * @param pk
     * @throws Exception
     */
    public void doRemoveLoadCityHisDOByPK(Serializable pk) throws Exception;

    /**
     * update entity object
     *
     * @param vo
     * @return
     * @throws Exception
     */
    public LoadCityHisDO doUpdateLoadCityHisDO(LoadCityHisDO vo) throws Exception;


    /**
     * find entity by PK
     *
     * @param pk
     * @return
     * @throws Exception
     */
    public LoadCityHisDO findLoadCityHisDOByPk(Serializable pk) throws Exception;

    /**
     * find 24loadCity by date and cityIds and caliberIds
     *
     * @param date
     * @param cityIds
     * @param caliberId
     * @return
     * @throws Exception
     */
    public List<CityValueDTO> find24LoadCityByDateAndCityIds(Date date, List<String> cityIds, String caliberId) throws Exception;

    /**
     * 查找指定日期、口径ID和城市ID的实际负荷曲线
     *
     * @param date 指定的日期
     * @param cityId 城市ID
     * @param caliberId 口径ID
     * @return 一条实际负荷曲线
     * @throws Exception 如果查询过程中出现错误，则抛出异常。
     */
    List<BigDecimal> findLoadCityHisDO(Date date, String cityId, String caliberId) throws Exception;

    List<BigDecimal> findLoadCityHis288DO(Date date, String cityId, String caliberId) throws Exception;

    List<BigDecimal> findLoadCityHisDO(String cityId,Date startDate, Date endDate ,String caliberId) throws Exception;

    /**
     * find entities by cityId in a period
     *
     * @param startDate
     * @param endDate
     * @param cityId
     * @param caliberId
     * @return
     * @throws Exception
     */
    List<LoadHisDataDTO> findLoadCityVOsByCityId(Date startDate, Date endDate, String cityId, String caliberId) throws Exception;

    /**
     * 获取指定日期的负荷曲线
     *
     * @param cityId
     * @param dates
     * @param caliberId
     * @return
     * @throws Exception
     */
    List<LoadCommonDTO<Date>> findLoadCityVOsByCityIdAndDates(String cityId, List<Date> dates, String caliberId) throws Exception;

    /**
     * 获取指定日期的历史负荷
     * @param cityId 城市id
     * @param dates 日期
     * @param caliberId 口径
     */
    List<LoadCityHisDO> findLoadCityDOsByCityIdInDates(String cityId, List<Date> dates, String caliberId) throws Exception;

    /**
     * 查询24点持续曲线
     *
     * @param cityId    城市ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param caliberId
     * @return
     * @throws Exception
     */
    CityValueDTO find24LoadCityBetweenDate(String cityId, Date startDate, Date endDate, String caliberId) throws Exception;

    /**
     * 批量更新96点数据
     *
     * @param loadHisDTOS
     * @return
     * @throws Exception
     */
    public List<LoadCityHisDO> doUpdateLoadCityHisDO(List<LoadHisDTO> loadHisDTOS, String caliberId) throws Exception;

    /**
     * 读取excel后
     *
     * @param dataList
     * @return
     * @throws BusinessException
     */
    public List<LoadCityHisDO> doUpdateLoadCityHisDOFromExcel(List<List<List<String>>> dataList) throws Exception;


    List<LoadCityHisDO> getLoadCityHisDOS(String cityId, String caliberId, Date startDate, Date endDate) throws Exception;

    void doInsertOrUpdate(LoadCityHisDO LoadCityHisDO) throws Exception;


    List<LoadCityHisDO> find24LoadCityHisDO(Date date, String cityId, String caliberId) throws Exception;


    /**
     * 预测查询--负荷预测
     * @param cityId
     * @param startDate
     * @param endDate
     * @param algorithmId
     * @return
     * @throws Exception
     */
    LoadQueryDTO findLoad(String cityId, String caliberId, Date startDate, Date endDate, String algorithmId, String batchId) throws Exception;

    List<CityLoadDTO> find24CityLoadDTO(Date today, List<String> cityIds, String caliberId) throws Exception;

    List<AreaLoadRateDTO> find24AreaLoadRateDTO(Date date, String caliberId) throws Exception;

    Date24LoadDTO findDate24LoadDTOS(Date date, String cityId,String caliberId) throws Exception;

    List<CurveViewDTO> findCurveViewDTOS(Date date, String caliberId, String cityId, Integer type, String dataType) throws Exception;

    /**
     * 查询基准日负荷
     * @param forecastDay
     * @param caliberId
     * @param cityId
     * @return
     */
    LoadCityHisDO getOne(Date date,String caliberId, String cityId) throws Exception;

    List<LoadCityHisDO> findLoadCityList(Date date, String caliberId);

    List<LoadCityHisDO> getListByCityDates(Date startDate,Date endDate, String cityId,String caliberId);


    List<LoadCityHisDO> getListByDates(Date startDate,Date endDate,String caliberId);

    List<BigDecimal> loadCityHisAccumulation(Date date, String caliberId) throws Exception;
    /**
     * what: 计算厂用电+网损值
     * <AUTHOR>
     * @date 2024/05/26 14:28
     */
    List<BigDecimal> getNetworkLoss(Date date);

    List<BigDecimal> getNetworkLossByDateList(List<Date> dates);

    LoadCityHisDO getLoadCityHisDO(String cityId, String caliberId, Date date) throws Exception;

    List<BaseLoadIntervalDO> selectListData(String cityId, String caliberId, Date startDate,
                                            Date endDate) throws Exception;

    void doInsertOrUpdateNotNull(LoadCityHisDO LoadCityHisDO) throws Exception;

}