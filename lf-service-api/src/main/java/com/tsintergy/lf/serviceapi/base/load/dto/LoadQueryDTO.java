package com.tsintergy.lf.serviceapi.base.load.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@ApiModel
public class LoadQueryDTO implements Serializable {
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "负荷实际值", example = "[32.3,32.3]")
    private List<BigDecimal> real;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "负荷上报值", example = "[32.3,32.3]")
    private List<BigDecimal> report;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "负荷预测值", example = "[32.3,32.3]")
    private List<BigDecimal> fc;

    /**
     * 昨日历史数据,只有在查寻一天的气象时才展示，查询多天气象不展示
     */
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "昨日历史数据")
    private List<BigDecimal> yesterday;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "营销负荷预测值", example = "[32.3,32.3]")
    private List<BigDecimal> fcYx;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "营销昨日历史数据")
    private List<BigDecimal> yesterdayYx;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "营销今日历史数据")
    private List<BigDecimal> todayYx;

    @ApiModelProperty(value = "算法id", example = "3")
    private String algorithmName;

    public String getAlgorithmName() {
        return algorithmName;
    }

    public void setAlgorithmName(String algorithmName) {
        this.algorithmName = algorithmName;
    }

    public List<BigDecimal> getReal() {
        return real;
    }

    public void setReal(List<BigDecimal> real) {
        this.real = real;
    }

    public List<BigDecimal> getReport() {
        return report;
    }

    public void setReport(List<BigDecimal> report) {
        this.report = report;
    }

    public List<BigDecimal> getFc() {
        return fc;
    }

    public void setFc(List<BigDecimal> fc) {
        this.fc = fc;
    }

    public List<BigDecimal> getYesterday() {
        return yesterday;
    }

    public void setYesterday(List<BigDecimal> yesterday) {
        this.yesterday = yesterday;
    }

    public List<BigDecimal> getFcYx() {
        return fcYx;
    }

    public void setFcYx(List<BigDecimal> fcYx) {
        this.fcYx = fcYx;
    }

    public List<BigDecimal> getYesterdayYx() {
        return yesterdayYx;
    }

    public void setYesterdayYx(List<BigDecimal> yesterdayYx) {
        this.yesterdayYx = yesterdayYx;
    }

    public List<BigDecimal> getTodayYx() {
        return todayYx;
    }

    public void setTodayYx(List<BigDecimal> todayYx) {
        this.todayYx = todayYx;
    }
}
