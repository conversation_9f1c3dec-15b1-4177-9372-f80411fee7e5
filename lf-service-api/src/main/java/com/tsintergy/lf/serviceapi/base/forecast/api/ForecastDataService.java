/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司
 * Author: liufeng
 * Date: 2018/6/5 9:09
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.forecast.api;

import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceapi.base.datamanage.pojo.DataCheckInfoDO;
import com.tsintergy.lf.serviceapi.base.forecast.dto.SearchDTO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHis288DO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisClctDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.typhoon.pojo.TyphoonArchiveDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.*;

import java.util.Date;
import java.util.List;

/**
 * Description: 预测数据业务接口 <br>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ForecastDataService {

    /**
     * 功能描述: 查询数据，<br>
     *
     * @param cityId    城市
     * @param caliberId 口径
     * @param start     开始日期
     * @param end       结束日期
     * @return 历史负荷集合
     * <AUTHOR>
     * @Date: 2018/6/5 9:13
     * @since 1.0.0
     */
    List<LoadCityHisDO> findHisLoad(String cityId, String caliberId, Date start, Date end) throws Exception;

    List<LoadFeatureCityDayHisDO> findLoadFeatureCityDayHisDOs(String cityId, Date startDate, Date endDate,
        String caliberId) throws Exception;

    List<WeatherFeatureCityDayHisDO> findWeatherFeature(String cityId, Date startDate, Date endDate,
        List<SearchDTO> screenData) throws Exception;

    List<LoadCityHis288DO> findHis288Load(String cityId, String caliberId, Date start, Date end) throws Exception;


    /**
     * 查询采集的历史数据
     *
     * @param cityId
     * @param caliberId
     * @param startDate
     * @param endDate
     * @return
     * @throws Exception
     */
    List<LoadCityHisClctDO> findHisLoadClct(String cityId, String caliberId, Date startDate, Date endDate) throws Exception;


    /**
     * 功能描述:查询历史气象信息 <br>
     *
     * @param cityId 城市
     * @param start  开始日期
     * @param end    结束日期
     * @return 历史气象信息
     * <AUTHOR>
     * @Date: 2018/6/5 9:17
     * @since 1.0.0
     */
    List<WeatherCityHisDO> findHisWeather(String cityId, Integer type, Date start, Date end) throws Exception;


    /**
     * 查询历史实感温度
     * @param cityId 城市
     * @param start 开始时间
     * @param end 结束时间
     * @param weatherType
     * @return 结果集
     */
    List<StatisticsSynthesizeWeatherCityDayHisDO> findSynthesizeHisWeather(String cityId, Date start, Date end,
                                                                           Integer weatherType)
            throws Exception;

    /**
     * 查询预测实感温度
     * @param cityId 城市
     * @param start 开始时间
     * @param end 结束时间
     * @param weatherType
     * @return 结果集
     */
    List<StatisticsSynthesizeWeatherCityDayFcDO> findSynthesizeFcWeather(String cityId, Date start, Date end,
                                                                         Integer weatherType)
            throws Exception;


    /**
     * 功能描述:查询预测气象信息 <br>
     *
     * @param cityId 城市
     * @param start  开始日期
     * @param end    结束日期
     * @return 历史气象信息
     * <AUTHOR>
     * @Date: 2018/6/5 9:17
     * @since 1.0.0
     */
    List<WeatherCityFcDO> findFcWeather(String cityId, Date start, Date end) throws Exception;

    /**
     * 气象局预测气象只有未来两天的数据，剩下的预测气象数据从电科院预测表中获取
     *
     * @param cityIds
     * @param start
     * @param end
     * @param existFcDOs
     * @return
     * @throws Exception
     */
    List<WeatherCityFcDO> fillMissingFcDataWithDiankeyuan(List<String> cityIds, Date start, Date end, List<WeatherCityFcDO> existFcDOs) throws Exception;

    /**
     * 功能描述: <br>
     * 查询一段时间的气象特性(实际)
     *
     * @param cityId    城市id
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return:
     * @since: 1.0.0
     * @Author:wangfeng
     * @Date: 2018/8/28 14:18
     */
    List<WeatherFeatureCityDayHisDO> findWeatherFeatureCityDayHisDO(String cityId, Date startDate, Date endDate) throws Exception;

    /**
     * 查询一段时间的预测气象特性（预测）
     *
     * @param cityId
     * @param startDate
     * @param endDate
     * @return
     * @throws Exception
     */
    List<WeatherFeatureCityDayFcDO> findWeatherFeatureCityDayFcDO(String cityId, Date startDate, Date endDate) throws Exception;


    LoadCityFcDO doCreate(LoadCityFcDO vo) throws Exception;


    LoadCityFcDO findLoadCityFc(String cityId, Date date, String caliberId,
                                String algorithmId) throws Exception;

    void removeLoadFc(LoadCityFcDO vo) throws Exception;

     CityDO findCityDOByCityId(String cityId) throws Exception;

     List<CityDO> findAllCity() throws Exception;

     List<HolidayDO> findAllHolidays() throws Exception;

     void doCreateDataCheck(DataCheckInfoDO dataCheckInfoDO) throws Exception;


    List<DataCheckInfoDO> findCheckInfoVOS(java.sql.Date startDate, java.sql.Date endDate, String cityId,
                                           String caliberId) throws Exception;

    void doRemoveDataCheckInfoDO(DataCheckInfoDO checkInfoVo) throws Exception;

    /**
     * 查询最近一段时间风速大于36的台风(12级)
     */
    List<TyphoonArchiveDO> getListRiskTyphoon(Date startDay, Date endDate) throws Exception;

    /**
     * 如果目标日期的实际气象缺点，使用预测气象补点
     *
     * @param weatherCityHisDOS
     * @return
     * @throws Exception
     */
    List<WeatherCityHisDO> mergeHisAndFcWeather(List<WeatherCityHisDO> weatherCityHisDOS) throws Exception;

    /**
     * 获取历史气象数据，需要使用气象局实际气象拼接防灾减灾网格实际气象
     *
     * @param cityIds
     * @param type
     * @param start
     * @param end
     * @return
     * @throws Exception
     */
    List<WeatherCityHisDO> getHisWeather(List<String> cityIds, Integer type, Date start, Date end) throws Exception;

    /**
     * 获取网格标准站点预报气象
     *
     * @param type
     * @param start
     * @param end
     * @return
     */
    List<WeatherCityFcDO> getFcWeather(Integer type, Date start, Date end);

    List<WeatherCityHisDO> getProvinceWeightAvgHisWeatherData(Integer type, Date start, Date end) throws Exception;

    List<WeatherCityHisDO> getCityAvgHisWeatherData(Integer type, Date start, Date end) throws Exception;

    List<WeatherCityHisDO> getCityAvgHisEffectiveWeatherData(Integer type, Date start, Date end) throws Exception;
}

