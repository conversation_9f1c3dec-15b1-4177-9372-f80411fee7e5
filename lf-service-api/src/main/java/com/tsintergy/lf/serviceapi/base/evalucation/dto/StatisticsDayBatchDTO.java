package com.tsintergy.lf.serviceapi.base.evalucation.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 多批次准确率结果对象
 */
@Data
public class StatisticsDayBatchDTO implements Serializable {

    private Date date;

    private String algoForeTime;

    private String batch;

    private String algorithm;

    private String algorithmId;

    private BigDecimal accuracy;

}
