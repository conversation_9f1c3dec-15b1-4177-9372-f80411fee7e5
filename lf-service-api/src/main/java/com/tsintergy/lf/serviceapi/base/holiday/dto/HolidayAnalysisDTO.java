/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  gss Date:  2019/6/26 9:31 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.holiday.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 节假日分析设置年数据
 *
 * <AUTHOR>
 * @create 2019/6/26
 * @since 1.0.0
 */
@ApiModel
public class HolidayAnalysisDTO implements Serializable {

    private static final long serialVersionUID = -7019473697870984446L;

    /**
     * 负荷曲线
     */
    @ApiModelProperty(value = "负荷曲线",example = "33.3,23,1,23,2")
    private List<List<BigDecimal>> holidayLoad;

    /**
     * 特性波动降幅
     */
    @ApiModelProperty(value = "特性波动降幅",example = "33.3,23,1,23,2")
    private List<List<BigDecimal>> fluctuate;

    /**
     * 特性波动平均值曲线
     */
    @ApiModelProperty(value = "特性波动平均值曲线",example = "33.3,23,1,23,2")
    private List<BigDecimal> avgFluctuate;

    /**
     * 降幅列表横坐标 T-1,T-2
     */
    @ApiModelProperty(value = "降幅列表横坐标 T-1,T-2",example = "T+1,T+2")
    private List<String> xData;

    /**
     * 年份列表
     */
    @ApiModelProperty(value = "年份列表",example = "2017.2018,2019")
    private List<String> yearList;

    /**
     * 不同年 假日当天波动比例
     */
    @ApiModelProperty(value = "假日当天波动比例",example = "33.3,23,1,23,2")
    private List<List<String>> eachFluctuateData;


    /**
     * 不同年 假日当天日最大/最小负荷
     */
    @ApiModelProperty(value = "假日当天日最大/最小负荷",example = "33.3,23,1,23,2")
    private List<List<String>> eachPowerData;

    public List<List<String>> getEachFluctuateData() {
        return eachFluctuateData;
    }

    public void setEachFluctuateData(List<List<String>> eachFluctuateData) {
        this.eachFluctuateData = eachFluctuateData;
    }

    public List<List<String>> getEachPowerData() {
        return eachPowerData;
    }

    public void setEachLoadData(List<List<String>> eachPowerData) {
        this.eachPowerData = eachPowerData;
    }

    public List<List<BigDecimal>> getHolidayLoad() {
        return holidayLoad;
    }

    public void setHolidayLoad(List<List<BigDecimal>> holidayLoad) {
        this.holidayLoad = holidayLoad;
    }

    public List<List<BigDecimal>> getFluctuate() {
        return fluctuate;
    }

    public void setFluctuate(List<List<BigDecimal>> fluctuate) {
        this.fluctuate = fluctuate;
    }

    public List<BigDecimal> getAvgFluctuate() {
        return avgFluctuate;
    }

    public void setAvgFluctuate(List<BigDecimal> avgFluctuate) {
        this.avgFluctuate = avgFluctuate;
    }

    public List<String> getxData() {
        return xData;
    }

    public void setxData(List<String> xData) {
        this.xData = xData;
    }

    public List<String> getYearList() {
        return yearList;
    }

    public void setYearList(List<String> yearList) {
        this.yearList = yearList;
    }

    @Override
    public String toString() {
        return "HolidayAnalysisDTO{" +
            "holidayLoad=" + holidayLoad +
            ", fluctuate=" + fluctuate +
            ", avgFluctuate=" + avgFluctuate +
            ", xData=" + xData +
            ", yearList=" + yearList +
            ", eachFluctuateData=" + eachFluctuateData +
            ", eachPowerData=" + eachPowerData +
            '}';
    }
}
