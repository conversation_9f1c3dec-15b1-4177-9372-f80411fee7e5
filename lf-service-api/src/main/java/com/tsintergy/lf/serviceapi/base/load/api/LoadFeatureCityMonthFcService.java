/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  wangchen Date:  2020/4/19 16:12 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.load.api;

import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityMonthFcDO;
import java.util.List;

/**
 * 统计 周预测特性
 *
 * <AUTHOR>
 * @create 2020/7/6
 * @since 1.0.0
 */
public interface LoadFeatureCityMonthFcService {

    void doSaveOrUpdateLoadFeatureCityMonthFcDOS(List<LoadFeatureCityMonthFcDO> monthFcVOS);


     List<LoadFeatureCityMonthFcDO> findLoadFeatureMonth(String cityId, String algorithmId,
         String startYm,
         String endYm, String caliberId) throws Exception;


    /**
     * 月 负荷特性   预测  计算逻辑
     */
    List<LoadFeatureCityMonthFcDO> statisticsMonthFeatures(List<LoadFeatureCityDayFcDO> monthFcVOS)
        throws Exception;
}
