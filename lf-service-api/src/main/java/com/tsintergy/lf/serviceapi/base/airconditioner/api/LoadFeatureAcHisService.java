/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.airconditioner.api;

import com.tsintergy.lf.serviceapi.base.airconditioner.dto.AcFeatureAnalyseDTO;
import com.tsintergy.lf.serviceapi.base.airconditioner.dto.AcFeatureStatisticsDTO;
import com.tsintergy.lf.serviceapi.base.airconditioner.dto.AcLoadCurveDTO;
import java.util.Date;
import java.util.List;

/**
 * Description:<br>
 *
 * @Author:<EMAIL>
 * @Date: 2022/5/23 15:12
 * @Version:1.0.0
 */
public interface LoadFeatureAcHisService {

    List<AcFeatureAnalyseDTO> getLoadFeatureAcHis(String cityId, String caliberId, Date startDate,
        Date endDate, String loadType, String weatherType, String weatherId) throws Exception;


    AcFeatureStatisticsDTO doStatisticsAcFeature(String cityId, String caliberId, Date date1, String loadType,
                                                 String weatherType, String weatherId) throws Exception;

    AcLoadCurveDTO getLoadCurve(String cityId, String caliberId, Date startDate, Date endDate, String weatherId);

    void doHisLoadFeatureCityDay(Date startDate, Date endDate) throws Exception;
}