
package com.tsintergy.lf.serviceapi.base.evalucation.api;

import com.tsintergy.lf.serviceapi.base.evalucation.dto.CityAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.PredictionAssessmentDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.WeatherDayDeviationDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.WeatherPreAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.forecast.dto.AlgorithmResultStatDTO;

import java.util.Date;
import java.util.List;

/**
 * 预测后评估服务
 * <AUTHOR>
 * @version $Id: EvalucationService.java, v 0.1 2018-01-31 10:15:11 tao Exp $$
 */

public interface EvalucationService {

    /**
     * 获取指定时间段指定城市的算法预测精度
     * @param cityId 城市ID
     * @param caliberId 口径ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param startPeriod 开始时段
     * @param endPeriod 结束时段
     * @return
     */
    List<CityAccuracyDTO> getCityAccuracy(String cityId, String caliberId, Date startDate, Date endDate,
                                          String startPeriod, String endPeriod, String isHoliday,String batchId);


    /**
     * 获取指定时间段指定城市的算法预测结果统计数据
     * @param cityId 城市ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param startPeriod 开始时段
     * @param endPeriod 结束时段
     * @param isHoliday 是否包含节假日，1包含，0不包含
     * @return
     */
    List<AlgorithmResultStatDTO> getAlgorithmFcStat(String cityId, String caliberId, Date startDate, Date endDate,
                                                    String startPeriod, String endPeriod, String isHoliday, String batchId) throws Exception;



    List<PredictionAssessmentDTO> findWeatherPredictionAccuracyBaseByDay(String cityId, Date startTime, Date endTime,
        String type) throws Exception;


    WeatherPreAccuracyDTO getAccuracyStatiesByDate(String cityId, Date startDate, Date endDate, String type) throws Exception;

    List<WeatherDayDeviationDTO> getDayDeviation(String cityId, String startDate, String endDate, String type) throws Exception;
}