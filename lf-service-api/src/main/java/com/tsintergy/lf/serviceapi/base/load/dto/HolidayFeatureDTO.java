/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司
 * Author:  wang<PERSON>
 * Date:  2020/3/4 1:59
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.load.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/3/4
 * @since 1.0.0
 */
@ApiModel
public class HolidayFeatureDTO implements Serializable {

    @ApiModelProperty(value = "日期",example = "2021-03-17")
    private Date date;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "早高峰",example = "2021")
    private BigDecimal mMax;
    @ApiModelProperty(value = "早高峰时刻",example = "0015")
    private String mMaxTime;
    @ApiModelProperty(value = "早环比增长",example = "12")
    private BigDecimal mLink;
    @ApiModelProperty(value = "早同比增长",example = "2321")
    private BigDecimal mBasics;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "午高峰",example = "2021")
    private BigDecimal aMax;
    @ApiModelProperty(value = "午高峰时刻",example = "1205")
    private String aMaxTime;
    @ApiModelProperty(value = "午环比增长",example = "21")
    private BigDecimal aLink;
    @ApiModelProperty(value = "午同比增长",example = "321")
    private BigDecimal aBasics;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "晚高峰",example = "2121")
    private BigDecimal eMax;
    @ApiModelProperty(value = "晚高峰时刻",example = "2025")
    private String eMaxTime;
    @ApiModelProperty(value = "晚环比增长",example = "213")
    private BigDecimal eLink;
    @ApiModelProperty(value = "晚同比增长",example = "1221")
    private BigDecimal eBasics;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "夜高峰",example = "2021")
    private BigDecimal nMin;
    @ApiModelProperty(value = "夜高峰时刻",example = "2345")
    private String nMinTime;
    @ApiModelProperty(value = "夜环比增长",example = "2021")
    private BigDecimal nLink;
    @ApiModelProperty(value = "夜同比增长",example = "2021")
    private BigDecimal nBasics;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "偏差",example = "12")
    private BigDecimal difference;


    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public BigDecimal getmMax() {
        return mMax;
    }

    public void setmMax(BigDecimal mMax) {
        this.mMax = mMax;
    }

    public String getmMaxTime() {
        return mMaxTime;
    }

    public void setmMaxTime(String mMaxTime) {
        this.mMaxTime = mMaxTime;
    }

    public BigDecimal getmLink() {
        return mLink;
    }

    public void setmLink(BigDecimal mLink) {
        this.mLink = mLink;
    }

    public BigDecimal getmBasics() {
        return mBasics;
    }

    public void setmBasics(BigDecimal mBasics) {
        this.mBasics = mBasics;
    }

    public BigDecimal getaMax() {
        return aMax;
    }

    public void setaMax(BigDecimal aMax) {
        this.aMax = aMax;
    }

    public String getaMaxTime() {
        return aMaxTime;
    }

    public void setaMaxTime(String aMaxTime) {
        this.aMaxTime = aMaxTime;
    }

    public BigDecimal getaLink() {
        return aLink;
    }

    public void setaLink(BigDecimal aLink) {
        this.aLink = aLink;
    }

    public BigDecimal getaBasics() {
        return aBasics;
    }

    public void setaBasics(BigDecimal aBasics) {
        this.aBasics = aBasics;
    }

    public BigDecimal geteMax() {
        return eMax;
    }

    public void seteMax(BigDecimal eMax) {
        this.eMax = eMax;
    }

    public String geteMaxTime() {
        return eMaxTime;
    }

    public void seteMaxTime(String eMaxTime) {
        this.eMaxTime = eMaxTime;
    }

    public BigDecimal geteLink() {
        return eLink;
    }

    public void seteLink(BigDecimal eLink) {
        this.eLink = eLink;
    }

    public BigDecimal geteBasics() {
        return eBasics;
    }

    public void seteBasics(BigDecimal eBasics) {
        this.eBasics = eBasics;
    }

    public BigDecimal getnMin() {
        return nMin;
    }

    public void setnMin(BigDecimal nMin) {
        this.nMin = nMin;
    }

    public String getnMinTime() {
        return nMinTime;
    }

    public void setnMinTime(String nMinTime) {
        this.nMinTime = nMinTime;
    }

    public BigDecimal getnLink() {
        return nLink;
    }

    public void setnLink(BigDecimal nLink) {
        this.nLink = nLink;
    }

    public BigDecimal getnBasics() {
        return nBasics;
    }

    public void setnBasics(BigDecimal nBasics) {
        this.nBasics = nBasics;
    }

    public BigDecimal getDifference() {
        return difference;
    }

    public void setDifference(BigDecimal difference) {
        this.difference = difference;
    }
}