/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  wang<PERSON>
 * Date:  2019/7/1 10:50
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.holiday.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/7/1
 * @since 1.0.0
 */
@ApiModel
public class HolidayCurveDTO implements Serializable {

    @ApiModelProperty(value = "日期",example = "2021-03-17")
    private Date date;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "预测曲线数值")
    private List<BigDecimal> forecast;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "人工修改后的曲线数值")
    private List<BigDecimal> modify;

    @ApiModelProperty(value = "数据日期",example = "2021-03-17")
    private Date normalDate;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "负荷数值")
    private List<BigDecimal> normalLoad;

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public List<BigDecimal> getForecast() {
        return forecast;
    }

    public void setForecast(List<BigDecimal> forecast) {
        this.forecast = forecast;
    }

    public List<BigDecimal> getModify() {
        return modify;
    }

    public void setModify(List<BigDecimal> modify) {
        this.modify = modify;
    }

    public Date getNormalDate() {
        return normalDate;
    }

    public void setNormalDate(Date normalDate) {
        this.normalDate = normalDate;
    }

    public List<BigDecimal> getNormalLoad() {
        return normalLoad;
    }

    public void setNormalLoad(List<BigDecimal> normalLoad) {
        this.normalLoad = normalLoad;
    }
}