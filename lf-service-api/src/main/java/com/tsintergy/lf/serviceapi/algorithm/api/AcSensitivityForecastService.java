/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.algorithm.api;

import com.tsintergy.aif.algorithm.serviceapi.base.dto.sensitivity.SensitivityResult;
import com.tsintergy.lf.serviceapi.base.forecast.dto.AcSensitivityAlgorithmDTO;

/**
 * Description:<br>
 *
 * @Author:<EMAIL>
 * @Date: 2022/5/24 21:31
 * @Version:1.0.0
 */
public interface AcSensitivityForecastService {

    SensitivityResult doAcSensitivityAlgorithm(AcSensitivityAlgorithmDTO algorithmDTO);
}