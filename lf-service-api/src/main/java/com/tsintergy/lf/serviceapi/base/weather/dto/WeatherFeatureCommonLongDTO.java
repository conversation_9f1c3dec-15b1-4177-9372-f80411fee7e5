/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.weather.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/5/19 9:29
 * @Version: 1.0.0
 */
@Data
public class WeatherFeatureCommonLongDTO implements Serializable {
    private String id;
    @BigdecimalJsonFormat(scale = 2)
    private BigDecimal lowestTemperature;
    @BigdecimalJsonFormat(scale = 2)
    private BigDecimal avgLoad;
    @BigdecimalJsonFormat(scale = 2)
    private BigDecimal minLoad;
    @BigdecimalJsonFormat(scale = 2)
    private BigDecimal maxLoad;
    private String week;
    private String cityId;
    @BigdecimalJsonFormat(scale = 2)
    private BigDecimal dayEnergy;
    @BigdecimalJsonFormat(scale = 2)
    private BigDecimal highestTemperature;
    private String date;
    private Date startDate;
    private Date endDate;
}