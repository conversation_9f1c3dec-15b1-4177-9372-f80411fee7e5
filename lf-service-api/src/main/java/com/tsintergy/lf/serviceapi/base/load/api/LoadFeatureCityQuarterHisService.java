
package com.tsintergy.lf.serviceapi.base.load.api;


import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureExtendDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityQuarterHisDO;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $Id: LoadFeatureCityQuarterHisService.java, v 0.1 2018-01-31 10:51:57 tao Exp $$
 */

public interface LoadFeatureCityQuarterHisService {
     /**
     *  search entity list
     * @param param
     * @return
     * @throws BusinessException
     */
    public DataPackage queryLoadFeatureCityQuarterHisVO(DBQueryParam param) throws Exception;
    
     /**
     * create entity
     * @param vo
     * @return
     * @throws Exception
     */
    public LoadFeatureCityQuarterHisDO doCreate(LoadFeatureCityQuarterHisDO vo) throws Exception;

    /**
     *  delete entity by object
     * @param vo
     * @throws Exception
     */
    public void doRemoveLoadFeatureCityQuarterHisVO(LoadFeatureCityQuarterHisDO vo) throws Exception;

    /**
     * delete entity by PK
     * @param pk
     * @throws Exception
     */
    public void doRemoveLoadFeatureCityQuarterHisVOByPK(Serializable pk) throws Exception;

    /**
     * update entity object
     * @param vo
     * @return
     * @throws Exception
     */
    public LoadFeatureCityQuarterHisDO doUpdateLoadFeatureCityQuarterHisVO(LoadFeatureCityQuarterHisDO vo) throws Exception;

    /**
     * find entity by PK
     * @param pk
     * @return
     * @throws Exception
     */
    public LoadFeatureCityQuarterHisDO findLoadFeatureCityQuarterHisVOByPk(Serializable pk) throws Exception;

    /**
     * 查询季负荷特性
     * @param cityId
     * @param year
     * @param quarter
     * @param caliberId
     * @return
     * @throws Exception
     */
    public LoadFeatureCityQuarterHisDO getLoadFeatureCityQuarterHisVO(String cityId, String year, String quarter,
        String caliberId) throws Exception;


    /**
     * 获取季负荷特性
     * @param cityId 城市ID
     * @param startYQ 开始日期，yyyy-q
     * @param endYQ 结束日期，yyyy-q
     * @param caliberId 口径ID
     * @return
     * @throws Exception
     */
    public List<LoadFeatureCityQuarterHisDO> getLoadFeatureCityQuarterHisVOS(String cityId, String startYQ,
        String endYQ, String caliberId) throws Exception;



    /**
     * 查询负荷K线数据
     * @param cityId
     * @param startDate
     * @param endDate
     * @param caliberId
     * @return
     * @throws Exception
     */
    public List<LoadFeatureExtendDTO> findLoadFeatureExtendDTOS(String cityId, Date startDate, Date endDate,
                                                                String caliberId) throws Exception;

}