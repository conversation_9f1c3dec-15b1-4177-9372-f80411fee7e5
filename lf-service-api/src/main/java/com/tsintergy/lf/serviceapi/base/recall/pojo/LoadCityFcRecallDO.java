/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.recall.pojo;

import com.tsintergy.lf.serviceapi.base.base.pojo.BaseLoadFcCityDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.sql.Timestamp;

/**
 * Description: 预测回溯 <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/1 12:05
 * @Version: 1.0.0
 */
@Data
@Entity
@Table(name = "load_city_fc_recall")
public class LoadCityFcRecallDO extends BaseLoadFcCityDO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_GENERATOR)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    @ApiModelProperty(value = "id")
    private String id;


    /**
     * 算法ID
     */
    @ApiModelProperty(value = "算法ID")
    @Column(name = "algorithm_id")
    private String algorithmId;


    /**
     * 是否为系统推荐的算法
     */
    @ApiModelProperty(value = "是否为系统推荐的算法")
    @Column(name = "recommend")
    private Boolean recommend;

    /**
     * 是否上报结果
     */
    @ApiModelProperty(value = "是否上报结果")
    @Column(name = "report")
    private Boolean report;

    /**
     * 操作者
     */
    @ApiModelProperty(value = "操作者")
    @Column(name = "user_id")
    private String userId;

    /**
     * 是否上报成功
     */
    @ApiModelProperty(value = "是否上报成功")
    @Column(name = "succeed")
    private Boolean succeed;

    @Column(name = "report_time")
    @ApiModelProperty(value = "上报时间")
    private Timestamp reportTime;

}