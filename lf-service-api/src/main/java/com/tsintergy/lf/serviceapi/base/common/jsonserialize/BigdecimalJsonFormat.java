/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司
 * Author:  wang<PERSON>
 * Date:  2020/11/5 1:45
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.common.jsonserialize;

import org.springframework.stereotype.Component;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.math.BigDecimal;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/11/5
 * @since 2.0.0
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD,ElementType.PARAMETER})
@Component
public @interface BigdecimalJsonFormat {


    /**
     *
     * 设置保留小数位，默认保留两位
     */
    int scale() default 2;

    /**
     *
     * 设置四舍五入规则,默认的是截掉
     */
    int roundingMode() default BigDecimal.ROUND_HALF_DOWN;


    /**
     * 设置百分比转换（乘1 或者乘100）
     * 默认是乘1，如果小数需要转百分比，则乘100
     *
     * @return
     */
    double percentConvert() default 1;


}