/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.weather.api;

import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherFeatureCityDayLongListVO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherFeatureLongDTO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayLongFcDO;
import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/5/17 15:03
 * @Version: 1.0.0
 */
public interface WeatherFeatureCityDayLongFcService {
    WeatherFeatureLongDTO findMonthFcPageByParam(String cityId, Date startDate, Date endDate, String caliberId) throws Exception;
    List<WeatherFeatureCityDayLongFcDO> findByParam(String cityId, Date startDate,Date endDate) throws Exception;

    void saveOrUpdate(WeatherFeatureCityDayLongFcDO weatherFeatureCityDayLongFcDO) throws Exception;
    void saveOrUpdate(WeatherFeatureCityDayLongListVO weatherFeatureCityDayLongListVO) throws Exception;
}
