/**
 * Copyright (C), 2015‐2022, 北京清能互联科技有限公司 Author:  <EMAIL> Date:  2022/5/24 9:59 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.airconditioner.pojo;

import com.tsieframework.core.base.dao.BaseDO;
import com.tsieframework.core.base.vo.annotation.EntityUniqueIndex;
import com.tsintergy.lf.serviceapi.base.common.enumeration.DateType2;
import com.tsintergy.lf.serviceapi.base.common.enumeration.TemperatureIndex;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2022/5/24
 * @since 1.0.0
 */
@Data
@Entity
@EntityUniqueIndex({"cityId","caliberId","solutionYear","season","dateType","weatherType"})
@Table(name = "load_solution_manage")
public class LoadSolutionManageDO implements BaseDO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    @ApiModelProperty(value = "城市id")
    @Column(name = "city_id")
    String cityId;

    @ApiModelProperty(value = "口径id")
    @Column(name = "caliber_id")
    String caliberId;

    @ApiModelProperty(value = "年份", example = "2022")
    @Column(name = "solution_year")
    private String solutionYear;

    @ApiModelProperty(value = "季节", example = "2022")
    @Column(name = "season")
    private String season;

    @ApiModelProperty(value = "日期类型", example = "1")
    @Column(name = "date_type")
    @Convert(converter = DateType2.DateType2Convertor.class)
    private DateType2 dateType;

    @ApiModelProperty(value = "春季目标范围开始日期", example = "2022-03-01")
    @Column(name = "spring_target_range_start_date")
    private Date springTargetRangeStartDate;

    @ApiModelProperty(value = "春季目标范围结束日期", example = "2022-04-30")
    @Column(name = "spring_target_range_end_date")
    private Date springTargetRangeEndDate;

    @ApiModelProperty(value = "秋季目标范围开始日期", example = "2022-09-01")
    @Column(name = "fall_target_range_start_date")
    private Date fallTargetRangeStartDate;

    @ApiModelProperty(value = "秋季目标范围开始日期", example = "2022-10-31")
    @Column(name = "fall_target_range_end_date")
    private Date fallTargetRangeEndDate;

    @ApiModelProperty(value = "温度指标", example = "MAX_TEMPERATURE")
    @Column(name = "temperature_index")
    private TemperatureIndex temperatureIndex;

    @ApiModelProperty(value = "温度范围开始(℃)", example = "20")
    @Column(name = "temperature_range_start")
    private BigDecimal TemperatureRangeStart;

    @ApiModelProperty(value = "温度范围结束(℃)", example = "25")
    @Column(name = "temperature_range_end")
    private BigDecimal TemperatureRangeEnd;

    @ApiModelProperty(value = "是否限制降水", example = "true")
    @Column(name = "limit_rain")
    private Boolean limitRain;

    @ApiModelProperty(value = "最大降水量(mm)", example = "0.5")
    @Column(name = "max_rain")
    private BigDecimal maxRain;

    @ApiModelProperty(value = "是否应用基础负荷曲线", example = "true")
    @Column(name = "enable_curve")
    private Boolean enableCurve;

    @ApiModelProperty(value = "创建时间")
    @Column(name = "create_time")
    @CreationTimestamp
    private Timestamp createTime;

    @ApiModelProperty(value = "更新时间")
    @Column(name = "update_time")
    @UpdateTimestamp
    private Timestamp updateTime;

    @Column(name = "weather_type")
    private Integer weatherType;
}
