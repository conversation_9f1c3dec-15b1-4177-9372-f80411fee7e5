
package com.tsintergy.lf.serviceapi.base.load.api;


import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureExtendDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityMonthHisDO;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $Id: LoadFeatureCityMonthHisService.java, v 0.1 2018-01-31 10:51:41 tao Exp $$
 */

public interface LoadFeatureCityMonthHisService {
     /**
     *  search entity list
     * @param param
     * @return
     * @throws BusinessException
     */
    public DataPackage queryLoadFeatureCityMonthHisDO(DBQueryParam param) throws Exception;
    
     /**
     * create entity
     * @param vo
     * @return
     * @throws Exception
     */
    public LoadFeatureCityMonthHisDO doCreate(LoadFeatureCityMonthHisDO vo) throws Exception;

    /**
     * delete entity by object
     * @param vo
     * @throws Exception
     */
    public void doRemoveLoadFeatureCityMonthHisDO(LoadFeatureCityMonthHisDO vo) throws Exception;

    /**
     * delete entity by PK
     * @param pk
     * @throws Exception
     */
    public void doRemoveLoadFeatureCityMonthHisDOByPK(Serializable pk) throws Exception;

    /**
     * update entity object
     * @param vo
     * @return
     * @throws Exception
     */
    public LoadFeatureCityMonthHisDO doUpdateLoadFeatureCityMonthHisDO(LoadFeatureCityMonthHisDO vo) throws Exception;

    /**
     * find entity by PK
     * @param pk
     * @return
     * @throws Exception
     */
    public LoadFeatureCityMonthHisDO findLoadFeatureCityMonthHisDOByPk(Serializable pk) throws Exception;

    /**
     * 获取月负荷特性
     * @param cityId 城市ID
     * @param year 年(yyyy)
     * @param month 月(MM)
     * @param caliberId 口径
     * @return
     * @throws Exception
     */
    public LoadFeatureCityMonthHisDO getLoadFeatureCityMonthHisDO(String cityId, String year, String month,
        String caliberId) throws Exception;

    List<LoadFeatureCityMonthHisDO> getLoadFeatureCityMonthHisDOList(String cityId, String year, String month,
        String caliberId) throws Exception;

    /**
     * 获取月负荷特性
     * @param cityId
     * @param startYM
     * @param endYM
     * @param caliberId
     * @return
     * @throws Exception
     */
    List<LoadFeatureCityMonthHisDO> getLoadFeatureCityMonthHisDOS(String cityId, String startYM, String endYM,
        String caliberId) throws Exception;


    /**
     * 获取月负荷特性
     * @param cityId
     * @param startYM,yyyy-MM
     * @param endYM,yyyy-MM
     * @param caliberId
     * @return
     * @throws Exception
     */
    List<LoadFeatureDTO> findMonthLoadFeatureDTOS(String cityId, String startYM, String endYM, String caliberId) throws Exception;

    /**
     * 查询负荷K线数据
     * @param cityId
     * @param startDate
     * @param endDate
     * @return
     * @throws Exception
     */
    List<LoadFeatureExtendDTO> findLoadFeatureExtendDTOS(String cityId, Date startDate, Date endDate) throws Exception;

    /**
     * 查询负荷K线数据
     * @param cityId
     * @param startDate
     * @param endDate
     * @param caliberId
     * @return
     * @throws Exception
     */
    public List<LoadFeatureExtendDTO> findLoadFeatureExtendDTOS(String cityId, Date startDate, Date endDate,
        String caliberId) throws Exception;

    List<LoadFeatureCityMonthHisDO> getLoadFeatureCityMonthHisVOByYear(String cityId, String year, String caliberId) throws Exception;


}