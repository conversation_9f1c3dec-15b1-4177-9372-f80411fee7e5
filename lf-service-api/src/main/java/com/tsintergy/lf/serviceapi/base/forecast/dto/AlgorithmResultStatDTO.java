package com.tsintergy.lf.serviceapi.base.forecast.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 预测算法结果统计数据DTO
 * User:taojingui
 * Date:18-2-8
 * Time:上午11:46
 */
@ApiModel
public class AlgorithmResultStatDTO implements Serializable{

    /**
     * 算法ID
     */
    @ApiModelProperty(value = "算法ID",example = "3")
    private String algorithmId;

    /**
     * 算法名称
     */
    @ApiModelProperty(value = "算法名称",example = "最相似日")
    private String name;

    /**
     * 准确率
     */
    @ApiModelProperty(value = "准确率",example = "0.33")
    private BigDecimal accuracy;

    /**
     * 离散率
     */
    @ApiModelProperty(value = "离散率",example = "0.33")
    private BigDecimal discrete;

    /**
     * 合格率
     */
    @ApiModelProperty(value = "合格率",example = "0.33")
    private BigDecimal pass;

    public String getAlgorithmId() {
        return algorithmId;
    }

    public void setAlgorithmId(String algorithmId) {
        this.algorithmId = algorithmId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getAccuracy() {
        return accuracy;
    }

    public void setAccuracy(BigDecimal accuracy) {
        this.accuracy = accuracy;
    }

    public BigDecimal getDiscrete() {
        return discrete;
    }

    public void setDiscrete(BigDecimal discrete) {
        this.discrete = discrete;
    }

    public BigDecimal getPass() {
        return pass;
    }

    public void setPass(BigDecimal pass) {
        this.pass = pass;
    }
}
