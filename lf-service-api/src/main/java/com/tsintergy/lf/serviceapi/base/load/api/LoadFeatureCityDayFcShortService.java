/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司
 * Author:  wangchen
 * Date:  2020/4/19 16:12
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.load.api;


import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureShortFcDayDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadShortFcAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayShortFcDO;

import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 */
public interface LoadFeatureCityDayFcShortService {

    /**
     * type 时间间隔 1五分钟 2 十五分钟
     *
     */
    List<LoadFeatureShortFcDayDTO> findFeatureStatisDTOS(Date startDate, Date endDate , String caliberId, String cityId , Integer type) throws Exception;

    void doSaveOrUpdateLoadFeatureCityDayFcDOS(List<LoadFeatureCityDayShortFcDO> loadFeatureCityDayShortFcDOS);

    List<LoadShortFcAccuracyDTO> findDayTimeAccuracy(Date date, String cityId, String caliberId, Integer type) throws Exception;
}
