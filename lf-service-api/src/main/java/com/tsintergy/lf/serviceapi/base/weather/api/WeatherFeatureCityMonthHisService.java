
package com.tsintergy.lf.serviceapi.base.weather.api;


import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityMonthHisDO;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $Id: WeatherFeatureCityMonthHisService.java, v 0.1 2018-04-04 09:53:56 tao Exp $$
 */

public interface WeatherFeatureCityMonthHisService {
     /**
     *  search entity list
     * @param param
     * @return
     * @throws BusinessException
     */
     DataPackage queryWeatherFeatureCityMonthHisDO(DBQueryParam param) throws BusinessException;
    
     /**
     * create entity
     * @param vo
     * @return
     * @throws BusinessException
     */
     WeatherFeatureCityMonthHisDO doCreate(WeatherFeatureCityMonthHisDO vo) throws BusinessException;

    /**
     *  delete entity by object
     * @param vo
     * @throws BusinessException
     */
     void doRemoveWeatherFeatureCityMonthHisDO(WeatherFeatureCityMonthHisDO vo) throws BusinessException;

    /**
     * delete entity by PK
     * @param pk
     * @throws BusinessException
     */
     void doRemoveWeatherFeatureCityMonthHisDOByPK(Serializable pk) throws BusinessException;

    /**
     * update entity object
     * @param vo
     * @return
     * @throws BusinessException
     */
     WeatherFeatureCityMonthHisDO doUpdateWeatherFeatureCityMonthHisDO(WeatherFeatureCityMonthHisDO vo) throws BusinessException;

    /**
     * find entity by PK
     * @param pk
     * @return
     * @throws BusinessException
     */
     WeatherFeatureCityMonthHisDO findWeatherFeatureCityMonthHisDOByPk(Serializable pk) throws BusinessException;

    /**
     * 获取月气象特性
     * @param cityId 城市ID
     * @param year 年(yyyy)
     * @param month 月(MM)
     * @return
     */
     WeatherFeatureCityMonthHisDO getWeatherFeatureCityMonthHisDO(String cityId, String year, String month) throws Exception;


    List<WeatherFeatureCityMonthHisDO> getWeatherFeatureCityYearHisDO(String cityId, String year) throws Exception;

    List<WeatherFeatureCityMonthHisDO> findWeatherFeatureCityMonthHisStat(String cityId, String startYM, String endYM)
            throws Exception;

}