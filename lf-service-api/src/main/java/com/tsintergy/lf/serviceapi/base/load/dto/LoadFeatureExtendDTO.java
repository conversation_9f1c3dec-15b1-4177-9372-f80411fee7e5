package com.tsintergy.lf.serviceapi.base.load.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.PercentSerialize2Digits;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * @date: 3/21/18 4:41 PM
 * @author: angel
 **/
@ApiModel
public class LoadFeatureExtendDTO implements Serializable {

    @ApiModelProperty(value = "主键" ,example = "1")
    private String id;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期" ,example = "2021-03-15")
    private String date;

    /**
     * 城市
     */
    @ApiModelProperty(value = "城市" ,example = "5")
    private String cityId;

    /**
     * 口径ID
     */
    @ApiModelProperty(value = "口径ID" ,example = "1")
    private String caliberId;

    /**
     * 最大负荷
     */
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最大负荷" ,example = "32121")
    private BigDecimal maxLoad;

    /**
     * 最小负荷
     */
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最小负荷" ,example = "12342")
    private BigDecimal minLoad;

    /**
     * 平均负荷
     */
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "平均负荷" ,example = "23121")
    private BigDecimal aveLoad;

    /**
     * 最大负荷发生时刻
     */
    @ApiModelProperty(value = "最大负荷发生时刻" ,example = "0030")
    private String maxTime;

    /**
     * 最小负荷发生时刻
     */
    @ApiModelProperty(value = "最小负荷发生时刻" ,example = "0015")
    private String minTime;

    /**
     * 尖峰平均负荷
     */
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "尖峰平均负荷" ,example = "12312")
    private BigDecimal peak;

    /**
     * 低谷平均负荷
     */
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "低谷平均负荷" ,example = "2131")
    private BigDecimal trough;

    /**
     * 峰谷差
     */
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "峰谷差" ,example = "21")
    private BigDecimal different;

    /**
     * 峰谷差率
     */
    @JsonSerialize(using = PercentSerialize2Digits.class)
    @BigdecimalJsonFormat(percentConvert = 100)
    @ApiModelProperty(value = "峰谷差率" ,example = "0.98")
    private BigDecimal gradient;

    /**
     * 负荷率
     */
    @JsonSerialize(using = PercentSerialize2Digits.class)
    @ApiModelProperty(value = "负荷率" ,example = "0.98")
    private BigDecimal loadGradient;

    /**
     * 日电量
     */
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "日电量" ,example = "21311")
    private BigDecimal energy;

    /**
     * 最大负荷利用小时数
     */
    @ApiModelProperty(value = "最大负荷利用小时数" ,example = "2")
    private BigDecimal useHours;

    /**
     * 日不均衡系数
     */
    @ApiModelProperty(value = "日不均衡系数" ,example = "3")
    private BigDecimal dayUnbalance;

    /**
     * 星期
     */
    @ApiModelProperty(value = "星期" ,example = "星期一")
    private String week;

    /**
     * 城市
     */
    @ApiModelProperty(value = "城市" ,example = "抚顺")
    private String city;

    /**
     * 极端天气的类型
     */
    @ApiModelProperty(value = "极端天气的类型" ,example = "1")
    private Integer type;


    private Date dataDate;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public BigDecimal getMaxLoad() {
        return maxLoad;
    }

    public void setMaxLoad(BigDecimal maxLoad) {
        this.maxLoad = maxLoad;
    }

    public BigDecimal getMinLoad() {
        return minLoad;
    }

    public void setMinLoad(BigDecimal minLoad) {
        this.minLoad = minLoad;
    }

    public BigDecimal getAveLoad() {
        return aveLoad;
    }

    public void setAveLoad(BigDecimal aveLoad) {
        this.aveLoad = aveLoad;
    }

    public String getMaxTime() {
        return maxTime;
    }

    public void setMaxTime(String maxTime) {
        this.maxTime = maxTime;
    }

    public String getMinTime() {
        return minTime;
    }

    public void setMinTime(String minTime) {
        this.minTime = minTime;
    }

    public BigDecimal getPeak() {
        return peak;
    }

    public void setPeak(BigDecimal peak) {
        this.peak = peak;
    }

    public BigDecimal getTrough() {
        return trough;
    }

    public void setTrough(BigDecimal trough) {
        this.trough = trough;
    }

    public BigDecimal getDifferent() {
        return different;
    }

    public void setDifferent(BigDecimal different) {
        this.different = different;
    }

    public BigDecimal getGradient() {
        return gradient;
    }

    public void setGradient(BigDecimal gradient) {
        this.gradient = gradient;
    }

    public BigDecimal getLoadGradient() {
        return loadGradient;
    }

    public void setLoadGradient(BigDecimal loadGradient) {
        this.loadGradient = loadGradient;
    }

    public BigDecimal getEnergy() {
        return energy;
    }

    public void setEnergy(BigDecimal energy) {
        this.energy = energy;
    }

    public String getWeek() {
        return week;
    }

    public void setWeek(String week) {
        this.week = week;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public BigDecimal getUseHours() {
        return useHours;
    }

    public void setUseHours(BigDecimal useHours) {
        this.useHours = useHours;
    }

    public BigDecimal getDayUnbalance() {
        return dayUnbalance;
    }

    public void setDayUnbalance(BigDecimal dayUnbalance) {
        this.dayUnbalance = dayUnbalance;
    }

    public String getCaliberId() {
        return caliberId;
    }

    public void setCaliberId(String caliberId) {
        this.caliberId = caliberId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Date getDataDate() {
        return dataDate;
    }

    public void setDataDate(Date dataDate) {
        this.dataDate = dataDate;
    }
}
