
package com.tsintergy.lf.serviceapi.base.weather.api;


import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsintergy.lf.serviceapi.base.load.dto.CityValueDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherNameDTO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $Id: WeatherCityHisService.java, v 0.1 2018-01-31 11:00:06 tao Exp $$
 */

public interface WeatherCityHisService {
    /**
     * search entity list
     *
     * @param param
     * @return
     * @throws BusinessException
     */
    DataPackage queryWeatherCityHisDO(DBQueryParam param) throws Exception;

    /**
     * create entity
     *
     * @param vo
     * @return
     * @throws Exception
     */
     WeatherCityHisDO doCreate(WeatherCityHisDO vo) throws Exception;

    /**
     * delete entity by object
     *
     * @param vo
     * @throws Exception
     */
     void doRemoveWeatherCityHisDO(WeatherCityHisDO vo) throws Exception;

    /**
     * delete entity by PK
     *
     * @param pk
     * @throws Exception
     */
     void doRemoveWeatherCityHisDOByPK(Serializable pk) throws Exception;

    /**
     * update entity object
     *
     * @param vo
     * @return
     * @throws Exception
     */
     WeatherCityHisDO doUpdateWeatherCityHisDO(WeatherCityHisDO vo) throws Exception;

    /**
     * find entity by PK
     *
     * @param pk
     * @return
     * @throws Exception
     */
     WeatherCityHisDO findWeatherCityHisDOByPk(Serializable pk) throws Exception;

    /**
     * 查询气象历史数据
     *
     * @param cityId    城市ID
     * @param type      气象类型
     * @param startDate 开始日期
     * @return endDate 结束日期
     */
     List<WeatherCityHisDO> findWeatherCityHisDOs(String cityId, Integer type, Date startDate, Date endDate) throws Exception;

    /**
     * find 24weatherCity by date and cityIds
     *
     * @param date
     * @param cityIds
     * @return  返回的结果对象城市为城市id  不是气象城市id
     * @throws Exception
     */
     List<CityValueDTO> find24WeahterCityByDateAndCityIds(Date date, List<String> cityIds, Integer weatherType) throws Exception;

    /**
     * 查询气象历史数据
     *
     * @param cityId    城市ID
     * @param type      气象类型
     * @param startDate 开始日期
     * @return endDate 结束日期
     */
    List<WeatherDTO> findWeatherCityHisDTOs(String cityId, Integer type, Date startDate, Date endDate) throws Exception;

    void doInsertOrUpdate(WeatherCityHisDO weatherCityHisVO) throws Exception;

    void doSaveAllData(WeatherCityHisDO weatherCityHisDO);

    List<WeatherCityHisDO> findWeatherCityHisDOSBySQLDate(String cityId, Integer type, java.sql.Date startDate,
        java.sql.Date endDate) throws Exception;

    List<WeatherCityHisDO> findWeatherCityHisDOSByCityIds(List<String> cityIds, Integer type, Date date) throws Exception;
    WeatherCityHisDO findWeatherCityHisDO(String cityIds, Integer type, Date date) throws Exception;

    List<WeatherCityHisDO> findAllWeather() throws Exception;

    List<WeatherCityHisDO> getWeatherCityHisDOs(List<String> cityIdList, Integer type, Date startDate, Date endDate) throws Exception;

    List<WeatherCityHisDO> findWeatherByDates(String cityId, Integer type, List<Date> dateList) throws Exception;

    List<WeatherNameDTO> findAllByDateAndCityId(String cityId, Date targetDate) throws Exception;


    List<BigDecimal> findWeatherCityHisValueList(String cityId, Date date, Integer weatherType) throws Exception;

    List<BigDecimal> findWeatherCityHisValueList(String cityId, Date startDate,Date endDate ,Integer weatherType) throws Exception;



    List<BigDecimal> find96WeatherCityHisValue(Date date, String cityId, Integer type) throws Exception;

    List<BigDecimal> findListPoint(Date startDate, Date endDate, String cityId, Integer type) throws Exception;

    /**
     * 站点气象转存到城市气象逻辑：
     * 1.根据cityAndStationMap查询城市默认站点，
     * 2.默认站点目标时段为null，从该城市的其他站点获取数据，多个站点的目标时刻数据求平均后作为城市目标时刻的数据，
     * 3.如果所有站点的目标时刻都是null，则暂时保存成null，最后通过前后不为null的数据，计算差值，求步长后计算出中间为null的几个点
     *
     * 20240617-屏蔽查询城市默认站点逻辑；当前为所有站点求均值
     * @param dates
     * @throws Exception
     */
    void doClctWeatherStationToCity(List<String> dates, Map<String, String> cityAndStationMap) throws Exception;


    /**
     * 通过格点气象站的数据计算城市气象数据
     * 1.将格点气象站气象24点数据转化为96点数据
     * 2.通过96点数据计算城市气象
     *
     * @throws Exception
     */
    void wgStationHisWeatherToCity(String cityId, Integer type, Date startDate, Date endDate) throws Exception;

    void doHisCityWeatherToProvince(Date startDate, Date endDate) throws Exception;

    /**
     * 通过前后不为null的数据，计算差值，求步长后计算出中间为null的几个点
     */
    void extracted(List<BigDecimal> decimalList);

    /**
     * 获取福建省标准站点加权平均气象数据
     *
     * @param type
     * @param startDate
     * @param endDate
     * @throws Exception
     */
    List<WeatherCityHisDO> getProvinceWeightAvgHisWeatherData(Integer type, Date startDate, Date endDate) throws Exception;

}