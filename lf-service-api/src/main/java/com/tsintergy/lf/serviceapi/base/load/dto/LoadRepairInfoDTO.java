/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  wangchen
 * Date:  2019/4/24 9:56
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.load.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Description: 修复信息记录 <br>
 *
 * <AUTHOR>
 * @create 2019/4/24 
 * @since 1.0.0
 */
@ApiModel
public class LoadRepairInfoDTO implements Serializable {

    @ApiModelProperty(value = "城市", example = "抚顺")
    private String city;

    @ApiModelProperty(value = "城市ID", example = "1")
    private String cityId;

    @ApiModelProperty(value = "日期", example = "2021-03-10")
    private Date date;

    @ApiModelProperty(value = "星期", example = "星期一")
    private String week;

    @ApiModelProperty(value = "修复信息")
    private List<ErrorPoint> data;

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getWeek() {
        return week;
    }

    public void setWeek(String week) {
        this.week = week;
    }

    public List<ErrorPoint> getData() {
        return data;
    }

    public void setData(List<ErrorPoint> data) {
        this.data = data;
    }

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }
}
