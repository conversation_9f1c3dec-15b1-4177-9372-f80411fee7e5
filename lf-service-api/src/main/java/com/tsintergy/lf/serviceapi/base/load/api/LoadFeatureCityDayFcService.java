/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  wangchen Date:  2020/4/19 16:12 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.load.api;


import com.tsintergy.lf.serviceapi.base.load.dto.FeatureStatisDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayFcDO;

import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/4/19 
 * @since 1.0.0
 */
public interface LoadFeatureCityDayFcService {

    List<FeatureStatisDTO> findFeatureStatisDTOS(Date date, String caliberId, String cityId,String dataType) throws Exception;

    void doSaveOrUpdateLoadFeatureCityDayFcDOS(List<LoadFeatureCityDayFcDO> loadFeatureCityDayHisVOs);


    List<LoadFeatureCityDayFcDO> findReportLoadFeatureCityDayFcList(List<String> cityIds, java.sql.Date startDate,
        java.sql.Date endDate, String caliberId) throws Exception;

    List<LoadFeatureCityDayFcDO> findLoadFeatureCityDayFcList(List<String> cityIds, java.sql.Date startDate,
        java.sql.Date endDate, String caliberId, String algorithmId, Boolean report) throws Exception;

    List<LoadFeatureCityDayFcDO> findReportIfNullUseRecommend(List<String> cityIds, java.sql.Date startDate,
        java.sql.Date endDate, String caliberId) throws Exception;

    LoadFeatureCityDayFcDO findLoadFeatureCityDayFcReport(String cityId, String caliberId, java.sql.Date date) throws Exception;

    List<LoadFeatureCityDayFcDO> findFcFeatureReports(String cityId, String caliberId, java.sql.Date date);

}
