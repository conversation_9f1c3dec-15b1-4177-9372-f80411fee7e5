package com.tsintergy.lf.serviceapi.base.forecast.api;

import com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyLoadCityFcDO;

import java.util.Date;
import java.util.List;

/**
 * 预测结果统计接口
 * User:taojingui
 * Date:18-2-23
 * Time:上午11:53
 */
public interface ForecastResultStatService {

    /**
     * 统计预测结果并入库
     * @param cityId 城市ID
     * @param algorithmId 算法ID
     * @param caliberId 口径ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    void statForecastResult(String cityId, String algorithmId, String caliberId, Date startDate, Date endDate) throws Exception;

    /**
     * 统计预测结果并入库
     * @param cityId 城市ID
     * @param algorithmId 算法ID
     * @param caliberId 口径ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    void statForecastRecallResult(String cityId, String algorithmId, String caliberId, Date startDate, Date endDate) throws Exception;

    List<AccuracyLoadCityFcDO> doSaveOrUpdateAccuracyLoadCityFcDOs(List<AccuracyLoadCityFcDO> accuracyLoadCityFcDOS) throws Exception;
}
