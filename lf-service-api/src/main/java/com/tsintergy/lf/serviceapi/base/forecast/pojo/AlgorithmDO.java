package com.tsintergy.lf.serviceapi.base.forecast.pojo;


import com.tsieframework.core.base.vo.CacheVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * 算法
 */
@Data
@Entity
@Table(name = "algorithm_base_init")
@ApiModel
public class AlgorithmDO extends CacheVO {


    private static final long serialVersionUID = 8050743254081999660L;

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    @ApiModelProperty(value = "id",example = "0")
    private String id;

    /**
     * 算法英文名
     */
    @Column(name = "algorithm_en")
    @ApiModelProperty(value = "算法英文名",example = "MD")
    private String algorithmEn;

    /**
     * 算法中文名
     */
    @Column(name = "algorithm_cn")
    @ApiModelProperty(value = "算法中文名",example = "人工决策")
    private String algorithmCn;

    /**
     * 算法编号
     */
    @Column(name = "code")
    @ApiModelProperty(value = "算法编号",example = "100")
    private String code;

    /**
     * 排序号
     */
    @Column(name = "order_no")
    @ApiModelProperty(value = "排序号",example = "1")
    private Integer orderNo;

    /**
     * 是否有效
     */
    @Column(name = "valid")
    @ApiModelProperty(value = "是否有效",example = "true")
    private Boolean valid;


    /**
     * 是否参与综合模型算法的计算
     */
    @Column(name = "join_model")
    @ApiModelProperty(value = "是否参与综合模型算法的计算",example = "0")
    private  String  joinModel;

    /**
     * 算法类型
     * 0 表示通用
     * 1 表示正常日
     * 2 表示节假日
     * 3 表示其他
     */
    @Column(name = "type")
    @ApiModelProperty(value = "算法类型",example = "MD")
    private String type;



    @Column(name = "province_view")
    private Boolean provinceView;


    @Column(name = "city_view")
    private Boolean cityView;

    /**
     * 算法使用的历史气象源
     */
    @Column(name = "his_weather_source")
    private String hisWeatherSource;

    /**
     * 算法使用的预测气象源
     */
    @Column(name = "fc_weather_source")
    private String fcWeatherSource;

    /**
     * 是否为区域定制化算法
     */
    @Column(name = "REGIONAL_CUSTOM_ALGORITHM")
    private Boolean regionalCustomAlgorithm;


    @Override
    public String getKey() {
        return this.id;
    }

    @Override
    public String getLabel() {
        return this.algorithmCn;
    }
}
