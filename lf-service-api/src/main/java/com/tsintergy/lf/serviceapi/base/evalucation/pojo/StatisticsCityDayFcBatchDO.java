package com.tsintergy.lf.serviceapi.base.evalucation.pojo;


import com.tsieframework.core.base.dao.BaseVO;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * 预测统计结果（日）批次结果
 */
@Data
@Entity
@Table(name = "statistics_city_day_fc_service_batch")
public class StatisticsCityDayFcBatchDO extends BaseVO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    /**
     * 城市ID
     */
    @Column(name = "city_id")
    private String cityId;

    /**
     * 日期
     */
    @Column(name = "date")
    private Date date;

    /**
     * 算法ID
     */
    @Column(name = "algorithm_id")
    private String algorithmId;

    /**
     * 算法ID
     */
    @Column(name = "batch_id")
    private Integer batchId;

    /**
     * 算法预测时间
     */
    @Column(name = "algo_fore_time")
    private String algoForeTime;

    /**
     * 口径ID
     */
    @Column(name = "caliber_id")
    private String caliberId;

    /**
     * 准确率
     */
    @Column(name = "accuracy")
    private BigDecimal accuracy;

    /**
     * 偏差率
     */
    @Column(name = "deviation")
    private BigDecimal deviation;

    /**
     * 合格率
     */
    @Column(name = "pass")
    private BigDecimal pass;

    /**
     * 离散度
     */
    @Column(name = "dispersion")
    private BigDecimal dispersion;

    /**
     * 考核标准准确率
     */
    @Column(name = "standard_accuracy")
    private BigDecimal standardAccuracy;

    /**
     * 创建时间
     */
    @Column(name = "createtime")
    private Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    private Timestamp updatetime;

    /**
     * 是否上报
     */
    @Column(name = "report")
    private Boolean report;


}
