/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  wang<PERSON>
 * Date:  2019/6/25 2:11
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.typhoon.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/6/25
 * @since 1.0.0
 */
@ApiModel
public class TyphoonSameDTO implements Serializable {

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期",example = "2021-03-24")
    private Date date;

    /**
     *
     */
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "负荷")
    private List<BigDecimal> load;

    /**
     * 风速
     */
    @ApiModelProperty(value = "风速")
    private List<BigDecimal> wind;

    /**
     * 降雨量
     */
    @ApiModelProperty(value = "降雨量")
    private List<BigDecimal> rainfall;

    /**
     * 差值
     */
    @ApiModelProperty(value = "差值")
    private List<BigDecimal> diff;

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public List<BigDecimal> getLoad() {
        return load;
    }

    public void setLoad(List<BigDecimal> load) {
        this.load = load;
    }

    public List<BigDecimal> getWind() {
        return wind;
    }

    public void setWind(List<BigDecimal> wind) {
        this.wind = wind;
    }

    public List<BigDecimal> getRainfall() {
        return rainfall;
    }

    public void setRainfall(List<BigDecimal> rainfall) {
        this.rainfall = rainfall;
    }

    public List<BigDecimal> getDiff() {
        return diff;
    }

    public void setDiff(List<BigDecimal> diff) {
        this.diff = diff;
    }
}