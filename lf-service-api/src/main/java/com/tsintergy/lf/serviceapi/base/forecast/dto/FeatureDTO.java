/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  wang<PERSON>
 * Date:  2019/8/20 10:25
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.forecast.dto;

import com.tsintergy.lf.serviceapi.algorithm.dto.SensitivityBean;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/8/20
 * @since 1.0.0
 */
@ApiModel
@Data
public class FeatureDTO implements Serializable {

    @ApiModelProperty(value = "拟合精度")
    @BigdecimalJsonFormat(scale = 2)
    private BigDecimal fittingAccuracy;

    @ApiModelProperty(value = "散点图")
    private List<List<BigDecimal>> featuresPoint;

    @ApiModelProperty(value = "折线图")
    private List<List<BigDecimal>> featuresLine;

    private List<SensitivityBean> analyze;

    public FeatureDTO() {}

    // 拷贝构造函数
    public FeatureDTO(FeatureDTO other) {
        if (other.featuresLine != null) {
            this.featuresLine = new ArrayList<>();
            for (List<BigDecimal> line : other.featuresLine) {
                this.featuresLine.add(new ArrayList<>(line));
            }
        }
        if (other.featuresPoint != null) {
            this.featuresPoint = new ArrayList<>();
            for (List<BigDecimal> point : other.featuresPoint) {
                this.featuresPoint.add(new ArrayList<>(point));
            }
        }
        this.fittingAccuracy = other.fittingAccuracy;
    }

    // Getter 和 Setter 方法
    public List<List<BigDecimal>> getFeaturesLine() {
        return featuresLine;
    }

    public void setFeaturesLine(List<List<BigDecimal>> featuresLine) {
        this.featuresLine = featuresLine;
    }

    public List<List<BigDecimal>> getFeaturesPoint() {
        return featuresPoint;
    }

    public void setFeaturesPoint(List<List<BigDecimal>> featuresPoint) {
        this.featuresPoint = featuresPoint;
    }

    public BigDecimal getFittingAccuracy() {
        return fittingAccuracy;
    }

    public void setFittingAccuracy(BigDecimal fittingAccuracy) {
        this.fittingAccuracy = fittingAccuracy;
    }

}