
package com.tsintergy.lf.serviceapi.base.base.api;

import com.tsieframework.core.base.exception.BusinessException;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $Id: CityService.java, v 0.1 2018-01-31 09:44:46 tao Exp $$
 */

public interface CityService {


    /**
     * create entity
     *
     * @param vo
     * @return
     * @throws Exception
     */
     CityDO doCreate(CityDO vo) throws Exception;
    /**
     * 查询全部地市排除澳门地方电以及广东电 true代表广东电存在 false 代表不存在
     *
     * @return
     * @throws Exception
     */
    List<CityDO> findAllCitysExcludeAoLocal(boolean guangdong) throws Exception;


    /**
     * find entity by PK
     *
     * @param pk
     * @return
     * @throws Exception
     */
     CityDO findCityVOByPk(Serializable pk) throws Exception;

    /**
     * find entitys' cityId by belongId from redis
     *
     * @param BelongId
     * @return
     * @throws Exception
     */
     List<String> findCityIdsByBelongId(String BelongId) throws Exception;

    /**
     * 查询下级地市
     *
     * @param belongId
     * @return
     * @throws Exception
     */
     List<CityDO> findCitysByBelongId(String belongId) throws Exception;

    /**
     * 查询全部地市
     *
     * @return
     * @throws Exception
     */
     List<CityDO> findAllCitys() throws Exception;

    /**
     * find entity by id from redis
     *
     * @param id
     * @return
     * @throws Exception
     */
    CityDO findCityById(String id) throws Exception;

    /**
     * find entity by name
     *
     * @param city
     * @return
     * @throws BusinessException
     */
     CityDO findCityByName(String city) throws BusinessException;


    /**
     * 查询省会城市
     *
     * @param cityId
     * @return
     * @throws Exception
     */
     CityDO findProvincialCapital(String cityId) throws Exception;


    /**
     * 获取省会城市
     * @param type 1为省会城市
     */
    CityDO findCityByType(Integer type) throws Exception;

    String findWeatherCityId(String cityId) throws Exception;

    List<String> findWeatherCityIds(List<String> cityIds) throws Exception;

    /**
     * 功能描述:<br>获取【城市id&气象城市id】对应map
     * @return key 城市id  value 气象城市id
     */
    Map<String, String> cityIdAndWeatherCityMap() throws Exception;



}