package com.tsintergy.lf.serviceapi.base.forecast.pojo;

import com.tsieframework.core.base.vo.annotation.EntityUniqueIndex;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.FcLoad;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Load;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.base.pojo.BaseLoadFcCityDO;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @description 算法预测批次数据模型
 * @date 2021/2/3 14:03
 */
@Data
@Entity
@Table(name = "load_city_fc_batch")
@EntityUniqueIndex({"date", "cityId", "caliberId", "algorithmId", "batchId"})
public class LoadCityFcBatchDO extends BaseLoadFcCityDO implements Load, FcLoad {

    private static final long serialVersionUID = 6685116501028072753L;

    /**
     * id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(generator = "generator")
    @GenericGenerator(name = "generator", strategy = "uuid")
    private String id;


    /**
     * 日期
     */@Column(name = "date")
    private Date date;

    /**
     * 城市ID
     */@Column(name = "city_id")
    private String cityId;

    /**
     * 口径ID
     */@Column(name = "caliber_id")
    private String caliberId;

    /**
     * 算法ID
     */@Column(name = "algorithm_id")
    private String algorithmId;

    /**
     * 批次号
     */@Column(name = "batch_id")
    private Integer batchId;

    /**
     * 是否为系统推荐的算法
     */@Column(name = "recommend")
    private Boolean recommend;

    /**
     * 是否上报结果
     */@Column(name = "report")
    private Boolean report;

    /**
     * 操作者
     */@Column(name = "user_id")
    private String userId;

    /**
     * 是否上报成功
     */@Column(name = "succeed")
    private Boolean succeed;
    @Column(name = "report_time")
    private Timestamp reportTime;

    @Override
    public List<BigDecimal> getloadList() {
        return BasePeriodUtils.toList(this, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
    }

    @Override
    public String getDeviceId() {
        return getCityId();
    }


    @Override
    public List<BigDecimal> getLoadList() {
        return BasePeriodUtils.toList(this, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
    }
}
