package com.tsintergy.lf.serviceapi.base.evalucation.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel
@Data
public class StatisticsDayDTO implements Serializable {

    @ApiModelProperty(value = "日期")
    private Date date;

    @ApiModelProperty(value = "算法id")
    private BigDecimal algorithm;

    @ApiModelProperty(value = "上报值")
    private BigDecimal report;

    @ApiModelProperty(value = "综合值")
    private BigDecimal compositeAccuracy;

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public BigDecimal getAlgorithm() {
        return algorithm;
    }

    public void setAlgorithm(BigDecimal algorithm) {
        this.algorithm = algorithm;
    }

    public BigDecimal getReport() {
        return report;
    }

    public void setReport(BigDecimal report) {
        this.report = report;
    }
}
