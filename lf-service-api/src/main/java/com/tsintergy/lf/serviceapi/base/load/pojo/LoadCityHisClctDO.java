/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  wangchen
 * Date:  2019/3/11 14:52
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.load.pojo;

import com.tsintergy.lf.serviceapi.base.base.pojo.Base96DO;
import java.sql.Date;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/3/11 
 * @since 1.0.0
 */

@Data
@Entity
@Table(name = "load_city_his_clct")
public class LoadCityHisClctDO extends Base96DO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    @Column(name = "date")
    private Date date;

    @Column(name = "city_id")
    private String cityId;

    @Column(name = "caliber_id")
    private String caliberId;

    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;
}