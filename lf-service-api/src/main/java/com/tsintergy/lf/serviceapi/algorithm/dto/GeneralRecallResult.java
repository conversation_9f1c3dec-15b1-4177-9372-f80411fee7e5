/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司 Author: wangfeng Date: 2018/8/6 14:35 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.algorithm.dto;

import com.tsintergy.lf.serviceapi.base.recall.pojo.LoadCityFcRecallDO;
import java.util.List;
import lombok.Data;
import org.springframework.util.CollectionUtils;

/**
 * 每日算法 结果对象 由result对象简易处理为 LoadCityFcDO 对象
 *
 * <AUTHOR>
 * @create 2020/8/28
 * @since 1.0.0
 */
@Data
public class GeneralRecallResult extends Result {

    private List<LoadCityFcRecallDO> result;

    public GeneralRecallResult add(GeneralRecallResult result) {
        if (result != null) {
            if (!CollectionUtils.isEmpty(result.getResult())) {
                //防止第一次累加空指针
                if (this.getResult() == null) {
                    return result;
                } else {
                    this.getResult().addAll(result.getResult());
                }
            }
        }
        return this;
    }
}