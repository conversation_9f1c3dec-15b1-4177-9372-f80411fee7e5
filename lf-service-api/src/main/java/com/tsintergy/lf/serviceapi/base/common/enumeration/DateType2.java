/**
 * Copyright (C), 2015‐2022, 北京清能互联科技有限公司 Author:  <EMAIL> Date:  2022/5/23 19:06 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.common.enumeration;

import com.tsieframework.core.base.enums.TsieEnum;
import com.tsieframework.core.base.enums.hibernate.TsieEnumTypeConvertor;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2022/5/23
 * @since 1.0.0
 */
public enum DateType2 implements TsieEnum {

    WORKDAY(1, "工作日"),

    //HOLIDAY(5, "休息日"),

    SATURDAY(2, "周六"),

    WEEKEND(3, "周末"),

    REST(4, "节假日")

    ;

    private Integer id;

    private String text;

    DateType2(Integer id, String text) {
        this.id = id;
        this.text = text;
    }

    @Override
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Override
    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    /**
     * 自定义转换器
     **/
    public static class DateType2Convertor extends TsieEnumTypeConvertor<DateType2> {

        @Override
        protected DateType2 getDefault() {
            return null;
        }
    }

    public static DateType2 fromId(int id) {
        for (DateType2 value : DateType2.values()) {
            if (value.id == id) {
                return value;
            }
        }
        return null;
    }
}
