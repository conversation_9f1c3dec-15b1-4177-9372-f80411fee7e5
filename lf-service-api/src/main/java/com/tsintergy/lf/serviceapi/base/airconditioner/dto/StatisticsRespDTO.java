/**
 * Copyright (C), 2015‐2022, 北京清能互联科技有限公司 Author:  <EMAIL> Date:  2022/5/25 14:10 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.airconditioner.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2022/5/25
 * @since 1.0.0
 */
@Data
@ApiModel
public class StatisticsRespDTO {

    @ApiModelProperty(value = "温度(℃)", example = "20")
    BigDecimal Temperature;

    @BigdecimalJsonFormat(percentConvert = 100)
    @ApiModelProperty(value = "百分比（%）", example = "11.76")
    BigDecimal percentage;
}  
