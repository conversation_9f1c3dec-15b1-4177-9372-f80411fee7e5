/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.airconditioner.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * Description:<br>
 *
 * @Author:<EMAIL>
 * @Date: 2022/5/23 18:33
 * @Version:1.0.0
 */
@Data
@ApiModel
public class AcLoadCurveDTO {

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "空调负荷曲线")
    List<BigDecimal> acLoad;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "基础负荷曲线")
    List<BigDecimal> baseLoad;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "温度曲线")
    List<BigDecimal> temperature;
}