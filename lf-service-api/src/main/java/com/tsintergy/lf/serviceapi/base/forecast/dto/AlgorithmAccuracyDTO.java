package com.tsintergy.lf.serviceapi.base.forecast.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 算法准确率
 * User:taojingui
 * Date:18-2-7
 * Time:上午11:54
 */
@ApiModel
public class AlgorithmAccuracyDTO implements Serializable {

    /**
     * 算法名称
     */
    @ApiModelProperty(value = "算法名称",example = "标准法")
    private String name;

    /**
     * 算法ID
     */
    @ApiModelProperty(value = "算法ID",example = "7")
    private String algorithmId;

    /**
     * 准确率
     */
    @ApiModelProperty(value = "准确率",example = "0.33")
    private BigDecimal accuracy;

    @ApiModelProperty(value = "准确率",example = "0.33")
    private BigDecimal pass;

    public AlgorithmAccuracyDTO(){}

    public AlgorithmAccuracyDTO(String name, String algorithmId, BigDecimal accuracy, BigDecimal pass){
        this.name = name;
        this.algorithmId = algorithmId;
        this.accuracy = accuracy;
        this.pass = pass;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getAccuracy() {
        return accuracy;
    }

    public void setAccuracy(BigDecimal accuracy) {
        this.accuracy = accuracy;
    }

    public String getAlgorithmId() {
        return algorithmId;
    }

    public void setAlgorithmId(String algorithmId) {
        this.algorithmId = algorithmId;
    }

    public BigDecimal getPass() {
        return pass;
    }

    public void setPass(BigDecimal pass) {
        this.pass = pass;
    }
}
