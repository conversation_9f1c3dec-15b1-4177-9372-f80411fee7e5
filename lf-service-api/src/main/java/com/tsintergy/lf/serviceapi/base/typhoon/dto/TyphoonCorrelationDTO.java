/**
 * Copyright(C),2015-2018,北京清能互联科技有限公司
 * Author:   jxm
 * Date:    2018/11/2913:42
 * History:
 * <author><time><version><desc>
 */
package com.tsintergy.lf.serviceapi.base.typhoon.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 *Description:回归分析<br>
 *
 *<AUTHOR>
 *@create2018/11/29
 *@since1.0.0
 */
@ApiModel
public class TyphoonCorrelationDTO implements Serializable {
    /**
     * 横坐标
     */
    @ApiModelProperty(value = "横坐标",example = "0")
    private int x;
    /**
     * 纵坐标
     */
    @ApiModelProperty(value = "纵坐标",example = "0")
    private int y;
    /**
     * 相关系数值
     */
    @ApiModelProperty(value = "相关系数值",example = "1")
    private BigDecimal v;

    public int getX() {
        return x;
    }

    public void setX(int x) {
        this.x = x;
    }

    public int getY() {
        return y;
    }

    public void setY(int y) {
        this.y = y;
    }

    public BigDecimal getV() {
        return v;
    }

    public void setV(BigDecimal v) {
        this.v = v;
    }

    @Override
    public String toString() {
        return "TyphoonCorrelationDTO{" +
                "x=" + x +
                ", y=" + y +
                ", v=" + v +
                '}';
    }
}

