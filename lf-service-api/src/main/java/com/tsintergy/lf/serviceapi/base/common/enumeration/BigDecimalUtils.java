/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  gss Date:  2020/1/9 14:28 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.common.enumeration;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.BiFunction;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/1/9
 * @since 1.0.0
 */
public class BigDecimalUtils {

    public static final RoundingMode ROUNDING_MODE = RoundingMode.HALF_UP;
    //    public static final int SCALE = 6;
    public static final int SCALE = 4;
    public static final MathContext MATH_CONTEXT = new MathContext(SCALE, ROUNDING_MODE);
    //气象数据大于100就算异常数据
    public static final BigDecimal INVALID_VALUE = new BigDecimal("100");

    public static boolean equalsBigDecimal(BigDecimal num1, BigDecimal num2) {
        if (num1 == null) {
            if (num2 == null) {
                return true;
            } else {
                return false;
            }
        } else {
            boolean equals = num1.setScale(4, BigDecimal.ROUND_HALF_DOWN)
                    .equals(num2.setScale(4, BigDecimal.ROUND_HALF_DOWN));
            return equals;
        }

    }

    public static List<BigDecimal> createList(BigDecimal value, int size) {
        List<BigDecimal> list = new ArrayList<BigDecimal>();
        for (int i = 0; i < size; i++) {
            list.add(value);
        }
        return list;
    }

    /**
     * 平均值, 如果列表所有元素为null，则返回null
     *
     * @param values
     * @return
     */
    public static BigDecimal listAvg(List<BigDecimal> values) {
        //除不尽会抛出异常，需设置四舍五入
        return listSum(values).divide(new BigDecimal(values.size()), MATH_CONTEXT);
    }

    public static final int SIZE_NOT_SET = 0;

    public static List<BigDecimal> listAdd(List<BigDecimal> values1, List<BigDecimal> values2) {
        return listCalc(values1, values2, BigDecimalUtils::valueAdd);
    }

    public static BigDecimal valueAdd(BigDecimal value1, BigDecimal value2) {
        if (value1 == null || value2 == null) {
            return null;
        }
        return value1.add(value2);
    }

    public static <T> List<T> listCalc(List<T> values1, List<T> values2, BiFunction<T, T, T> function) {
        return listCalc(values1, values2, function, SIZE_NOT_SET);
    }

    public static <T> List<T> listCalc(List<T> values1, List<T> values2, BiFunction<T, T, T> function, int size) {
        if (size < SIZE_NOT_SET) {
            throw new RuntimeException("size需大于" + SIZE_NOT_SET);
        }

        if (size == SIZE_NOT_SET) {
            if (values1 == null && values2 == null) {
                throw new RuntimeException("value1 与 value2 为 null，建议传入size");
            } else if (values1 != null && values2 != null && values1.size() != values2.size()) {
                throw new RuntimeException("大小不一致，values1.size() = " + values1.size() + ", value2.size() = " + values2.size());
            } else {
                size = values1 != null ? values1.size() : values2.size();
            }
        } else {
            if (values1.size() != size || values2.size() != size) {
                throw new RuntimeException("大小不一致，size = " + size + " values1.size() = " + values1.size() + ", value2.size() = " + values2.size());
            }
        }

        values1 = populateList(values1, null, size);
        values2 = populateList(values2, null, size);

        List<T> resultList = new ArrayList<>(size);

        ListIterator<T> listIterator1 = values1.listIterator();
        ListIterator<T> listIterator2 = values2.listIterator();

        // 由于size已经相等，可以确保两个list同时迭代至最后一个元素
        while (listIterator1.hasNext()) {
            T v = function.apply(listIterator1.next(), listIterator2.next());
            resultList.add(v);
        }

        return resultList;
    }

    public static final BigDecimal ZERO = new BigDecimal("0");

    public static List<BigDecimal> listDivideValue(List<BigDecimal> values, BigDecimal itemValue) {
        return listDivide(values, constantToList(itemValue, getListSize(values)));
    }

    public static <T> Integer getListSize(List<T> values) {
        return values != null ? values.size() : null;
    }
    public static List<BigDecimal> constantToList(BigDecimal value, Integer size) {
        if (size == null) {
            return null;
        }
        List<BigDecimal> result = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            result.add(value);
        }
        return result;
    }

    public static List<BigDecimal> listDivide(List<BigDecimal> values1, List<BigDecimal> values2) {
        //除不尽会抛出异常，需设置四舍五入
//        return SimpleListLambadaExpression.<BigDecimal>of((v1, v2) -> valueDivide(v1, v2)).eval(values1, values2);
        return listCalc(values1, values2, BigDecimalUtils::valueDivide);
    }

    public static BigDecimal valueDivide(BigDecimal value1, BigDecimal value2) {
        if (value1 == null || value2 == null) {
            return null;
        }
        if (ZERO.compareTo(value2) == 0) {
            return null;
        }
        return value1.divide(value2, MATH_CONTEXT);
    }

    public static <T> List<T> populateList(List<T> list, T defaultValue, int size) {
        //判断集合是否为空
        if (list != null) {
            //判断size是否相等
            if (list.size() == size) {
                return list;
            } else {
                throw new RuntimeException("list的大小需为" + size);
            }
        } else {
            list = new ArrayList<>(size);
            for (int i = 0; i < size; i++) {
                list.add(null); //默认都为空值
            }
        }

        //赋值默认值
        for (int i = 0; i < size; i++) {
            list.set(i, defaultValue);
        }
        return list;
    }

    /**
     * 列表求和
     *
     * @param values
     * @return
     */
    public static BigDecimal listSum(List<BigDecimal> values) {
        return listStatistics(values, (v1, v2) -> valueCalc(v1, v2, BigDecimal::add)).orElse(new BigDecimal("0"));
    }

    /**
     * @param values
     * @param function 统计函数
     * @param <T>
     * @return
     */
    public static <T> Optional<T> listStatistics(List<T> values, BiFunction<T, T, T> function) {
        if (CollectionUtils.isEmpty(values)) {
            return Optional.empty();
        } else {
            return values.stream().filter(Objects::nonNull).reduce(function::apply);
        }
    }

    /**
     * v1 operator v2, operator: + - * / 等。如果v1 == null，返回v2，如果v2 == null，返回v1，否则 v1 operator v2
     *
     * @param v1
     * @param v2
     * @param operator
     * @return
     */
    public static BigDecimal valueCalc(BigDecimal v1, BigDecimal v2, BiFunction<BigDecimal, BigDecimal, BigDecimal> operator) {
        if (v1 == null) {
            return v2;
        } else if (v2 == null) {
            return v1;
        } else {
            return operator.apply(v1, v2);
        }
    }

    public static BigDecimal stringToBigdecimal(String value, int scale, int prec) {
        return StringUtils.isEmpty(value) ? null : new BigDecimal(value).setScale(scale, prec);
    }

    public static boolean isValidValue(BigDecimal value) {
        return value != null && value.compareTo(INVALID_VALUE) < 0;
    }

    public static BigDecimal effTem(BigDecimal tem, BigDecimal wind, BigDecimal humidity) {
        if (tem == null) {
            return null;
        } else {
            if (wind == null || humidity == null) {
                return tem;
            }
        }
        BigDecimal divide = BigDecimal.valueOf(17.27).multiply(tem).divide((BigDecimal.valueOf(237.27).add(tem)), 9,
                BigDecimal.ROUND_HALF_UP);
        double exp = Math.exp(divide.doubleValue());
        BigDecimal e = humidity.divide(BigDecimal.valueOf(100)).multiply(BigDecimal.valueOf(6.105)).multiply(BigDecimal.valueOf(exp));
        BigDecimal effTem = BigDecimal.valueOf(1.07).multiply(tem).add(BigDecimal.valueOf(0.2).multiply(e)).subtract(
                BigDecimal.valueOf(0.065).multiply(wind)).subtract(BigDecimal.valueOf(2.7)).setScale(2, BigDecimal.ROUND_HALF_UP);
        return effTem;
    }
}
