/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  wangchen
 * Date:  2019/8/28 10:04
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.weather.pojo;

import com.tsintergy.lf.serviceapi.base.base.pojo.Base96DO;
import java.sql.Date;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;


/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/8/28 
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "statistics_synthesize_weather_city_day_fc")
public class StatisticsSynthesizeWeatherCityDayFcDO extends BaseWeatherDO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    @Column(name = "date")
    private Date date;

    @Column(name = "city_id")
    private String cityId;

    @Column(name = "type")
    private Integer type;

    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        StatisticsSynthesizeWeatherCityDayFcDO that = (StatisticsSynthesizeWeatherCityDayFcDO) o;

        return new EqualsBuilder()
                .append(type, that.type)
                .append(id, that.id)
                .append(date, that.date)
                .append(cityId, that.cityId)
                .append(createtime, that.createtime)
                .append(updatetime, that.updatetime)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(id)
                .append(date)
                .append(cityId)
                .append(type)
                .append(createtime)
                .append(updatetime)
                .toHashCode();
    }
}
