
package com.tsintergy.lf.serviceapi.base.base.api;

import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsintergy.lf.serviceapi.base.base.dto.HolidayDTO;
import com.tsintergy.lf.serviceapi.base.base.dto.SkipHolidayDTO;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $Id: HolidayService.java, v 0.1 2018-01-31 09:45:58 tao Exp $$
 */

public interface HolidayService {

    /**
     * search entity list
     */
    DataPackage queryHolidayVO(DBQueryParam param) throws Exception;

    /**
     * create entity
     */
    HolidayDO doCreate(HolidayDO vo) throws Exception;

    /**
     * delete entity by object
     */
    void doRemoveHolidayVO(HolidayDO vo) throws Exception;

    /**
     * delete entity by PK
     */
    void doRemoveHolidayVOByPK(Serializable pk) throws Exception;

    /**
     * update entity object
     */
    HolidayDO doUpdateHolidayVO(HolidayDO vo) throws Exception;

    /**
     * find entity by PK
     */
    HolidayDO findHolidayVOByPk(Serializable pk) throws Exception;

    /**
     * is holiday
     */
    Boolean isHoliday(Date date) throws Exception;

    /**
     * 获取全部节假日
     */
    List<Date> getAllHolidays() throws Exception;

    /**
     * 获取全部节假日对象
     */
    List<HolidayDO> getAllHolidayVOS() throws BusinessException;

    /**
     * 获取所有节假日，按年份分类
     */
    List<HolidayDTO> findHolidayVOsSortByYear() throws BusinessException;

    /**
     * 查询某年的全部节假日信息
     */
    List<HolidayDO> findByYear(String year) throws Exception;


    /**
     * 查询某年的全部节假日信息
     */
    List<HolidayDO> findByYearAndCode(String year, Integer code) throws Exception;

    /**
     * 导入节假日
     */
    void doImportHolidays(List<Map<Integer, Object>> list) throws Exception;

    /**
     * 查找一段时间内的节假日
     */
    List<Date> findHoliday(Date startDate, Date endDate) throws BusinessException;

    /**
     * 查找一点时间内的节假日对象
     */
    List<HolidayDO> findHolidayVOS(Date startDate, Date endDate) throws BusinessException;

    /**
     * 将某段日期跳过节假日，并经行正常日顺延，返回拆分后的时间段 map里面key有两个：startDate和endDate
     */
    List<Map<String, Date>> skipHoliday(Date startDate, Date endDate) throws BusinessException;

    /**
     * 将某段日期跳过节假日，并经行正常日顺延,返回拆分后的正常日时间段和休息日时间段
     */
    SkipHolidayDTO findSkipHolidays(Date startDate, Date endDate) throws BusinessException;

    /**
     * 批量删除节假日
     */
    void doRemoveHolidaysByIds(List<String> ids);


    List<HolidayDO> getListHolidayByCode(String startYear, String endYear, Integer code) throws Exception;

    List<HolidayDO> getListHoliday(Date startDate, Date endDate) throws Exception;

    /**
     * 根据年份查询节假日内所有日期列表
     *
     * @param year 年份
     * @return 日期列表
     * @throws Exception 异常信息
     */
    List<Date> findHolidayByYear(String year) throws Exception;

    List<Date> findHolidayByYearAndOffDates(String year) throws Exception;
}