package com.tsintergy.lf.serviceapi.base.weather.pojo;


import com.tsieframework.core.base.dao.BaseVO;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.WeatherFeature;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;

/**
 * 气象特性（实际）
 */
@Data
@Entity
@Table(name = "weather_feature_city_day_his_service")
@ApiModel
public class WeatherFeatureCityDayHisDO extends BaseVO implements WeatherFeature {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    /**
     * 日期f
     */
    @Column(name = "date")
    @ApiModelProperty(value = "日期",example = "2021-01-01")
    private Date date;

    /**
     * 年
     */
   // private String year;

    /**
     * 年月
     */

   // private String ym;

    /**
     * 城市ID
     */
    @Column(name = "city_id")
    @ApiModelProperty(value = "城市ID",example = "2")
    private String cityId;

    /**
     * 最高温度
     */
    @Column(name = "highest_temperature")
    @ApiModelProperty(value = "最高温度",example = "2")
    private BigDecimal highestTemperature;

    /**
     * 最低温度
     */
    @Column(name = "lowest_temperature")
    @ApiModelProperty(value = "最低温度",example = "2")
    private BigDecimal lowestTemperature;

    /**
     * 平均温度
     */
    @Column(name = "ave_temperature")
    @ApiModelProperty(value = "平均温度",example = "2")
    private BigDecimal aveTemperature;

    /**
     * 最高相对湿度
     */
    @Column(name = "highest_humidity")
    @ApiModelProperty(value = "最高相对湿度",example = "2")
    private BigDecimal highestHumidity;

    /**
     * 最低相对湿度
     */
    @Column(name = "lowest_humidity")
    @ApiModelProperty(value = "最低相对湿度",example = "2")
    private BigDecimal lowestHumidity;

    /**
     * 平均相对湿度
     */
    @Column(name = "ave_humidity")
    @ApiModelProperty(value = "平均相对湿度",example = "2")
    private BigDecimal aveHumidity;

    /**
     * 最大风速
     */
    @Column(name = "max_winds")
    @ApiModelProperty(value = "最大风速",example = "2")
    private BigDecimal maxWinds;

    /**
     * 最小风速
     */
    @Column(name = "min_winds")
    @ApiModelProperty(value = "最小风速",example = "2")
    private BigDecimal minWinds;

    /**
     * 平均风速
     */
    @Column(name = "ave_winds")
    @ApiModelProperty(value = "平均风速",example = "2")
    private BigDecimal aveWinds;

    /**
     * 最大人体舒适度
     */
    @Column(name = "highest_comfort")
    @ApiModelProperty(value = "最大人体舒适度",example = "2")
    private BigDecimal highestComfort;

    /**
     * 最低人体舒适度
     */
    @Column(name = "lowest_comfort")
    @ApiModelProperty(value = "最低人体舒适度",example = "2")
    private BigDecimal lowestComfort;

    /**
     * 平均人体舒适度
     */
    @Column(name = "ave_comfort")
    @ApiModelProperty(value = "平均人体舒适度",example = "2")
    private BigDecimal aveComfort;

    /**
     * 最高实感温度
     */
    @Column(name = "highest_effective_temperature")
    @ApiModelProperty(value = "最高实感温度",example = "2")
    private BigDecimal highestEffectiveTemperature;

    /**
     * 最低实感温度
     */
    @Column(name = "lowest_effective_temperature")
    @ApiModelProperty(value = "最低实感温度",example = "2")
    private BigDecimal lowestEffectiveTemperature;

    /**
     * 平均实感温度
     */
    @Column(name = "ave_effective_temperature")
    @ApiModelProperty(value = "平均实感温度",example = "2")
    private BigDecimal aveEffectiveTemperature;

    /**
     * 最大寒冷指数
     */
    @Column(name = "max_coldness")
    @ApiModelProperty(value = "最大寒冷指数",example = "2")
    private BigDecimal maxColdness;

    /**
     * 最小寒冷指数
     */
    @Column(name = "min_coldness")
    @ApiModelProperty(value = "最小寒冷指数",example = "2")
    private BigDecimal minColdness;

    /**平均寒冷指数
     *
     */
    @Column(name = "ave_coldness")
    @ApiModelProperty(value = "平均寒冷指数",example = "2")
    private BigDecimal aveColdness;

    /**
     * 最大温湿指数
     */
    @Column(name = "max_temperature_humidity")
    @ApiModelProperty(value = "最大温湿指数",example = "2")
    private BigDecimal maxTemperatureHumidity;

    /**
     * 最小温湿指数
     */
    @Column(name = "min_temperature_humidity")
    @ApiModelProperty(value = "最小温湿指数",example = "2")
    private BigDecimal minTemperatureHumidity;

    /**
     * 近三天降雨量
     */
    @Column(name = "recently_rainfall")
    @ApiModelProperty(value = "近三天降雨量",example = "2")
    private BigDecimal recentlyRainfall;

    /**
     * 平均温湿指数
     */
    @Column(name = "ave_temperature_humidity")
    @ApiModelProperty(value = "平均温湿指数",example = "2")
    private BigDecimal aveTemperatureHumidity;

    /**
     * 降雨量
     */
    @Column(name = "rainfall")
    @ApiModelProperty(value = "降雨量",example = "2")
    private BigDecimal rainfall;

    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    @ApiModelProperty(value = "创建时间",example = "2021-01-01")
    private Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    @ApiModelProperty(value = "更新时间",example = "2021-01-01")
    private Timestamp updatetime;



    public String getYear() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy");
        return simpleDateFormat.format(this.getDate());
    }

    public String getYm(){
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMM");
        return simpleDateFormat.format(this.getDate());
    }
}
