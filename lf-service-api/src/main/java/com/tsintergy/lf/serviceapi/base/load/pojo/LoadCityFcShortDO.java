package com.tsintergy.lf.serviceapi.base.load.pojo;


import com.tsieframework.core.base.dao.BaseVO;
import com.tsieframework.core.base.vo.annotation.EntityUniqueIndex;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * 超短期预测实体
 * <AUTHOR>
 */
@Data
@Entity
@EntityUniqueIndex({"cityId","date", "caliberId","algorithm_id","startTime", "type"})
@Table(name = "load_city_fc_short")
public class LoadCityFcShortDO extends BaseVO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    /**
     * 日期
     */
    @Column(name = "date")
    private Date date;

    /**
     * 城市ID
     */
    @Column(name = "city_id")
    private String cityId;

    /**
     * 口径ID
     */
    @Column(name = "caliber_id")
    private String caliberId;

    /**
     * 算法ID
     */
    @Column(name = "algorithm_id")
    private String algorithmId;

    /**
     * 开始时间 例 0015
     */
    @Column(name = "start_time")
    private String startTime;

    /**
     * 1 五分钟间隔 2 十分钟间隔
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

    @Column(name = "t1")
    private BigDecimal t1;

    @Column(name = "t2")
    private BigDecimal t2;

    @Column(name = "t3")
    private BigDecimal t3;

    @Column(name = "t4")
    private BigDecimal t4;

    @Column(name = "t5")
    private BigDecimal t5;

    @Column(name = "t6")
    private BigDecimal t6;

    @Column(name = "t7")
    private BigDecimal t7;

    @Column(name = "t8")
    private BigDecimal t8;

    @Column(name = "t9")
    private BigDecimal t9;

    @Column(name = "t10")
    private BigDecimal t10;

    @Column(name = "t11")
    private BigDecimal t11;

    @Column(name = "t12")
    private BigDecimal t12;

    @Column(name = "t13")
    private BigDecimal t13;

    @Column(name = "t14")
    private BigDecimal t14;

    @Column(name = "t15")
    private BigDecimal t15;

    @Column(name = "t16")
    private BigDecimal t16;

    @Column(name = "t17")
    private BigDecimal t17;

    @Column(name = "t18")
    private BigDecimal t18;

    @Column(name = "t19")
    private BigDecimal t19;

    @Column(name = "t20")
    private BigDecimal t20;

    @Column(name = "t21")
    private BigDecimal t21;

    @Column(name = "t22")
    private BigDecimal t22;

    @Column(name = "t23")
    private BigDecimal t23;

    @Column(name = "t24")
    private BigDecimal t24;

    @Column(name = "t25")
    private BigDecimal t25;

    @Column(name = "t26")
    private BigDecimal t26;

    @Column(name = "t27")
    private BigDecimal t27;

    @Column(name = "t28")
    private BigDecimal t28;

    @Column(name = "t29")
    private BigDecimal t29;

    @Column(name = "t30")
    private BigDecimal t30;

    @Column(name = "t31")
    private BigDecimal t31;

    @Column(name = "t32")
    private BigDecimal t32;

    @Column(name = "t33")
    private BigDecimal t33;

    @Column(name = "t34")
    private BigDecimal t34;

    @Column(name = "t35")
    private BigDecimal t35;

    @Column(name = "t36")
    private BigDecimal t36;

    @Column(name = "t37")
    private BigDecimal t37;

    @Column(name = "t38")
    private BigDecimal t38;

    @Column(name = "t39")
    private BigDecimal t39;

    @Column(name = "t40")
    private BigDecimal t40;

    @Column(name = "t41")
    private BigDecimal t41;

    @Column(name = "t42")
    private BigDecimal t42;

    @Column(name = "t43")
    private BigDecimal t43;

    @Column(name = "t44")
    private BigDecimal t44;

    @Column(name = "t45")
    private BigDecimal t45;

    @Column(name = "t46")
    private BigDecimal t46;

    @Column(name = "t47")
    private BigDecimal t47;

    @Column(name = "t48")
    private BigDecimal t48;

    @Column(name = "t49")
    private BigDecimal t49;

    @Column(name = "t50")
    private BigDecimal t50;

    @Column(name = "t51")
    private BigDecimal t51;

    @Column(name = "t52")
    private BigDecimal t52;

    @Column(name = "t53")
    private BigDecimal t53;

    @Column(name = "t54")
    private BigDecimal t54;

    @Column(name = "t55")
    private BigDecimal t55;

    @Column(name = "t56")
    private BigDecimal t56;

    @Column(name = "t57")
    private BigDecimal t57;

    @Column(name = "t58")
    private BigDecimal t58;

    @Column(name = "t59")
    private BigDecimal t59;

    @Column(name = "t60")
    private BigDecimal t60;

    @Column(name = "t61")
    private BigDecimal t61;

    @Column(name = "t62")
    private BigDecimal t62;

    @Column(name = "t63")
    private BigDecimal t63;

    @Column(name = "t64")
    private BigDecimal t64;

    @Column(name = "t65")
    private BigDecimal t65;

    @Column(name = "t66")
    private BigDecimal t66;

    @Column(name = "t67")
    private BigDecimal t67;

    @Column(name = "t68")
    private BigDecimal t68;

    @Column(name = "t69")
    private BigDecimal t69;

    @Column(name = "t70")
    private BigDecimal t70;

    @Column(name = "t71")
    private BigDecimal t71;

    @Column(name = "t72")
    private BigDecimal t72;

    @Column(name = "t73")
    private BigDecimal t73;

    @Column(name = "t74")
    private BigDecimal t74;

    @Column(name = "t75")
    private BigDecimal t75;

    @Column(name = "t76")
    private BigDecimal t76;

    @Column(name = "t77")
    private BigDecimal t77;

    @Column(name = "t78")
    private BigDecimal t78;

    @Column(name = "t79")
    private BigDecimal t79;

    @Column(name = "t80")
    private BigDecimal t80;

    @Column(name = "t81")
    private BigDecimal t81;

    @Column(name = "t82")
    private BigDecimal t82;

    @Column(name = "t83")
    private BigDecimal t83;

    @Column(name = "t84")
    private BigDecimal t84;

    @Column(name = "t85")
    private BigDecimal t85;

    @Column(name = "t86")
    private BigDecimal t86;

    @Column(name = "t87")
    private BigDecimal t87;

    @Column(name = "t88")
    private BigDecimal t88;

    @Column(name = "t89")
    private BigDecimal t89;

    @Column(name = "t90")
    private BigDecimal t90;

    @Column(name = "t91")
    private BigDecimal t91;

    @Column(name = "t92")
    private BigDecimal t92;

    @Column(name = "t93")
    private BigDecimal t93;

    @Column(name = "t94")
    private BigDecimal t94;

    @Column(name = "t95")
    private BigDecimal t95;

    @Column(name = "t96")
    private BigDecimal t96;

}
