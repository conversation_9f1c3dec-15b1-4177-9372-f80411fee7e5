package com.tsintergy.lf.serviceapi.base.evalucation.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
@ApiModel
@Data
public class StatisticsCityDayDTO implements Serializable {

    @ApiModelProperty(value = "预测准确率")
    private BigDecimal fcAccuracy;

    @ApiModelProperty(value = "综合准确率")
    private BigDecimal compositeAccuracy;

    @ApiModelProperty(value = "上报准确率")
    private BigDecimal reportAccuracy;

    public BigDecimal getFcAccuracy() {
        return fcAccuracy;
    }

    public void setFcAccuracy(BigDecimal fcAccuracy) {
        this.fcAccuracy = fcAccuracy;
    }

    public BigDecimal getReportAccuracy() {
        return reportAccuracy;
    }

    public void setReportAccuracy(BigDecimal reportAccuracy) {
        this.reportAccuracy = reportAccuracy;
    }
}
