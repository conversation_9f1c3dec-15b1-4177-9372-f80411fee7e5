/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.recall.api;

import com.tsintergy.lf.serviceapi.base.recall.dto.AccuracyLoadFcRecallDTO;
import com.tsintergy.lf.serviceapi.base.recall.pojo.AccuracyLoadCityFcServiceRecallDO;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/1 14:23
 * @Version: 1.0.0
 */
public interface AccuracyLoadCityFcRecallService {

    AccuracyLoadCityFcServiceRecallDO doSaveOrUpdate(AccuracyLoadCityFcServiceRecallDO accuracyLoadCityFcServiceRecallDO);

    List<AccuracyLoadCityFcServiceRecallDO> getAccuracyLoadCityFcRecallDO(String cityId, String caliberId, String algorithmId, Date date);

    List<AccuracyLoadCityFcServiceRecallDO> doSaveOrUpdateBatch(
        List<AccuracyLoadCityFcServiceRecallDO> accuracyLoadCityFcServiceRecallDOs);

    List<AccuracyLoadCityFcServiceRecallDO> getAccuracyLoadCityFcServiceRecallDOS(String cityId, String caliberId, Date startDate, Date endDate, String algorithmId) throws Exception;

    List<AccuracyLoadFcRecallDTO> getDTOS(String cityId, String caliberId, Date startDate, Date endDate, String algorithmId);

    String getMethodName(String time);

    BigDecimal getBigDecimalVal(Object obj, String methodName);

}
