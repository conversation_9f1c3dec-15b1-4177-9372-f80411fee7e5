/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  wang<PERSON>
 * Date:  2019/11/6 9:44
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.forecast.api;


import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.common.enumeration.LongMonthCategoryEnum;

import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 * 预测流程梳理
 * * 定时任务--自动预测（正常日和节假日）
 *
 * <AUTHOR>
 * @create 2019/11/6
 * @since 1.0.0
 */
public interface AutoForecastService {


    /**
     * @param uid
     * @param cityId
     * @param caliberId
     * @param startDate
     * @param endDate
     * @param enums
     * @param type
     * @throws Exception
     */
    void autoLongMonthForecast(String uid, String cityId, String caliberId, Date startDate, Date endDate,
                               List<AlgorithmEnum> enums, LongMonthCategoryEnum type) throws Exception;

    /**
     * 短期正常日自动预测
     *
     * @param forecastType
     * @param uid
     * @param cityId
     * @param caliberId
     * @param startDate
     * @param endDate
     * @throws Exception
     */
    void autoForecast(Integer forecastType, String uid, String cityId, String caliberId, Date startDate,
                      Date endDate, List<AlgorithmEnum> enums, Integer fcstDayWeatherType, Integer type, Integer pointNum) throws Exception;

    /**
     * 节假日预测
     *
     * @param uid
     * @param cityId
     * @param cailberId
     * @param startDate
     * @param endDate
     * @throws Exception
     */
    void doForecastHoliday(String uid, String cityId, String cailberId, Date startDate, Date endDate)
            throws Exception;

    /**
     * 短期正常日自动预测
     *
     * @param forecastType
     * @param uid
     * @param cityId
     * @param caliberId
     * @param startDate
     * @param endDate
     * @throws Exception
     */
    void autoForecastRecall(Integer forecastType, String uid, String cityId, String caliberId, Date startDate,
                            Date endDate, List<AlgorithmEnum> enums, Integer fcstDayWeatherType, Integer type, Integer pointNum) throws Exception;

    /**
     * Gru算法预测回溯
     */
    void doGruForecastRecall(Date startDate, Date endDate, Boolean userIndustry, Integer pointNum, String uid) throws Exception;

    /**
     * trans算法回溯
     *
     * @param startDate
     * @param endDate
     * @param pointNum
     * @param uid
     */
    void transForecastRecall(Date startDate, Date endDate, Integer pointNum, String uid);

    /**
     * GtNet算法预测回溯
     */
    void doGtNetForecastRecall(Date startDate, Date endDate, String uid) throws Exception;

    /**
     * ConvLstm算法预测回溯
     */
    void doConvLstmForecastRecall(Date startDate, Date endDate, Integer pointNum, String uid) throws Exception;

    /**
     * 节假日预测回溯
     *
     * @param uid
     * @param cityId
     * @param cailberId
     * @param startDate
     * @param endDate
     * @throws Exception
     */
    void doForecastHolidayRecall(String uid, String cityId, String cailberId, Date startDate, Date endDate)
            throws Exception;

    /**
     * 综合模型预测（自动预测)
     *
     * @param cityId
     * @param caliberId
     * @param startDate
     * @param endDate
     * @throws Exception
     */
    void autoModelFusionforecast(String cityId, String caliberId, Date startDate, Date endDate) throws Exception;

    /**
     * 综合模型预测（补预测)
     *
     * @param cityId
     * @param caliberId
     * @param startDate
     * @param endDate
     * @param fcWay     T+1   T+2
     * @throws Exception
     */
    void completionModelFusionforecast(String cityId, String caliberId, Date startDate, Date endDate, Integer fcWay) throws Exception;


}