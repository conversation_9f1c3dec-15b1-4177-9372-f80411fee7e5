/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  wangfeng Date:  2020/3/4 2:19 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.load.api;

import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityFcShortDO;
import java.util.Date;
import java.util.List;

/**
 * 超短期预测
 *
 * <AUTHOR>
 * @create 2020/6/17
 * @since 1.0.0
 */
public interface LoadCityFcShortService {

    /**
     * 保存or更新
     * @since 1.0.0     
     * <AUTHOR>
     */
    void saveOrUpdate(LoadCityFcShortDO loadCityFcShortDO) throws Exception;

    LoadCityFcShortDO findShortData(String startTime, Date date, String cityId, String caliberId, Integer type);

}