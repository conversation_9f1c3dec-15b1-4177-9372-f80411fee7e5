/**
 * Copyright (C), 2015‐2022, 北京清能互联科技有限公司 Author:  <EMAIL> Date:  2022/5/24 13:57 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.airconditioner.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Description: 基础负荷曲线 <br>
 *
 * <AUTHOR>
 * @create 2022/5/24
 * @since 1.0.0
 */
@ApiModel
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BaseLoadCurveRespDTO {

    @ApiModelProperty(value = "方案id", example = "123456")
    String solutionId;

    @ApiModelProperty(value = "96点曲线值")
    List<BigDecimal> dataValue;
}  
