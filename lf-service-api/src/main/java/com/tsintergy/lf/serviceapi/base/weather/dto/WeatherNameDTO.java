package com.tsintergy.lf.serviceapi.base.weather.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @date: 3/5/18 10:11 AM
 * @author: angel
 **/
@ApiModel
public class WeatherNameDTO implements Serializable{

    @ApiModelProperty(value = "名称",example = "一")
    private String name;

    @ApiModelProperty(value = "数据",example = "[1,2,3]")
    private List<BigDecimal> weather;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<BigDecimal> getWeather() {
        return weather;
    }

    public void setWeather(List<BigDecimal> weather) {
        this.weather = weather;
    }
}
