/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.algorithm.dto;

import com.tsintergy.lf.serviceapi.base.forecast.dto.SearchDTO;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Description:<br>
 *
 * @Author:<EMAIL>
 * @Date: 2022/5/24 15:19
 * @Version:1.0.0
 */
public class AcSensitivityParam extends com.tsintergy.aif.algorithm.serviceapi.base.dto.sensitivity.SensitivityParam {

    /**
     * 相似气象筛选条件列表
     */
    private List<SearchDTO> searchDTOS;

    /**
     * 不包含日期
     */
    List<String> dateNotIncluded;

    /**
     * 城市ID
     */
    protected String cityId;

    /**
     * 口径ID
     */
    protected String caliberId;

    /**
     * 调用用户id
     */
    protected String userId;

    /**
     * 是否正常日
     */
    boolean isNormalDay;

    /**
     * 时候休息日
     */
    boolean isRestDay;

    /**
     * 最小日降水量
     */
    private BigDecimal minRain;

    /**
     * 最大日降水量
     */
    private BigDecimal maxRain;

    public AcSensitivityParam(Date beginDate, Date endDate, String[] distinguishParams, String minTemp,
        String maxTemp, String step, String loadType, String weatherType) {
        super(beginDate, endDate, distinguishParams, minTemp, maxTemp, step, loadType, weatherType);
    }

    public List<SearchDTO> getSearchDTOS() {
        return searchDTOS;
    }

    public void setSearchDTOS(List<SearchDTO> searchDTOS) {
        this.searchDTOS = searchDTOS;
    }

    public List<String> getDateNotIncluded() {
        return dateNotIncluded;
    }

    public void setDateNotIncluded(List<String> dateNotIncluded) {
        this.dateNotIncluded = dateNotIncluded;
    }

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getCaliberId() {
        return caliberId;
    }

    public void setCaliberId(String caliberId) {
        this.caliberId = caliberId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public boolean isNormalDay() {
        return isNormalDay;
    }

    public void setNormalDay(boolean normalDay) {
        isNormalDay = normalDay;
    }

    public boolean isRestDay() {
        return isRestDay;
    }

    public void setRestDay(boolean restDay) {
        isRestDay = restDay;
    }

    public BigDecimal getMinRain() {
        return minRain;
    }

    public void setMinRain(BigDecimal minRain) {
        this.minRain = minRain;
    }

    public BigDecimal getMaxRain() {
        return maxRain;
    }

    public void setMaxRain(BigDecimal maxRain) {
        this.maxRain = maxRain;
    }
}