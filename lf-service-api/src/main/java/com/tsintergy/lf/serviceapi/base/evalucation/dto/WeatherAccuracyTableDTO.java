package com.tsintergy.lf.serviceapi.base.evalucation.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 气象预测准确率表格DTO
 */
@Data
@ApiModel("气象预测准确率表格")
public class WeatherAccuracyTableDTO {

    @ApiModelProperty("日期列表（D-1到D-10对应的实际日期）")
    private List<Date> dates;

    @ApiModelProperty("准确率数据，key为日期，value为D-1到D-10的准确率列表")
    private Map<Date, List<BigDecimal>> accuracyData;

    @ApiModelProperty("当前选择的气象指标类型（0-最高温度，1-最低温度，2-平均温度，3-相对湿度，4-累计降雨量，5-最大风速）")
    private Integer weatherIndicatorType;

    @ApiModelProperty("气象指标类型选项")
    private List<WeatherIndicatorOption> indicatorOptions;

    @Data
    @ApiModel("气象指标选项")
    public static class WeatherIndicatorOption {
        @ApiModelProperty("指标类型值")
        private Integer type;
        
        @ApiModelProperty("指标名称")
        private String name;
    }
}
