/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  ThinkPad Date:  2019/3/11 17:19 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.forecast.enums;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/3/11
 * @since 1.0.0
 */
public enum WeatherNewEnum {
    //湿度
    HUMIDITY(1, "湿度", "humidity"),

    TEMPERATURE(2, "温度", "temperature"),

    RAINFALL(3, "降雨量", "rainfall"),

    WINDSPEED(4, "风速", "windspeed");


    private Integer type;

    private String typeName;

    /**
     *  映射到setting_system_init的名字
     */
    private String systemSettingName;

    WeatherNewEnum(Integer type, String typeName, String systemSettingName) {
        this.type = type;
        this.typeName = typeName;
        this.systemSettingName = systemSettingName;
    }

    public static String getValueByName(Integer type) {
        for (WeatherNewEnum w : WeatherNewEnum.values()) {
            if (w.getType().equals(type)) {
                return w.getTypeName();
            }
        }
        return null;
    }

    public static WeatherNewEnum getEnumByType(Integer type) {
        for (WeatherNewEnum w : WeatherNewEnum.values()) {
            if (w.getType().equals(type)) {
                return w;
            }
        }
        return null;
    }


    public static Integer getTypeBySettingName(String systemSettingName) {
        for (WeatherNewEnum w : WeatherNewEnum.values()) {
            if (w.getSystemSettingName().equals(systemSettingName)) {
                return w.getType();
            }
        }
        return null;
    }


    public Integer value() {
        return this.type;
    }

    public String typeName() {
        return this.typeName;
    }


    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getSystemSettingName() {
        return systemSettingName;
    }

    public void setSystemSettingName(String systemSettingName) {
        this.systemSettingName = systemSettingName;
    }
}