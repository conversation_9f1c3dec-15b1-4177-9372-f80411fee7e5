/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  wang<PERSON>
 * Date:  2019/8/13 4:43
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.algorithm.dto;

import com.tsintergy.lf.serviceapi.base.forecast.dto.SearchDTO;
import lombok.Data;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 灵敏度分析算法请求参数
 *
 * <AUTHOR>
 * @create 2022/3/9
 * @since 1.0.0
 */
@Data
public class SensitivityParam extends Param {

    private Date beginDate;

    private Date endDate;

    private String max;

    private String min;

    private String step;

    private String loadType;

    private String weatherType;

    private String weatherId;

    private Integer type;

    private Integer configType;

    private String polyOrder;

    private String accumulate;

    /**
     * 相似气象筛选条件列表
     */
    private List<SearchDTO> searchDTOS;

    /**
     * 不包含日期
     */
    List<String> dateNotIncluded;

    private List<SensitivityAcInfo> sensitivityAcInfo;


    public String getYear() {
        SimpleDateFormat sf = new SimpleDateFormat("yyyy");
        String year = sf.format(beginDate);
        return year;
    }
}