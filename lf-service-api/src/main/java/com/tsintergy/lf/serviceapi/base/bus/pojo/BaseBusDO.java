package com.tsintergy.lf.serviceapi.base.bus.pojo;

import com.tsieframework.core.base.dao.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.Date;

/**
 * base_bus
 * 母线基础信息表
 */
@Data
@Entity
@Table(name = "base_bus_clct")
@ApiModel(description = "母线基础信息表")
public class BaseBusDO implements BaseDO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    @ApiModelProperty(value = "id",example = "5")
    private String id;


    @ApiModelProperty(value = "母线名称")
    @Column(name = "NAME")
    private String name;

    @ApiModelProperty(value = "所属站点id")
    @Column(name = "STATION_ID")
    private String stationId;



    @ApiModelProperty(value = "所属城市id")
    @Column(name = "CITY_ID")
    private String cityId;


    @ApiModelProperty(value = "调管")
    @Column(name = "CONTROL")
    private Integer control;


    @ApiModelProperty(value = "母线容量")
    @Column(name = "CAPACITY")
    private Integer capacity;


    @ApiModelProperty(value = "电压等级（KW）")
    @Column(name = "VOLTAGE")
    private Integer voltage;


    @ApiModelProperty(value = "运行状态 1运行、2停运")
    @Column(name = "STATUS")
    private Integer status;

    @ApiModelProperty(value = "重点母线 1是、0否")
    @Column(name = "IMPORTANT")
    private Integer important;


    @ApiModelProperty(value = "预测对象 1是、0否")
    @Column(name = "FORECAST")
    private Integer forecast;


    @ApiModelProperty(value = "考核对象 1是、0否")
    @Column(name = "EXAMINE")
    private Integer examine;


    @ApiModelProperty(value = "参与协调 1是、0否")
    @Column(name = "COORDINATE")
    private Integer coordinate;

    @ApiModelProperty(value = "断面id")
    @Column(name = "SECTION_ID")
    private String sectionId;

    @ApiModelProperty(value = "创建时间")
    @Column(name = "CREATETIME")
    private Date createtime;

    @ApiModelProperty(value = "更新时间")
    @Column(name = "UPDATETIME")
    private Date updatetime;

    @Column(name = "SEND_A")
    private Integer sendA;
    @Column(name = "NAME_A")
    private String nameA;
    @Column(name = "SEND_B")
    private Integer sendB;
    @Column(name = "NAME_B")
    private String nameB;
    @Column(name = "SEND_C")
    private Integer sendC;
    @Column(name = "NAME_C")
    private String nameC;
    @Column(name = "SEND_D")
    private Integer sendD;
    @Column(name = "NAME_D")
    private String nameD;


}