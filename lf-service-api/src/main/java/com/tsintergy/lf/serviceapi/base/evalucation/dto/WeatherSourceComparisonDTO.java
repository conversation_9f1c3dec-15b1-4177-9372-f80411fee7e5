package com.tsintergy.lf.serviceapi.base.evalucation.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 气象源对比DTO
 */
@Data
@ApiModel("气象源对比")
public class WeatherSourceComparisonDTO {

    @ApiModelProperty("气象源名称")
    private String sourceName;

    @ApiModelProperty("D-1到D-10的平均准确率列表")
    private List<BigDecimal> averageAccuracyList;

    @ApiModelProperty("总体平均准确率")
    private BigDecimal overallAverageAccuracy;
}
