/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.airconditioner.pojo;

import com.tsintergy.lf.serviceapi.base.base.pojo.Base96DO;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * Description:<br>
 *
 * @Author:<EMAIL>
 * @Date: 2022/5/24 10:37
 * @Version:1.0.0
 */
@Data
@Entity
@Table(name = "foundation_load_his_month")
public class FoundationLoadHisMonthDO extends Base96DO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    /**
     * 城市
     */
    @Column(name = "city_id")
    protected String cityId;

    /**
     * 口径ID
     */
    @Column(name = "caliber_id")
    protected String caliberId;

    /**
     * 年
     */
    @Column(name = "year")
    private String year;

    /**
     * 月
     */
    @Column(name = "month")
    private String month;


    /**
     * 1为正常日，2为休息日
     */
    private int type;

    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    protected Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    protected Timestamp updatetime;
}