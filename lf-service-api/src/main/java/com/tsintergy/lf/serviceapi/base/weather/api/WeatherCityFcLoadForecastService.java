/**
 *   
 *  Copyright (C), 2015‐2020, 北京清能互联科技有限公司  
 *  Author:  EDZ  
 *  Date:  2020/10/12 14:07  
 *  History:  
 *  <author> <time> <version> <desc> 
 */
package com.tsintergy.lf.serviceapi.base.weather.api;

import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherFeatureDTO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcLoadForecastDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisLoadForecastDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisLoadForecastRecallDO;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**  
 * Description:  <br> 
 * 
 * <AUTHOR>  
 * @create 2020/10/12  
 * @since 1.0.0  
 */
public interface WeatherCityFcLoadForecastService {

    Date findWeatherFcTime(String cityId, Integer type, List<String> algorithmId, Date date,
        String caliberId) throws Exception;

    List<WeatherDTO> findWeatherFc(String cityId, Integer type, List<String> algorithmId, Date date, String caliberId) throws Exception;

    List<WeatherFeatureDTO> findWeatherFeature(String cityId, Integer type, List<String> algorithmId, Date date,
        List<WeatherFeatureDTO> weatherFeatureList, String caliberId) throws Exception;

    void insertOrUpdateWeatherFcInfo(String cityId, Date date, String systemAlgorithmId, String caliberId,
        Integer batchId, Timestamp createtime);

    List<WeatherCityFcLoadForecastDO> findWeatherInfoFcLoadForecast(String cityId, Integer type, String algorithmId,
        Date date, String caliberId) throws Exception;
    List<WeatherCityFcLoadForecastDO> findWeatherInfoFcLoadForecastByBatchId(String cityId, Integer type, String algorithmId,
        Date date, String caliberId, Integer batchId) throws Exception;


    List<WeatherCityFcLoadForecastDO> findWeatherInfoFcLoadForecast(String cityId, Integer type, String algorithmId,
        Date startDate, Date endDate,String caliberId) throws Exception;
    List<WeatherCityFcLoadForecastDO> findWeatherInfoFcLoadForecastBatch(String cityId, Integer type, List<String> algorithmIds,List<Integer> batchIds,
        Date startDate, Date endDate,String caliberId) throws Exception;

    void doCreateAndFlush(WeatherCityFcLoadForecastDO weatherCityFcLoadForecastDO);

    void doSaveOrUpdate(WeatherCityFcLoadForecastDO weatherCityFcLoadForecastDO1);

    void doDelete(String id) throws Exception;

    void insertOrUpdateWeatherHisInfo(String cityId, Date date, String systemAlgorithmId, String caliberId,
        Integer batchId, Timestamp createtime);

    void doSaveOrUpdateFc(WeatherCityFcLoadForecastDO weatherCityFcLoadForecastDO);

    void doSaveOrUpdateHis(WeatherCityHisLoadForecastDO weatherCityHisLoadForecastDO);

    void doSaveOrUpdateHisRecall(WeatherCityHisLoadForecastRecallDO weatherCityHisLoadForecastDO);

    void insertOrUpdateWeatherHisInfoRecall(String cityId, Date date, String systemAlgorithmId, String caliberId);

    List<WeatherCityFcLoadForecastDO> findWeatherFcLoadForecastBatch(String cityId,
        String algorithmId, Date date, String caliberId, String batchId) throws Exception;

    List<WeatherCityHisLoadForecastDO> findWeatherHisLoadForecastBatch(String cityId,
        String algorithmId, Date date, String caliberId, String batchId) throws Exception;

    List<WeatherCityFcLoadForecastDO> findWeatherFcLoadForecastBatchByType(String cityId,
        String algorithmId,String caliberId, String batchId, Integer type,  Date startDate, Date endDate) throws Exception;



}