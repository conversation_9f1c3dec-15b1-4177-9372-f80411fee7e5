package com.tsintergy.lf.serviceapi.base.system.api;

import com.tsintergy.lf.serviceapi.base.system.pojo.SettingBatchInitDO;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/3/5 14:00
 **/
public interface SettingBatchInitService {

    /**
     * 获取系统设置的所有批次对象；
     * @return
     */
    List<SettingBatchInitDO> getBatchList();

    /**
     * 根据传入的时间，命中系统设置的时间段对应的批次id
     * @param date 年月日时分秒；
     * @return 批次id
     */
    Integer getBatchIdByTime(Date date);

    /**
     * 获取批次列表批次id-名称的map
     */
    Map<String, String> getBatchNameMap();


    /**
     * 通过id获取系统设置的所有批次对象；id为null时查询全天时段；
     * @return
     */
    SettingBatchInitDO getBatchById(String id);
}
