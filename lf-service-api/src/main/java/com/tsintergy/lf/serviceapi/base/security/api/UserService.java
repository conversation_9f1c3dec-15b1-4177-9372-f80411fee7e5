package com.tsintergy.lf.serviceapi.base.security.api;


import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsintergy.lf.serviceapi.base.base.pojo.LoadUserDO;

import java.util.List;

/**
 * @date: 6/14/18 3:39 PM
 * @author: angel
 **/
public interface UserService {

    DataPackage queryLoaderVO(DBQueryParam param) throws BusinessException;

    LoadUserDO findUserById(String id) throws BusinessException;

    void doDeleteUserById(String id) throws BusinessException;

    LoadUserDO doSaveOrUpdateUser(LoadUserDO loadUserVO) throws BusinessException;

    List<LoadUserDO> findLoadUserList(String cityId);

    LoadUserDO findByCloudUserId(String cloudUserId);
}
