package com.tsintergy.lf.serviceapi.base.evalucation.api;

import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyAlgorithmCurveDataDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyAlgorithmDataDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyDetailValueDataDTO;
import java.util.Date;
import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2025-02-13
 * @since 1.0.0
 */
public interface AccuracyAlgorithmService {

    List<AccuracyAlgorithmDataDTO> getAlgorithmListData(String cityId, Date startDate, Date endDate,
        String algorithmId, String caliberId) throws Exception;

    List<AccuracyDetailValueDataDTO> getAlgorithmListDetailData(String cityId, Date startDate, Date endDate,
        String algorithmId, String caliberId, String days) throws Exception;

    List<AccuracyAlgorithmCurveDataDTO> getAlgorithmListDetailCurveData(String cityId, Date startDate, Date endDate,
        String algorithmId, String caliberId, String days) throws Exception;
}
