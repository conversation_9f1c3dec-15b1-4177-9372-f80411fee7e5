/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.airconditioner.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * Description:<br>
 *
 * @Author:<EMAIL>
 * @Date: 2022/5/23 15:49
 * @Version:1.0.0
 */
@Data
@ApiModel
public class AcFeatureAnalyseDTO implements Serializable {

    @ApiModelProperty(value = "日期", example = "2021-03-16")
    private Date date;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "日最大空调负荷", example = "123121")
    private BigDecimal maxLoad;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "日最小空调负荷", example = "123121")
    private BigDecimal minLoad;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "日平均空调负荷", example = "123121")
    private BigDecimal avgLoad;

    @BigdecimalJsonFormat(percentConvert = 100)
    @ApiModelProperty(value = "最大空调负荷占比", example = "86.79%")
    private BigDecimal maxLoadProportion;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "累计电量", example = "123121")
    private BigDecimal energy;

    @ApiModelProperty(value = "日最高温度", example = "38")
    private BigDecimal highestTemperature;
}