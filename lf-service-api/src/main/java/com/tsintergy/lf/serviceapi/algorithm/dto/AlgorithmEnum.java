/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司
 * Author: wang<PERSON>
 * Date: 2018/7/31 13:22
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.algorithm.dto;

/**
 * Description: 算法枚举类型
 *
 * <AUTHOR>
 * @create 2018/7/31
 * @since 1.0.0
 */
public enum AlgorithmEnum {

    /**
     * 算法code值
     */
    RESULT_REPORT("", "最终上报", "-1"),
    FORECAST_MODIFY("100", "人工修正", "0"),
    FORECAST_SIMILAR("101", "相似日", "3"),
    FORECAST_SIMILAR_OFFSET("102", "偏差补偿", "4"),
    FORECAST_SVM("103", "支持向量机", "5"),
    FORECAST_SVM_LARGE("104", "标典", "7"),
    REPLENISH_LGB("105", "决策树", "8"),
    FORECAST_XGBOOST("106", "梯度提升", "6"),
    HOLIDAY_FEST_SVM("107", "节假日算法", "9"),
    LOAD_PRE_PROCESS("108", "数据清洗算法", "-1"),
    FORECAST_INNOVATION("109", "BP神经网络", "10"),
    //使用福州气象
    FORECAST_INNOVATION_CHECK("209", "BP神经网络优化", "209"),
    GRU_FACTORS("811_bak", "GRU神经网络", "811_bak"),
    //不使用行业负荷数据
    GRU_FACTORS_CHECK("812_bak", "GRU神经网络优化", "812_bak"),
    QR("813", "清软上报", "813"),
    G_TRANSFORMER("820", "GTransformer算法", "820"),
    GT_NET("700", "GtNet算法", "700"),
    CONV_LSTM("701", "ConvLstm卷积循环神经网络算法", "701"),
    TYPHOON_SVM("110", "台风预测算法", "11"),
    COMPREHENSIVE_MODEL("800", "综合模型", "16"),
    SENSITIVITY("111", "灵敏度算法", "1"),
    AC_SENSITIVITY("111", "灵敏度算法", "1"),
    STABILITY("113", "稳定度分析算法", "2"),
    SHORT_FORECAST("112", "超短期", "12"),
    ALL_CITY_FORECAST("114", "子网累加", "14"),
    SIMILAR_DAY_SEARCH("200", "综合相似日查找", "13"),
    LONE_COMPOSITE("201", "中长期综合模型", "21"),

    LONG_VEC_MAX("202", "中长期VEC算法-最大负荷", "22"),

    LONG_VEC_MIN("203", "中长期VEC算法-最小负荷", "23"),

    LONG_VEC_ENERGY("204", "中长期VEC算法-电量", "24"),
    REPORT_FINAL("822", "最终上报", "822"),
    BP_CHECK_OPTIMIZE("830", "bpCheck优化", "830"),
    G_TRANSFORMER_LONG_HOLIDAY("1813", "gTransformer长节假日算法", "1813"),
    G_TRANSFORMER_SHORT_HOLIDAY("2813", "gTransformer短节假日算法", "2813"),
    TRANS("823", "trans算法", "823"),
    Day_96_FORECAST_FC("517","96点预测（气象局）","812"),
    Day_96_FORECAST_EC("517","96点预测（ec）","811"),
    DAY_MAX_LOAD_FORECAST("516", "日最大负荷预测", "516")
            ;

    private String type;
    private String description;
    private String id;

    AlgorithmEnum(String type, String description, String id) {
        this.type = type;
        this.description = description;
        this.id = id;
    }

    public static AlgorithmEnum findById(String id) {
        AlgorithmEnum[] values = AlgorithmEnum.values();
        for (AlgorithmEnum algorithmEnum : values) {
            if (algorithmEnum.getId().equals(id)) {
                return algorithmEnum;
            }
        }
        return null;
    }

    public static AlgorithmEnum findByType(String type) {
        AlgorithmEnum[] values = AlgorithmEnum.values();
        for (AlgorithmEnum algorithmEnum : values) {
            if (algorithmEnum.getType().equals(type)) {
                return algorithmEnum;
            }
        }
        return null;
    }

    public static AlgorithmEnum findByCode(String code) {
        AlgorithmEnum[] values = AlgorithmEnum.values();
        for (AlgorithmEnum algorithmEnum : values) {
            if (algorithmEnum.getType().equals(code)) {
                return algorithmEnum;
            }
        }
        return null;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
