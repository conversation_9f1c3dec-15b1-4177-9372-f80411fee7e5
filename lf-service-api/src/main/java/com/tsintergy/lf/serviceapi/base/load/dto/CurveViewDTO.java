/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司
 * Author:  wangchen
 * Date:  2020/4/19 15:48
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.load.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/4/19
 * @since 1.0.0
 */
@ApiModel
public class CurveViewDTO implements Serializable {

    /**
     * 今日预测
     */
    public static final String  TODAY_FC = "todyFc";
    /**
     * 今日历史
     */
    public static final String  TODAY_HIS = "todayHis";
    /**
     * 昨日历史
     */
    public static final String  YESTARDAY_HIS = "yestardayHis";
    /**
     * 预测温度
     */
    public static final String TEMPERATURE_FC = "temperatureFc";
    /**
     * 实际温度
     */
    public static final String TEMPERATURE_HIS = "temperatureHis";

    public static final String YESTERDAY_HIS_LOAD = "yesterdayHisLoad";

    public static final String TODAY_FC_LOAD = "todayFcLoad";

    public static final String TODAY_HIS_LOAD = "todayHisLoad";

    private static final long serialVersionUID = -7756791576953100240L;

    @ApiModelProperty(value = "码值", example = "1")
    private String code;

    @ApiModelProperty(value = "对应的值", example = "[121,321,32]")
    private List<BigDecimal> value;

    public CurveViewDTO(String code, List<BigDecimal> value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public List<BigDecimal> getValue() {
        return value;
    }

    public void setValue(List<BigDecimal> value) {
        this.value = value;
    }
}
