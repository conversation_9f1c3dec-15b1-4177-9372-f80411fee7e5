/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.load.api;

import com.tsintergy.lf.serviceapi.base.load.dto.LoadAccuracyDTO;
import java.util.Date;

/**
 * Description:  <br>
 *
 * @Author: liu<PERSON><EMAIL>
 * @Date: 2022/3/31 14:16
 * @Version: 1.0.0
 */
public interface LoadResultService {
    /**
     * 时刻准确率
     * @param cityId
     * @param caliberId
     * @param date
     * @return
     * @throws Exception
     */
    LoadAccuracyDTO findAccuracy(String cityId, String caliberId, Date date) throws Exception;

}
