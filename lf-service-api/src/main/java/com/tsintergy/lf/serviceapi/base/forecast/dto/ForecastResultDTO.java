package com.tsintergy.lf.serviceapi.base.forecast.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.PercentSerialize1Digits;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * ForecastResultDTO
 *
 * <AUTHOR>
 * @date 2018/05/23 10:36
 */
@ApiModel
public class ForecastResultDTO implements Serializable {

    /**
     * 算法id
     */
    @ApiModelProperty(value = "算法id",example = "3")
    private String algorithmId;

    /**
     * 算法名字
     */
    @ApiModelProperty(value = "算法名字",example = "相似日")
    private String algorithmName;

    /**
     * 峰谷差率
     */
    @ApiModelProperty(value = "峰谷差率",example = "0.83")
    @BigdecimalJsonFormat(percentConvert =100)
    private BigDecimal gradient;

    /**
     * 负荷率
     */
    @BigdecimalJsonFormat(percentConvert =100)
    @ApiModelProperty(value = "负荷率",example = "0.93")
    private BigDecimal loadGradient;

    /**
     * 最大负荷
     */
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最大负荷",example = "32.3")
    private BigDecimal maxLoad;

    /**
     * 最小负荷
     */
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最小负荷",example = "32.3")
    private BigDecimal minLoad;

    /**
     * 负荷值
     */
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "负荷值",example = "[32.3,32.3]")
    private List<BigDecimal> value;

    public String getAlgorithmId() {
        return algorithmId;
    }

    public void setAlgorithmId(String algorithmId) {
        this.algorithmId = algorithmId;
    }

    public String getAlgorithmName() {
        return algorithmName;
    }

    public void setAlgorithmName(String algorithmName) {
        this.algorithmName = algorithmName;
    }

    public BigDecimal getGradient() {
        return gradient;
    }

    public void setGradient(BigDecimal gradient) {
        this.gradient = gradient;
    }

    public BigDecimal getLoadGradient() {
        return loadGradient;
    }

    public void setLoadGradient(BigDecimal loadGradient) {
        this.loadGradient = loadGradient;
    }

    public BigDecimal getMaxLoad() {
        return maxLoad;
    }

    public void setMaxLoad(BigDecimal maxLoad) {
        this.maxLoad = maxLoad;
    }

    public BigDecimal getMinLoad() {
        return minLoad;
    }

    public void setMinLoad(BigDecimal minLoad) {
        this.minLoad = minLoad;
    }

    public List<BigDecimal> getValue() {
        return value;
    }

    public void setValue(List<BigDecimal> value) {
        this.value = value;
    }
}
