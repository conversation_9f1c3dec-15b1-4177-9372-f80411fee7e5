/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  ThinkPad Date:  2019/5/15 7:26 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.forecast.dto;


import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

/**
 * 相似气象筛选条件
 *
 * <AUTHOR>
 * @create 2019/7/4
 * @since 1.0.0
 */

public class SearchDTO implements Serializable {

    /**
     * 日最高温1、日平均温2、日最低温3、日降雨量4、近3日降雨量5、日平均湿度6、日最大风速7、日平均风速8
     */
    @ApiModelProperty(value = "类型")
    private Integer type;

    /**
     * 大于1、等于2、小于3、介于4
     */
    @ApiModelProperty(value = "状态")
    private Integer condition;
    @ApiModelProperty(value = "值")
    private String value;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getCondition() {
        return condition;
    }

    public void setCondition(Integer condition) {
        this.condition = condition;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

}