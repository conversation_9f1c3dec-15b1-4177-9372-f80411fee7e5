package com.tsintergy.lf.serviceapi.base.weather.api;


import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayFcDO;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version $Id: WeatherFeatureCityDayFcService.java, v 0.1 2018-01-31 11:00:21 tao Exp $$
 */

public interface WeatherFeatureCityDayFcService  extends BaseFcWeatherFeatureService {

    /**
     * create entity
     */
    WeatherFeatureCityDayFcDO doCreate(WeatherFeatureCityDayFcDO vo) throws Exception;

    /**
     * find entity at target day
     */
    WeatherFeatureCityDayFcDO findWeatherFeatureCityFcVOByDate(String cityId, Date date) throws Exception;


    DataPackage listWeatherFeatureCityDayByPage(String cityId, Date startDate, Date endDate) throws Exception;

    WeatherFeatureCityDayFcDO weatherFeatureCityDayFcVO(Date date, String cityId) throws Exception;

    WeatherFeatureCityDayFcDO findWeatherFeatureCityDayFcVO(Date date, String cityId) throws Exception;

    void doSaveOrUpdateWeatherFeatureCityDayFcDOs(List<WeatherFeatureCityDayFcDO> weatherFeatureCityDayFcDOS);

    List<WeatherFeatureCityDayFcDO> findByDate(Date date);

    List<WeatherFeatureCityDayFcDO> getWeatherFeatureCityDayFcDOList(String cityId, Date startDate, Date endDate) throws Exception;
}