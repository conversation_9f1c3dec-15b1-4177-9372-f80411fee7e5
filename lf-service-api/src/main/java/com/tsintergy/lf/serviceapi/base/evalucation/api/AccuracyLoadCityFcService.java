
package com.tsintergy.lf.serviceapi.base.evalucation.api;


import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyLoadCityFcDO;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $Id: AccuracyLoadCityFcService.java, v 0.1 2018-01-31 10:15:11 tao Exp $$
 */

public interface AccuracyLoadCityFcService {
    /**
     *  search entity list
     * @param param
     * @return
     * @throws BusinessException
     */
    public DataPackage queryAccuracyLoadCityFcDO(DBQueryParam param) throws Exception;

    /**
     * create entity
     * @param vo
     * @return
     * @throws Exception
     */
    public AccuracyLoadCityFcDO doCreate(AccuracyLoadCityFcDO vo) throws Exception;

    /**
     *  delete entity by object
     * @param vo
     * @throws Exception
     */
    public void doRemoveAccuracyLoadCityFcDO(AccuracyLoadCityFcDO vo) throws Exception;

    /**
     * delete entity by PK
     * @param pk
     * @throws Exception
     */
    public void doRemoveAccuracyLoadCityFcDOByPK(Serializable pk) throws Exception;

    /**
     * update entity object
     * @param vo
     * @return
     * @throws Exception
     */
    public AccuracyLoadCityFcDO doUpdateAccuracyLoadCityFcDO(AccuracyLoadCityFcDO vo) throws Exception;

    /**
     * find entity by PK
     * @param pk
     * @return
     * @throws Exception
     */
    public AccuracyLoadCityFcDO findAccuracyLoadCityFcDOByPk(Serializable pk) throws Exception;

    List<AccuracyLoadCityFcDO> getAccuracyLoadCityFcDOList(String cityId, String caliberId, String algorithmId, Date startDate, Date endDate, Boolean isReport)throws Exception;


    List<AccuracyLoadCityFcDO> getAccuracyLoadCityFcDOList(String cityId, String caliberId, Date date, String algorithmId) throws Exception;

    List<AccuracyLoadCityFcDO> getAccuracyLoadCityFcDOList(List<String> cityIds,String algorithmId,Date startDate,Date endDate,String caliberId, Boolean isReport) throws Exception;



}