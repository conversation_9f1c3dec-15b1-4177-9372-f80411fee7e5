package com.tsintergy.lf.serviceapi.base.evalucation.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 气象预测准确率评估响应DTO
 */
@Data
@ApiModel("气象预测准确率评估响应")
public class WeatherAccuracyAssessmentDTO {

    @ApiModelProperty("气象预测准确率表格数据")
    private WeatherAccuracyTableDTO accuracyTable;

    @ApiModelProperty("预测结果对比数据")
    private List<WeatherSourceComparisonDTO> sourceComparison;

    @ApiModelProperty("负荷预测偏差数据")
    private WeatherDeviationDetailDTO deviationDetail;

    @ApiModelProperty("气象预测对比数据")
    private WeatherForecastComparisonDTO forecastComparison;
}
