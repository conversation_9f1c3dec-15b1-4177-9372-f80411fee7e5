package com.tsintergy.lf.serviceapi.base.recall.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class AccuracyLoadFcRecallDTO implements Serializable {
    @ApiModelProperty(value = "日期", example = "2021-01-01")
    private String date;

    @ApiModelProperty(value = "预测准确率", example = "0.99")

    private BigDecimal fcAccuracy;

    @ApiModelProperty(value = "回溯准确率", example = "0.98")
    private BigDecimal recallAccuracy;

    @BigdecimalJsonFormat(percentConvert = 100, scale = 2)
    @ApiModelProperty(value = "回溯提升", example = "0.96")
    private BigDecimal recallDeviation;

    @ApiModelProperty(value = "气温预测偏差", example = "0.91")
    private BigDecimal highestTemperatureFcDeviation;

    @ApiModelProperty(value = "高峰预测偏差")
    private BigDecimal peakFcDeviation;

    @ApiModelProperty(value = "高峰回溯偏差")
    private BigDecimal peakRecallDeviation;

    @ApiModelProperty(value = "高峰预测准确率")
    private BigDecimal peakFcAccuracy;

    @ApiModelProperty(value = "高峰回溯准确率")
    private BigDecimal peakRecallAccuracy;
}
