/**
 * Copyright(C),2015-2018,北京清能互联科技有限公司
 * Author:   jxm
 * Date:    2018/11/298:58
 * History:
 * <author><time><version><desc>
 */
package com.tsintergy.lf.serviceapi.base.typhoon.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Description:气象散点图<br>
 *
 * <AUTHOR>
 * @create2018/11/29
 * @since1.0.0
 */
@ApiModel
public class TyphoonScatterDTO implements Serializable {

    @ApiModelProperty(value = "日期",example = "2021-03-24")
    private Date date;
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "行",example = "0")
    private BigDecimal row;
    @ApiModelProperty(value = "列",example = "0")
    private BigDecimal column;

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public BigDecimal getRow() {
        return row;
    }

    public void setRow(BigDecimal row) {
        this.row = row;
    }

    public BigDecimal getColumn() {
        return column;
    }

    public void setColumn(BigDecimal column) {
        this.column = column;
    }

    @Override
    public String toString() {
        return "TyphoonScatterDTO{" +
                "row=" + row +
                ", column=" + column +
                '}';
    }
}

