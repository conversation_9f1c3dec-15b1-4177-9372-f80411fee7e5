/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.recall.api;

import com.tsintergy.lf.serviceapi.base.recall.dto.LoadFcQueryDTO;
import com.tsintergy.lf.serviceapi.base.recall.pojo.LoadCityFcRecallDO;
import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/1 14:09
 * @Version: 1.0.0
 */
public interface LoadCityFcRecallService {


    /**
     * 查询回溯预测曲线
     *
     * @param cityId
     * @param caliberId
     * @param startDate
     * @param endDate
     * @param algorithmId
     * @return
     * @throws Exception
     */
    List<LoadCityFcRecallDO> getLoadCityFc(String cityId, String caliberId, Date startDate, Date endDate, String algorithmId) throws Exception;



    /**
     * 保存预测结果
     *
     * @param loadCityFcRecallDO
     * @throws Exception
     */
    LoadCityFcRecallDO doSaveOrUpdateLoadCityFcDO96(LoadCityFcRecallDO loadCityFcRecallDO) throws Exception;


    /**
     * 预测负荷曲线
     */
    LoadCityFcRecallDO getLoadCityFcRecallDO(String cityId, String caliberId, Date date, String algorithmId) throws Exception;

    /**
     * 用于获取返回值
     */
    LoadFcQueryDTO getLoadFcQueryDTO(String cityId, String caliberId, Date date, String algorithmId) throws Exception;

    LoadFcQueryDTO getLoadBatchFcQueryDTO(String cityId, String caliberId, Date date, String algorithmId
            , String batchId, Integer day);

}
