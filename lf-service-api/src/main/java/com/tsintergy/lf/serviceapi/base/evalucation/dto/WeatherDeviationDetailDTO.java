package com.tsintergy.lf.serviceapi.base.evalucation.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 气象预测偏差详情DTO
 */
@Data
@ApiModel("气象预测偏差详情")
public class WeatherDeviationDetailDTO {

    @ApiModelProperty("选中的日期")
    private Date selectedDate;

    @ApiModelProperty("选中的预测天数（D-1到D-10）")
    private Integer selectedDay;

    @ApiModelProperty("日最高温度偏差")
    private BigDecimal maxTemperatureDeviation;

    @ApiModelProperty("日最低温度偏差")
    private BigDecimal minTemperatureDeviation;

    @ApiModelProperty("日平均温度偏差")
    private BigDecimal avgTemperatureDeviation;

    @ApiModelProperty("预测最高温度")
    private BigDecimal forecastMaxTemperature;

    @ApiModelProperty("实际最高温度")
    private BigDecimal actualMaxTemperature;

    @ApiModelProperty("预测最低温度")
    private BigDecimal forecastMinTemperature;

    @ApiModelProperty("实际最低温度")
    private BigDecimal actualMinTemperature;

    @ApiModelProperty("预测平均温度")
    private BigDecimal forecastAvgTemperature;

    @ApiModelProperty("实际平均温度")
    private BigDecimal actualAvgTemperature;
}
