
package com.tsintergy.lf.serviceapi.base.forecast.api;

import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.forecast.dto.ForecastDTO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFc288DO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcTempDO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherDTO;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version $Id: LoadCityFcService.java, v 0.1 2018-01-31 10:23:53 tao Exp $$
 */

public interface LoadCityFcService {
    /**
     * search entity list
     *
     * @param param
     * @return
     * @throws BusinessException
     */
    DataPackage queryLoadCityFcDO(DBQueryParam param) throws Exception;

    /**
     * create entity
     *
     * @param vo
     * @return
     * @throws Exception
     */
    LoadCityFcDO doCreate(LoadCityFcDO vo) throws Exception;

    /**
     * delete entity by object
     *
     * @param vo
     * @throws Exception
     */
    void doRemoveLoadCityFcDO(LoadCityFcDO vo) throws Exception;

    /**
     * delete entity by PK
     *
     * @param pk
     * @throws Exception
     */
    void doRemoveLoadCityFcDOByPK(Serializable pk) throws Exception;

    /**
     * update entity object
     *
     * @param vo
     * @return
     * @throws Exception
     */
    LoadCityFcDO doUpdateLoadCityFcDO(LoadCityFcDO vo) throws Exception;

    /**
     * find entity by PK
     *
     * @param pk
     * @return
     * @throws Exception
     */
    LoadCityFcDO findLoadCityFcDOByPk(Serializable pk) throws Exception;


    /**
     * find forecast LoadCityFcDO by date and cityId and caliberId
     *
     * @param date
     * @param cityId
     * @param caliberId
     * @param algorithmId
     * @return
     * @throws Exception
     */
    LoadCityFcDO getLoadCityFcDO(Date date, String cityId, String caliberId, String algorithmId) throws Exception;


    /**
     * find forecast LoadCityFcDO by date and cityId and caliberId
     *
     * @param startDate
     * @param endDate
     * @param cityId
     * @param caliberId
     * @param algorithmId
     * @return
     * @throws Exception
     */
    List<WeatherDTO> getLoadCityFcByDateVO(Date startDate, Date endDate, String cityId, String caliberId, String algorithmId) throws Exception;


    /**
     * find forecast LoadCityFcDO.toList by date and cityId and caliberId
     *
     * @param date
     * @param cityId
     * @param caliberId
     * @param algorithmId
     * @return
     * @throws Exception
     */
    List<BigDecimal> findLoadCityFcDO(Date date, String cityId, String caliberId, String algorithmId) throws Exception;


    /**
     * 查询上报结果
     *
     * @param date
     * @param cityId
     * @return
     * @throws Exception
     */
    LoadCityFcDO getReportLoadCityFcDO(Date date, String cityId, String caliberId) throws Exception;

    /**
     * 查询上报结果
     *
     * @param date
     * @param cityId
     * @return
     * @throws Exception
     */
    ForecastDTO findReportForecastDTO(Date date, String cityId, String caliberId) throws Exception;

    /**
     * 保存预测结果
     *
     * @param LoadCityFcDO
     * @throws Exception
     */
    LoadCityFcDO doSaveOrUpdateLoadCityFcDO96(LoadCityFcDO LoadCityFcDO) throws Exception;

    /**
     * 保存预测结果
     *
     * @param LoadCityFcDO
     * @throws Exception
     */
    LoadCityFc288DO doSaveOrUpdateLoadCityFcDO288(LoadCityFc288DO LoadCityFcDO) throws Exception;

    /**
     * 上报预测结果
     *
     * @param LoadCityFcDO
     * @return
     */
    LoadCityFcDO doReport(LoadCityFcDO LoadCityFcDO) throws Exception;


    /**
     * find ForecastDTO by date and cityId and caliberId
     *
     * @param date
     * @param cityId
     * @param caliberId
     * @param algorithmId
     * @return
     * @throws Exception
     */
    ForecastDTO findForecastDTO(Date date, String cityId, String caliberId, String algorithmId) throws Exception;

    /**
     * 获取上报算法ID
     *
     * @param cityId
     * @param date
     * @return
     */
    String getReportAlgorithmId(String cityId, Date date, String caliberId) throws Exception;

    List<BigDecimal> findReportLoadCityFcDO(Date date, String cityId, String caliberId, String algorithmId,
        Boolean report) throws Exception;

    LoadCityFcDO findReportCityFcDO(Date date, String cityId, String caliberId,
        String algorithmId, Boolean report) throws Exception;

    List<BigDecimal> findReportLoadCityFc288DO(Date date, String cityId, String caliberId, String algorithmId) throws Exception;

    List<LoadCityFcDO> findAll() throws Exception;

    LoadCityFcDO getRecommendLoadCityFcDO(String cityId, String caliberId, Date date) throws Exception;

    LoadCityFc288DO find288DOByDate(Date date, String cityId, String caliberId, String algorithmId)
        throws Exception;

    List<LoadCityFcDO> findLoadCityFcDO(String cityId, String caliberId, String algorithmId) throws Exception;


    LoadCityFcDO getLoadCityFc(String cityId, String caliberId, Date startDate, Date endDate, String algorithmId) throws Exception;


    List<LoadCityFcDO> listLoadCityFc(String cityId, String caliberId, Date startDate, Date endDate, String algorithmId) throws Exception;

    List<LoadCityFcDO> listReportLoadCityFc(String cityId, String caliberId, Date startDate, Date endDate, String algorithmId) throws Exception;

    LoadCityFcDO getReport(String cityId, String caliberId, Date date) throws Exception;


    public LoadCityFcDO getReportLoadCityFcDOWithOutNull(Date date, String cityId, String caliberId) throws Exception;


    List<LoadCityFcDO> getLoadFcByAlgorithmId(Date date, String cityId, String caliberId, List<String> algorithmId) throws Exception;


    LoadCityFcDO getLoadFc(Date date, String cityId, String caliberId, String algorithmId) throws Exception;


    LoadCityFcTempDO doSaveOrUpdate(List<List<String>> dataList, String cityId, String userId, String caliberId) throws BusinessException;

    /**
     * 查询符合条件的上报预测数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param caliberId 口径I
     * @param cityId 城市ID
     * @return 所有符合条件的对象
     * @throws Exception 如果查询过程中出现错误，则抛出异常。
     */
    List<LoadCityFcDO> findReportLoadFc(String cityId, String caliberId, Date startDate, Date endDate) throws Exception;

    List<LoadCityFcDO> findFcByAlgorithmId(String cityId, String caliberId, String algorithmId,Date startDate, Date endDate) throws Exception;

    List<LoadCityFcDO> getReportLoadCityFcDO(String cityId, String caliberId,  Date startDate,
                                             Date endDate , Integer report) throws Exception;

    List<LoadCityFcDO> findLoadCityFcReport(Date date,String caliberId) throws Exception;

    LoadCityFcDO find96LoadCityFcValue(Date date, String caliberId, String cityId) throws Exception;

    List<LoadCityFcDO> findLoadCityFc(List<String> cityIds, Date startDate, Date endDate, String caliberId) throws Exception;

    void doIntegratedModelAlgorithmData(CityDO CityDO, Date startDate) throws Exception;

    void doCreateAndFlush(LoadCityFcDO loadCityFcDO);

    List<LoadCityFcDO> findLoadCityFcReportByUserId(String cityId, String caliberId, String userId, Date startDate, Date endDate);



    void doStatisticsAllCityLoad(String caliberId,Date date)throws Exception;
    /**
     * 查找指定日期、口径ID和城市ID的预测负荷曲线
     *
     * @param date 指定的日期
     * @param caliberId 口径I
     * @param cityId 城市ID
     * @return 一条预测负荷曲线
     * @throws Exception 如果查询过程中出现错误，则抛出异常。
     */
    List<BigDecimal> findReportLoad(Date date, String caliberId, String cityId) throws Exception;
    /**
     * 查找指定日期、口径ID的九地市累加预测负荷曲线
     *
     * @param date 指定的日期
     * @param caliberId 口径I
     * @return 一条九地市累加后的预测负荷曲线
     * @throws Exception 如果查询过程中出现错误，则抛出异常。
     */
    List<BigDecimal> loadCityFcAccumulation(Date date, String caliberId) throws Exception;

    List<BigDecimal> findCityFcAdd(String caliberId, String algorithmId, Date targetDay);

    void reportDayForecastResultFileToGuoDiao(Date date) throws Exception;

    void reportDayForecastResultFileToDky(Date date) throws Exception;
}