
package com.tsintergy.lf.serviceapi.base.forecast.api;


import com.tsieframework.core.base.exception.BusinessException;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.algorithm.dto.GeneralResult;
import com.tsintergy.lf.serviceapi.base.forecast.dto.*;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadDecomposeCityWeekStabilityDO;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 预测服务
 *
 * <AUTHOR>
 * @version $Id: CheckService.java, v 0.1 2018-01-31 10:15:11 tao Exp $$
 */

public interface ForecastService {

    /**
     * 获取预测概览信息
     *
     * @param cityId 城市ID
     * @param date 日期
     */
    ForecastOverviewDTO getForecastOverview(String cityId, Date date, String caliberId) throws Exception;

    /**
     * 曲线平滑
     */
    List<BigDecimal> smoothLine(List<BigDecimal> singleLoadCityVOList) throws Exception;

    /**
     * 根据考核点进行曲线平滑
     */
    List<BigDecimal> smoothLineByAssess(List<BigDecimal> singleLoadCityVOList) throws Exception;

    /**
     * 曲线修正
     */
    List<BigDecimal> recorrectLoad(List<BigDecimal> originLoad, BigDecimal distMax, BigDecimal distMin)
            throws BusinessException;

    /**
     * 根据考核点进行曲线修正
     */
    List<BigDecimal> recorrectLoadByAssess(List<BigDecimal> originLoad, BigDecimal earlyPeak, BigDecimal noonPeak, BigDecimal eveningPeak, BigDecimal waistLoad, BigDecimal trough) throws BusinessException;

    /**
     * 高级正常日预测
     */
    List<ForecastNormalDTO> doAdvancedForecastNormal(String cityId, String caliberId, List<Date> forecastDays,
                                                     List<AlgorithmEnum> algorithmEnums)
            throws Exception;

    List<LoadCityFcDO> doForecastNormal(String cityId, String caliberId, List<Date> forecastDays,
                                        List<AlgorithmEnum> algorithmEnums)
        throws Exception;

    /**
     * 用户页面手动调用算法入口
     */
    void doForecastNormal(String cityId, String caliberId, List<Date> forecastDays,
        List<AlgorithmEnum> algorithmEnums, String weatherCode)
        throws Exception;


    List<LoadCityFcDO> insertNormalData(String cityId, String caliberId,
        GeneralResult result)
        throws Exception;
    /**
     * 自动预测通用接口
     */
    void doForecast(String cityId, String caliber, List<Date> forecastDates, AlgorithmEnum algorithmEnums)
        throws Exception;

    /**
     * 获取最大or最小置信上限或下限
     */
    Map<String, List<BigDecimal>> getMaxMinConfidence(String cityId, String caliberId, String algorithmId, Date date)
            throws Exception;

    /**
     * 执行稳定度算法 入库
     */
    LoadDecomposeCityWeekStabilityDO doPrecisionAnalysize(String caliberId, String cityId, Date date, int weeks,
                                                          String userId) throws Exception;

    /**
     * 获取最近一次预测
     */
    List<ForecastNormalDTO> getForecastNormal(String cityId, String caliberId, Date startDate, Date EndDate)
            throws BusinessException;

    /**
     * 节假日预测
     */
    List<ForecastNormalDTO> doForecastHoliday(String uid, String cityId, String cailberId, Date startDate, Date endDate)
        throws Exception;

    void insertOrUpdateHoliday(String cityId, String caliberId, GeneralResult result) throws Exception;
    /**
     * 台风预测
     *
     * @param loginDay 台风登陆日
     */
    void doForecastTyphoon(String cityId, String caliberId, Date loginDay, Integer type)
        throws Exception;

    /**
     * 功能描述: <br> 调用数据修正算法
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    void doDataRepairAlgorithm(String cityId, String caliberId, Date startDate, Date endDate) throws Exception;

    /**
     * 平均偏差
     */
    List<BigDecimal> getAvgDeviation(String cityId, String caliberId, String algorithmId, Date date) throws Exception;


    /**
     * 灵敏度分析计算&查询接口
     */
    ResultDTO doSensitivityAlgorithm(SensitivityAlgorithmDTO algorithmDTO) throws Exception;

    ResultDTO doAcSensitivityAlgorithm(SensitivityAlgorithmDTO algorithmDTO) throws Exception;

    ResultMonthDTO doAcMonthSensitivityAlgorithm(SensitivityAlgorithmDTO algorithmDTO) throws Exception;

    FeatureDTO doAcSensitivityFeatureAlgorithm(SensitivityAlgorithmDTO algorithmDTO) throws Exception;

    void doAcSensitivitySaveConfig(String cityId, String year, Integer seasonType, Integer type, Integer loadTyp,
                                   BigDecimal value) throws Exception;

    List<String> queryAcSensitivitySaveConfig(String cityId, String year, Integer seasonType, Integer type, Integer loadTyp,
                                   BigDecimal value) throws Exception;

    void acSaveDateConfig(String cityId, String year, Integer seasonType, Integer type, Integer loadTyp,
                          List<String> dateList, Integer acType) throws Exception;

    void doShortForecast(String cityId, String caliberId, Date date, Integer timeSpan,
        Integer startTimePoint) throws Exception;

    ForecastLoadFeatureDTO getFcLoadFeatureData(Date targetDate, String cityId, String algorithmId) throws Exception;

    void copyForecastResult(String sourceAlgoId, String targetAlgoId, Date startDate, Date endDate) throws Exception;

    List<ForecastBpCheckDTO> getForecastBpCheckData(String cityId, String caliberId, Date date) throws Exception;

    List<ForecastBpCheckDTO> getForecastBpCheckFeatureData(String cityId, String caliberId, Date date, String algorithmId) throws Exception;

    List<BigDecimal> getBpCheckHisData(String cityId, String caliberId, Date date) throws Exception;

    void saveBpCheckHisData(String cityId, String caliberId, Date date, List<BigDecimal> data, Date targetDate, String userId) throws Exception;

    void saveDayBpCheckHisData(Date date, String cityId, String caliberId, String uid) throws Exception;
}
