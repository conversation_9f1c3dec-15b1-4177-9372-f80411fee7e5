package com.tsintergy.lf.serviceapi.base.load.pojo;

import com.tsieframework.core.base.dao.BaseVO;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

/**
 * 月负荷特性
 */
@Data
@Entity
@Table(name = "load_feature_city_month_his_service")
public class LoadFeatureCityMonthHisDO extends BaseVO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    /**
     * 年
     */
    @Column(name = "year")
    private String year;

    /**
     * 月
     */
    @Column(name = "month")
    private String month;

    /**
     * 城市ID
     */
    @Column(name = "city_id")
    private String cityId;

    /**
     * 口径ID
     */
    @Column(name = "caliber_id")
    private String caliberId;

    /**
     * 最大负荷
     */
    @Column(name = "max_load")
    private BigDecimal maxLoad;

    /**
     * 最小负荷
     */
    @Column(name = "min_load")
    private BigDecimal minLoad;

    /**
     * 平均负荷
     */
    @Column(name = "ave_load")
    private BigDecimal aveLoad;

    /**
     * 最大负荷日
     */
    @Column(name = "max_date")
    private Date maxDate;

    /**
     * 最大负荷发生时刻
     */
    @Column(name = "max_time")
    private String maxTime;

    /**
     * 最小负荷发生时刻
     */
    @Column(name = "min_time")
    private String minTime;

    /**
     * 尖峰平均负荷
     */
    @Column(name = "peak")
    private BigDecimal peak;

    /**
     * 低谷平均负荷
     */
    @Column(name = "trough")
    private BigDecimal trough;

    /**
     * 峰谷差
     */
    @Column(name = "different")
    private BigDecimal different;

    /**
     * 峰谷差率
     */
    @Column(name = "gradient")
    private BigDecimal gradient;

    /**
     * 负荷率
     */
    @Column(name = "load_gradient")
    private BigDecimal loadGradient;

    /**
     * 月电量
     */
    @Column(name = "energy")
    private BigDecimal energy;

    /**
     * 日不均衡系数
     */
    @Column(name = "day_unbalance")
    private BigDecimal dayUnbalance;

    /**
     * 稳定度上限
     */
    @Column(name = "upper_stability")
    private BigDecimal upperStability;

    /**
     * 稳定度下限
     */
    @Column(name = "lower_stability")
    private BigDecimal lowerStability;

    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;
}
