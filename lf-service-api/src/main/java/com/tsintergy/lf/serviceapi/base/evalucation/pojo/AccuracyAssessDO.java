package com.tsintergy.lf.serviceapi.base.evalucation.pojo;


import com.tsieframework.core.base.dao.BaseVO;
import com.tsieframework.core.base.vo.annotation.EntityUniqueIndex;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * 考核点准确率实体
 */
@Data
@Entity
@Table(name = "accuracy_assess_service")
@EntityUniqueIndex({"cityId", "date", "batchId","algorithmId", "caliberId", "assessName"})
public class AccuracyAssessDO extends BaseVO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    /**
     * 城市ID
     */
    @Column(name = "city_id")
    private String cityId;

    /**
     * 日期
     */
    @Column(name = "date")
    private Date date;

    /**
     * 算法ID
     */
    @Column(name = "algorithm_id")
    private String algorithmId;

    /**
     * 批次号
     */@Column(name = "batch_id")
    private Integer batchId;

    /**
     * 口径ID
     */
    @Column(name = "caliber_id")
    private String caliberId;


    /**
     * 考核点名称
     */
    @Column(name = "assess_name")
    private String assessName;

    /**
     * 考核点ID
     */
    @Column(name = "assess_id")
    private String assessId;

    /**
     * 计算【考核点准确率】时使用的考核值类型：1最大 2最小 3平均
     */
    @Column(name = "assess_type")
    private Integer assessType;

    /**
     * 准确率
     */
    @Column(name = "accuracy")
    private BigDecimal accuracy;


    /**
     * 创建时间
     */
    @Column(name = "createtime")
    private Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

    /**
     * 是否上报
     */
    @Column(name = "report")
    private Boolean report;


}
