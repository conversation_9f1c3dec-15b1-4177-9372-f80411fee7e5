/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司 Author: wangfeng Date: 2018/7/31 10:03 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.algorithm.dto;

import java.util.Date;
import lombok.Data;

/**
 * 超短期算法参数
 *
 * <AUTHOR>
 * @create 2020/6/16
 * @since 1.0.0
 */
@Data
public class ShortForecastParam extends ForecastParam {


    /**
     * 预测日期
     */
    private Date forecastDate;


    /**
     * 时间间隔5 or 15
     */
    private String  timeSpan;

    /**
     * 预测起始点数
     */
    private String startTimePoint;

    /**
     * 预测点数
     */
    private String forecastPoint;


    private String startTimeStr;




}