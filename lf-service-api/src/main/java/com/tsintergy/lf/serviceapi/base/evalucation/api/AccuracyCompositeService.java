
package com.tsintergy.lf.serviceapi.base.evalucation.api;


import com.tsintergy.lf.serviceapi.base.evalucation.dto.AssessAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.BatchAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.CityAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.forecast.dto.AlgorithmAccuracyDTO;

import java.util.Date;
import java.util.List;

/**
 * 国调综合准确率
 *
 * <AUTHOR>
 */
public interface AccuracyCompositeService {

    /**
     * 获取指定时间段指定城市的算法预测精度
     * @param cityId 城市ID
     * @param caliberId 口径ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return
     */
    List<CityAccuracyDTO> getCityAccuracy(String cityId, String caliberId, String accuracyName, Date startDate, Date endDate, String isHoliday, String batchId);

    void doCalculateCompositeAccuracy(String cityId, String caliberId, String algorithmId, Date startDate,
                                      Date endDate);


    /**
     * 获取指定时间段指定城市的算法平均准确率
     * @return
     */
    List<AlgorithmAccuracyDTO> getCityAvgAccuracy(List<CityAccuracyDTO> cityAccuracy, String cityId) throws Exception;

    /**
     *
     * <AUTHOR>
     * @param cityId: 城市ID
     * @param caliberId: 口径ID
     * @param algorithmId: 算法ID
     * @param startDate: 开始时间
     * @param endDate: 结束时间
     * @Return void
     * @Since version
     */
    void doCalculateRecallCompositeAccuracy(String cityId, String caliberId, String algorithmId, Date startDate,
                                            Date endDate);

    /**
     * 获取日前N-1到N-7的综合准确率
     *
     * @param cityId
     * @param caliberId
     * @param algorithmId
     * @param startDate
     * @param endDate
     * @param batchId
     * @param statMethod
     * @return
     */
    List<BatchAccuracyDTO> getBatchAccuracy(String cityId, String caliberId, List<String> algorithmId, Date startDate, Date endDate, String batchId, String statMethod);

    /**
     * 获取对应批次考核点综合准确率
     *
     * @param cityId
     * @param caliberId
     * @param algorithmIds
     * @param batchId
     * @param date
     * @param endDate
     * @param days
     * @return
     */
    List<AssessAccuracyDTO> getAssessAccuracy(String cityId, String caliberId, List<String> algorithmIds, String batchId, Date startDate, Date endDate, Integer days);
}
