package com.tsintergy.lf.serviceapi.base.evalucation.pojo;


import com.tsintergy.lf.serviceapi.base.base.pojo.BaseLoadFcCityDO;
import java.sql.Date;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * 预测准确率
 */
@Data
@Entity
@Table(name = "accuracy_load_city_fc_service")
public class AccuracyLoadCityFcDO extends BaseLoadFcCityDO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    /**
     * 日期
     */
    @Column(name = "date")
    private Date date;

    /**
     * 城市ID
     */
    @Column(name = "city_id")
    private String cityId;

    /**
     * 算法ID
     */
    @Column(name = "algorithm_id")
    private String algorithmId;

    /**
     * 口径ID
     */
    @Column(name = "caliber_id")
    private String caliberId;

    /**
     * 是否上报
     */
    @Column(name = "report")
    private Boolean report;

    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;


}
