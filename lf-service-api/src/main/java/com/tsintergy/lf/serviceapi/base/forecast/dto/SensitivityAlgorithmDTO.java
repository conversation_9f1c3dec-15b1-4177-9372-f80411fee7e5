/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangfeng Date:  2019/8/14 3:28 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.forecast.dto;


import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 灵敏度分析算法调用参数对象
 *
 * <AUTHOR>
 * @create 2022/3/8
 * @since 1.0.0
 */
@Data
public class SensitivityAlgorithmDTO implements Serializable {

    @ApiModelProperty(value = "城市ID")
    private String cityId;

    @ApiModelProperty(value = "开始日期")
    private Date startDate;

    @ApiModelProperty(value = "结束日期")
    private Date endDate;

    @ApiModelProperty(value = "因变量类型 负荷 1 最大 2 平均 3 最小")
    private String loadType;

    @ApiModelProperty(value = "自变量类型 温度：21最大  22 平均 23最小）（实感 51最大 52平均,53最小")
    private String weatherType;

    @ApiModelProperty(value = "气象类别（1福州气象、2福建气象）")
    private String weatherId;

    private String seasonType;

    @ApiModelProperty(value = "最小值")
    private String min;

    @ApiModelProperty(value = "最大值")
    private String max;

    @ApiModelProperty(value = "step")
    private String step;

    @ApiModelProperty(value = "口径id")
    private String caliberId;

    @ApiModelProperty(value = "异常干扰日期列表")
    private List<String> dateNotIncluded;

    @ApiModelProperty(value = "用户id")
    private String userId;

    private Integer type;

    private String year;

    private Boolean colorType;

    private String polyOrder;

}