package com.tsintergy.lf.serviceapi.base.common.enumeration;

public enum CityEnum {
    henan("410000", "河南"),
    zhengzhou("410100", "郑州市"),
    kaifeng("410200", "开封市"),
    luoyang("410300", "洛阳市"),
    ping<PERSON>shan("410400", "平顶山市"),
    anyang("410500", "安阳市"),
    hebi("410600", "鹤壁市"),
    xinxiang("410700", "新乡市"),
    jiaozuo("410800", "焦作市"),
    puyang("410900", "濮阳市"),
    x<PERSON>ang("411000", "许昌市"),
    luo<PERSON>("411100", "漯河市"),
    sanmenxia("411200", "三门峡市"),
    nanyang("411300", "南阳市"),
    shangqiu("411400", "商丘市"),
    xinyang("411500", "信阳市"),
    z<PERSON><PERSON>u("411600", "周口市"),
    zhumadian("411700", "驻马店市"),

    ;


    private String id;
    private String name;

    private CityEnum(String id, String name) {
        this.id = id;
        this.name = name;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getCityById(String cityId) {
        for (CityEnum value : CityEnum.values()) {
            if (value.id.equals(cityId)) {
                return value.name;
            }
        }
        return null;
    }
    public static String getIdByCity(String city) {
        for (CityEnum value : CityEnum.values()) {
            if (value.name.equals(city)) {
                return value.id;
            }
        }
        return null;
    }
}
