/**
 * Copyright (C), 2015‐2022, 北京清能互联科技有限公司 Author:  <EMAIL> Date:  2022/5/25 14:10 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.airconditioner.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2022/5/25
 * @since 1.0.0
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class SampleRespDTO {

    @ApiModelProperty(value = "日期", example = "2022-03-01")
    Date date;

    @ApiModelProperty(value = "温度(℃)", example = "20")
    BigDecimal Temperature;

    BigDecimal highestTemperature;

    BigDecimal lowestTemperature;

    String cityName;

    @ApiModelProperty(value = "降水量（mm）", example = "0.5")
    BigDecimal rainfall;
}  
