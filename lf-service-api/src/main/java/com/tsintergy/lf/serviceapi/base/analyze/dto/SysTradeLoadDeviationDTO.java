/**
 * Copyright(C),2015‐2024,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.analyze.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 * @description: 系统-全社会负荷分析-日度
 * <AUTHOR>
 * @date 2024/05/11 16:39
 * @version: 1.0
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@ApiModel(description = "系统-全社会负荷分析-日度")
public class SysTradeLoadDeviationDTO {

    @ApiModelProperty(name = "名称")
    private String name;
    @ApiModelProperty(name = "最大值")
    @BigdecimalJsonFormat(scale = 4)
    private BigDecimal maxLoad;
    @ApiModelProperty(name = "最小值")
    @BigdecimalJsonFormat(scale = 4)
    private BigDecimal minLoad;
    @ApiModelProperty(name = "实际最大负荷时刻值")
    @BigdecimalJsonFormat(scale = 4)
    private BigDecimal hisMaxLoad;
    @ApiModelProperty(name = "实际最小负荷时刻值")
    @BigdecimalJsonFormat(scale = 4)
    private BigDecimal hisMinLoad;
    @ApiModelProperty(name = "96时刻点曲线")
    private List<BigDecimal> loadCurve;


}