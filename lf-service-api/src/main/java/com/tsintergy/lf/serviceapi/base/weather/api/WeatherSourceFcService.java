package com.tsintergy.lf.serviceapi.base.weather.api;

import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherFcAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherFcDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherFcFeatureDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherFcPointDTO;
import java.util.Date;
import java.util.List;

/**
 *  Description:  <br>     <AUTHOR>  @create 2021/7/27  @since 1.0.0 
 */
public interface WeatherSourceFcService {

    List<WeatherFcDTO> findSourceWeatherPoint(List<String> cityIdS, List<String> sources, Date startDate, Date endDate,
        String type)
        throws Exception;

    List<WeatherFcPointDTO> findSourceWeatherPointList(String cityId, Date startDate, Date endDate) throws Exception;

    List<WeatherFcFeatureDTO> findSourceWeatherFeature(List<String> cityIdS, List<String> sources, Date startDate,
        Date endDate)
        throws Exception;

    List<WeatherFcAccuracyDTO> findSourceWeatherAccuracy(String cityId, List<String> sources, Date startDate,
        Date endDate) throws Exception;
}
