package com.tsintergy.lf.serviceapi.base.load.dto;

import java.io.Serializable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class LoadBatchAccuracyRequest implements Serializable {

    @ApiModelProperty(value = "开始时间")
    private Date startDate;
    @ApiModelProperty(value = "结束日期")
    private Date endDate;
    @ApiModelProperty(value = "开始时刻")
    private String startPeriodTime;

    @ApiModelProperty(value = "结束时刻")
    private String endPeriodTime;


    /**
     *
     * 时间间隔 1 全时段 2 分时段
     */
    @ApiModelProperty(value = "时间间隔")
    private Integer periodType;

    /**
    *
    * 时间间隔 1 五分钟 2 十五分钟
    */
    @ApiModelProperty(value = "类型")
    private Integer type;
    @ApiModelProperty(value = "城市ID")
    private String cityId;
    @ApiModelProperty(value = "点数")
    private Integer customPoint;
    @ApiModelProperty(value = "口径ID")
    private String caliberId;
}
