/**
 * Copyright (C), 2015‐2022, 北京清能互联科技有限公司 Author:  <EMAIL> Date:  2022/5/26 17:14 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.load.pojo;

import com.tsieframework.core.base.dao.BaseVO;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2022/5/26
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "load_feature_ac_fc_service")
public class LoadFeatureAcFcDO  extends BaseVO {
    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    /**
     * 算法id
     */
    @Column(name = "algorithm_Id")
    protected String algorithmId;

    /**
     * 日期
     */
    @Column(name = "date")
    protected Date date;

    /**
     * 城市
     */
    @Column(name = "city_id")
    protected String cityId;

    /**
     * 口径ID
     */
    @Column(name = "caliber_id")
    protected String caliberId;

    /**
     * 最大空调负荷
     */
    @Column(name = "max_load")
    @BigdecimalJsonFormat(scale = 2)
    protected BigDecimal maxLoad;

    /**
     * 最小空调负荷
     */
    @Column(name = "min_load")
    @BigdecimalJsonFormat(scale = 2)
    protected BigDecimal minLoad;

    /**
     * 平均空调负荷
     */
    @Column(name = "avg_load")
    @BigdecimalJsonFormat(scale = 2)
    protected BigDecimal avgLoad;

    /**
     * 最大负荷发生时刻
     */
    @Column(name = "max_time")
    protected String maxTime;

    /**
     * 最小负荷发生时刻
     */
    @Column(name = "min_time")
    protected String minTime;

    /**
     * 最大空调负荷占比
     */
    @Column(name = "max_load_proportion")
    @BigdecimalJsonFormat(percentConvert = 100)
    private BigDecimal maxLoadProportion;

    /**
     * 累计电量
     */
    @Column(name = "energy")
    protected BigDecimal energy;

    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    protected Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    protected Timestamp updatetime;
}  
