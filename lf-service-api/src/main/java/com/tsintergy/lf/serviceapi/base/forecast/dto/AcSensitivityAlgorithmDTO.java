/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.forecast.dto;

import java.math.BigDecimal;
import lombok.Data;

/**
 * Description:<br>
 *
 * @Author:<EMAIL>
 * @Date: 2022/5/24 15:51
 * @Version:1.0.0
 */
@Data
public class AcSensitivityAlgorithmDTO extends SensitivityAlgorithmDTO {

    /**
     * 是否正常日
     */
    boolean isNormalDay;

    /**
     * 时候休息日
     */
    boolean isRestDay;

    /**
     * 最小日降水量
     */
    private BigDecimal minRain;

    /**
     * 最大日降水量
     */
    private BigDecimal maxRain;

}