
package com.tsintergy.lf.serviceapi.base.weather.api;


import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherDeatilDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherNameDTO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcBatchDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $Id: WeatherCityFcService.java, v 0.1 2018-01-31 10:59:49 tao Exp $$
 */

public interface WeatherCityFcService  extends BaseFcWeatherService{
     /**
     *  search entity list
     * @param param
     * @return
     * @throws BusinessException
     */
    DataPackage queryWeatherCityFcDO(DBQueryParam param) throws Exception;

    List<WeatherDTO> findWeatherCityFcDTOs(String cityId, Integer type, Date startDate, Date endDate) throws Exception;

    /**
     * create entity
     * @param vo
     * @return
     * @throws Exception
     */
     WeatherCityFcDO doCreate(WeatherCityFcDO vo) throws Exception;

    /**
     *  delete entity by object
     * @param vo
     * @throws Exception
     */
     void doRemoveWeatherCityFcDO(WeatherCityFcDO vo) throws Exception;

    /**
     * delete entity by PK
     * @param pk
     * @throws Exception
     */
     void doRemoveWeatherCityFcDOByPK(Serializable pk) throws Exception;

    /**
     * update entity object
     * @param vo
     * @return
     * @throws Exception
     */
     WeatherCityFcDO doUpdateWeatherCityFcDO(WeatherCityFcDO vo) throws Exception;

    /**
     * find entity by PK
     * @param pk
     * @return
     * @throws Exception
     */
     WeatherCityFcDO findWeatherCityFcDOByPk(Serializable pk) throws Exception;

    /**
     * 查询气象预测数据
     * @param cityId 气象城市ID
     * @param type 气象类型
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return
     */
    List<WeatherCityFcDO> findWeatherCityFcDOs(String cityId, Integer type, Date startDate, Date endDate) throws Exception;

    /**
     * 查询該城市目标日的所有基础气象数据
     * @param cityId
     * @param targetDate
     * @return
     * @throws Exception
     */
    List<WeatherNameDTO> findAllByDateAndCityId(String cityId, Date targetDate) throws Exception;

    WeatherCityFcDO findWeatherCityFcDO(String cityId,Integer type, Date date) throws Exception;

    void doInsertOrUpdate(WeatherCityFcDO weatherCityFcVO) throws Exception;


    List<BigDecimal> find96WeatherCityFcValue(Date date, String cityId, Integer type) throws Exception;

    List<BigDecimal> findListPoint(Date startDate, Date endDate, String cityId, Integer type) throws Exception;

    WeatherDeatilDTO findWeatherDeatilDTO(Date date, String cityId) throws Exception;

    List<WeatherCityFcDO> findWeatherCityFcDOs(String cityId, Date date, Date date1) throws Exception;

    List<WeatherCityFcDO> findWeatherCityHisDOs(List<String> cityIds, Integer type, Date hisStartDate, Date endDate);

    /**
     * 通过格点气象站的数据计算城市预测气象数据
     * 1.将格点气象站气象24点数据转化为96点数据
     * 2.通过96点数据计算城市气象
     *
     * @throws Exception
     */
    void wgStationFcWeatherToCity(String cityId, Integer type, Date startDate, Date endDate) throws Exception;

    void doFcCityWeatherToProvince(Date startDate, Date endDate) throws Exception;


    /**
     * 获取福建省标准站点加权平均气象数据
     *
     * @param type
     * @param startDate
     * @param endDate
     * @throws Exception
     */
    List<WeatherCityFcDO> getProvinceWeightAvgFcWeatherData(Integer type, Date startDate, Date endDate) throws Exception;

    void doAutoStatFcDaysTemperature(String cityId, Date startDate, Date endDate) throws Exception;

    void doSaveOuUpdatePreLoadWeather(WeatherCityFcBatchDO weatherCityFcBatchDO);


}