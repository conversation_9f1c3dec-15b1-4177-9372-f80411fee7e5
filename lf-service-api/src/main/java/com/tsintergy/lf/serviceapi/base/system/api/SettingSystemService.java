package com.tsintergy.lf.serviceapi.base.system.api;

import com.tsintergy.aif.algorithm.serviceapi.base.pojo.NotIncludedDate;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.system.dto.SaveSystemStepDTO;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemData;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemStepDTO;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemWeatherDTO;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingSystemDO;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @version $Id: SettingSystemService.java, v 0.1 2018-01-31 10:57:14 tao Exp $$
 */

public interface SettingSystemService {

    List<NotIncludedDate> getNotIncludedDateList();

    /**
     * create entity
     */
    SettingSystemDO doCreate(SettingSystemDO vo) throws Exception;


    /**
     * 获取字段值
     */
    String getValue(String field) throws Exception;

    /**
     * 保存字段值
     */
    SettingSystemDO doSaveValue(String field, String value) throws Exception;

    /**
     * 获取尖峰时段
     */
    List<String> getPeakTimes() throws Exception;

    /**
     * 获取低谷时段
     */
    List<String> getTroughTimes() throws Exception;

    /**
     * 获取尖峰时段，根据","拆分数组
     */
    String[] getPeak() throws Exception;



    SystemWeatherDTO getWeatherSwitchAndValue(String weatherType) throws Exception;

    /**
     * 获取开启的气象偏差率
     *
     * @param open true 查询开启的气象   false 查询所有
     */
    List<SystemWeatherDTO> getWeatherSwitchList(Boolean open) throws Exception;


    SettingSystemDO findByFieldId(String fieldId) throws Exception;

    /**
     * 更新气象偏差率开关
     */
    void updateWeatherSwitch(List<SystemWeatherDTO> dtos) throws Exception;

    /**
     * 更新系统设置页的数据
     */
    void doUpdate(SystemData data) throws Exception;

    /**
     * 获取系统设置页的数据
     */
    SystemData getSystemSetting() throws Exception;

    /**
     * 获取所有系统参数设置
     */
    List<SettingSystemDO> getAllSettingSystemDO() throws Exception;


    boolean findIsLater(Date report, LoadCityFcDO loadCityFcVO) throws Exception;


    /**
     * key : 早高峰，无高峰 晚高峰 value： 时刻点
     */
    Map<Integer, List<String>> getTimes(String filedId) throws Exception;


    /*
     * 更新某个key值对应的value
     * */

    void doUpdateOrSaveValueByKey(String key, String value) throws Exception;


    /**
     * 查询上报截止时间
     * @return
     * @throws Exception
     */
    String findEndReportTime() throws Exception;


    List<SettingSystemDO> findByKey(String key);

    SettingSystemDO getTargetAccuracy(String key);




    /**
     * 系统设置-查询步长设置
     *
     * @return {@link SystemStepDTO}
     * @throws Exception 异常信息
     */
    SystemStepDTO findStepSetting() throws Exception;

    /**
     * 系统设置-保存步长设置
     *
     * @param request 步长设置DTO
     * @throws Exception 异常信息
     */
    void updateStepSetting(SaveSystemStepDTO request) throws Exception;
}