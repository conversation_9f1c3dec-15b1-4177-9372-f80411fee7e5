package com.tsintergy.lf.serviceapi.base.forecast.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

/**
 * 算法DTO
 * User:taojingui
 * Date:18-2-7
 * Time:上午11:54
 */
@ApiModel
public class AlgorithmDTO implements Serializable {

    /**
     * 算法ID
     */
    @ApiModelProperty(value = "算法ID",example ="1" )
    private String id;

    /**
     * 算法名称
     */
    @ApiModelProperty(value = "算法名称",example ="支持向量" )
    private String algorithm;

    /**
     * 是否默认算法（自动预测的算法）
     */
    @ApiModelProperty(value = "是否默认算法（自动预测的算法）",example ="true" )
    private Boolean isDefault;

    public String getAlgorithm() {
        return algorithm;
    }

    public void setAlgorithm(String algorithm) {
        this.algorithm = algorithm;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Boolean getDefault() {
        return isDefault;
    }

    public void setDefault(Boolean aDefault) {
        isDefault = aDefault;
    }
}
