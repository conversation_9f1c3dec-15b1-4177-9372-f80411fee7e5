package com.tsintergy.lf.serviceapi.base.forecast.pojo;


import com.tsieframework.core.base.vo.annotation.EntityUniqueIndex;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Load;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.base.pojo.BaseLoadCityDO;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import com.tsintergy.lf.serviceapi.base.base.pojo.BaseLoadFcCityDO;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * 地区预测负荷
 */
@Data
@Entity
@Table(name = "load_city_fc_basic")
@EntityUniqueIndex({"date","cityId","caliberId","algorithmId"})
public class LoadCityFcDO extends BaseLoadFcCityDO implements Load {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_GENERATOR)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    @Column(name = "date")
    private Date date;

    /**
     * 城市ID
     */
    @ApiModelProperty(value = "城市ID")
    @Column(name = "city_id")
    private String cityId;

    /**
     * 口径ID
     */
    @ApiModelProperty(value = "口径ID")
    @Column(name = "caliber_id")
    private String caliberId;

    /**
     * 算法ID
     */
    @ApiModelProperty(value = "算法ID")
    @Column(name = "algorithm_id")
    private String algorithmId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @Column(name = "createtime")
    private Timestamp createtime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @Column(name = "updatetime")
    private Timestamp updatetime;

    /**
     * 是否为系统推荐的算法
     */
    @ApiModelProperty(value = "是否为系统推荐的算法")
    @Column(name = "recommend")
    private Boolean recommend;

    /**
     * 是否上报结果
     */
    @ApiModelProperty(value = "是否上报结果")
    @Column(name = "report")
    private Boolean report;

    /**
     * 操作者
     */
    @ApiModelProperty(value = "操作者")
    @Column(name = "user_id")
    private String userId;

    /**
     * 是否上报成功
     */
    @ApiModelProperty(value = "是否上报成功")
    @Column(name = "succeed")
    private Boolean succeed;

    @Column(name = "report_time")
    @ApiModelProperty(value = "上报时间")
    private Timestamp reportTime;


    public LoadCityFcDO(Date date, String cityId, String caliberId, String algorithmId) {
        this.date = date;
        this.cityId = cityId;
        this.caliberId = caliberId;
        this.algorithmId = algorithmId;
    }


    public LoadCityFcDO(Date date, String cityId, String caliberId, String algorithmId, Timestamp createtime, Timestamp updatetime, Boolean recommend, Boolean report, String userId, Boolean succeed, Timestamp reportTime) {
        this.date = date;
        this.cityId = cityId;
        this.caliberId = caliberId;
        this.algorithmId = algorithmId;
        this.createtime = createtime;
        this.updatetime = updatetime;
        this.recommend = recommend;
        this.report = report;
        this.userId = userId;
        this.succeed = succeed;
        this.reportTime = reportTime;
    }

    public LoadCityFcDO() {
    }


    @Override
    public List<BigDecimal> getloadList() {
        return BasePeriodUtils.toList(this, Constants.LOAD_CURVE_POINT_NUM,Constants.LOAD_CURVE_START_WITH_ZERO);
    }

    @Override
    public  String getDeviceId() {
        return this.cityId;
    }
}
