package com.tsintergy.lf.serviceapi.algorithm.api;


import com.tsintergy.lf.serviceapi.algorithm.dto.Param;
import com.tsintergy.lf.serviceapi.algorithm.dto.Result;

import java.util.Map;

/**
 * 特定条件触发算法服务 通用顶层接口 param中只负责传参 在该层查询并merge所需要的数据
 *
 * <AUTHOR>
 */
public interface CustomizationForecastService<E extends Param, T extends Result> {

    /**
     * 预测顶层接口 默认实现中规范了整体的执行流程；自定义流程需重写方法
     */
    void forecast(E param) throws Exception;

    /**
     * 该接口用于 查询并merge所需要的数据到map中，以供算法的Executor写入数据
     *
     * @param param  数据参数
     * @param srcMap 数据集合map
     */
    void mergeData(E param, Map<String, Object> srcMap) throws Exception;


    /**
     * 解析算法生成的文件，其中自定义后续操作，如：入库；放入redis缓存；通知下一步计算等。
     *
     * @param tarPath        算法生成文件路径，不带文件名称
     * @param supplementData 补充参数；个别算法用于甄别file out。例如超短期的区分5or15分钟；稳定度分析的week信息等等
     */
    void resolverResult(String tarPath, Map<String, String> supplementData) throws Exception;

}

