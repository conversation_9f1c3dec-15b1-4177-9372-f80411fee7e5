/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司
 * Author: wang<PERSON>
 * Date: 2018/7/31 10:03
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.algorithm.dto;

import com.tsieframework.core.base.format.datetime.DateUtils;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 每日跑批的预测算法通用参数 包含实施页面的一些参数；如 weatherModify 是否使用预测气象
 *
 * <AUTHOR>
 * @create 2020/8/25
 * @since 1.0.0
 */
@Data
public class ForecastParam extends Param {

    /**
     * 1 算法内部滚动预测 预测多天，只调用一次算法  2 程序循环一天一天调用 默认为 1
     */
    public final static Integer FORECAST_CYCLIC_TYPE = 1;
    /**
     * 手动预测
     */
    public final static Integer FORECAST_TYPE = 1;


    public ForecastParam() {
    }

    public ForecastParam(String uid, String cityId,String cityName,  String caliberId, List<AlgorithmEnum>algorithmEnums, List<Date>dateList) {
        this.uid = uid;
        this.cityId = cityId;
        this.cityName = cityName;
        this.caliberId = caliberId;
        this.algorithmEnums = algorithmEnums;
        this.dateList = dateList;
    }

    private final int PRE_DAY = -2;

    /**
     * 实施页面 是否使用预测气象
     */
    private Boolean weatherModify;

    /**
     *  实施页面调用传参 待预测日气象参数 0使用历史气象 1 使用预测气象
     */
    private  Integer fcstDayWeatherType;

    /**
     * 预测开始日期
     */
    Date forecastDate;


    /**
     * 预测结束日期
     */
    Date forecastEndDate;

    /**
     * 基准日
     */
    private Date baseDay;


    private String cityName;

    /**
     * 预测类型 0 自动预测 1 手动预测
     */
    private Integer type;

    /**
     * 训练气象选择 1湿度 2 温度 3降雨量 4 风速
     */
    private List<Integer> weatherType;


    /**
     * 预测方式 1 T**** T+2  实施页面传参
     */
    private Integer fway;

    /**
     * 新息点数 实施页面传参
     */
    private Integer pointNum;

    /**
     * 待预测日 list （程序循环一天一天调用时使用）其余情况预测起始日从中取
     */
    private List<Date> dateList;

    /**
     * 每日跑批的多个算法
     */
    private List<AlgorithmEnum> algorithmEnums;

    private Boolean shortForecast;

    public Date getInitBaseDay() {
        baseDay = DateUtils.addDays(forecastDate, PRE_DAY);
        return baseDay;
    }

}