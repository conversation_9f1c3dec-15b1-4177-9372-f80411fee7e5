/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.airconditioner.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * Description:<br>
 *
 * @Author:<EMAIL>
 * @Date: 2022/5/23 17:33
 * @Version:1.0.0
 */
@Data
@ApiModel
public class AcFeatureStatisticsDTO implements Serializable {

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "日最大空调负荷", example = "123121")
    private BigDecimal maxLoad;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "去年同期日最大空调负荷", example = "123121")
    private BigDecimal lastYearMaxLoad;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "日最小空调负荷", example = "123121")
    private BigDecimal minLoad;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "去年同期日最小空调负荷", example = "123121")
    private BigDecimal lastYearMinLoad;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "日平均空调负荷", example = "123121")
    private BigDecimal avgLoad;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "去年同期日平均空调负荷", example = "123121")
    private BigDecimal lastYearAvgLoad;


    @BigdecimalJsonFormat(percentConvert = 100)
    @ApiModelProperty(value = "最大空调负荷占比", example = "123121")
    private BigDecimal maxLoadProportion;

    @BigdecimalJsonFormat(percentConvert = 100)
    @ApiModelProperty(value = "去年同期最大空调负荷占比", example = "123121")
    private BigDecimal lastYearMaxLoadProportion;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "累计电量", example = "123121")
    private BigDecimal energy;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "去年同期累计电量", example = "123121")
    private BigDecimal lastYearEnergy;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最高温度", example = "123121")
    private BigDecimal highestTemperature;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "去年同期最高温度", example = "123121")
    private BigDecimal lastYearHighestTemperature;

}