package com.tsintergy.lf.serviceapi.base.forecast.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class ForecastBpCheckDTO implements DTO {

    private String dateStr;

    private Date date;

    private String temp;

    private String week;

    private BigDecimal earlyPeak;

    private BigDecimal noonPeak;

    private BigDecimal eveningPeak;

    private BigDecimal waistLoad;

    private BigDecimal trough;

    /**
     * 预测数据
     */
    private List<BigDecimal> fcBigDecimalList;

    /**
     * 人工修正上报数据
     */
    private List<BigDecimal> mdBigDecimalList;

    /**
     * 是否可以修改
     */
    private Boolean flag;

}
