
package com.tsintergy.lf.serviceapi.base.forecast.api;


import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsintergy.lf.serviceapi.algorithm.dto.SimilarDateBean;
import com.tsintergy.lf.serviceapi.base.analyze.dto.CompositePowerDTO;
import com.tsintergy.lf.serviceapi.base.analyze.dto.CompositeRequestDTO;
import com.tsintergy.lf.serviceapi.base.forecast.dto.SimilarDTO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.SimilarDayDO;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version $Id: SimilarDayService.java, v 0.1 2018-01-31 10:44:22 tao Exp $$
 */

public interface SimilarDayService {

    /**
     * search entity list
     */
    DataPackage querySimilarDayDO(DBQueryParam param) throws Exception;

    /**
     * create entity
     */
    SimilarDayDO doCreate(SimilarDayDO vo) throws Exception;

    /**
     * delete entity by object
     */
    void doRemoveSimilarDayDO(SimilarDayDO vo) throws Exception;

    /**
     * delete entity by PK
     */
    void doRemoveSimilarDayDOByPK(Serializable pk) throws Exception;

    /**
     * update entity object
     */
    SimilarDayDO doUpdateSimilarDayDO(SimilarDayDO vo) throws Exception;

    /**
     * find entity by PK
     */
    SimilarDayDO findSimilarDayDOByPk(Serializable pk) throws Exception;


    /**
     * find entity which degree is the max at the targetDay
     */
    SimilarDayDO findMaxSimilarDay(String cityId, Date targetDate, String caliberId) throws Exception;


    /**
     * find forecast targetDay and its SimilarDay
     */
    List<SimilarDTO> findTargetSimilarDay(String cityId, Date date, Integer forecastDay, String caliberId)
            throws Exception;


    List<SimilarDayDO> findSimilarDays(String cityId, String caliberId, Date date) throws Exception;


    List<SimilarDateBean> listSimilarDayWithFcWeather(String cityId, String caliberId, Date date, Date startDate,
        Date endDate, Integer size, String userId) throws Exception;

    /**
     * 综合相似查询
     * <AUTHOR>
     * @param requestDTO 参数对象
     * @return 相似数据
     */
    List<CompositePowerDTO> listSimilarComposite(CompositeRequestDTO requestDTO) throws Exception;


}