/**
 * Copyright (C), 2015‐2022, 北京清能互联科技有限公司 Author:  <EMAIL> Date:  2022/5/24 9:41 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.airconditioner.dto;

import com.tsintergy.lf.serviceapi.base.common.enumeration.DateType2;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.persistence.Convert;
import lombok.Data;

/**
 * Description: 基础负荷方案管理 <br>
 *
 * <AUTHOR>
 * @create 2022/5/24
 * @since 1.0.0
 */
@Data
@ApiModel
public class LoadSolutionManageDTO implements Serializable {

    @ApiModelProperty(value = "城市id")
    String cityId;

    @ApiModelProperty(value = "口径id")
    String caliberId;

    @ApiModelProperty(value = "日期类型")
    @Convert(converter = DateType2.DateType2Convertor.class)
    DateType2 dateType;

    @ApiModelProperty(value = "开始年份")
    String startYear;

    @ApiModelProperty(value = "结束年份")
    String endYear;

    String season;
}  
