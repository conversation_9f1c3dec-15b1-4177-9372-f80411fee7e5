/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司 Author: wangfeng Date: 2018/9/10 19:32 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.algorithm.dto;

import java.util.Date;
import lombok.Data;

/**
 *  节假日请求参数
 *
 * <AUTHOR>
 * @create 2020/8/24
 * @since 1.0.0
 */
@Data
public class HolidayParam extends Param {

    public HolidayParam(String uid, Date forecastBeginDate, Date forecastEndDate, String cityId, String caliberId,
        AlgorithmEnum algorithmEnum, String cityName, String isRecall) {
        super.uid = uid;
        super.cityId = cityId;
        super.caliberId = caliberId;
        super.algorithmEnum = algorithmEnum;
        this.forecastBeginDate = forecastBeginDate;
        this.forecastEndDate = forecastEndDate;
        this.cityName = cityName;
        this.isRecall = isRecall;
    }
    public HolidayParam(String uid, Date forecastBeginDate, Date forecastEndDate, String cityId, String caliberId,
        AlgorithmEnum algorithmEnum, String cityName) {
        super.uid = uid;
        super.cityId = cityId;
        super.caliberId = caliberId;
        super.algorithmEnum = algorithmEnum;
        this.forecastBeginDate = forecastBeginDate;
        this.forecastEndDate = forecastEndDate;
        this.cityName = cityName;
    }

    /**
     * 预测开始日期
     */
    private Date forecastBeginDate;

    /**
     * 预测结束日期
     */
    private Date forecastEndDate;

    /**
     * 基准日期
     */
    private Date baseDate;


    private String cityName;

    /**
     * 是否为回溯预测
     */
    private  String isRecall;

}