package com.tsintergy.lf.serviceapi.base.weather.pojo;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Load;
import com.tsintergy.lf.core.constants.Constants;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 气象预测数据
 */
@Data
@Entity
@Table(name = "WEATHER_CITY_FC_METEO_BASIC")
public class WeatherCityFcDO extends BaseWeatherDO implements Load {

    public WeatherCityFcDO() {
        super();
    }

    public WeatherCityFcDO(String cityId, Integer type, java.util.Date date) {
        super(cityId, type, date);
    }

    @Override
    public List<BigDecimal> getloadList() {
        return BasePeriodUtils.toList(this, Constants.LOAD_CURVE_POINT_NUM,Constants.LOAD_CURVE_START_WITH_ZERO);
    }

    @Override
    public String getDeviceId() {
        return this.getCityId();
    }
}
