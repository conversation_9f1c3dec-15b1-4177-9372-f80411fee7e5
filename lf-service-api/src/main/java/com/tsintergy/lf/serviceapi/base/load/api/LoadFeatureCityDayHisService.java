
package com.tsintergy.lf.serviceapi.base.load.api;


import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsintergy.lf.serviceapi.base.load.dto.AreaLoadFeatureDTOS;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureExtendDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version $Id: LoadFeatureCityDayHisService.java, v 0.1 2018-01-31 10:51:24 tao Exp $$
 */

public interface LoadFeatureCityDayHisService {
    /**
     * search entity list
     */
    DataPackage queryLoadFeatureCityDayHisDO(DBQueryParam param) throws Exception;

    /**
     * create entity
     */
    LoadFeatureCityDayHisDO doCreate(LoadFeatureCityDayHisDO vo) throws Exception;

    /**
     * delete entity by object
     */
    void doRemoveLoadFeatureCityDayHisDO(LoadFeatureCityDayHisDO vo) throws Exception;


    /**
     * update entity object
     */
    LoadFeatureCityDayHisDO doUpdateLoadFeatureCityDayHisDO(LoadFeatureCityDayHisDO vo) throws Exception;

    /**
     * find entity by PK
     */
    LoadFeatureCityDayHisDO findLoadFeatureCityDayHisDOByPk(Serializable pk) throws Exception;


    /**
     * find entities by cityId in a period
     */
    List<LoadFeatureCityDayHisDO> findLoadFeatureCityDayHisDOS(String cityId, Date startDate, Date endDate,
                                                               String caliberId) throws Exception;

    /**
     * find entities by cityId in a period
     */
    List<LoadFeatureExtendDTO> findLoadFeatureExtendDTOS(String cityId, Date startDate, Date endDate, String caliberId)
            throws Exception;


    /**
     * 查询日负荷特性
     */
    List<LoadFeatureDTO> findDayLoadFeatureDTOS(String cityId, Date startDate, Date endDate, String caliberId)
            throws Exception;


    List<LoadFeatureCityDayHisDO> listLoadFeature(String cityId, String caliberId, List<Date> dateList)
            throws Exception;

    AreaLoadFeatureDTOS findAreaLoadFeatureDTO(String caliberId, Date date, Integer loadType) throws Exception;

    List<LoadFeatureCityDayHisDO> findLoadFeatureCityDayHisVOS(String cityId, Date startDate, Date endDate, String caliberId) throws Exception;
}