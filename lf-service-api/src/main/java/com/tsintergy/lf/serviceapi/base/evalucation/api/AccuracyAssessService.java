
package com.tsintergy.lf.serviceapi.base.evalucation.api;


import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyAssessDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyAssessDataDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyDistributionDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyAssessDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyAssessRecallDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyCompositeDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.BaseLoadIntervalDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisBasicByMinuteDO;
import com.tsintergy.lf.serviceapi.base.recall.dto.StatisticsAccuracyDTO;

import java.util.Date;
import java.util.List;

/**
 * 考核点准确率
 *
 * <AUTHOR>
 */
public interface AccuracyAssessService {

    List<AccuracyAssessDO> findAccuracyList(String cityId, String caliberId, String algorithmId, Date startDate,
        Date endDate,String batchId);

    List<AccuracyAssessDO> findAccuracyList(String cityId, String caliberId, String accuracyName,List<String> algorithmId, Date startDate,
                                            Date endDate);

    /**
     *
     * @param batchId 批次id  1 上午时间段 2 下午时间段；请查询批次设置表中设置的时间，查询到时间段内最新的一次数据返回
     * @param day 1 2；代表t-1 t-2
     * @return
     * @throws Exception
     */
    List<AccuracyAssessDTO> findAccuracyDTO(String cityId, String caliberId, String algorithmId, Date startDate,
        Date endDate,String batchId, Integer day)
        throws Exception;

    void doCalculateAssessAccuracy(String cityId, String caliberId, String algorithmId, Date startDate, Date endDate)
        throws Exception;

    List<AccuracyDistributionDTO> findAccuracyDistribution(String cityId, String caliberId, String algorithmId,
        String accuracyName, Date startDate, Date endDate,String batchId, Integer day) throws Exception;

    List<String> findNameList(String cityId, String caliberId, String algorithmId,
        Date startDate, Date endDate);

    void doCalculateRecallAssessAccuracy(String cityId, String caliberId, String algorithmId, Date startDate,
                                         Date endDate) throws Exception;

    List<AccuracyAssessRecallDO> findAccuracyRecallList(String cityId, String caliberId, String algorithmId, Date startDate,
                                                        Date endDate);

    List<AccuracyAssessDTO> findAccuracyAccessRecallList(String cityId, String caliberId, String algorithmId,
                                                         Date startDate, Date endDate, String batchId, Integer day);

    StatisticsAccuracyDTO getStatisticsAccuracyAccessRecall(String cityId, String caliberId, Date startDate,
                                                            Date endDate, String algorithmId, String batchId,
                                                            String accuracyName, Integer day);


    /**
     * 条件查询后 返回批次覆盖的时间范围内的所有批次数据；考核点准确率
     */
    List<AccuracyAssessDO> selectListInBatchAssessTime(String cityId, String caliberId, String algorithmId,
        Date startDate,
        Date endDate, String batchId);

    /**
     * 条件查询后 返回批次覆盖的时间范围内最新的一条数据；考核点准确率
     */
    List<AccuracyAssessDO> selectListNewestInNameAndBatchTime(String cityId, String caliberId, String accuracyName,
        Date startDate, Date endDate, String batchId);

    List<AccuracyAssessDO> selectListNewestInNameAndBatchData(String cityId, String caliberId, String accuracyName,
        Date startDate, Date endDate, String batchId);

    List<AccuracyCompositeDO> selectListNewestInNameAndBatch(String cityId, String caliberId, String accuracyName,
        Date startDate, Date endDate, String batchId);

    /**
     * 条件查询后 返回批次覆盖的时间范围内最新的一条数据；综合准确率
     */
    List<AccuracyCompositeDO> selectCompositeNewestInNameAndBatchTime(String cityId, String caliberId,
        String accuracyName,
        Date startDate, Date endDate, String batchId);
}
