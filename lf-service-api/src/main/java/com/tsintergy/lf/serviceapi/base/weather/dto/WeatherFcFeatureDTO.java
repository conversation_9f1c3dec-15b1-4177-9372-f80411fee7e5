package com.tsintergy.lf.serviceapi.base.weather.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 *  Description:  <br>     <AUTHOR>  @create 2021/7/25  @since 1.0.0 
 */
@Data
public class WeatherFcFeatureDTO implements Serializable {
    private String cityId;

    private String cityName;

    private String type;

    private String source;

    private String sourceName;

    private Date date;

    private BigDecimal temperatureMax;

    private BigDecimal temperatureMin;

    private BigDecimal temperatureAvg;

    private BigDecimal accumulatePrecipitation;

    private BigDecimal humidityMax;

    private BigDecimal humidityMin;

    private BigDecimal humidityAvg;

    private BigDecimal speedMax;

    private BigDecimal speedMin;

    private BigDecimal speedAvg;

}
