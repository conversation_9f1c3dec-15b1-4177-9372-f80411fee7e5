/**
 * Copyright(C),2015‐2020,北京清能互联科技有限公司
 * Author:yangjin
 * Date:2020/2/2613:29
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.load.api;

import com.tsintergy.lf.serviceapi.base.base.pojo.TaskInfoDO;

import java.util.Date;
import java.util.List;

/**
 *Description: <br>
 *
 *<AUTHOR>
 *@create 2020/2/26
 *@since 1.0.0
 */
public interface TaskInfoService {

    List<TaskInfoDO> getTaskInfo(String type, String date);

    TaskInfoDO findById(String id);

    public void reSendTask(String taskId, Date date, String xxljobId);
}