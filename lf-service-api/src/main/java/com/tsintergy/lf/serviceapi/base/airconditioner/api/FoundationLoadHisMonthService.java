/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.airconditioner.api;

import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.FoundationLoadHisMonthDO;

import java.util.List;

/**
 * Description:<br>
 *
 * @Author:<EMAIL>
 * @Date: 2022/5/24 10:42
 * @Version:1.0.0
 */
public interface FoundationLoadHisMonthService {

    List<FoundationLoadHisMonthDO> getFoundationLoadHisMonth(String cityId, String caliberId, String year,
                                                             String month, Integer type);


    void doSave(FoundationLoadHisMonthDO foundationLoadHisMonthDO);


    void doUpdate(FoundationLoadHisMonthDO foundationLoadHisMonthDO);

    void wrapperNowYearBasicLoad(String caliberId, String cityId, int nowMonthNum, int nowYear, int type) throws Exception;
    
}