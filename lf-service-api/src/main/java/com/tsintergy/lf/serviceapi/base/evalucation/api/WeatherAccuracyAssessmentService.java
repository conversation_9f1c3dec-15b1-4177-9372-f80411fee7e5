package com.tsintergy.lf.serviceapi.base.evalucation.api;

import com.tsintergy.lf.serviceapi.base.evalucation.dto.*;

import java.util.Date;

/**
 * 气象预测准确率评估服务接口
 */
public interface WeatherAccuracyAssessmentService {

    /**
     * 获取气象预测准确率评估数据
     * 
     * @param cityId 城市ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param weatherSource 气象源
     * @param batchId 批次ID
     * @param weatherIndicatorType 气象指标类型（0-最高温度，1-最低温度，2-平均温度，3-相对湿度，4-累计降雨量，5-最大风速）
     * @param selectedDate 选中的日期（用于偏差详情和预测对比）
     * @param selectedDay 选中的预测天数（D-1到D-10）
     * @return 气象预测准确率评估数据
     * @throws Exception
     */
    WeatherAccuracyAssessmentDTO getWeatherAccuracyAssessment(String cityId, Date startDate, Date endDate, 
                                                              String weatherSource, String batchId, 
                                                              Integer weatherIndicatorType, Date selectedDate, 
                                                              Integer selectedDay) throws Exception;

    /**
     * 获取气象预测准确率表格数据
     * 
     * @param cityId 城市ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param weatherSource 气象源
     * @param batchId 批次ID
     * @param weatherIndicatorType 气象指标类型
     * @return 气象预测准确率表格数据
     * @throws Exception
     */
    WeatherAccuracyTableDTO getWeatherAccuracyTable(String cityId, Date startDate, Date endDate, 
                                                     String weatherSource, String batchId, 
                                                     Integer weatherIndicatorType) throws Exception;

    /**
     * 获取气象预测偏差详情
     * 
     * @param cityId 城市ID
     * @param selectedDate 选中的日期
     * @param selectedDay 选中的预测天数
     * @param weatherSource 气象源
     * @param batchId 批次ID
     * @return 气象预测偏差详情
     * @throws Exception
     */
    WeatherDeviationDetailDTO getWeatherDeviationDetail(String cityId, Date selectedDate, Integer selectedDay, 
                                                         String weatherSource, String batchId) throws Exception;

    /**
     * 获取气象预测对比数据
     * 
     * @param cityId 城市ID
     * @param selectedDate 选中的日期
     * @param selectedDay 选中的预测天数
     * @param weatherSource 气象源
     * @param batchId 批次ID
     * @param weatherType 气象类型
     * @return 气象预测对比数据
     * @throws Exception
     */
    WeatherForecastComparisonDTO getWeatherForecastComparison(String cityId, Date selectedDate, Integer selectedDay, 
                                                               String weatherSource, String batchId, 
                                                               Integer weatherType) throws Exception;
}
