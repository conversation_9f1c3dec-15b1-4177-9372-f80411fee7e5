package com.tsintergy.lf.serviceapi.base.load.api;


import com.tsintergy.lf.serviceapi.base.load.dto.LoadBatchAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadBatchAccuracyRequest;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadShortFcAccuracyDTO;

import java.util.List;

/**
 * Description:  <br>
 * 超短期预测 批次准确率
 */
public interface LoadBatchAccuracyService {

   List<LoadBatchAccuracyDTO> findBatchFcAccuracy(LoadBatchAccuracyRequest request) throws Exception;

    List<LoadShortFcAccuracyDTO> findBatchFcTimeAccuracy(String batchId) throws  Exception;
}
