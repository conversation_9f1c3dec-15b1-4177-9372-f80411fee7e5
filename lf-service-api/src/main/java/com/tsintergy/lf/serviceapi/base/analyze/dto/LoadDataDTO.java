/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司
 * Author: wang<PERSON>
 * Date: 2018/8/15 16:27
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.analyze.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * Description: 真实曲线 上报曲线 修正曲线 <br>
 *
 * <AUTHOR>
 * @create 2018/8/15
 * @since 1.0.0
 */
@ApiModel
public class LoadDataDTO implements Serializable {

    /**
     * 真实负荷曲线
     */
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "真实负荷曲线")
    private List<BigDecimal> realLoad;

    /**
     * 最终上报曲线   （如果自动预测曲线 人工没有做修正 直接上报 则最终上报曲线就是自动预测曲线）
     */
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最终上报曲线")
    private List<BigDecimal> repairLoad;

    /**
     * 自动预测曲线
     */
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "自动预测曲线")
    private List<BigDecimal> autoLoad;


    public List<BigDecimal> getRealLoad() {
        return realLoad;
    }

    public void setRealLoad(List<BigDecimal> realLoad) {
        this.realLoad = realLoad;
    }

    public List<BigDecimal> getRepairLoad() {
        return repairLoad;
    }

    public void setRepairLoad(List<BigDecimal> repairLoad) {
        this.repairLoad = repairLoad;
    }

    public List<BigDecimal> getAutoLoad() {
        return autoLoad;
    }

    public void setAutoLoad(List<BigDecimal> autoLoad) {
        this.autoLoad = autoLoad;
    }
}