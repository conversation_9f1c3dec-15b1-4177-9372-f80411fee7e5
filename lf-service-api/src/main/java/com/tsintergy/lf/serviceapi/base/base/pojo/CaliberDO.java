package com.tsintergy.lf.serviceapi.base.base.pojo;

import com.tsieframework.core.base.vo.CacheVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * 口径VO User:taojingui Date:18-6-5 Time:上午10:49
 */
@Data
@Entity
@Table(name = "caliber_base_init")
@ApiModel
public class CaliberDO extends CacheVO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    @ApiModelProperty(value = "id",example = "5")
    private String id;

    /**
     * 口径名称
     */
    @Column(name = "name")
    @ApiModelProperty(value = "口径名称",example = "5")
    private String name;

    /**
     * 是否有效
     */
    @Column(name = "valid")
    @ApiModelProperty(value = "是否有效",example = "true")
    private Boolean valid;

    /**
     * 描述
     */
    @Column(name = "description")
    @ApiModelProperty(value = "描述",example = "无")
    private String description;


    /**
     * 省调展示口径
     */
    @Column(name = "province_view")
    @ApiModelProperty(value = "省调展示口径",example = "无")
    private String provinceView;

    /**
     * 地调展示口径
     */
    @Column(name = "city_view")
    @ApiModelProperty(value = "地调展示口径",example = "无")
    private String cityView;


    /**
     * 排序
     */
    @Column(name = "order_no")
    @ApiModelProperty(value = "排序",example = "5")
    private Integer orderNo;

    @Override
    @ApiModelProperty(value = "id",example = "5")
    public String getKey() {
        return this.id;
    }

    @Override
    @ApiModelProperty(value = "id",example = "5")
    public String getLabel() {
        return this.name;
    }




}
