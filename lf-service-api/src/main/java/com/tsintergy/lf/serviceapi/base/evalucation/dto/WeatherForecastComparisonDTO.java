package com.tsintergy.lf.serviceapi.base.evalucation.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 气象预测对比DTO
 */
@Data
@ApiModel("气象预测对比")
public class WeatherForecastComparisonDTO {

    @ApiModelProperty("选中的日期")
    private Date selectedDate;

    @ApiModelProperty("选中的预测天数（D-1到D-10）")
    private Integer selectedDay;

    @ApiModelProperty("预测创建时间")
    private Date forecastCreateTime;

    @ApiModelProperty("预测气象数据（96点）")
    private List<BigDecimal> forecastWeatherData;

    @ApiModelProperty("实际气象数据（96点）")
    private List<BigDecimal> actualWeatherData;

    @ApiModelProperty("气象类型（1-湿度，2-温度，3-降雨量，4-风速）")
    private Integer weatherType;

    @ApiModelProperty("气象类型名称")
    private String weatherTypeName;
}
