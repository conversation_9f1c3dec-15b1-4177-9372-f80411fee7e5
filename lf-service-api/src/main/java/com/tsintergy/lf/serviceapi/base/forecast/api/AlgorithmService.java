
package com.tsintergy.lf.serviceapi.base.forecast.api;

import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmDO;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version $Id: AlgorithmService.java, v 0.1 2018-01-31 10:23:25 tao Exp $$
 */

public interface AlgorithmService {


    /**
     * find entity by PK
     */
    AlgorithmDO findAlgorithmVOByPk(Serializable pk) throws Exception;

    /**
     * 获取算法
     */
    AlgorithmDO getAlgorithmDOById(String algorithmId);

    /**
     * 获取算法中文名称
     */
    String getAlgorithmCn(String algorithmId);

    /**
     * 获取算法英文名称
     */
    String getAlgorithmEn(String algorithmId);

    /**
     * 获取全部算法
     */
    List<AlgorithmDO> getAllAlgorithms();

    /**
     * 获取算法
     */
    AlgorithmDO getAlgorithmVOByCode(String code);

    /**
     * 从数据库获取算法
     */
    List<AlgorithmDO> getAllAlgorithmsNotCache() throws Exception;
}